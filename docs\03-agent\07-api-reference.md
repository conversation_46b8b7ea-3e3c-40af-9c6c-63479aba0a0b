---
title: "Agent API Reference"
section: "Agent"
order: 7
tags: ["agent", "api", "rest", "endpoints"]
last_updated: "2025-11-08"
---

# Agent API Reference

## Introduction

The VM Gateway Agent exposes a local REST API for administrative operations, health checks, and metrics export. This API is primarily used for local management, monitoring integration (Prometheus), and troubleshooting. The API runs on `localhost:9090` by default and can optionally be secured with TLS.

**Base URL:** `http://localhost:9090`

**Authentication:** None (local access only) or API key (if configured)

**Content-Type:** `application/json`

## API Endpoints

### Health and Status

#### GET /health

Health check endpoint for monitoring systems.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-11-08T10:30:00Z",
  "uptime_seconds": 86400,
  "version": "a.1.0-15",
  "controller_connected": true,
  "last_heartbeat": "2025-11-08T10:29:30Z"
}
```

**Status Codes:**
- `200 OK` - Agent is healthy
- `503 Service Unavailable` - Agent is unhealthy

---

#### GET /status

Detailed agent status information.

**Response:**
```json
{
  "agent_id": "agent-web-01",
  "hostname": "web-server-01",
  "version": "a.1.0-15",
  "uptime_seconds": 86400,
  "status": {
    "controller_connected": true,
    "last_heartbeat": "2025-11-08T10:29:30Z",
    "last_scan": "2025-11-08T10:28:00Z",
    "discovered_services": 12,
    "active_tunnels": 2,
    "error_count": 0
  },
  "system": {
    "os": "Linux",
    "os_version": "5.15.0-91-generic",
    "cpu_count": 4,
    "total_memory_bytes": **********,
    "boot_time": "2025-11-07T10:30:00Z"
  },
  "configuration": {
    "scan_interval": "60s",
    "metrics_interval": "30s",
    "log_level": "INFO"
  }
}
```

**Status Codes:**
- `200 OK` - Success

---

### Service Discovery

#### GET /api/services

List all discovered services.

**Query Parameters:**
- `type` (optional) - Filter by service type (e.g., `web_server`, `database`)
- `status` (optional) - Filter by health status (`healthy`, `degraded`, `unhealthy`)
- `port` (optional) - Filter by port number

**Response:**
```json
{
  "services": [
    {
      "service_id": "svc-001",
      "port": 80,
      "protocol": "tcp",
      "bind_address": "0.0.0.0",
      "process": {
        "pid": 1234,
        "name": "nginx",
        "exe": "/usr/sbin/nginx",
        "cmdline": ["nginx", "-g", "daemon off;"],
        "username": "www-data",
        "create_time": "2025-11-07T10:35:00Z"
      },
      "classification": {
        "type": "web_server",
        "name": "Nginx",
        "confidence": 0.95,
        "tier": 1
      },
      "health": {
        "status": "healthy",
        "last_check": "2025-11-08T10:29:00Z",
        "response_time_ms": 15.2,
        "consecutive_failures": 0
      },
      "discovered_at": "2025-11-07T10:35:00Z",
      "last_seen": "2025-11-08T10:28:00Z"
    }
  ],
  "total": 12,
  "timestamp": "2025-11-08T10:30:00Z"
}
```

**Status Codes:**
- `200 OK` - Success

---

#### GET /api/services/{service_id}

Get detailed information about a specific service.

**Path Parameters:**
- `service_id` - Service identifier

**Response:**
```json
{
  "service_id": "svc-001",
  "port": 80,
  "protocol": "tcp",
  "bind_address": "0.0.0.0",
  "process": {
    "pid": 1234,
    "name": "nginx",
    "exe": "/usr/sbin/nginx",
    "cmdline": ["nginx", "-g", "daemon off;"],
    "username": "www-data",
    "cwd": "/",
    "create_time": "2025-11-07T10:35:00Z",
    "cpu_percent": 2.5,
    "memory_rss_bytes": 157286400,
    "num_threads": 4,
    "num_fds": 42
  },
  "classification": {
    "type": "web_server",
    "name": "Nginx",
    "version": "1.18.0",
    "confidence": 0.95,
    "tier": 1,
    "method": "exact_match"
  },
  "health": {
    "status": "healthy",
    "last_check": "2025-11-08T10:29:00Z",
    "response_time_ms": 15.2,
    "consecutive_failures": 0,
    "uptime_percent_24h": 99.95
  },
  "metrics": {
    "cpu_percent": 2.5,
    "memory_rss_bytes": 157286400,
    "connections_count": 25,
    "io_read_bytes": 1048576,
    "io_write_bytes": 2097152
  },
  "discovered_at": "2025-11-07T10:35:00Z",
  "last_seen": "2025-11-08T10:28:00Z"
}
```

**Status Codes:**
- `200 OK` - Success
- `404 Not Found` - Service not found

---

#### POST /api/scan

Trigger an immediate service discovery scan.

**Request Body:**
```json
{
  "force": true,
  "full_scan": true
}
```

**Response:**
```json
{
  "scan_id": "scan-abc123",
  "status": "started",
  "timestamp": "2025-11-08T10:30:00Z"
}
```

**Status Codes:**
- `202 Accepted` - Scan started
- `409 Conflict` - Scan already in progress

---

#### GET /api/scan/{scan_id}

Get status of a scan operation.

**Path Parameters:**
- `scan_id` - Scan identifier

**Response:**
```json
{
  "scan_id": "scan-abc123",
  "status": "completed",
  "started_at": "2025-11-08T10:30:00Z",
  "completed_at": "2025-11-08T10:30:03Z",
  "duration_ms": 3245,
  "services_discovered": 12,
  "services_added": 1,
  "services_removed": 0,
  "services_modified": 2
}
```

**Status Codes:**
- `200 OK` - Success
- `404 Not Found` - Scan not found

---

### Metrics

#### GET /metrics

Prometheus-compatible metrics endpoint.

**Response:** (Prometheus text format)
```
# HELP vm_gateway_cpu_usage_percent CPU usage percentage
# TYPE vm_gateway_cpu_usage_percent gauge
vm_gateway_cpu_usage_percent 15.2

# HELP vm_gateway_memory_usage_bytes Memory usage in bytes
# TYPE vm_gateway_memory_usage_bytes gauge
vm_gateway_memory_usage_bytes **********

# HELP vm_gateway_service_cpu_percent Service CPU usage
# TYPE vm_gateway_service_cpu_percent gauge
vm_gateway_service_cpu_percent{service_id="svc-001"} 2.5

# HELP vm_gateway_service_health Service health status
# TYPE vm_gateway_service_health gauge
vm_gateway_service_health{service_id="svc-001"} 1
```

**Status Codes:**
- `200 OK` - Success

---

#### GET /api/metrics/system

Get current system metrics in JSON format.

**Response:**
```json
{
  "timestamp": "2025-11-08T10:30:00Z",
  "cpu": {
    "total_percent": 15.2,
    "user_percent": 10.5,
    "system_percent": 4.7,
    "per_core_percent": [12.3, 18.5, 14.2, 16.0],
    "load_average_1m": 1.25,
    "load_average_5m": 1.50,
    "load_average_15m": 1.35
  },
  "memory": {
    "total_bytes": **********,
    "available_bytes": **********,
    "used_bytes": **********,
    "percent_used": 43.8,
    "swap_total_bytes": **********,
    "swap_used_bytes": 0,
    "swap_percent_used": 0.0
  },
  "disk": {
    "partitions": [
      {
        "mountpoint": "/",
        "total_bytes": **********00,
        "used_bytes": 53687091200,
        "free_bytes": 53687091200,
        "percent_used": 50.0
      }
    ]
  },
  "network": {
    "interfaces": {
      "eth0": {
        "bytes_sent": **********,
        "bytes_recv": **********,
        "packets_sent": 1000000,
        "packets_recv": 2000000
      }
    }
  }
}
```

**Status Codes:**
- `200 OK` - Success

---

#### GET /api/metrics/services

Get metrics for all services.

**Response:**
```json
{
  "timestamp": "2025-11-08T10:30:00Z",
  "services": [
    {
      "service_id": "svc-001",
      "cpu_percent": 2.5,
      "memory_rss_bytes": 157286400,
      "memory_percent": 1.8,
      "num_threads": 4,
      "num_fds": 42,
      "connections_count": 25,
      "io_read_bytes": 1048576,
      "io_write_bytes": 2097152
    }
  ]
}
```

**Status Codes:**
- `200 OK` - Success

---

### Configuration

#### GET /api/config

Get current agent configuration.

**Response:**
```json
{
  "agent": {
    "id": "agent-web-01",
    "hostname": "web-server-01",
    "version": "a.1.0-15"
  },
  "controller": {
    "url": "wss://controller.example.com:8443/agent/ws",
    "connected": true
  },
  "discovery": {
    "scan_interval": "60s",
    "enable_ipv6": true,
    "detect_docker": true
  },
  "metrics": {
    "system_metrics_interval": "30s",
    "service_metrics_interval": "60s",
    "retention_days": 7
  },
  "logging": {
    "level": "INFO",
    "file": "/var/log/vm-gateway/agent.log"
  }
}
```

**Status Codes:**
- `200 OK` - Success

---

#### PATCH /api/config

Update agent configuration (runtime changes only).

**Request Body:**
```json
{
  "discovery": {
    "scan_interval": "30s"
  },
  "logging": {
    "level": "DEBUG"
  }
}
```

**Response:**
```json
{
  "status": "updated",
  "changes": [
    "discovery.scan_interval: 60s -> 30s",
    "logging.level: INFO -> DEBUG"
  ],
  "restart_required": false
}
```

**Status Codes:**
- `200 OK` - Configuration updated
- `400 Bad Request` - Invalid configuration
- `403 Forbidden` - Configuration change not allowed

---

### Tunnels

#### GET /api/tunnels

List active tunnels.

**Response:**
```json
{
  "tunnels": [
    {
      "tunnel_id": "tunnel-123",
      "service_id": "svc-001",
      "client_id": "client-456",
      "established_at": "2025-11-08T10:25:00Z",
      "bytes_sent": 1048576,
      "bytes_received": 2097152,
      "status": "active"
    }
  ],
  "total": 2
}
```

**Status Codes:**
- `200 OK` - Success

---

#### DELETE /api/tunnels/{tunnel_id}

Close a specific tunnel.

**Path Parameters:**
- `tunnel_id` - Tunnel identifier

**Response:**
```json
{
  "tunnel_id": "tunnel-123",
  "status": "closed",
  "timestamp": "2025-11-08T10:30:00Z"
}
```

**Status Codes:**
- `200 OK` - Tunnel closed
- `404 Not Found` - Tunnel not found

---

### Logs

#### GET /api/logs

Retrieve recent log entries.

**Query Parameters:**
- `level` (optional) - Filter by log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `limit` (optional) - Number of entries to return (default: 100, max: 1000)
- `since` (optional) - ISO 8601 timestamp to retrieve logs after

**Response:**
```json
{
  "logs": [
    {
      "timestamp": "2025-11-08T10:30:00Z",
      "level": "INFO",
      "message": "Service discovery scan completed",
      "context": {
        "scan_duration_ms": 3245,
        "services_discovered": 12
      }
    },
    {
      "timestamp": "2025-11-08T10:29:30Z",
      "level": "DEBUG",
      "message": "Heartbeat sent to controller",
      "context": {
        "response_time_ms": 25
      }
    }
  ],
  "total": 2,
  "has_more": false
}
```

**Status Codes:**
- `200 OK` - Success

---

### Administrative Operations

#### POST /api/restart

Restart the agent (graceful restart).

**Response:**
```json
{
  "status": "restarting",
  "timestamp": "2025-11-08T10:30:00Z",
  "message": "Agent will restart in 5 seconds"
}
```

**Status Codes:**
- `202 Accepted` - Restart initiated

---

#### POST /api/reload

Reload configuration without restarting.

**Response:**
```json
{
  "status": "reloaded",
  "timestamp": "2025-11-08T10:30:00Z",
  "changes": [
    "discovery.scan_interval updated",
    "logging.level updated"
  ]
}
```

**Status Codes:**
- `200 OK` - Configuration reloaded
- `400 Bad Request` - Invalid configuration

---

#### GET /api/diagnostics

Generate diagnostic information for troubleshooting.

**Response:**
```json
{
  "agent": {
    "id": "agent-web-01",
    "version": "a.1.0-15",
    "uptime_seconds": 86400,
    "pid": 1234
  },
  "system": {
    "os": "Linux",
    "kernel": "5.15.0-91-generic",
    "cpu_count": 4,
    "total_memory_bytes": **********
  },
  "connectivity": {
    "controller_connected": true,
    "last_heartbeat": "2025-11-08T10:29:30Z",
    "connection_latency_ms": 25
  },
  "discovery": {
    "last_scan": "2025-11-08T10:28:00Z",
    "scan_duration_ms": 3245,
    "services_discovered": 12,
    "scan_errors": 0
  },
  "metrics": {
    "pending_upload": 150,
    "last_upload": "2025-11-08T10:25:00Z",
    "upload_errors": 0
  },
  "resource_usage": {
    "cpu_percent": 0.8,
    "memory_rss_bytes": 104857600,
    "num_threads": 8,
    "num_fds": 25
  },
  "errors": []
}
```

**Status Codes:**
- `200 OK` - Success

---

## Error Responses

All error responses follow this format:

```json
{
  "error": {
    "code": "SERVICE_NOT_FOUND",
    "message": "Service with ID 'svc-999' not found",
    "timestamp": "2025-11-08T10:30:00Z",
    "request_id": "req-abc123"
  }
}
```

### Common Error Codes

- `INVALID_REQUEST` - Malformed request
- `SERVICE_NOT_FOUND` - Requested service doesn't exist
- `SCAN_IN_PROGRESS` - Cannot start scan while another is running
- `CONFIGURATION_ERROR` - Invalid configuration
- `INTERNAL_ERROR` - Internal server error
- `UNAUTHORIZED` - Authentication required (if API key enabled)

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Default Limit:** 100 requests per minute per IP
- **Burst:** 20 requests
- **Headers:**
  - `X-RateLimit-Limit` - Request limit
  - `X-RateLimit-Remaining` - Remaining requests
  - `X-RateLimit-Reset` - Time when limit resets (Unix timestamp)

**Rate Limit Exceeded Response:**
```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded. Try again in 30 seconds.",
    "retry_after": 30
  }
}
```

**Status Code:** `429 Too Many Requests`

## Authentication (Optional)

When API key authentication is enabled:

**Header:**
```
Authorization: Bearer <api_key>
```

**Example:**
```bash
curl -H "Authorization: Bearer sk_live_abc123..." http://localhost:9090/api/services
```

## WebSocket API

For real-time updates, the agent exposes a WebSocket endpoint:

**Endpoint:** `ws://localhost:9090/ws`

**Message Format:**
```json
{
  "type": "service_discovered",
  "timestamp": "2025-11-08T10:30:00Z",
  "data": {
    "service_id": "svc-013",
    "port": 3000,
    "classification": {
      "type": "web_server",
      "name": "Node.js Application"
    }
  }
}
```

**Event Types:**
- `service_discovered` - New service found
- `service_removed` - Service stopped
- `service_modified` - Service configuration changed
- `health_status_changed` - Service health changed
- `scan_completed` - Discovery scan finished

## Code Examples

### Python

```python
import requests

# Get all services
response = requests.get('http://localhost:9090/api/services')
services = response.json()['services']

for service in services:
    print(f"{service['classification']['name']} on port {service['port']}")

# Trigger scan
response = requests.post('http://localhost:9090/api/scan', json={'force': True})
scan_id = response.json()['scan_id']

# Get system metrics
response = requests.get('http://localhost:9090/api/metrics/system')
metrics = response.json()
print(f"CPU: {metrics['cpu']['total_percent']}%")
print(f"Memory: {metrics['memory']['percent_used']}%")
```

### cURL

```bash
# Health check
curl http://localhost:9090/health

# List services
curl http://localhost:9090/api/services

# Get specific service
curl http://localhost:9090/api/services/svc-001

# Trigger scan
curl -X POST http://localhost:9090/api/scan -H "Content-Type: application/json" -d '{"force": true}'

# Get Prometheus metrics
curl http://localhost:9090/metrics

# Get logs
curl "http://localhost:9090/api/logs?level=ERROR&limit=50"
```

### JavaScript

```javascript
// Fetch services
fetch('http://localhost:9090/api/services')
  .then(response => response.json())
  .then(data => {
    data.services.forEach(service => {
      console.log(`${service.classification.name} on port ${service.port}`);
    });
  });

// WebSocket for real-time updates
const ws = new WebSocket('ws://localhost:9090/ws');

ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  console.log(`Event: ${message.type}`, message.data);
};

ws.onerror = (error) => {
  console.error('WebSocket error:', error);
};
```

## Summary

The Agent API provides comprehensive programmatic access to agent functionality for monitoring, management, and integration. The REST API offers simple HTTP access for most operations, while the WebSocket API enables real-time event streaming.

Key features:

- **RESTful Design**: Standard HTTP methods and status codes
- **Prometheus Compatible**: Native metrics export
- **Real-Time Updates**: WebSocket for live events
- **Comprehensive**: Full access to discovery, metrics, and configuration
- **Secure**: Optional API key authentication and rate limiting

## Next Steps

- **[Installation](06-installation.md)**: Configure the API during agent installation
- **[Overview](01-overview.md)**: Review agent architecture and capabilities
- **[Metrics Collection](04-metrics-collection.md)**: Understand the metrics exposed by the API
