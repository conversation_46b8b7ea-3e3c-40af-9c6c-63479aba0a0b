---
title: "Common Issues & Solutions"
section: "Troubleshooting"
order: 1
tags: ["troubleshooting", "errors", "solutions", "debugging"]
last_updated: "2025-11-09"
---

# Common Issues & Solutions

## Overview

This comprehensive troubleshooting guide covers the most common issues encountered when deploying, configuring, and operating VM Gateway. Each issue includes symptoms, root causes, diagnostic steps, and detailed solutions.

## Table of Contents

1. [Controller Issues](#controller-issues)
2. [Agent Issues](#agent-issues)
3. [Client Issues](#client-issues)
4. [Authentication Issues](#authentication-issues)
5. [Network & Connectivity Issues](#network--connectivity-issues)
6. [Database Issues](#database-issues)
7. [Performance Issues](#performance-issues)
8. [Service Discovery Issues](#service-discovery-issues)

---

## Controller Issues

### Issue: Controller Fails to Start

**Symptoms**:
- Controller process exits immediately after starting
- Error message: "Failed to bind to port"
- Error message: "Database connection failed"
- No web interface accessible

**Common Causes**:
1. Port already in use
2. Database not accessible
3. Redis not running
4. Invalid configuration
5. Missing dependencies
6. Permission issues

**Diagnostic Steps**:

```bash
# Check if port is in use
netstat -ano | findstr :8000

# Check database connectivity
psql -h localhost -U vmgateway -d vmgateway -c "SELECT 1;"

# Check Redis connectivity
redis-cli ping

# Check controller logs
tail -f logs/controller/controller.log

# Check systemd status (Linux)
systemctl status vm-gateway-controller

# Verify configuration
python -m vm_gateway.controller.config validate
```

**Solutions**:

**Solution 1: Port Already in Use**
```bash
# Find process using port
netstat -ano | findstr :8000

# Kill the process (Windows)
taskkill /PID <PID> /F

# Or change controller port in .env
CONTROLLER_PORT=8080
```

**Solution 2: Database Connection Failed**
```bash
# Verify database is running
systemctl status postgresql

# Test connection
psql -h localhost -U vmgateway -d vmgateway

# Check DATABASE_URL in .env
DATABASE_URL=postgresql://vmgateway:password@localhost:5432/vmgateway

# Reset database password
sudo -u postgres psql
ALTER USER vmgateway WITH PASSWORD 'newpassword';
```

**Solution 3: Redis Not Running**
```bash
# Start Redis
systemctl start redis-server

# Verify Redis is running
redis-cli ping

# Check REDIS_URL in .env
REDIS_URL=redis://localhost:6379/0
```

**Solution 4: Missing Dependencies**
```bash
# Reinstall dependencies
pip install -r requirements.txt

# Or reinstall package
pip install -e .
```

---

### Issue: Controller Running But Web Interface Not Accessible

**Symptoms**:
- Controller process is running
- Cannot access web interface in browser
- Connection timeout or refused
- 502 Bad Gateway error

**Common Causes**:
1. Firewall blocking port
2. Nginx misconfiguration
3. Controller bound to wrong interface
4. TLS certificate issues
5. DNS issues

**Diagnostic Steps**:

```bash
# Check if controller is listening
netstat -ano | findstr :8000

# Test local access
curl http://localhost:8000/health

# Check firewall rules (Windows)
netsh advfirewall firewall show rule name=all | findstr 8000

# Check Nginx status
systemctl status nginx

# Check Nginx error log
tail -f /var/log/nginx/error.log

# Test DNS resolution
nslookup your-controller-domain.com
```

**Solutions**:

**Solution 1: Firewall Blocking**
```bash
# Windows: Add firewall rule
netsh advfirewall firewall add rule name="VM Gateway" dir=in action=allow protocol=TCP localport=8000

# Linux: Add iptables rule
sudo iptables -A INPUT -p tcp --dport 8000 -j ACCEPT
sudo iptables-save
```

**Solution 2: Controller Bound to Wrong Interface**
```bash
# In .env, ensure controller binds to all interfaces
CONTROLLER_HOST=0.0.0.0

# Or bind to specific interface
CONTROLLER_HOST=*************

# Restart controller
systemctl restart vm-gateway-controller
```

**Solution 3: Nginx Misconfiguration**
```bash
# Test Nginx configuration
nginx -t

# Check upstream is correct
cat /etc/nginx/sites-enabled/vm-gateway

# Reload Nginx
systemctl reload nginx
```

---

### Issue: High Memory Usage

**Symptoms**:
- Controller using excessive RAM (>4GB)
- System becomes slow
- Out of memory errors
- Controller crashes

**Common Causes**:
1. Too many concurrent connections
2. Memory leak in application
3. Large metrics dataset
4. Insufficient database connection pooling
5. Large audit logs in memory

**Diagnostic Steps**:

```bash
# Check memory usage
ps aux | grep vm_gateway

# Check Python memory usage
python -m memory_profiler vm_gateway/controller/main.py

# Check database connection pool
psql -U vmgateway -d vmgateway -c "SELECT count(*) FROM pg_stat_activity;"

# Check Redis memory
redis-cli info memory

# Monitor in real-time
top -p $(pgrep -f vm_gateway)
```

**Solutions**:

**Solution 1: Adjust Database Connection Pool**
```bash
# In .env
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=5

# Restart controller
systemctl restart vm-gateway-controller
```

**Solution 2: Enable Metrics Aggregation**
```bash
# In .env
METRICS_AGGREGATION_ENABLED=true
METRICS_RETENTION_DAYS=30

# This reduces memory by aggregating old metrics
```

**Solution 3: Increase System Resources**
```bash
# Allocate more RAM to controller
# Edit systemd service file
sudo nano /etc/systemd/system/vm-gateway-controller.service

# Add memory limit
[Service]
MemoryLimit=8G

sudo systemctl daemon-reload
sudo systemctl restart vm-gateway-controller
```

---

## Agent Issues

### Issue: Agent Not Connecting to Controller

**Symptoms**:
- Agent shows "Disconnected" status in controller
- Agent logs show connection errors
- Services not appearing in catalog
- No metrics being reported

**Common Causes**:
1. Network connectivity issues
2. Invalid registration token
3. TLS certificate verification failure
4. Firewall blocking connection
5. Controller URL incorrect
6. Agent not running

**Diagnostic Steps**:

```bash
# Check agent status
systemctl status vm-gateway-agent

# Check agent logs
tail -f /var/log/vm-gateway-agent/agent.log

# Test connectivity to controller
curl https://your-controller/health

# Test WebSocket connection
wscat -c wss://your-controller/ws/agent

# Verify agent configuration
cat /opt/vm-gateway-agent/config.yaml

# Check network connectivity
ping your-controller
traceroute your-controller
```

**Solutions**:

**Solution 1: Invalid Registration Token**
```bash
# Generate new token in controller web UI
# Or via CLI
vm-gateway-cli generate-token --name "Production Server"

# Update agent config
sudo nano /opt/vm-gateway-agent/config.yaml

controller:
  token: NEW_TOKEN_HERE

# Restart agent
sudo systemctl restart vm-gateway-agent
```

**Solution 2: TLS Certificate Issues**
```bash
# Temporarily disable SSL verification (NOT for production)
# In agent config.yaml
controller:
  verify_ssl: false

# Or add custom CA certificate
controller:
  ca_cert: /path/to/ca-cert.pem

# Restart agent
sudo systemctl restart vm-gateway-agent
```

**Solution 3: Firewall Blocking**
```bash
# Test if port is accessible
telnet your-controller 443

# Windows: Add firewall rule for outbound
netsh advfirewall firewall add rule name="VM Gateway Agent" dir=out action=allow protocol=TCP remoteport=443

# Linux: Check iptables
sudo iptables -L OUTPUT -v -n
```

---

### Issue: Services Not Being Discovered

**Symptoms**:
- Agent connected but no services shown
- Some services missing from catalog
- Services discovered but not classified correctly
- Stale services not removed

**Common Causes**:
1. Agent doesn't have permission to scan ports
2. Services running on non-standard ports
3. Firewall blocking local port scanning
4. Classification rules too restrictive
5. Scan interval too long

**Diagnostic Steps**:

```bash
# Check agent permissions
ps aux | grep vm-gateway-agent

# Manually scan ports
netstat -ano | findstr LISTENING

# Check agent logs for discovery errors
grep "discovery" /var/log/vm-gateway-agent/agent.log

# Force immediate scan
curl -X POST http://localhost:9090/api/scan

# Check classification logs
grep "classification" /var/log/vm-gateway-agent/agent.log
```

**Solutions**:

**Solution 1: Grant Agent Permissions**
```bash
# Linux: Run agent with CAP_NET_RAW capability
sudo setcap cap_net_raw+ep /opt/vm-gateway-agent/venv/bin/python

# Or run agent as root (not recommended)
sudo systemctl edit vm-gateway-agent
[Service]
User=root

sudo systemctl daemon-reload
sudo systemctl restart vm-gateway-agent
```

**Solution 2: Adjust Scan Configuration**
```yaml
# In agent config.yaml
discovery:
  scan_interval: 30  # Scan every 30 seconds
  port_range: "1-65535"  # Scan all ports
  include_localhost: true
  scan_udp: true  # Also scan UDP ports

classification:
  confidence_threshold: 0.5  # Lower threshold for more matches
  enable_ml: true
```

**Solution 3: Add Custom Classification Rules**
```yaml
# In agent config.yaml
classification:
  custom_rules:
    - port: 8080
      process_pattern: "java.*tomcat"
      service_type: "web"
      name: "Tomcat"
    - port: 3306
      service_type: "database"
      name: "MySQL"
```

---

## Authentication Issues

### Issue: Cannot Login to Web Interface

**Symptoms**:
- "Invalid credentials" error
- Login page redirects back to itself
- Session expires immediately
- MFA code not accepted

**Common Causes**:
1. Incorrect username/password
2. Account locked after failed attempts
3. MFA misconfigured
4. Session cookie issues
5. Database connection issues
6. JWT secret changed

**Diagnostic Steps**:

```bash
# Check user exists in database
psql -U vmgateway -d vmgateway -c "SELECT email, is_active, failed_login_attempts FROM users WHERE email='<EMAIL>';"

# Check controller logs for auth errors
grep "authentication" logs/controller/controller.log

# Verify JWT secret is set
grep JWT_SECRET .env

# Test API authentication
curl -X POST https://your-controller/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"<EMAIL>","password":"password"}'

# Check session storage (Redis)
redis-cli keys "session:*"
```

**Solutions**:

**Solution 1: Reset User Password**
```bash
# Via CLI
python -m vm_gateway.cli reset-password --email <EMAIL>

# Or via database
psql -U vmgateway -d vmgateway
UPDATE users SET password_hash = crypt('newpassword', gen_salt('bf')) WHERE email = '<EMAIL>';
UPDATE users SET failed_login_attempts = 0, locked_until = NULL WHERE email = '<EMAIL>';
```

**Solution 2: Unlock Account**
```bash
# Via CLI
python -m vm_gateway.cli unlock-user --email <EMAIL>

# Or via database
psql -U vmgateway -d vmgateway
UPDATE users SET failed_login_attempts = 0, locked_until = NULL WHERE email = '<EMAIL>';
```

**Solution 3: Reset MFA**
```bash
# Via CLI
python -m vm_gateway.cli disable-mfa --email <EMAIL>

# User will need to re-enable MFA after login
```

**Solution 4: Clear Sessions**
```bash
# Clear all Redis sessions
redis-cli FLUSHDB

# Or clear specific user sessions
redis-cli keys "session:user:*" | xargs redis-cli DEL
```

---

### Issue: SSO Authentication Not Working

**Symptoms**:
- SSO login button doesn't appear
- Redirect to IdP fails
- "SAML assertion invalid" error
- User created but no roles assigned

**Common Causes**:
1. SSO not enabled in configuration
2. Metadata XML incorrect
3. Certificate mismatch
4. Attribute mapping incorrect
5. Clock skew between systems
6. IdP not configured correctly

**Diagnostic Steps**:

```bash
# Check SSO configuration
grep SSO .env

# Check SAML metadata
cat /opt/vm-gateway/saml-metadata.xml

# Check controller logs for SAML errors
grep "SAML" logs/controller/controller.log

# Verify system time
date
ntpq -p

# Test SAML assertion
# Use browser dev tools to capture SAML response
```

**Solutions**:

**Solution 1: Enable SSO**
```bash
# In .env
SSO_ENABLED=true
SSO_TYPE=saml
SAML_METADATA_PATH=/opt/vm-gateway/saml-metadata.xml

# Restart controller
systemctl restart vm-gateway-controller
```

**Solution 2: Fix Clock Skew**
```bash
# Sync system time
sudo ntpdate pool.ntp.org

# Or enable NTP
sudo systemctl enable systemd-timesyncd
sudo systemctl start systemd-timesyncd
```

**Solution 3: Update Attribute Mapping**
```yaml
# In controller configuration
sso:
  saml:
    attribute_mapping:
      email: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
      name: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"
      groups: "http://schemas.xmlsoap.org/claims/Group"
```

---

## Network & Connectivity Issues

### Issue: Cannot Connect to Services Through Proxy

**Symptoms**:
- 502 Bad Gateway error
- Connection timeout
- "Service unavailable" error
- Proxy works for some services but not others

**Common Causes**:
1. Agent offline
2. Service not running
3. Network connectivity between controller and agent
4. Firewall blocking connection
5. Service listening on localhost only
6. Proxy timeout too short

**Diagnostic Steps**:

```bash
# Check agent status in controller
curl https://your-controller/api/v1/agents

# Check service status
curl https://your-controller/api/v1/services/SERVICE_ID

# Test direct connection from controller to agent
ssh agent-vm
curl http://localhost:SERVICE_PORT

# Check proxy logs
grep "proxy" logs/controller/controller.log

# Test WebSocket connection
wscat -c wss://your-controller/ws/proxy/SERVICE_ID
```

**Solutions**:

**Solution 1: Increase Proxy Timeout**
```bash
# In .env
PROXY_TIMEOUT=300  # 5 minutes

# Restart controller
systemctl restart vm-gateway-controller
```

**Solution 2: Configure Service to Listen on All Interfaces**
```bash
# Example for PostgreSQL
# Edit postgresql.conf
listen_addresses = '*'

# Restart PostgreSQL
systemctl restart postgresql
```

**Solution 3: Add Firewall Rules on Agent VM**
```bash
# Allow controller to connect
sudo iptables -A INPUT -s CONTROLLER_IP -p tcp --dport SERVICE_PORT -j ACCEPT
```

---

## Database Issues

### Issue: Database Connection Pool Exhausted

**Symptoms**:
- "Too many connections" error
- Slow API responses
- Timeouts on database queries
- Controller becomes unresponsive

**Common Causes**:
1. Connection pool too small
2. Connections not being released
3. Long-running queries
4. Too many concurrent users
5. Database max_connections too low

**Diagnostic Steps**:

```bash
# Check active connections
psql -U vmgateway -d vmgateway -c "SELECT count(*) FROM pg_stat_activity;"

# Check max connections
psql -U vmgateway -d vmgateway -c "SHOW max_connections;"

# Check long-running queries
psql -U vmgateway -d vmgateway -c "SELECT pid, now() - query_start as duration, query FROM pg_stat_activity WHERE state = 'active' ORDER BY duration DESC;"

# Check connection pool settings
grep DATABASE .env
```

**Solutions**:

**Solution 1: Increase Connection Pool**
```bash
# In .env
DATABASE_POOL_SIZE=50
DATABASE_MAX_OVERFLOW=20

# Restart controller
systemctl restart vm-gateway-controller
```

**Solution 2: Increase Database Max Connections**
```bash
# Edit postgresql.conf
max_connections = 200

# Restart PostgreSQL
systemctl restart postgresql
```

**Solution 3: Kill Long-Running Queries**
```sql
-- Find long-running queries
SELECT pid, now() - query_start as duration, query 
FROM pg_stat_activity 
WHERE state = 'active' 
ORDER BY duration DESC;

-- Kill specific query
SELECT pg_terminate_backend(PID);
```

---

## Performance Issues

### Issue: Slow Web Interface

**Symptoms**:
- Pages take >5 seconds to load
- API requests timeout
- Dashboard doesn't update
- High CPU usage on controller

**Common Causes**:
1. Too many services in catalog
2. Inefficient database queries
3. No caching enabled
4. Large metrics dataset
5. Insufficient resources

**Diagnostic Steps**:

```bash
# Check controller CPU/memory
top -p $(pgrep -f vm_gateway)

# Check database query performance
psql -U vmgateway -d vmgateway -c "SELECT query, calls, total_time, mean_time FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"

# Check Redis performance
redis-cli --latency

# Profile API requests
curl -w "@curl-format.txt" -o /dev/null -s https://your-controller/api/v1/services

# Check network latency
ping your-controller
```

**Solutions**:

**Solution 1: Enable Caching**
```bash
# In .env
CACHE_ENABLED=true
CACHE_TTL=300  # 5 minutes

# Restart controller
systemctl restart vm-gateway-controller
```

**Solution 2: Optimize Database**
```sql
-- Analyze tables
ANALYZE;

-- Vacuum tables
VACUUM ANALYZE;

-- Add missing indexes
CREATE INDEX idx_services_vm_id ON services(vm_id);
CREATE INDEX idx_metrics_timestamp ON metrics(timestamp);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);
```

**Solution 3: Archive Old Data**
```bash
# Archive old metrics
python -m vm_gateway.cli archive-metrics --older-than 90

# Archive old audit logs
python -m vm_gateway.cli archive-audit-logs --older-than 365
```

---

## Service Discovery Issues

### Issue: Duplicate Services Appearing

**Symptoms**:
- Same service appears multiple times
- Services with different IDs but same details
- Service count keeps increasing

**Common Causes**:
1. Multiple agents on same VM
2. Service fingerprinting not working
3. Port reuse by different services
4. Agent restarting frequently

**Diagnostic Steps**:

```bash
# Check for duplicate agents
curl https://your-controller/api/v1/agents | jq '.[] | select(.hostname=="HOSTNAME")'

# Check service fingerprints
psql -U vmgateway -d vmgateway -c "SELECT id, name, port, fingerprint FROM services WHERE vm_id='VM_ID';"

# Check agent logs
grep "duplicate" /var/log/vm-gateway-agent/agent.log
```

**Solutions**:

**Solution 1: Remove Duplicate Agents**
```bash
# Via web UI: Go to Agents, delete duplicate agents

# Via CLI
python -m vm_gateway.cli delete-agent --id AGENT_ID

# Ensure only one agent per VM
systemctl status vm-gateway-agent
```

**Solution 2: Enable Service Fingerprinting**
```yaml
# In agent config.yaml
discovery:
  fingerprinting:
    enabled: true
    methods:
      - process_hash
      - port_signature
      - service_banner
```

---

## Related Documentation

- [Agent Installation Guide](/docs/03-agent/06-installation.md)
- [Controller Configuration](/docs/04-controller/03-api-server.md)
- [Authentication Setup](/docs/06-authentication/01-overview.md)
- [Performance Tuning](/docs/10-development/06-performance.md)
- [Debugging Guide](/docs/10-development/05-debugging.md)
- [Error Messages Reference](/docs/11-troubleshooting/02-error-messages.md)
- [Network Troubleshooting](/docs/11-troubleshooting/03-network-issues.md)

## Summary

This troubleshooting guide covers the most common issues encountered with VM Gateway. For issues not covered here, check the detailed error messages reference, enable debug logging, and consult the community forums or support channels. Remember to always check logs first, verify configuration, and test connectivity before making changes.

