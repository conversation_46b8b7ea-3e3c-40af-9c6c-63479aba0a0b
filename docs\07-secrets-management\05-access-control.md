# Secret Access Control

The VM Gateway platform implements fine-grained access control for secrets, ensuring that only authorized users and services can access sensitive credentials.

## Permission Model

### Permission Types

**Read Metadata**: View secret name, tags, creation date (not value)
**Read Value**: Decrypt and retrieve secret value
**Write**: Create or update secrets
**Delete**: Remove secrets
**Manage Permissions**: Grant/revoke access to others
**Rotate**: Trigger secret rotation

### Permission Levels

**Owner**: Full control over secret
**Admin**: Read, write, delete, manage permissions
**Writer**: Read and write secret values
**Reader**: Read secret values only
**Viewer**: Read metadata only (not values)

## Access Control Lists (ACLs)

Each secret has an ACL defining who can access it:

```json
{
  "secret_id": "secret_abc123",
  "owner": "user_xyz789",
  "permissions": [
    {
      "principal": "user_xyz789",
      "type": "user",
      "level": "owner"
    },
    {
      "principal": "group_admins",
      "type": "group",
      "level": "admin"
    },
    {
      "principal": "service_agent_*",
      "type": "service_pattern",
      "level": "reader"
    }
  ]
}
```

## Role-Based Access Control (RBAC)

Integrate with platform RBAC system:

**Roles**:
- `secrets:admin` - Full secret management
- `secrets:writer` - Create and update secrets
- `secrets:reader` - Read secret values
- `secrets:viewer` - View metadata only

**Policy Example**:
```yaml
roles:
  - name: database_admin
    permissions:
      - secrets:read:database/*
      - secrets:write:database/*
      - secrets:rotate:database/*
  
  - name: developer
    permissions:
      - secrets:read:dev/*
      - secrets:view:prod/*
```


## Principal Types

### Users

Individual user accounts:

```json
{
  "principal_id": "user_xyz789",
  "principal_type": "user",
  "email": "<EMAIL>",
  "permissions": ["read_value", "write", "manage_permissions"]
}
```

### Groups

User groups for easier management:

```json
{
  "principal_id": "group_admins",
  "principal_type": "group",
  "members": ["user_xyz789", "user_abc456"],
  "permissions": ["read_value", "write", "delete"]
}
```

### Service Accounts

Automated services and agents:

```json
{
  "principal_id": "service_agent_001",
  "principal_type": "service",
  "description": "Production agent on host-01",
  "permissions": ["read_value"]
}
```

### Patterns

Wildcard patterns for dynamic access:

```json
{
  "principal_pattern": "service_agent_*",
  "principal_type": "service_pattern",
  "permissions": ["read_value"],
  "conditions": {
    "tags": ["production"],
    "ip_ranges": ["10.0.0.0/8"]
  }
}
```

## Permission Inheritance

### Hierarchical Permissions

Permissions can be inherited through hierarchy:

```
/prod/
  ├── database/
  │   ├── primary/password    (inherits /prod/ permissions)
  │   └── replica/password    (inherits /prod/ permissions)
  └── api/
      └── external/key        (inherits /prod/ permissions)
```

**Configuration**:
```yaml
permissions:
  - path: /prod/*
    principal: group_prod_admins
    level: admin
    inherit: true
```

### Group Membership

Users inherit permissions from groups:

```python
def get_effective_permissions(user_id: str, secret_id: str):
    """Calculate effective permissions for user"""
    permissions = set()
    
    # Direct user permissions
    permissions.update(get_user_permissions(user_id, secret_id))
    
    # Group permissions
    for group in get_user_groups(user_id):
        permissions.update(get_group_permissions(group, secret_id))
    
    # Role permissions
    for role in get_user_roles(user_id):
        permissions.update(get_role_permissions(role, secret_id))
    
    return list(permissions)
```

## Access Control Policies

### Policy-Based Access Control

Define policies for fine-grained control:

```yaml
policies:
  - name: production_database_access
    description: Access to production database secrets
    principals:
      - group_database_admins
      - service_backup_*
    resources:
      - secret:prod/database/*
    permissions:
      - read_value
      - rotate
    conditions:
      time_range:
        start: "00:00"
        end: "23:59"
      ip_ranges:
        - "10.0.0.0/8"
        - "**********/12"
      mfa_required: true
  
  - name: developer_staging_access
    description: Developer access to staging secrets
    principals:
      - group_developers
    resources:
      - secret:staging/*
    permissions:
      - read_value
      - read_metadata
    conditions:
      time_range:
        start: "06:00"
        end: "22:00"
      require_vpn: true
```

### Conditional Access

Apply conditions to access grants:

**Time-Based Access**:
```python
condition:
  type: time_range
  start_time: "09:00"
  end_time: "17:00"
  timezone: "America/New_York"
  days: ["monday", "tuesday", "wednesday", "thursday", "friday"]
```

**Location-Based Access**:
```python
condition:
  type: ip_range
  allowed_ranges:
    - "10.0.0.0/8"
    - "**********/12"
  denied_ranges:
    - "**********/24"  # Quarantine network
```

**MFA Requirement**:
```python
condition:
  type: mfa_required
  methods: ["totp", "webauthn"]
  grace_period_hours: 8
```

## Access Request Workflow

### Request Process

Users can request access to secrets:

1. **Submit Request**:
```python
POST /api/secrets/{secret_id}/access-requests
{
  "requested_permission": "read_value",
  "justification": "Need access for incident response",
  "duration_hours": 24
}
```

2. **Approval Workflow**:
- Notify secret owner
- Notify approvers (based on policy)
- Require N approvals
- Optional: Require MFA for approval

3. **Grant Access**:
- Temporary permission grant
- Audit log entry
- Notification to requester
- Auto-revoke after duration

4. **Revocation**:
- Automatic after duration
- Manual revocation by owner
- Emergency revocation by security team

### Approval Policies

Configure approval requirements:

```yaml
approval_policies:
  - name: production_secrets
    resources:
      - secret:prod/*
    requirements:
      approvers: 2
      approver_groups:
        - group_security_team
        - group_prod_admins
      max_duration_hours: 24
      require_justification: true
      require_mfa: true
  
  - name: sensitive_secrets
    resources:
      - secret:*/database/*
      - secret:*/api-keys/*
    requirements:
      approvers: 1
      approver_groups:
        - group_security_team
      max_duration_hours: 8
      require_justification: true
```

## Access Auditing

### Access Logs

All access attempts are logged:

```python
{
  "timestamp": "2025-11-08T10:30:00Z",
  "actor": "user_xyz789",
  "secret_id": "secret_abc123",
  "action": "read_value",
  "result": "success",
  "ip_address": "*********",
  "user_agent": "vm-gateway-cli/1.0",
  "mfa_verified": true
}
```

### Access Reviews

Periodic access reviews:

```python
def generate_access_review(secret_id: str):
    """Generate access review report"""
    return {
        "secret_id": secret_id,
        "secret_name": get_secret_name(secret_id),
        "current_permissions": get_all_permissions(secret_id),
        "recent_access": get_access_logs(secret_id, days=90),
        "unused_permissions": find_unused_permissions(secret_id, days=90),
        "recommendations": [
            "Remove unused permissions",
            "Review service account access",
            "Update group memberships"
        ]
    }
```

## Emergency Access

### Break-Glass Access

Emergency access for critical situations:

```python
def request_emergency_access(secret_id: str, justification: str):
    """Request emergency break-glass access"""
    # Grant immediate access
    grant_temporary_permission(
        secret_id=secret_id,
        principal=current_user(),
        permission="read_value",
        duration_hours=1
    )
    
    # High-priority alert
    alert_security_team(
        level="critical",
        message=f"Emergency access granted to {secret_id}",
        user=current_user(),
        justification=justification
    )
    
    # Detailed audit log
    audit_log("secret.emergency_access", {
        "secret_id": secret_id,
        "user": current_user(),
        "justification": justification,
        "granted_at": now()
    })
```

### Emergency Revocation

Revoke all access in emergency:

```python
def emergency_revoke_all_access(secret_id: str):
    """Emergency revocation of all access"""
    # Revoke all permissions except owner
    revoke_all_permissions(secret_id, except_owner=True)
    
    # Rotate secret immediately
    rotate_secret(secret_id, emergency=True)
    
    # Alert all stakeholders
    notify_emergency_revocation(secret_id)
    
    # Audit log
    audit_log("secret.emergency_revocation", {
        "secret_id": secret_id,
        "revoked_by": current_user(),
        "revoked_at": now()
    })
```

## Best Practices

1. **Principle of least privilege**: Grant minimum necessary permissions
2. **Use groups**: Manage permissions through groups, not individual users
3. **Regular reviews**: Review and audit permissions quarterly
4. **Time-limited access**: Use temporary permissions for one-time needs
5. **Conditional access**: Apply conditions based on context
6. **Approval workflows**: Require approval for sensitive secrets
7. **Monitor access**: Alert on unusual access patterns
8. **Document policies**: Clear documentation of access policies

## Integration with Platform RBAC

### Unified Permission Model

Integrate with platform-wide RBAC:

```python
# Platform roles automatically grant secret permissions
platform_roles:
  - name: platform_admin
    secret_permissions:
      - secrets:*:*  # Full access to all secrets
  
  - name: developer
    secret_permissions:
      - secrets:read:dev/*
      - secrets:read:staging/*
      - secrets:view:prod/*
  
  - name: operator
    secret_permissions:
      - secrets:read:prod/*
      - secrets:rotate:prod/*
```

## Next Steps

- [Audit](07-audit.md) - Audit logging and compliance
- [Rotation](06-rotation.md) - Automatic rotation policies
- [Lifecycle](04-lifecycle.md) - Secret lifecycle management
- [Overview](01-overview.md) - Secrets management overview
