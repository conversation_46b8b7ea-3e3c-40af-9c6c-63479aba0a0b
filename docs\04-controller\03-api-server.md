---
title: "API Server"
section: "Controller"
order: 3
tags: ["controller", "api", "rest", "endpoints"]
last_updated: "2025-11-08"
---

# API Server

The Controller's API server provides a comprehensive REST API for programmatic access to all platform functionality. Built with FastAPI, the API offers automatic OpenAPI documentation, request validation, and high-performance async request handling.

## Architecture

### FastAPI Framework

FastAPI provides the foundation for the API server with several key advantages:

**Automatic Documentation**: FastAPI generates interactive OpenAPI (Swagger) and ReDoc documentation automatically from code annotations and type hints.

**Request Validation**: Pydantic models validate all incoming requests, ensuring data integrity and providing clear error messages for invalid requests.

**Async Support**: Native async/await support enables high-concurrency request handling without blocking.

**Type Safety**: Python type hints provide IDE autocomplete and catch errors at development time.

**Performance**: FastAPI is one of the fastest Python frameworks, comparable to Node.js and Go frameworks.

### API Design Principles

**RESTful Design**: The API follows REST principles with resource-based URLs, standard HTTP methods (GET, POST, PUT, PATCH, DELETE), and appropriate status codes.

**Versioning**: API versioned via URL path (`/api/v1/...`) to maintain backward compatibility while allowing evolution.

**Consistency**: Consistent naming conventions, response formats, error handling, and pagination across all endpoints.

**Idempotency**: PUT and DELETE operations are idempotent. POST operations use idempotency keys for critical operations.

**Filtering and Pagination**: List endpoints support filtering, sorting, and pagination with consistent query parameters.



## Authentication

### Authentication Methods

**JWT Bearer Tokens**: Primary authentication method for API access. Tokens obtained via login endpoint and included in Authorization header.

```http
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Personal Access Tokens**: Long-lived tokens for automated systems and scripts. Created in web interface, used same as JWT tokens.

**API Keys**: Alternative authentication for service accounts. Included in X-API-Key header.

```http
X-API-Key: pk_live_abc123def456...
```

**OAuth 2.0 Client Credentials**: For service-to-service authentication. Exchange client ID and secret for access token.

### Token Lifecycle

**Obtaining Tokens**: POST to `/api/v1/auth/login` with credentials returns access token and refresh token.

**Token Expiration**: Access tokens expire after 1 hour (configurable). Refresh tokens expire after 30 days.

**Token Refresh**: POST to `/api/v1/auth/refresh` with refresh token returns new access token.

**Token Revocation**: DELETE to `/api/v1/auth/token` revokes current token. Logout endpoint revokes all tokens for user.

### Rate Limiting

**Per-User Limits**: 1000 requests per hour per user (configurable).

**Per-IP Limits**: 5000 requests per hour per IP address (prevents abuse).

**Per-Endpoint Limits**: Some endpoints have stricter limits (e.g., login attempts limited to 10 per hour).

**Rate Limit Headers**: Responses include rate limit information:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 847
X-RateLimit-Reset: 1699564800
```

**Rate Limit Exceeded**: Returns 429 Too Many Requests with Retry-After header.

## Core API Endpoints

### Authentication Endpoints

#### POST /api/v1/auth/login

Authenticate user and obtain tokens.

**Request Body**:
```json
{
  "username": "<EMAIL>",
  "password": "SecurePassword123!",
  "mfa_code": "123456"
}
```

**Response** (200 OK):
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600,
  "user": {
    "id": "usr_abc123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "roles": ["developer", "viewer"]
  }
}
```

**Error Responses**:
- 401 Unauthorized: Invalid credentials
- 403 Forbidden: MFA required but not provided
- 429 Too Many Requests: Too many failed attempts

#### POST /api/v1/auth/refresh

Refresh access token using refresh token.

**Request Body**:
```json
{
  "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response** (200 OK):
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

#### POST /api/v1/auth/logout

Logout and revoke tokens.

**Headers**: `Authorization: Bearer <token>`

**Response** (204 No Content)

#### GET /api/v1/auth/me

Get current user information.

**Headers**: `Authorization: Bearer <token>`

**Response** (200 OK):
```json
{
  "id": "usr_abc123",
  "email": "<EMAIL>",
  "name": "John Doe",
  "roles": ["developer", "viewer"],
  "permissions": ["vm:view", "service:connect"],
  "mfa_enabled": true,
  "last_login": "2025-11-08T10:30:00Z"
}
```

### VM Management Endpoints

#### GET /api/v1/vms

List all VMs.

**Query Parameters**:
- `status`: Filter by status (online, offline, degraded)
- `environment`: Filter by environment tag
- `search`: Search by name or IP
- `page`: Page number (default: 1)
- `per_page`: Items per page (default: 50, max: 100)
- `sort`: Sort field (name, status, last_seen)
- `order`: Sort order (asc, desc)

**Response** (200 OK):
```json
{
  "items": [
    {
      "id": "vm_abc123",
      "name": "vm-prod-01",
      "hostname": "vm-prod-01.internal",
      "ip_address": "*********",
      "status": "online",
      "agent_version": "r.1.5-454",
      "services_count": 12,
      "cpu_usage": 45.2,
      "memory_usage": 62.8,
      "last_seen": "2025-11-08T10:35:00Z",
      "tags": ["production", "web"],
      "environment": "production"
    }
  ],
  "total": 42,
  "page": 1,
  "per_page": 50,
  "pages": 1
}
```

#### GET /api/v1/vms/{vm_id}

Get VM details.

**Response** (200 OK):
```json
{
  "id": "vm_abc123",
  "name": "vm-prod-01",
  "hostname": "vm-prod-01.internal",
  "ip_addresses": ["*********", "************"],
  "status": "online",
  "agent": {
    "version": "r.1.5-454",
    "uptime": 86400,
    "last_heartbeat": "2025-11-08T10:35:00Z",
    "connection_latency": 12
  },
  "system": {
    "os": "Ubuntu 22.04 LTS",
    "kernel": "5.15.0-91-generic",
    "cpu_cores": 8,
    "memory_total": 16384,
    "disk_total": 512000
  },
  "metrics": {
    "cpu_usage": 45.2,
    "memory_usage": 62.8,
    "disk_usage": 38.5,
    "network_in": 1024000,
    "network_out": 512000
  },
  "services_count": 12,
  "tags": ["production", "web"],
  "environment": "production",
  "created_at": "2025-10-01T08:00:00Z",
  "updated_at": "2025-11-08T10:35:00Z"
}
```

#### POST /api/v1/vms/{vm_id}/command

Send command to VM agent.

**Request Body**:
```json
{
  "command": "rescan",
  "parameters": {
    "full_scan": true
  }
}
```

**Response** (202 Accepted):
```json
{
  "command_id": "cmd_xyz789",
  "status": "pending",
  "submitted_at": "2025-11-08T10:36:00Z"
}
```

**Available Commands**:
- `rescan`: Trigger service discovery scan
- `restart`: Restart agent
- `update_config`: Update agent configuration
- `collect_diagnostics`: Collect diagnostic information

### Service Catalog Endpoints

#### GET /api/v1/services

List all services.

**Query Parameters**:
- `vm_id`: Filter by VM
- `type`: Filter by service type (web, database, cache, etc.)
- `status`: Filter by health status
- `environment`: Filter by environment
- `tags`: Filter by tags (comma-separated)
- `search`: Search by name or description
- `page`, `per_page`, `sort`, `order`: Pagination and sorting

**Response** (200 OK):
```json
{
  "items": [
    {
      "id": "svc_def456",
      "name": "postgres-main",
      "type": "database",
      "subtype": "postgresql",
      "version": "14.5",
      "vm_id": "vm_abc123",
      "vm_name": "vm-prod-db",
      "port": 5432,
      "protocol": "tcp",
      "bind_address": "0.0.0.0",
      "status": "healthy",
      "health_score": 100,
      "process": {
        "pid": 1234,
        "user": "postgres",
        "command": "/usr/lib/postgresql/14/bin/postgres"
      },
      "metrics": {
        "cpu_usage": 12.5,
        "memory_usage": 2048,
        "connections": 45,
        "requests_per_sec": 120
      },
      "tags": ["production", "critical"],
      "discovered_at": "2025-11-01T10:00:00Z",
      "last_seen": "2025-11-08T10:35:00Z"
    }
  ],
  "total": 156,
  "page": 1,
  "per_page": 50,
  "pages": 4
}
```

#### GET /api/v1/services/{service_id}

Get service details.

**Response** (200 OK):
```json
{
  "id": "svc_def456",
  "name": "postgres-main",
  "description": "Main PostgreSQL database",
  "type": "database",
  "subtype": "postgresql",
  "version": "14.5",
  "vm_id": "vm_abc123",
  "vm_name": "vm-prod-db",
  "port": 5432,
  "protocol": "tcp",
  "bind_address": "0.0.0.0",
  "status": "healthy",
  "health_score": 100,
  "health_check": {
    "type": "tcp_connect",
    "interval": 30,
    "timeout": 5,
    "last_check": "2025-11-08T10:35:00Z",
    "last_result": "success"
  },
  "process": {
    "pid": 1234,
    "user": "postgres",
    "command": "/usr/lib/postgresql/14/bin/postgres",
    "working_directory": "/var/lib/postgresql/14/main"
  },
  "metrics": {
    "cpu_usage": 12.5,
    "memory_usage": 2048,
    "connections": 45,
    "requests_per_sec": 120,
    "response_time_p50": 5.2,
    "response_time_p95": 12.8,
    "error_rate": 0.01
  },
  "uptime": {
    "current_uptime": 604800,
    "uptime_percentage_24h": 100.0,
    "uptime_percentage_7d": 99.98,
    "uptime_percentage_30d": 99.95
  },
  "access_control": {
    "users_with_access": 12,
    "active_connections": 3
  },
  "tags": ["production", "critical"],
  "metadata": {
    "owner": "database-team",
    "cost_center": "engineering",
    "documentation_url": "https://wiki.example.com/postgres"
  },
  "discovered_at": "2025-11-01T10:00:00Z",
  "last_seen": "2025-11-08T10:35:00Z",
  "updated_at": "2025-11-08T10:35:00Z"
}
```

#### GET /api/v1/services/{service_id}/metrics

Get service metrics.

**Query Parameters**:
- `metric`: Metric name (cpu_usage, memory_usage, requests_per_sec, etc.)
- `start`: Start time (ISO 8601)
- `end`: End time (ISO 8601)
- `resolution`: Data resolution (1m, 5m, 1h, 1d)

**Response** (200 OK):
```json
{
  "service_id": "svc_def456",
  "metric": "cpu_usage",
  "start": "2025-11-08T09:00:00Z",
  "end": "2025-11-08T10:00:00Z",
  "resolution": "1m",
  "data_points": [
    {
      "timestamp": "2025-11-08T09:00:00Z",
      "value": 12.3
    },
    {
      "timestamp": "2025-11-08T09:01:00Z",
      "value": 13.1
    }
  ],
  "statistics": {
    "min": 10.2,
    "max": 18.7,
    "avg": 12.8,
    "p50": 12.5,
    "p95": 16.2,
    "p99": 17.9
  }
}
```



### Connection Management Endpoints

#### POST /api/v1/connections

Request connection to service.

**Request Body**:
```json
{
  "service_id": "svc_def456",
  "local_port": 5432,
  "tunnel_type": "wireguard",
  "duration": 3600
}
```

**Response** (201 Created):
```json
{
  "connection_id": "conn_ghi789",
  "service_id": "svc_def456",
  "service_name": "postgres-main",
  "tunnel": {
    "type": "wireguard",
    "endpoint": "agent.example.com:51820",
    "public_key": "...",
    "allowed_ips": ["**********/32"],
    "credentials": {
      "private_key": "...",
      "address": "**********/32"
    }
  },
  "local_port": 5432,
  "expires_at": "2025-11-08T11:36:00Z",
  "created_at": "2025-11-08T10:36:00Z"
}
```

#### GET /api/v1/connections

List active connections.

**Query Parameters**:
- `user_id`: Filter by user
- `service_id`: Filter by service
- `vm_id`: Filter by VM
- `status`: Filter by status (active, expired, terminated)

**Response** (200 OK):
```json
{
  "items": [
    {
      "connection_id": "conn_ghi789",
      "user_id": "usr_abc123",
      "user_name": "John Doe",
      "service_id": "svc_def456",
      "service_name": "postgres-main",
      "vm_name": "vm-prod-db",
      "local_port": 5432,
      "status": "active",
      "bytes_sent": 1024000,
      "bytes_received": 2048000,
      "started_at": "2025-11-08T10:36:00Z",
      "expires_at": "2025-11-08T11:36:00Z",
      "last_activity": "2025-11-08T10:40:00Z"
    }
  ],
  "total": 15,
  "page": 1,
  "per_page": 50
}
```

#### DELETE /api/v1/connections/{connection_id}

Terminate connection.

**Response** (204 No Content)

### User Management Endpoints

#### GET /api/v1/users

List users (requires admin permission).

**Query Parameters**:
- `status`: Filter by status (active, disabled, locked)
- `role`: Filter by role
- `search`: Search by name or email
- `page`, `per_page`, `sort`, `order`

**Response** (200 OK):
```json
{
  "items": [
    {
      "id": "usr_abc123",
      "email": "<EMAIL>",
      "name": "John Doe",
      "status": "active",
      "roles": ["developer", "viewer"],
      "mfa_enabled": true,
      "last_login": "2025-11-08T10:30:00Z",
      "created_at": "2025-10-01T08:00:00Z"
    }
  ],
  "total": 87,
  "page": 1,
  "per_page": 50
}
```

#### POST /api/v1/users

Create new user (requires admin permission).

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "name": "Jane Smith",
  "password": "SecurePassword123!",
  "roles": ["viewer"],
  "send_invitation": true
}
```

**Response** (201 Created):
```json
{
  "id": "usr_xyz789",
  "email": "<EMAIL>",
  "name": "Jane Smith",
  "status": "active",
  "roles": ["viewer"],
  "created_at": "2025-11-08T10:45:00Z"
}
```

#### GET /api/v1/users/{user_id}

Get user details.

**Response** (200 OK):
```json
{
  "id": "usr_abc123",
  "email": "<EMAIL>",
  "name": "John Doe",
  "status": "active",
  "roles": ["developer", "viewer"],
  "groups": ["engineering", "backend-team"],
  "permissions": ["vm:view", "service:connect"],
  "mfa_enabled": true,
  "mfa_methods": ["totp", "webauthn"],
  "last_login": "2025-11-08T10:30:00Z",
  "last_password_change": "2025-10-15T14:20:00Z",
  "failed_login_attempts": 0,
  "created_at": "2025-10-01T08:00:00Z",
  "updated_at": "2025-11-08T10:30:00Z"
}
```

#### PATCH /api/v1/users/{user_id}

Update user (partial update).

**Request Body**:
```json
{
  "name": "John A. Doe",
  "roles": ["developer", "viewer", "operator"]
}
```

**Response** (200 OK): Returns updated user object.

#### DELETE /api/v1/users/{user_id}

Delete user.

**Response** (204 No Content)

### Alert Management Endpoints

#### GET /api/v1/alerts

List alerts.

**Query Parameters**:
- `status`: Filter by status (active, acknowledged, resolved)
- `severity`: Filter by severity (info, warning, critical)
- `resource_type`: Filter by resource type (vm, service)
- `resource_id`: Filter by specific resource
- `start`, `end`: Time range filter

**Response** (200 OK):
```json
{
  "items": [
    {
      "id": "alert_jkl012",
      "name": "High CPU Usage",
      "severity": "warning",
      "status": "active",
      "resource_type": "vm",
      "resource_id": "vm_abc123",
      "resource_name": "vm-prod-01",
      "condition": "cpu_usage > 80",
      "current_value": 85.3,
      "threshold": 80,
      "triggered_at": "2025-11-08T10:25:00Z",
      "acknowledged_at": null,
      "acknowledged_by": null,
      "resolved_at": null,
      "notification_sent": true
    }
  ],
  "total": 8,
  "page": 1,
  "per_page": 50
}
```

#### POST /api/v1/alerts/{alert_id}/acknowledge

Acknowledge alert.

**Request Body**:
```json
{
  "note": "Investigating high CPU usage"
}
```

**Response** (200 OK):
```json
{
  "id": "alert_jkl012",
  "status": "acknowledged",
  "acknowledged_at": "2025-11-08T10:50:00Z",
  "acknowledged_by": "usr_abc123"
}
```

#### POST /api/v1/alerts/{alert_id}/resolve

Resolve alert.

**Request Body**:
```json
{
  "note": "CPU usage returned to normal after process optimization"
}
```

**Response** (200 OK):
```json
{
  "id": "alert_jkl012",
  "status": "resolved",
  "resolved_at": "2025-11-08T11:00:00Z",
  "resolved_by": "usr_abc123"
}
```

### Log Query Endpoints

#### GET /api/v1/logs

Query logs.

**Query Parameters**:
- `source`: Filter by source (agent, controller, service)
- `level`: Filter by log level (ERROR, WARN, INFO, DEBUG)
- `vm_id`: Filter by VM
- `service_id`: Filter by service
- `user_id`: Filter by user
- `start`, `end`: Time range
- `search`: Full-text search
- `page`, `per_page`

**Response** (200 OK):
```json
{
  "items": [
    {
      "id": "log_mno345",
      "timestamp": "2025-11-08T10:35:22Z",
      "level": "INFO",
      "source": "agent",
      "vm_id": "vm_abc123",
      "service_id": "svc_def456",
      "message": "Service health check successful",
      "metadata": {
        "response_time": 5.2,
        "status_code": 200
      }
    }
  ],
  "total": 1523,
  "page": 1,
  "per_page": 50
}
```

#### GET /api/v1/audit-logs

Query audit logs (requires admin permission).

**Query Parameters**:
- `user_id`: Filter by user
- `action`: Filter by action type
- `resource_type`: Filter by resource type
- `resource_id`: Filter by specific resource
- `result`: Filter by result (success, failure, denied)
- `start`, `end`: Time range
- `page`, `per_page`

**Response** (200 OK):
```json
{
  "items": [
    {
      "id": "audit_pqr678",
      "timestamp": "2025-11-08T10:36:15Z",
      "user_id": "usr_abc123",
      "user_email": "<EMAIL>",
      "action": "service:connect",
      "resource_type": "service",
      "resource_id": "svc_def456",
      "resource_name": "postgres-main",
      "result": "success",
      "ip_address": "************",
      "user_agent": "VMGateway-Client/1.5.0",
      "details": {
        "connection_id": "conn_ghi789",
        "local_port": 5432
      }
    }
  ],
  "total": 4521,
  "page": 1,
  "per_page": 50
}
```

### Configuration Endpoints

#### GET /api/v1/config/global

Get global configuration (requires admin permission).

**Response** (200 OK):
```json
{
  "platform_name": "VM Gateway",
  "default_timezone": "UTC",
  "session_timeout": 28800,
  "mfa_policy": "optional",
  "password_policy": {
    "min_length": 12,
    "require_uppercase": true,
    "require_lowercase": true,
    "require_numbers": true,
    "require_special": true,
    "expiration_days": 90
  },
  "api_rate_limits": {
    "per_user": 1000,
    "per_ip": 5000
  }
}
```

#### PATCH /api/v1/config/global

Update global configuration (requires admin permission).

**Request Body**:
```json
{
  "session_timeout": 14400,
  "mfa_policy": "required"
}
```

**Response** (200 OK): Returns updated configuration.

#### GET /api/v1/config/vms/{vm_id}

Get VM-specific configuration.

**Response** (200 OK):
```json
{
  "vm_id": "vm_abc123",
  "scan_interval": 300,
  "metric_collection_interval": 60,
  "log_level": "INFO",
  "resource_limits": {
    "cpu_percent": 50,
    "memory_mb": 512
  },
  "custom_classification_rules": [
    {
      "port": 9999,
      "process": "custom-app",
      "name": "Internal API",
      "type": "api"
    }
  ]
}
```

#### PATCH /api/v1/config/vms/{vm_id}

Update VM configuration.

**Request Body**:
```json
{
  "scan_interval": 600,
  "log_level": "DEBUG"
}
```

**Response** (200 OK): Returns updated configuration.

## Webhook System

### Webhook Registration

#### POST /api/v1/webhooks

Register webhook.

**Request Body**:
```json
{
  "url": "https://api.example.com/webhooks/vmgateway",
  "events": ["service.discovered", "service.status_changed", "alert.triggered"],
  "secret": "webhook_secret_key",
  "enabled": true
}
```

**Response** (201 Created):
```json
{
  "id": "webhook_stu901",
  "url": "https://api.example.com/webhooks/vmgateway",
  "events": ["service.discovered", "service.status_changed", "alert.triggered"],
  "enabled": true,
  "created_at": "2025-11-08T11:00:00Z"
}
```

### Webhook Events

**Available Events**:
- `service.discovered`: New service discovered
- `service.removed`: Service no longer detected
- `service.status_changed`: Service health status changed
- `vm.connected`: VM agent connected
- `vm.disconnected`: VM agent disconnected
- `alert.triggered`: Alert triggered
- `alert.resolved`: Alert resolved
- `user.login`: User logged in
- `user.logout`: User logged out
- `connection.established`: User connected to service
- `connection.terminated`: Connection ended

**Webhook Payload Format**:
```json
{
  "event": "service.discovered",
  "timestamp": "2025-11-08T11:05:00Z",
  "data": {
    "service_id": "svc_vwx234",
    "name": "redis-cache",
    "type": "cache",
    "vm_id": "vm_abc123",
    "port": 6379
  }
}
```

**Webhook Security**:
- HMAC-SHA256 signature in `X-Webhook-Signature` header
- Verify signature using webhook secret
- Retry failed deliveries with exponential backoff (3 attempts)
- Timeout after 10 seconds

## Error Handling

### Error Response Format

All errors return consistent JSON format:

```json
{
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "Service with ID 'svc_invalid' not found",
    "details": {
      "resource_type": "service",
      "resource_id": "svc_invalid"
    },
    "request_id": "req_abc123xyz"
  }
}
```

### HTTP Status Codes

**2xx Success**:
- 200 OK: Request successful
- 201 Created: Resource created
- 202 Accepted: Request accepted for processing
- 204 No Content: Request successful, no response body

**4xx Client Errors**:
- 400 Bad Request: Invalid request format or parameters
- 401 Unauthorized: Authentication required or failed
- 403 Forbidden: Authenticated but not authorized
- 404 Not Found: Resource not found
- 409 Conflict: Resource conflict (e.g., duplicate)
- 422 Unprocessable Entity: Validation failed
- 429 Too Many Requests: Rate limit exceeded

**5xx Server Errors**:
- 500 Internal Server Error: Unexpected server error
- 502 Bad Gateway: Upstream service error
- 503 Service Unavailable: Service temporarily unavailable
- 504 Gateway Timeout: Upstream service timeout

### Error Codes

**Authentication Errors**:
- `INVALID_CREDENTIALS`: Username or password incorrect
- `MFA_REQUIRED`: MFA code required but not provided
- `INVALID_MFA_CODE`: MFA code incorrect
- `TOKEN_EXPIRED`: Access token expired
- `TOKEN_INVALID`: Token malformed or invalid

**Authorization Errors**:
- `INSUFFICIENT_PERMISSIONS`: User lacks required permission
- `APPROVAL_REQUIRED`: Action requires approval
- `ACCESS_DENIED`: Access explicitly denied by policy

**Resource Errors**:
- `RESOURCE_NOT_FOUND`: Requested resource doesn't exist
- `RESOURCE_CONFLICT`: Resource already exists
- `RESOURCE_LOCKED`: Resource locked by another operation

**Validation Errors**:
- `VALIDATION_ERROR`: Request validation failed
- `INVALID_PARAMETER`: Specific parameter invalid
- `MISSING_PARAMETER`: Required parameter missing

**Rate Limit Errors**:
- `RATE_LIMIT_EXCEEDED`: Too many requests

## Pagination

List endpoints support pagination with consistent parameters:

**Query Parameters**:
- `page`: Page number (1-indexed, default: 1)
- `per_page`: Items per page (default: 50, max: 100)

**Response Format**:
```json
{
  "items": [...],
  "total": 156,
  "page": 2,
  "per_page": 50,
  "pages": 4,
  "has_next": true,
  "has_prev": true,
  "next_page": 3,
  "prev_page": 1
}
```

**Link Header**: Responses include Link header with pagination URLs:
```http
Link: <https://api.example.com/api/v1/services?page=1>; rel="first",
      <https://api.example.com/api/v1/services?page=1>; rel="prev",
      <https://api.example.com/api/v1/services?page=3>; rel="next",
      <https://api.example.com/api/v1/services?page=4>; rel="last"
```

## Filtering and Sorting

**Filtering**: Use query parameters matching field names:
```
GET /api/v1/services?type=database&status=healthy&environment=production
```

**Sorting**: Use `sort` and `order` parameters:
```
GET /api/v1/services?sort=name&order=asc
```

**Multiple Sort Fields**: Comma-separated:
```
GET /api/v1/services?sort=status,name&order=desc,asc
```

**Search**: Use `search` parameter for full-text search:
```
GET /api/v1/services?search=postgres
```

## Versioning

**Current Version**: v1

**Version in URL**: `/api/v1/...`

**Backward Compatibility**: v1 API maintained for at least 12 months after v2 release.

**Deprecation**: Deprecated endpoints return `X-API-Deprecated` header with sunset date.

**Version Header**: Responses include `X-API-Version: v1` header.

## Performance Considerations

**Caching**: Responses include cache headers where appropriate:
```http
Cache-Control: public, max-age=300
ETag: "abc123def456"
```

**Conditional Requests**: Support If-None-Match and If-Modified-Since headers.

**Compression**: Responses compressed with gzip or brotli if client supports.

**Field Selection**: Some endpoints support `fields` parameter to return only requested fields:
```
GET /api/v1/services?fields=id,name,status
```

**Batch Operations**: Batch endpoints for creating/updating multiple resources in single request.

## SDK and Client Libraries

**Official SDKs**:
- Python: `pip install vmgateway-sdk`
- JavaScript/TypeScript: `npm install @vmgateway/sdk`
- Go: `go get github.com/vmgateway/go-sdk`

**SDK Features**:
- Automatic authentication and token refresh
- Type-safe request/response models
- Retry logic with exponential backoff
- Rate limit handling
- WebSocket support for real-time updates
- Comprehensive documentation and examples
