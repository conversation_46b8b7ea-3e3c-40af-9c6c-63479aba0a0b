# Kubernetes Deployment

Deploy the VM Gateway platform on Kubernetes for scalability, high availability, and cloud-native operations.

## Architecture

```
┌─────────────────────────────────────────────┐
│         Kubernetes Cluster                  │
│                                             │
│  ┌───────────────────────────────────────┐  │
│  │  Ingress (nginx/traefik)              │  │
│  │  - TLS termination                    │  │
│  │  - Load balancing                     │  │
│  └──────────────┬────────────────────────┘  │
│                 │                            │
│  ┌──────────────▼────────────────────────┐  │
│  │  Controller Service                   │  │
│  │  - ClusterIP                          │  │
│  └──────────────┬────────────────────────┘  │
│                 │                            │
│  ┌──────────────▼────────────────────────┐  │
│  │  Controller Deployment                │  │
│  │  - 3 replicas                         │  │
│  │  - Rolling updates                    │  │
│  └───────────────────────────────────────┘  │
│                                             │
│  ┌───────────────────────────────────────┐  │
│  │  PostgreSQL StatefulSet               │  │
│  │  - Primary + 2 replicas               │  │
│  │  - Persistent volumes                 │  │
│  └───────────────────────────────────────┘  │
│                                             │
│  ┌───────────────────────────────────────┐  │
│  │  Redis Deployment                     │  │
│  │  - 3 replicas (cluster mode)          │  │
│  └───────────────────────────────────────┘  │
└─────────────────────────────────────────────┘
```

## Kubernetes Manifests

### Namespace

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: vm-gateway
```

### ConfigMap

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: vm-gateway-config
  namespace: vm-gateway
data:
  DATABASE_HOST: postgres-service
  DATABASE_PORT: "5432"
  DATABASE_NAME: vm_gateway
  REDIS_HOST: redis-service
  REDIS_PORT: "6379"
```

### Secrets

```yaml
# secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: vm-gateway-secrets
  namespace: vm-gateway
type: Opaque
stringData:
  DATABASE_PASSWORD: your_db_password
  SECRET_KEY: your_secret_key
  AGENT_TOKEN: your_agent_token
```

### Controller Deployment

```yaml
# controller-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: controller
  namespace: vm-gateway
spec:
  replicas: 3
  selector:
    matchLabels:
      app: controller
  template:
    metadata:
      labels:
        app: controller
    spec:
      containers:
      - name: controller
        image: vm-gateway-controller:1.0.0
        ports:
        - containerPort: 80
          name: http
        - containerPort: 8080
          name: websocket
        env:
        - name: DATABASE_URL
          value: postgresql://$(DATABASE_USER):$(DATABASE_PASSWORD)@$(DATABASE_HOST):$(DATABASE_PORT)/$(DATABASE_NAME)
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: vm-gateway-secrets
              key: DATABASE_PASSWORD
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: vm-gateway-secrets
              key: SECRET_KEY
        envFrom:
        - configMapRef:
            name: vm-gateway-config
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 5
```

### Controller Service

```yaml
# controller-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: controller-service
  namespace: vm-gateway
spec:
  selector:
    app: controller
  ports:
  - name: http
    port: 80
    targetPort: 80
  - name: websocket
    port: 8080
    targetPort: 8080
  type: ClusterIP
```

### PostgreSQL StatefulSet

```yaml
# postgres-statefulset.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: vm-gateway
spec:
  serviceName: postgres-service
  replicas: 3
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_DB
          value: vm_gateway
        - name: POSTGRES_USER
          value: vm_gateway
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: vm-gateway-secrets
              key: DATABASE_PASSWORD
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "4Gi"
            cpu: "2000m"
          limits:
            memory: "8Gi"
            cpu: "4000m"
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 100Gi
```

### Ingress

```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: vm-gateway-ingress
  namespace: vm-gateway
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - vm-gateway.example.com
    secretName: vm-gateway-tls
  rules:
  - host: vm-gateway.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: controller-service
            port:
              number: 80
```

## Deployment Commands

### Apply Manifests

```bash
# Create namespace
kubectl apply -f namespace.yaml

# Create secrets
kubectl apply -f secrets.yaml

# Create configmap
kubectl apply -f configmap.yaml

# Deploy database
kubectl apply -f postgres-statefulset.yaml
kubectl apply -f postgres-service.yaml

# Deploy Redis
kubectl apply -f redis-deployment.yaml
kubectl apply -f redis-service.yaml

# Deploy controller
kubectl apply -f controller-deployment.yaml
kubectl apply -f controller-service.yaml

# Deploy ingress
kubectl apply -f ingress.yaml
```

### Verify Deployment

```bash
# Check pods
kubectl get pods -n vm-gateway

# Check services
kubectl get svc -n vm-gateway

# Check ingress
kubectl get ingress -n vm-gateway

# View logs
kubectl logs -f deployment/controller -n vm-gateway
```

## Scaling

### Horizontal Pod Autoscaler

```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: controller-hpa
  namespace: vm-gateway
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: controller
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## Best Practices

1. **Use namespaces**: Isolate resources
2. **Resource limits**: Define requests and limits
3. **Health checks**: Implement liveness and readiness probes
4. **Secrets management**: Use external secrets operator
5. **Persistent storage**: Use StatefulSets for databases
6. **Monitoring**: Deploy Prometheus and Grafana
7. **Logging**: Use EFK or Loki stack
8. **Backups**: Regular volume snapshots

## Next Steps

- [High Availability](07-high-availability.md) - HA configuration
- [Backup Recovery](08-backup-recovery.md) - Backup strategies
- [DNS Routing](05-dns-routing.md) - Domain configuration
