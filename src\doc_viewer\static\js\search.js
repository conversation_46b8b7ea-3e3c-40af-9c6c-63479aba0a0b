/**
 * Search module for VM Gateway documentation viewer.
 * Handles search input, debouncing, API calls, and result rendering.
 */

const Search = (function() {
    'use strict';
    
    let searchTimeout = null;
    const DEBOUNCE_DELAY = 300; // milliseconds
    
    /**
     * Initialize the search system
     */
    function init() {
        const searchInput = document.getElementById('search-input');
        if (!searchInput) return;
        
        // Setup search input event listener with debouncing
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            
            // Clear previous timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
            
            // Hide results if query is too short
            if (query.length < 2) {
                hideResults();
                return;
            }
            
            // Debounce search
            searchTimeout = setTimeout(() => {
                performSearch(query);
            }, DEBOUNCE_DELAY);
        });
        
        // Close search results when clicking outside
        document.addEventListener('click', (e) => {
            const searchContainer = document.querySelector('.search-container');
            if (searchContainer && !searchContainer.contains(e.target)) {
                hideResults();
            }
        });
        
        // Handle escape key to close search
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                hideResults();
                searchInput.blur();
            }
        });
    }
    
    /**
     * Perform search via API
     */
    async function performSearch(query) {
        try {
            const response = await fetch(`/api/docs/search?q=${encodeURIComponent(query)}`);
            
            if (!response.ok) {
                throw new Error('Search request failed');
            }
            
            const data = await response.json();
            renderResults(data.results, query);
            
        } catch (error) {
            console.error('Search failed:', error);
            showError('Search failed. Please try again.');
        }
    }
    
    /**
     * Render search results
     */
    function renderResults(results, query) {
        const resultsEl = document.getElementById('search-results');
        if (!resultsEl) return;
        
        // Clear previous results
        resultsEl.innerHTML = '';
        
        if (results.length === 0) {
            resultsEl.innerHTML = `
                <div class="search-no-results">
                    No results found for "${escapeHtml(query)}"
                </div>
            `;
            resultsEl.classList.remove('hidden');
            return;
        }
        
        // Create result items
        results.forEach(result => {
            const item = createResultItem(result, query);
            resultsEl.appendChild(item);
        });
        
        resultsEl.classList.remove('hidden');
    }
    
    /**
     * Create a search result item element
     */
    function createResultItem(result, query) {
        const div = document.createElement('div');
        div.className = 'search-result-item';
        
        // Highlight query in snippet
        const highlightedSnippet = highlightText(result.snippet, query);
        
        div.innerHTML = `
            <div class="search-result-title">${escapeHtml(result.title)}</div>
            <div class="search-result-section">${escapeHtml(result.section)}</div>
            <div class="search-result-snippet">${highlightedSnippet}</div>
        `;
        
        // Click handler to navigate to document
        div.addEventListener('click', () => {
            Nav.loadDocumentByPath(result.path);
            hideResults();
            clearSearchInput();
            Nav.closeMobileMenu();
        });
        
        return div;
    }
    
    /**
     * Highlight search query in text
     */
    function highlightText(text, query) {
        const escapedText = escapeHtml(text);
        const escapedQuery = escapeRegex(query);
        
        // Case-insensitive highlighting
        const regex = new RegExp(`(${escapedQuery})`, 'gi');
        return escapedText.replace(regex, '<mark style="background-color: #fef08a; padding: 0 2px;">$1</mark>');
    }
    
    /**
     * Hide search results
     */
    function hideResults() {
        const resultsEl = document.getElementById('search-results');
        if (resultsEl) {
            resultsEl.classList.add('hidden');
        }
    }
    
    /**
     * Clear search input
     */
    function clearSearchInput() {
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.value = '';
        }
    }
    
    /**
     * Show error message in search results
     */
    function showError(message) {
        const resultsEl = document.getElementById('search-results');
        if (!resultsEl) return;
        
        resultsEl.innerHTML = `
            <div class="search-no-results" style="color: #dc2626;">
                ${escapeHtml(message)}
            </div>
        `;
        resultsEl.classList.remove('hidden');
    }
    
    /**
     * Escape HTML to prevent XSS
     */
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    /**
     * Escape special regex characters
     */
    function escapeRegex(text) {
        return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    
    // Public API
    return {
        init
    };
})();
