<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Results - VM Gateway Documentation</title>
    <link rel="stylesheet" href="/static/css/styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>VM Gateway Documentation</h1>
            <div class="search-container">
                <input type="text" id="search-input" placeholder="Search documentation..." />
                <button id="search-button">Search</button>
            </div>
        </header>

        <main>
            <div id="search-results-container">
                <h2 id="results-title">Search Results</h2>
                <div id="results-meta"></div>
                <div id="results-list"></div>
                <div id="no-results" style="display: none;">
                    <p>No results found for your search query.</p>
                    <p>Try:</p>
                    <ul>
                        <li>Using different keywords</li>
                        <li>Checking your spelling</li>
                        <li>Using more general terms</li>
                        <li>Browsing the <a href="/">documentation index</a></li>
                    </ul>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Get search query from URL
        const urlParams = new URLSearchParams(window.location.search);
        const query = urlParams.get('q') || '';
        
        // Set search input value
        document.getElementById('search-input').value = query;
        
        // Perform search if query exists
        if (query) {
            performSearch(query);
        }
        
        // Search button click handler
        document.getElementById('search-button').addEventListener('click', () => {
            const searchQuery = document.getElementById('search-input').value;
            if (searchQuery) {
                window.location.href = `/search-results.html?q=${encodeURIComponent(searchQuery)}`;
            }
        });
        
        // Enter key handler
        document.getElementById('search-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const searchQuery = document.getElementById('search-input').value;
                if (searchQuery) {
                    window.location.href = `/search-results.html?q=${encodeURIComponent(searchQuery)}`;
                }
            }
        });
        
        async function performSearch(query) {
            try {
                const response = await fetch(`/api/docs/search?q=${encodeURIComponent(query)}`);
                const data = await response.json();
                
                displayResults(data, query);
            } catch (error) {
                console.error('Search failed:', error);
                document.getElementById('results-meta').innerHTML = '<p class="error">Search failed. Please try again.</p>';
            }
        }
        
        function displayResults(data, query) {
            const resultsTitle = document.getElementById('results-title');
            const resultsMeta = document.getElementById('results-meta');
            const resultsList = document.getElementById('results-list');
            const noResults = document.getElementById('no-results');
            
            // Update title
            resultsTitle.textContent = `Search Results for "${query}"`;
            
            // Display meta information
            resultsMeta.innerHTML = `<p>Found ${data.total} result${data.total !== 1 ? 's' : ''}</p>`;
            
            // Clear previous results
            resultsList.innerHTML = '';
            
            if (data.total === 0) {
                noResults.style.display = 'block';
                return;
            }
            
            noResults.style.display = 'none';
            
            // Display results
            data.results.forEach(result => {
                const resultItem = document.createElement('div');
                resultItem.className = 'search-result-item';
                
                // Highlight search term in snippet
                const highlightedSnippet = highlightSearchTerm(result.snippet, query);
                
                resultItem.innerHTML = `
                    <h3><a href="/docs/${result.path}">${result.title}</a></h3>
                    <p class="result-section">${result.section}</p>
                    <p class="result-snippet">${highlightedSnippet}</p>
                    <p class="result-path">${result.path}</p>
                `;
                
                resultsList.appendChild(resultItem);
            });
        }
        
        function highlightSearchTerm(text, term) {
            const regex = new RegExp(`(${escapeRegex(term)})`, 'gi');
            return text.replace(regex, '<mark>$1</mark>');
        }
        
        function escapeRegex(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }
    </script>
    
    <style>
        .search-result-item {
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .search-result-item:last-child {
            border-bottom: none;
        }
        
        .search-result-item h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.25rem;
        }
        
        .search-result-item h3 a {
            color: #2563eb;
            text-decoration: none;
        }
        
        .search-result-item h3 a:hover {
            text-decoration: underline;
        }
        
        .result-section {
            color: #6b7280;
            font-size: 0.875rem;
            margin: 0 0 0.5rem 0;
        }
        
        .result-snippet {
            margin: 0.5rem 0;
            line-height: 1.6;
        }
        
        .result-snippet mark {
            background-color: #fef3c7;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
        }
        
        .result-path {
            color: #9ca3af;
            font-size: 0.875rem;
            font-family: monospace;
            margin: 0.5rem 0 0 0;
        }
        
        #results-meta {
            margin-bottom: 1.5rem;
            color: #6b7280;
        }
        
        #no-results {
            padding: 2rem;
            background-color: #f9fafb;
            border-radius: 0.5rem;
        }
        
        #no-results ul {
            margin-top: 1rem;
        }
        
        .error {
            color: #dc2626;
            padding: 1rem;
            background-color: #fee2e2;
            border-radius: 0.5rem;
        }
    </style>
</body>
</html>
