---
title: "Component Interaction"
section: "Architecture"
order: 2
tags: ["architecture", "components", "communication", "protocols"]
last_updated: "2025-11-08"
---

# Component Interaction

## Introduction

The VM Network Gateway & Access Control Platform consists of three primary components that work together to provide secure, granular access to services across multiple virtual machines. This document details how these components communicate, the protocols they use, and the data flows between them.

## Component Overview

### The Three Core Components

1. **Agent Software**: Lightweight daemon installed on each monitored VM that discovers services, collects metrics, and manages connections
2. **Controller & Web Interface**: Centralized management system providing authentication, authorization, service catalog, and web-based access
3. **Client Software**: Desktop application that establishes secure tunnels for port forwarding to remote services

Each component has distinct responsibilities but must coordinate seamlessly to provide the platform's functionality.

## Communication Protocols

### Agent-Controller Communication

The agent and controller maintain a persistent, bidirectional connection for real-time updates and command execution.

#### Connection Establishment

**Initial Registration Flow:**

```
1. Agent starts and loads configuration
   - Reads controller URL from config file
   - Loads or generates mTLS certificate
   - Prepares agent metadata (hostname, IP, version, OS)

2. Agent initiates WebSocket connection to controller
   - URL: wss://controller.domain.com/agent/ws
   - TLS 1.3 with mutual authentication
   - Certificate pinning for security

3. Agent sends REGISTER message
   {
     "type": "REGISTER",
     "agent_id": "vm-prod-01",
     "hostname": "prod-database-server",
     "ip_addresses": ["*********", "*************"],
     "version": "r.1.5-454",
     "os": "Ubuntu 22.04 LTS",
     "capabilities": ["service_discovery", "metrics", "tunneling"],
     "timestamp": "2025-11-08T10:30:00Z"
   }

4. Controller validates agent certificate
   - Checks certificate against CA
   - Verifies agent_id matches certificate CN
   - Checks if agent is authorized

5. Controller sends REGISTER_ACK
   {
     "type": "REGISTER_ACK",
     "status": "success",
     "agent_id": "vm-prod-01",
     "config": {
       "scan_interval": 60,
       "metrics_interval": 30,
       "heartbeat_interval": 30,
       "log_level": "INFO"
     },
     "timestamp": "2025-11-08T10:30:01Z"
   }

6. Connection established, agent begins normal operation
```

#### Heartbeat Mechanism

The agent sends periodic heartbeats to prove it's alive and report basic status:

```python
# Sent every 30 seconds (configurable)
{
  "type": "HEARTBEAT",
  "agent_id": "vm-prod-01",
  "timestamp": "2025-11-08T10:31:00Z",
  "status": "healthy",
  "system_load": {
    "cpu_percent": 45.2,
    "memory_percent": 62.8,
    "disk_percent": 71.3
  },
  "last_scan": "2025-11-08T10:30:45Z",
  "error_count": 0,
  "active_tunnels": 3
}
```

**Controller Response:**
```python
{
  "type": "HEARTBEAT_ACK",
  "timestamp": "2025-11-08T10:31:00Z"
}
```

**Missed Heartbeat Handling:**
- Controller tracks last heartbeat time for each agent
- After 2 missed heartbeats (60 seconds): Agent marked as "degraded"
- After 5 missed heartbeats (150 seconds): Agent marked as "offline"
- Alert triggered if agent goes offline
- When agent reconnects, status restored and sync initiated

#### Service Catalog Synchronization

When the agent discovers new services or detects changes, it pushes updates to the controller:

```python
{
  "type": "SYNC_CATALOG",
  "agent_id": "vm-prod-01",
  "timestamp": "2025-11-08T10:32:15Z",
  "services": [
    {
      "service_id": "postgres-main",
      "name": "PostgreSQL Database",
      "type": "database",
      "subtype": "postgresql",
      "port": 5432,
      "protocol": "tcp",
      "bind_address": "0.0.0.0",
      "process": {
        "pid": 1234,
        "user": "postgres",
        "executable": "/usr/lib/postgresql/14/bin/postgres",
        "command": "postgres -D /var/lib/postgresql/14/main"
      },
      "version": "14.5",
      "status": "healthy",
      "health_check": {
        "last_check": "2025-11-08T10:32:10Z",
        "response_time_ms": 12,
        "status_code": 200
      },
      "metadata": {
        "discovered_at": "2025-11-08T09:15:00Z",
        "last_seen": "2025-11-08T10:32:15Z",
        "tags": ["production", "database", "critical"]
      }
    },
    {
      "service_id": "api-server",
      "name": "REST API Server",
      "type": "web",
      "subtype": "api",
      "port": 8080,
      "protocol": "http",
      "bind_address": "0.0.0.0",
      "process": {
        "pid": 5678,
        "user": "appuser",
        "executable": "/usr/bin/node",
        "command": "node /opt/api/server.js"
      },
      "version": "18.2.0",
      "status": "healthy",
      "tls_enabled": false,
      "metadata": {
        "discovered_at": "2025-11-08T09:15:05Z",
        "last_seen": "2025-11-08T10:32:15Z",
        "tags": ["production", "api"]
      }
    }
  ],
  "removed_services": ["old-service-id"],
  "full_sync": false
}
```

**Controller Response:**
```python
{
  "type": "SYNC_ACK",
  "timestamp": "2025-11-08T10:32:16Z",
  "services_accepted": 2,
  "services_rejected": 0,
  "errors": []
}
```

#### Metrics Batch Upload

Agents collect metrics locally and periodically upload batches to the controller:

```python
{
  "type": "METRICS_BATCH",
  "agent_id": "vm-prod-01",
  "timestamp": "2025-11-08T10:33:00Z",
  "time_range": {
    "start": "2025-11-08T10:32:00Z",
    "end": "2025-11-08T10:33:00Z"
  },
  "system_metrics": {
    "cpu": [
      {"timestamp": "2025-11-08T10:32:00Z", "value": 45.2},
      {"timestamp": "2025-11-08T10:32:30Z", "value": 46.1}
    ],
    "memory": [
      {"timestamp": "2025-11-08T10:32:00Z", "value": 62.8},
      {"timestamp": "2025-11-08T10:32:30Z", "value": 63.1}
    ],
    "disk_io": {
      "read_bytes": 1048576,
      "write_bytes": 524288
    },
    "network_io": {
      "bytes_in": 2097152,
      "bytes_out": 1572864
    }
  },
  "service_metrics": {
    "postgres-main": {
      "connections": [
        {"timestamp": "2025-11-08T10:32:00Z", "value": 12},
        {"timestamp": "2025-11-08T10:32:30Z", "value": 15}
      ],
      "cpu_percent": 8.5,
      "memory_mb": 256
    },
    "api-server": {
      "requests_per_second": [
        {"timestamp": "2025-11-08T10:32:00Z", "value": 45},
        {"timestamp": "2025-11-08T10:32:30Z", "value": 52}
      ],
      "response_time_p95_ms": 125,
      "error_rate": 0.02
    }
  }
}
```

#### Command Execution

The controller can send commands to agents for various operations:

**Rescan Command:**
```python
{
  "type": "COMMAND",
  "command_id": "cmd-12345",
  "command": "rescan",
  "timestamp": "2025-11-08T10:35:00Z",
  "parameters": {
    "full_scan": true,
    "force": false
  }
}
```

**Agent Response:**
```python
{
  "type": "COMMAND_RESULT",
  "command_id": "cmd-12345",
  "status": "success",
  "timestamp": "2025-11-08T10:35:15Z",
  "result": {
    "services_found": 15,
    "new_services": 1,
    "removed_services": 0,
    "scan_duration_ms": 14523
  }
}
```

**Other Commands:**
- `update_config`: Update agent configuration
- `restart`: Restart agent service
- `collect_diagnostics`: Generate diagnostic bundle
- `test_service`: Test connectivity to specific service
- `update_agent`: Trigger agent self-update

#### Tunnel Request Flow

When a client wants to connect to a service, the controller coordinates with the agent:

```python
# Controller → Agent
{
  "type": "TUNNEL_REQUEST",
  "tunnel_id": "tunnel-abc123",
  "service_id": "postgres-main",
  "user_id": "user-john-doe",
  "client_id": "client-xyz789",
  "timestamp": "2025-11-08T10:40:00Z",
  "tunnel_config": {
    "protocol": "wireguard",
    "client_public_key": "...",
    "allowed_ips": ["**********/32"],
    "endpoint": "client.external.ip:51820"
  },
  "authorization": {
    "token": "eyJhbGc...",
    "expires_at": "2025-11-08T11:40:00Z",
    "max_duration": 3600
  }
}
```

**Agent Response:**
```python
{
  "type": "TUNNEL_RESPONSE",
  "tunnel_id": "tunnel-abc123",
  "status": "established",
  "timestamp": "2025-11-08T10:40:02Z",
  "tunnel_endpoint": {
    "protocol": "wireguard",
    "server_public_key": "...",
    "server_endpoint": "*********:51820",
    "assigned_ip": "**********"
  }
}
```

### Client-Controller Communication

The client application communicates with the controller via REST API and WebSocket connections.

#### Authentication Flow

**OAuth 2.0 Device Flow:**

```
1. Client requests device code
   POST /api/auth/device/code
   Response: {
     "device_code": "abc123",
     "user_code": "WXYZ-1234",
     "verification_uri": "https://controller.domain.com/activate",
     "expires_in": 600,
     "interval": 5
   }

2. Client opens browser to verification_uri
   User logs in and enters user_code

3. Client polls for token
   POST /api/auth/device/token
   Body: {"device_code": "abc123"}
   
   While pending:
   Response: {"error": "authorization_pending"}
   
   When approved:
   Response: {
     "access_token": "eyJhbGc...",
     "refresh_token": "eyJhbGc...",
     "token_type": "Bearer",
     "expires_in": 28800
   }

4. Client stores tokens in OS keyring
5. Client uses access_token for all API calls
```

#### Service Catalog Retrieval

```
GET /api/services
Headers:
  Authorization: Bearer eyJhbGc...

Response:
{
  "services": [
    {
      "id": "postgres-main",
      "name": "PostgreSQL Database",
      "type": "database",
      "vm": {
        "id": "vm-prod-01",
        "name": "Production Database Server",
        "ip": "*********"
      },
      "port": 5432,
      "status": "healthy",
      "version": "14.5",
      "tags": ["production", "database", "critical"],
      "permissions": {
        "can_connect": true,
        "can_configure": false,
        "requires_approval": false
      }
    }
  ],
  "total": 1,
  "page": 1,
  "per_page": 50
}
```

#### Connection Request

```
POST /api/connections
Headers:
  Authorization: Bearer eyJhbGc...
Body:
{
  "service_id": "postgres-main",
  "tunnel_protocol": "wireguard",
  "local_port": 5432,
  "client_public_key": "..."
}

Response:
{
  "connection_id": "conn-xyz789",
  "tunnel_id": "tunnel-abc123",
  "status": "establishing",
  "tunnel_config": {
    "protocol": "wireguard",
    "server_public_key": "...",
    "server_endpoint": "*********:51820",
    "assigned_ip": "**********",
    "allowed_ips": ["**********/32"]
  },
  "service_endpoint": {
    "host": "**********",
    "port": 5432
  },
  "expires_at": "2025-11-08T11:40:00Z"
}
```

#### Real-Time Updates via WebSocket

```
# Client establishes WebSocket connection
wss://controller.domain.com/api/ws?token=eyJhbGc...

# Server sends updates
{
  "type": "service_status_changed",
  "service_id": "postgres-main",
  "old_status": "healthy",
  "new_status": "degraded",
  "reason": "high_latency",
  "timestamp": "2025-11-08T10:45:00Z"
}

{
  "type": "connection_terminated",
  "connection_id": "conn-xyz789",
  "reason": "session_expired",
  "timestamp": "2025-11-08T11:40:00Z"
}

{
  "type": "new_service_discovered",
  "service": {
    "id": "redis-cache",
    "name": "Redis Cache",
    "vm": "vm-prod-02",
    "port": 6379
  },
  "timestamp": "2025-11-08T10:50:00Z"
}
```

### Client-Agent Direct Communication

For optimal performance, clients can establish direct tunnels to agents after controller authorization.

#### Direct Tunnel Establishment

```
1. Client requests connection from controller (as shown above)
2. Controller validates permissions and coordinates with agent
3. Controller returns agent endpoint and ephemeral credentials
4. Client establishes direct encrypted tunnel to agent
5. Agent validates credentials with controller (one-time check)
6. Tunnel established, data flows directly: Client ↔ Agent ↔ Service
7. Controller not in data path (only control plane)
```

**WireGuard Tunnel Example:**

```
# Client configuration (generated from controller response)
[Interface]
PrivateKey = <client_private_key>
Address = **********/32

[Peer]
PublicKey = <agent_public_key>
Endpoint = *********:51820
AllowedIPs = **********/32
PersistentKeepalive = 25

# Traffic flow
Application → localhost:5432 → WireGuard tunnel → **********:5432 → Agent → Service
```

## Data Flow Patterns

### Service Discovery Flow

```
┌─────────┐
│  Agent  │
└────┬────┘
     │ 1. Scan local ports
     │ 2. Identify processes
     │ 3. Classify services
     │ 4. Collect metadata
     │
     ├─────────────────────────────────┐
     │                                 │
     ▼                                 ▼
┌─────────────┐              ┌──────────────┐
│   SQLite    │              │  Controller  │
│ (Local DB)  │              │   (via WS)   │
└─────────────┘              └──────┬───────┘
                                    │
                                    ▼
                             ┌──────────────┐
                             │  PostgreSQL  │
                             │ (Central DB) │
                             └──────┬───────┘
                                    │
                                    ▼
                             ┌──────────────┐
                             │   Web UI     │
                             │  (Service    │
                             │   Catalog)   │
                             └──────────────┘
```

### Connection Establishment Flow

```
┌────────┐         ┌────────────┐         ┌───────┐         ┌─────────┐
│ Client │         │ Controller │         │ Agent │         │ Service │
└───┬────┘         └─────┬──────┘         └───┬───┘         └────┬────┘
    │                    │                    │                  │
    │ 1. Request access  │                    │                  │
    ├───────────────────>│                    │                  │
    │                    │                    │                  │
    │                    │ 2. Check perms     │                  │
    │                    │    (RBAC)          │                  │
    │                    │                    │                  │
    │                    │ 3. Request tunnel  │                  │
    │                    ├───────────────────>│                  │
    │                    │                    │                  │
    │                    │ 4. Tunnel ready    │                  │
    │                    │<───────────────────┤                  │
    │                    │                    │                  │
    │ 5. Tunnel config   │                    │                  │
    │<───────────────────┤                    │                  │
    │                    │                    │                  │
    │ 6. Establish tunnel (direct)            │                  │
    ├─────────────────────────────────────────>│                  │
    │                    │                    │                  │
    │ 7. Validate token  │                    │                  │
    │                    │<───────────────────┤                  │
    │                    │                    │                  │
    │                    │ 8. Token valid     │                  │
    │                    ├───────────────────>│                  │
    │                    │                    │                  │
    │ 9. Tunnel established                   │                  │
    │<────────────────────────────────────────┤                  │
    │                    │                    │                  │
    │ 10. Application data (encrypted)        │                  │
    ├─────────────────────────────────────────>│ 11. Forward    │
    │                    │                    ├─────────────────>│
    │                    │                    │                  │
    │                    │                    │ 12. Response     │
    │                    │                    │<─────────────────┤
    │ 13. Response (encrypted)                │                  │
    │<────────────────────────────────────────┤                  │
    │                    │                    │                  │
```

### Metrics Collection Flow

```
┌─────────┐
│  Agent  │
└────┬────┘
     │ Collect every 30s
     │
     ├─────────────────────────────────┐
     │                                 │
     ▼                                 ▼
┌─────────────┐              ┌──────────────┐
│   SQLite    │              │  Controller  │
│ (Buffer)    │              │ (Batch every │
│             │              │   5 minutes) │
└─────────────┘              └──────┬───────┘
                                    │
                                    ▼
                             ┌──────────────┐
                             │  PostgreSQL  │
                             │ (Time-series │
                             │    data)     │
                             └──────┬───────┘
                                    │
                                    ▼
                             ┌──────────────┐
                             │   Web UI     │
                             │  (Graphs &   │
                             │   Charts)    │
                             └──────────────┘
```

## Error Handling and Resilience

### Connection Failures

**Agent-Controller Connection Lost:**
1. Agent detects connection failure
2. Agent continues local operations (discovery, metrics collection)
3. Agent buffers updates in SQLite
4. Agent attempts reconnection with exponential backoff
5. When reconnected, agent syncs buffered data
6. Controller marks agent as offline after timeout
7. Web UI shows agent status as "disconnected"

**Client-Controller Connection Lost:**
1. Client detects connection failure
2. Active tunnels continue (direct to agent)
3. Client shows "Controller unreachable" warning
4. Client cannot establish new connections
5. Client retries connection automatically
6. When reconnected, client refreshes service catalog

**Client-Agent Tunnel Failure:**
1. Client detects tunnel failure (keepalive timeout)
2. Client attempts automatic reconnection
3. If reconnection fails, client requests new tunnel from controller
4. User notified only if reconnection fails
5. Application connections may timeout during reconnection

### Message Delivery Guarantees

**Agent-Controller Messages:**
- Heartbeats: Best effort (loss acceptable)
- Service catalog updates: At-least-once delivery (with deduplication)
- Metrics: Best effort with buffering (some loss acceptable)
- Commands: Exactly-once delivery (with acknowledgment)
- Tunnel requests: Exactly-once delivery (critical)

**Client-Controller Messages:**
- API calls: Synchronous with retries
- WebSocket updates: Best effort (client can poll if needed)
- Connection requests: Exactly-once (idempotent with request ID)

## Security Considerations

### Mutual TLS (mTLS)

All agent-controller communication uses mTLS:
- Agent has unique certificate signed by platform CA
- Controller validates agent certificate
- Agent validates controller certificate
- Certificate pinning prevents MITM attacks
- Certificates rotated periodically (90 days)

### Token-Based Authentication

Client-agent tunnels use short-lived tokens:
- Tokens issued by controller after authorization
- Tokens valid for 5-15 minutes (configurable)
- Tokens include: user_id, service_id, expiration, signature
- Agent validates token signature with controller public key
- Tokens cannot be reused after expiration
- Token revocation list maintained by controller

### Encryption

All communication channels encrypted:
- Agent-Controller: TLS 1.3 over WebSocket
- Client-Controller: HTTPS (TLS 1.3)
- Client-Agent: WireGuard (ChaCha20-Poly1305) or TLS 1.3
- Data at rest: AES-256-GCM (database, local storage)

## Performance Optimization

### Connection Pooling

- Controller maintains connection pool to database
- Agents reuse WebSocket connection for all messages
- Clients reuse HTTP connections (keep-alive)

### Caching

- Controller caches service catalog in Redis
- Agents cache configuration locally
- Clients cache service list (with TTL)

### Batching

- Metrics sent in batches (reduce message overhead)
- Service updates batched when multiple changes occur
- Log entries batched before transmission

### Compression

- WebSocket messages compressed (permessage-deflate)
- HTTP responses compressed (gzip/brotli)
- Metrics data compressed before storage

## Monitoring and Observability

### Health Checks

Each component exposes health check endpoints:
- Agent: `http://localhost:9090/health`
- Controller: `https://controller.domain.com/health`
- Client: Internal health monitoring (no endpoint)

### Metrics Exposure

Components expose Prometheus-compatible metrics:
- Agent: Connection count, scan duration, error rate
- Controller: Request rate, response time, active sessions
- Database: Connection pool usage, query performance

### Distributed Tracing

Optional OpenTelemetry integration:
- Trace requests across components
- Identify bottlenecks
- Debug complex issues
- Correlate logs and metrics

## Conclusion

The VM Network Gateway platform's component interaction model is designed for reliability, security, and performance. The separation of control plane (authentication, authorization) and data plane (actual connections) ensures scalability while maintaining centralized management. Persistent connections enable real-time updates, while buffering and retry logic provide resilience against network failures.
