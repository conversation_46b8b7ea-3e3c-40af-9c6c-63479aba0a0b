---
title: "Development Environment Setup"
section: "Development"
order: 1
tags: ["development", "setup", "environment"]
last_updated: "2025-11-08"
---

# Development Environment Setup

This guide walks you through setting up a complete development environment for the VM Network Gateway & Access Control Platform. By the end of this guide, you'll have all components running locally and be ready to contribute to the project.

## Prerequisites

### Required Software

Before starting, ensure you have the following installed:

**Core Requirements:**
- **Python 3.11 or higher** - Primary development language
- **Node.js 18.x or higher** - For frontend development
- **PostgreSQL 14 or higher** - Primary database
- **Redis 7.x or higher** - Caching and real-time features
- **Git** - Version control

**Recommended Tools:**
- **Docker & Docker Compose** - For containerized development
- **VS Code** or **PyCharm** - IDE with Python support
- **Postman** or **Insomnia** - API testing
- **pgAdmin** or **DBeaver** - Database management

### System Requirements

**Minimum:**
- 8 GB RAM
- 4 CPU cores
- 20 GB free disk space
- Linux, macOS, or Windows 10/11 with WSL2

**Recommended:**
- 16 GB RAM
- 8 CPU cores
- 50 GB free disk space
- SSD storage

## Installation Steps

### 1. Clone the Repository

```bash
# Clone the repository
git clone https://github.com/your-org/vm-gateway.git
cd vm-gateway

# Create a development branch
git checkout -b feature/your-feature-name
```

### 2. Set Up Python Environment

```bash
# Create virtual environment
python3.11 -m venv venv

# Activate virtual environment
# On Linux/macOS:
source venv/bin/activate
# On Windows:
venv\Scripts\activate

# Upgrade pip
pip install --upgrade pip

# Install development dependencies
pip install -e ".[dev]"

# Verify installation
python --version  # Should show Python 3.11.x
pip list  # Should show all installed packages
```

### 3. Install Node.js Dependencies

```bash
# Navigate to frontend directory
cd src/controller/frontend

# Install dependencies
npm install

# Verify installation
npm list
node --version  # Should show v18.x or higher
```

### 4. Set Up PostgreSQL Database

**Option A: Local Installation**

```bash
# On Ubuntu/Debian
sudo apt-get update
sudo apt-get install postgresql-14 postgresql-contrib

# On macOS with Homebrew
brew install postgresql@14
brew services start postgresql@14

# On Windows
# Download and install from https://www.postgresql.org/download/windows/
```

**Create Database and User:**

```bash
# Connect to PostgreSQL
sudo -u postgres psql

# In PostgreSQL shell:
CREATE DATABASE vm_gateway_dev;
CREATE USER vm_gateway WITH PASSWORD 'dev_password_123';
GRANT ALL PRIVILEGES ON DATABASE vm_gateway_dev TO vm_gateway;
ALTER USER vm_gateway CREATEDB;  # For running tests
\q
```

**Option B: Docker**

```bash
# Start PostgreSQL in Docker
docker run -d \
  --name vm-gateway-postgres \
  -e POSTGRES_DB=vm_gateway_dev \
  -e POSTGRES_USER=vm_gateway \
  -e POSTGRES_PASSWORD=dev_password_123 \
  -p 5432:5432 \
  postgres:14

# Verify it's running
docker ps | grep vm-gateway-postgres
```

### 5. Set Up Redis

**Option A: Local Installation**

```bash
# On Ubuntu/Debian
sudo apt-get install redis-server
sudo systemctl start redis-server

# On macOS with Homebrew
brew install redis
brew services start redis

# On Windows
# Download from https://github.com/microsoftarchive/redis/releases
```

**Option B: Docker**

```bash
# Start Redis in Docker
docker run -d \
  --name vm-gateway-redis \
  -p 6379:6379 \
  redis:7-alpine

# Verify it's running
docker ps | grep vm-gateway-redis
```

### 6. Configure Environment Variables

```bash
# Copy example environment file
cp .env.example .env

# Edit .env with your settings
nano .env  # or use your preferred editor
```

**Example .env file:**

```bash
# Application
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG

# Database
DATABASE_URL=postgresql://vm_gateway:dev_password_123@localhost:5432/vm_gateway_dev
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis
REDIS_URL=redis://localhost:6379/0
REDIS_MAX_CONNECTIONS=50

# Security
SECRET_KEY=dev_secret_key_change_in_production_abc123def456
JWT_SECRET_KEY=dev_jwt_secret_key_change_in_production_xyz789
JWT_ACCESS_TOKEN_EXPIRES=900
JWT_REFRESH_TOKEN_EXPIRES=604800

# Authentication
PASSWORD_MIN_LENGTH=12
MFA_ISSUER=VM Gateway Dev
SESSION_TIMEOUT=28800

# API
API_RATE_LIMIT=1000
API_RATE_LIMIT_WINDOW=3600

# Frontend
FRONTEND_URL=http://localhost:3000
CORS_ORIGINS=http://localhost:3000,http://localhost:8000

# Email (for development, use MailHog or similar)
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=<EMAIL>

# Monitoring
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

# Feature Flags
FEATURE_ADVANCED_MONITORING=true
FEATURE_ML_CLASSIFICATION=false
```

### 7. Initialize Database Schema

```bash
# Run database migrations
alembic upgrade head

# Verify tables were created
psql -U vm_gateway -d vm_gateway_dev -c "\dt"

# Seed development data (optional)
python scripts/seed_dev_data.py
```

### 8. Start Development Servers

**Terminal 1 - Controller Backend:**

```bash
# Activate virtual environment
source venv/bin/activate

# Start FastAPI development server
uvicorn src.controller.main:app \
  --reload \
  --host 0.0.0.0 \
  --port 8000 \
  --log-level debug

# Server will be available at http://localhost:8000
# API docs at http://localhost:8000/docs
```

**Terminal 2 - Frontend Development Server:**

```bash
# Navigate to frontend directory
cd src/controller/frontend

# Start Vite development server
npm run dev

# Frontend will be available at http://localhost:3000
```

**Terminal 3 - Agent (Optional):**

```bash
# Activate virtual environment
source venv/bin/activate

# Start agent in development mode
python -m src.agent.main \
  --config config/agent-dev.yaml \
  --log-level DEBUG

# Agent will connect to controller at http://localhost:8000
```

**Terminal 4 - Celery Worker (Optional):**

```bash
# Activate virtual environment
source venv/bin/activate

# Start Celery worker for background tasks
celery -A src.controller.tasks worker \
  --loglevel=debug \
  --concurrency=4

# Worker will process background jobs
```

### 9. Verify Installation

**Check Backend:**

```bash
# Test API health endpoint
curl http://localhost:8000/api/v1/health

# Expected response:
# {"status": "healthy", "version": "a.1.0-15", "timestamp": "2025-11-08T16:30:00Z"}
```

**Check Frontend:**

```bash
# Open browser to http://localhost:3000
# You should see the login page
```

**Check Database Connection:**

```bash
# Test database connection
python -c "from src.shared.database import engine; print(engine.connect())"

# Should print connection object without errors
```

**Check Redis Connection:**

```bash
# Test Redis connection
redis-cli ping

# Should respond with: PONG
```

## Docker Compose Setup (Alternative)

For a simpler setup, use Docker Compose to run all services:

```bash
# Start all services
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop all services
docker-compose -f docker-compose.dev.yml down

# Stop and remove volumes (clean slate)
docker-compose -f docker-compose.dev.yml down -v
```

**docker-compose.dev.yml:**

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: vm_gateway_dev
      POSTGRES_USER: vm_gateway
      POSTGRES_PASSWORD: dev_password_123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  controller:
    build:
      context: .
      dockerfile: Dockerfile.dev
    command: uvicorn src.controller.main:app --reload --host 0.0.0.0 --port 8000
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: ******************************************************/vm_gateway_dev
      REDIS_URL: redis://redis:6379/0
    volumes:
      - .:/app
    depends_on:
      - postgres
      - redis

  frontend:
    build:
      context: ./src/controller/frontend
      dockerfile: Dockerfile.dev
    command: npm run dev
    ports:
      - "3000:3000"
    volumes:
      - ./src/controller/frontend:/app
      - /app/node_modules
    environment:
      VITE_API_URL: http://localhost:8000

  mailhog:
    image: mailhog/mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI

volumes:
  postgres_data:
  redis_data:
```

## IDE Configuration

### VS Code

**Recommended Extensions:**

```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.vscode-pylance",
    "ms-python.black-formatter",
    "charliermarsh.ruff",
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "bradlc.vscode-tailwindcss",
    "ms-azuretools.vscode-docker",
    "eamodio.gitlens"
  ]
}
```

**Settings (.vscode/settings.json):**

```json
{
  "python.defaultInterpreterPath": "${workspaceFolder}/venv/bin/python",
  "python.linting.enabled": true,
  "python.linting.ruffEnabled": true,
  "python.formatting.provider": "black",
  "python.formatting.blackArgs": ["--line-length", "100"],
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  },
  "[python]": {
    "editor.defaultFormatter": "ms-python.black-formatter",
    "editor.tabSize": 4
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.tabSize": 2
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.tabSize": 2
  }
}
```

### PyCharm

1. **Open Project**: File → Open → Select vm-gateway directory
2. **Configure Interpreter**: Settings → Project → Python Interpreter → Add → Existing Environment → Select venv/bin/python
3. **Enable Django Support**: Settings → Languages & Frameworks → Django → Enable Django Support
4. **Configure Database**: Database → + → PostgreSQL → Enter connection details
5. **Set Run Configuration**: Run → Edit Configurations → + → Python → Configure uvicorn startup

## Troubleshooting

### Common Issues

**Issue: Port already in use**

```bash
# Find process using port 8000
lsof -i :8000  # On Linux/macOS
netstat -ano | findstr :8000  # On Windows

# Kill the process
kill -9 <PID>  # On Linux/macOS
taskkill /PID <PID> /F  # On Windows
```

**Issue: Database connection refused**

```bash
# Check if PostgreSQL is running
sudo systemctl status postgresql  # On Linux
brew services list  # On macOS
docker ps  # If using Docker

# Check connection parameters
psql -U vm_gateway -d vm_gateway_dev -h localhost -p 5432
```

**Issue: Redis connection refused**

```bash
# Check if Redis is running
redis-cli ping

# Start Redis if not running
sudo systemctl start redis-server  # On Linux
brew services start redis  # On macOS
docker start vm-gateway-redis  # If using Docker
```

**Issue: Python module not found**

```bash
# Reinstall dependencies
pip install -e ".[dev]"

# Verify virtual environment is activated
which python  # Should point to venv/bin/python
```

**Issue: Frontend build errors**

```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Clear Vite cache
rm -rf node_modules/.vite
```

## Next Steps

Now that your development environment is set up:

1. **Read the [Coding Standards](./02-coding-standards.md)** to understand code style and conventions
2. **Review the [Testing Guide](./03-testing.md)** to learn how to write and run tests
3. **Check the [Contributing Guide](./04-contributing.md)** for the development workflow
4. **Explore the [Debugging Guide](./05-debugging.md)** for troubleshooting techniques

## Additional Resources

- [Python Virtual Environments](https://docs.python.org/3/tutorial/venv.html)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://react.dev/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Redis Documentation](https://redis.io/documentation)
- [Docker Documentation](https://docs.docker.com/)

## Getting Help

If you encounter issues not covered in this guide:

1. Check the [GitHub Issues](https://github.com/your-org/vm-gateway/issues)
2. Ask in the `#development` Slack channel
3. Review the [FAQ](../01-overview/06-faq.md)
4. Contact the development <NAME_EMAIL>
