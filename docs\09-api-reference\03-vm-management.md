---
title: "VM Management API"
section: "API Reference"
order: 3
tags: ["api", "vm", "management", "endpoints"]
last_updated: "2025-11-08"
---

# VM Management API

The VM Management API provides endpoints for registering, configuring, and monitoring virtual machines in the platform. These endpoints allow you to manage the lifecycle of VMs and their associated agents.

## Base URL

```
https://your-gateway.example.com/api/v1/vms
```

## Endpoints

### GET /vms

List all registered VMs with optional filtering and pagination.

**Request:**

```http
GET /api/v1/vms?status=online&environment=production&page=1&limit=50 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `status` | string | Filter by status: `online`, `offline`, `degraded`, `unknown` | all |
| `environment` | string | Filter by environment tag | all |
| `search` | string | Search by name, hostname, or IP | - |
| `tags` | string | Comma-separated list of tags | - |
| `page` | integer | Page number (1-indexed) | 1 |
| `limit` | integer | Results per page (max 100) | 50 |
| `sort` | string | Sort field: `name`, `created_at`, `last_seen` | `name` |
| `order` | string | Sort order: `asc`, `desc` | `asc` |

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "vms": [
    {
      "id": "vm_abc123def456",
      "name": "prod-web-01",
      "hostname": "prod-web-01.internal",
      "ip_addresses": ["*********", "***********"],
      "status": "online",
      "environment": "production",
      "tags": ["web", "frontend", "critical"],
      "agent": {
        "version": "a.1.0-15",
        "status": "connected",
        "last_heartbeat": "2025-11-08T14:59:30Z",
        "uptime_seconds": 86400
      },
      "system": {
        "os": "Ubuntu 22.04 LTS",
        "kernel": "5.15.0-91-generic",
        "architecture": "x86_64",
        "cpu_cores": 8,
        "memory_total_gb": 32,
        "disk_total_gb": 500
      },
      "metrics": {
        "cpu_percent": 45.2,
        "memory_percent": 62.8,
        "disk_percent": 38.5,
        "network_in_mbps": 12.4,
        "network_out_mbps": 8.7
      },
      "services_count": 12,
      "created_at": "2025-10-01T10:00:00Z",
      "last_seen": "2025-11-08T14:59:30Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 1,
    "total_pages": 1
  }
}
```

---

### GET /vms/{vm_id}

Get detailed information about a specific VM.

**Request:**

```http
GET /api/v1/vms/vm_abc123def456 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "vm": {
    "id": "vm_abc123def456",
    "name": "prod-web-01",
    "hostname": "prod-web-01.internal",
    "ip_addresses": ["*********", "***********"],
    "status": "online",
    "environment": "production",
    "tags": ["web", "frontend", "critical"],
    "description": "Production web server - primary",
    "agent": {
      "id": "agent_xyz789",
      "version": "a.1.0-15",
      "status": "connected",
      "last_heartbeat": "2025-11-08T14:59:30Z",
      "uptime_seconds": 86400,
      "config": {
        "scan_interval": 60,
        "metrics_interval": 30,
        "log_level": "INFO"
      }
    },
    "system": {
      "os": "Ubuntu 22.04 LTS",
      "kernel": "5.15.0-91-generic",
      "architecture": "x86_64",
      "cpu_cores": 8,
      "cpu_model": "Intel Xeon E5-2680 v4",
      "memory_total_gb": 32,
      "disk_total_gb": 500,
      "boot_time": "2025-11-05T10:00:00Z"
    },
    "metrics": {
      "cpu_percent": 45.2,
      "memory_percent": 62.8,
      "memory_used_gb": 20.1,
      "memory_available_gb": 11.9,
      "disk_percent": 38.5,
      "disk_used_gb": 192.5,
      "disk_available_gb": 307.5,
      "network_in_mbps": 12.4,
      "network_out_mbps": 8.7,
      "load_average": [2.1, 2.3, 2.5],
      "process_count": 245
    },
    "services_count": 12,
    "active_connections": 3,
    "created_at": "2025-10-01T10:00:00Z",
    "updated_at": "2025-11-08T14:00:00Z",
    "last_seen": "2025-11-08T14:59:30Z"
  }
}
```

---

### POST /vms

Register a new VM (typically called by agent during installation).

**Request:**

```http
POST /api/v1/vms HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "name": "prod-db-02",
  "hostname": "prod-db-02.internal",
  "ip_addresses": ["*********"],
  "environment": "production",
  "tags": ["database", "postgresql", "critical"],
  "description": "Production PostgreSQL database server",
  "system": {
    "os": "Ubuntu 22.04 LTS",
    "kernel": "5.15.0-91-generic",
    "architecture": "x86_64",
    "cpu_cores": 16,
    "cpu_model": "AMD EPYC 7543",
    "memory_total_gb": 64,
    "disk_total_gb": 2000
  },
  "agent_version": "a.1.0-15"
}
```

**Response:**

```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,
  "vm": {
    "id": "vm_new123abc456",
    "name": "prod-db-02",
    "hostname": "prod-db-02.internal",
    "ip_addresses": ["*********"],
    "status": "online",
    "environment": "production",
    "tags": ["database", "postgresql", "critical"],
    "agent": {
      "id": "agent_new789",
      "version": "a.1.0-15",
      "status": "connected",
      "registration_token": "reg_token_abc123def456"
    },
    "created_at": "2025-11-08T15:00:00Z"
  },
  "message": "VM registered successfully"
}
```

---

### PATCH /vms/{vm_id}

Update VM configuration and metadata.

**Request:**

```http
PATCH /api/v1/vms/vm_abc123def456 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "name": "prod-web-01-updated",
  "description": "Production web server - primary (load balanced)",
  "tags": ["web", "frontend", "critical", "load-balanced"],
  "agent_config": {
    "scan_interval": 30,
    "metrics_interval": 15,
    "log_level": "DEBUG"
  }
}
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "vm": {
    "id": "vm_abc123def456",
    "name": "prod-web-01-updated",
    "description": "Production web server - primary (load balanced)",
    "tags": ["web", "frontend", "critical", "load-balanced"],
    "updated_at": "2025-11-08T15:05:00Z"
  },
  "message": "VM updated successfully"
}
```

---

### DELETE /vms/{vm_id}

Unregister a VM from the platform.

**Request:**

```http
DELETE /api/v1/vms/vm_abc123def456 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "message": "VM unregistered successfully"
}
```

---

### GET /vms/{vm_id}/metrics

Get historical metrics for a VM.

**Request:**

```http
GET /api/v1/vms/vm_abc123def456/metrics?metric=cpu_percent&from=2025-11-08T00:00:00Z&to=2025-11-08T23:59:59Z&resolution=5m HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| `metric` | string | Metric name: `cpu_percent`, `memory_percent`, `disk_percent`, `network_in_mbps`, `network_out_mbps` | Yes |
| `from` | string | Start time (ISO 8601) | Yes |
| `to` | string | End time (ISO 8601) | Yes |
| `resolution` | string | Data resolution: `1m`, `5m`, `1h`, `1d` | No (default: `5m`) |

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "metric": "cpu_percent",
  "resolution": "5m",
  "data_points": [
    {
      "timestamp": "2025-11-08T00:00:00Z",
      "value": 42.5,
      "min": 38.2,
      "max": 48.9,
      "avg": 42.5
    },
    {
      "timestamp": "2025-11-08T00:05:00Z",
      "value": 45.1,
      "min": 41.0,
      "max": 52.3,
      "avg": 45.1
    }
  ],
  "total_points": 288
}
```

---

### POST /vms/{vm_id}/actions/restart-agent

Restart the agent on a specific VM.

**Request:**

```http
POST /api/v1/vms/vm_abc123def456/actions/restart-agent HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 202 Accepted
Content-Type: application/json

{
  "success": true,
  "message": "Agent restart command sent",
  "action_id": "action_abc123"
}
```

---

### POST /vms/{vm_id}/actions/rescan

Trigger an immediate service discovery scan.

**Request:**

```http
POST /api/v1/vms/vm_abc123def456/actions/rescan HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 202 Accepted
Content-Type: application/json

{
  "success": true,
  "message": "Rescan command sent",
  "action_id": "action_def456"
}
```

---

### GET /vms/{vm_id}/logs

Get agent logs for a specific VM.

**Request:**

```http
GET /api/v1/vms/vm_abc123def456/logs?level=ERROR&from=2025-11-08T00:00:00Z&limit=100 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `level` | string | Log level: `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL` | all |
| `from` | string | Start time (ISO 8601) | 24 hours ago |
| `to` | string | End time (ISO 8601) | now |
| `search` | string | Search in log messages | - |
| `limit` | integer | Maximum number of logs (max 1000) | 100 |

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "logs": [
    {
      "timestamp": "2025-11-08T14:30:15Z",
      "level": "ERROR",
      "component": "scanner",
      "message": "Failed to connect to service on port 8080",
      "context": {
        "port": 8080,
        "error": "Connection refused"
      }
    }
  ],
  "total": 1
}
```

---

## Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VM_NOT_FOUND` | 404 | VM with specified ID does not exist |
| `VM_ALREADY_EXISTS` | 409 | VM with same hostname already registered |
| `AGENT_OFFLINE` | 503 | Agent is not connected |
| `INVALID_VM_CONFIG` | 400 | VM configuration is invalid |
| `INSUFFICIENT_PERMISSIONS` | 403 | User lacks permission to manage VMs |

## Code Examples

### Python

```python
import requests

headers = {"Authorization": "Bearer YOUR_ACCESS_TOKEN"}

# List all VMs
response = requests.get(
    "https://your-gateway.example.com/api/v1/vms",
    headers=headers,
    params={"status": "online", "environment": "production"}
)

vms = response.json()["vms"]
for vm in vms:
    print(f"{vm['name']}: {vm['status']} - {vm['services_count']} services")

# Get VM details
vm_id = "vm_abc123def456"
response = requests.get(
    f"https://your-gateway.example.com/api/v1/vms/{vm_id}",
    headers=headers
)

vm_details = response.json()["vm"]
print(f"CPU: {vm_details['metrics']['cpu_percent']}%")
print(f"Memory: {vm_details['metrics']['memory_percent']}%")
```

### JavaScript

```javascript
const headers = {
  'Authorization': 'Bearer YOUR_ACCESS_TOKEN'
};

// List all VMs
const response = await fetch(
  'https://your-gateway.example.com/api/v1/vms?status=online&environment=production',
  { headers }
);

const data = await response.json();
data.vms.forEach(vm => {
  console.log(`${vm.name}: ${vm.status} - ${vm.services_count} services`);
});

// Trigger rescan
await fetch(
  `https://your-gateway.example.com/api/v1/vms/${vmId}/actions/rescan`,
  {
    method: 'POST',
    headers
  }
);
```

## Related Documentation

- [API Overview](./01-overview.md) - API design principles
- [Service Catalog API](./04-service-catalog.md) - Service management
- [Metrics API](./06-metrics.md) - Metrics and monitoring
