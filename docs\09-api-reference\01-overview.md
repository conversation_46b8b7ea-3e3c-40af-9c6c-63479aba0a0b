# API Reference Overview

The VM Gateway platform provides a comprehensive REST API for programmatic access to all platform features.

## API Design Principles

### RESTful Architecture

- **Resource-based URLs**: `/api/vms`, `/api/services`, `/api/connections`
- **HTTP methods**: GET (read), POST (create), PUT/PATCH (update), DELETE (remove)
- **Status codes**: Proper HTTP status codes for all responses
- **JSON format**: All requests and responses use JSON

### Versioning

API versioning through URL path:
- `/api/v1/vms` - Version 1
- `/api/v2/vms` - Version 2 (future)

Current version: **v1**

### Authentication

All API requests require authentication:

**Bearer Token**:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  https://vm-gateway.example.com/api/v1/vms
```

**API Key**:
```bash
curl -H "X-API-Key: YOUR_API_KEY" \
  https://vm-gateway.example.com/api/v1/vms
```

## Base URL

```
https://vm-gateway.example.com/api/v1
```

## Common Response Format

### Success Response

```json
{
  "success": true,
  "data": {
    "id": "vm_123",
    "name": "web-server-01"
  },
  "meta": {
    "timestamp": "2025-11-08T10:30:00Z",
    "request_id": "req_abc123"
  }
}
```

### Error Response

```json
{
  "success": false,
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "VM not found",
    "details": {
      "vm_id": "vm_123"
    }
  },
  "meta": {
    "timestamp": "2025-11-08T10:30:00Z",
    "request_id": "req_abc123"
  }
}
```

## HTTP Status Codes

- **200 OK**: Successful GET, PUT, PATCH
- **201 Created**: Successful POST
- **204 No Content**: Successful DELETE
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **409 Conflict**: Resource conflict
- **422 Unprocessable Entity**: Validation error
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error
- **503 Service Unavailable**: Service temporarily unavailable

## Pagination

List endpoints support pagination:

```bash
GET /api/v1/vms?page=2&per_page=50
```

**Response**:
```json
{
  "success": true,
  "data": [...],
  "meta": {
    "page": 2,
    "per_page": 50,
    "total": 150,
    "total_pages": 3
  }
}
```

## Filtering

Filter results using query parameters:

```bash
GET /api/v1/vms?status=running&environment=production
```

## Sorting

Sort results using `sort` parameter:

```bash
GET /api/v1/vms?sort=created_at:desc
```

## Field Selection

Select specific fields:

```bash
GET /api/v1/vms?fields=id,name,status
```

## Rate Limiting

- **Authenticated**: 1000 requests/hour
- **Unauthenticated**: 100 requests/hour

Rate limit headers:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1699459200
```

## Webhooks

Subscribe to events via webhooks:

```json
POST /api/v1/webhooks
{
  "url": "https://your-app.com/webhook",
  "events": ["vm.created", "vm.deleted"],
  "secret": "webhook_secret"
}
```

## SDKs and Libraries

Official SDKs:
- Python: `pip install vm-gateway-sdk`
- JavaScript/TypeScript: `npm install @vm-gateway/sdk`
- Go: `go get github.com/vm-gateway/go-sdk`

## API Endpoints

- [Authentication](02-authentication.md) - Authentication endpoints
- [VM Management](03-vm-management.md) - VM management endpoints
- [Service Catalog](04-service-catalog.md) - Service catalog endpoints
- [Connections](05-connections.md) - Connection management endpoints
- [Metrics](06-metrics.md) - Metrics and monitoring endpoints
- [Configuration](07-configuration.md) - Configuration endpoints
- [Webhooks](08-webhooks.md) - Webhook system

## Example Request

```bash
# List all VMs
curl -X GET \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  https://vm-gateway.example.com/api/v1/vms

# Create a VM
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name": "web-server-01", "environment": "production"}' \
  https://vm-gateway.example.com/api/v1/vms
```

## OpenAPI Specification

Full OpenAPI 3.0 specification available at:
```
https://vm-gateway.example.com/api/v1/openapi.json
```

## Next Steps

Explore detailed endpoint documentation:
- [Authentication](02-authentication.md)
- [VM Management](03-vm-management.md)
- [Service Catalog](04-service-catalog.md)
- [Connections](05-connections.md)
