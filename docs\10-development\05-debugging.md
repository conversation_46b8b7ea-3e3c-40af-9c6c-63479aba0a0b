---
title: "Debugging Guide"
section: "Development"
order: 5
tags: ["development", "debugging", "troubleshooting"]
last_updated: "2025-11-08"
---

# Debugging Guide

This guide provides techniques, tools, and best practices for debugging the VM Network Gateway & Access Control Platform.

## Python Debugging

### Using pdb

The Python debugger (pdb) is built into Python:

```python
# Add breakpoint in code
import pdb; pdb.set_trace()

# Or use Python 3.7+ breakpoint()
breakpoint()

# Common pdb commands:
# n (next) - Execute next line
# s (step) - Step into function
# c (continue) - Continue execution
# l (list) - Show current code
# p variable - Print variable value
# pp variable - Pretty print variable
# w (where) - Show stack trace
# u (up) - Move up stack frame
# d (down) - Move down stack frame
# q (quit) - Quit debugger
```

### Using debugpy (VS Code)

**launch.json configuration:**

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: FastAPI",
      "type": "python",
      "request": "launch",
      "module": "uvicorn",
      "args": [
        "src.controller.main:app",
        "--reload",
        "--host", "0.0.0.0",
        "--port", "8000"
      ],
      "jinja": true,
      "justMyCode": false
    },
    {
      "name": "Python: Current File",
      "type": "python",
      "request": "launch",
      "program": "${file}",
      "console": "integratedTerminal"
    },
    {
      "name": "Python: Pytest",
      "type": "python",
      "request": "launch",
      "module": "pytest",
      "args": [
        "${file}",
        "-v"
      ]
    }
  ]
}
```

### Logging for Debugging

```python
import logging

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# Add debug logs
logger.debug(f"Processing service: {service.id}")
logger.debug(f"User permissions: {user.permissions}")
logger.debug(f"Query result: {result}")

# Log with extra context
logger.info(
    "Connection established",
    extra={
        "connection_id": conn.id,
        "service_id": service.id,
        "user_id": user.id
    }
)
```

### Performance Profiling

```python
# Using cProfile
import cProfile
import pstats

profiler = cProfile.Profile()
profiler.enable()

# Code to profile
result = expensive_function()

profiler.disable()
stats = pstats.Stats(profiler)
stats.sort_stats('cumulative')
stats.print_stats(20)  # Top 20 functions

# Using line_profiler
# Install: pip install line_profiler
# Add @profile decorator to function
# Run: kernprof -l -v script.py

@profile
def expensive_function():
    # Function code
    pass

# Using memory_profiler
# Install: pip install memory_profiler
# Add @profile decorator
# Run: python -m memory_profiler script.py

from memory_profiler import profile

@profile
def memory_intensive_function():
    # Function code
    pass
```

## JavaScript/TypeScript Debugging

### Browser DevTools

**Console Debugging:**

```javascript
// Basic logging
console.log('Value:', value);
console.error('Error:', error);
console.warn('Warning:', warning);

// Structured logging
console.table(arrayOfObjects);
console.dir(object);

// Timing
console.time('operation');
// ... code ...
console.timeEnd('operation');

// Grouping
console.group('Group Name');
console.log('Item 1');
console.log('Item 2');
console.groupEnd();

// Conditional logging
console.assert(value > 0, 'Value must be positive');

// Stack trace
console.trace('Trace point');
```

**Breakpoints:**

```javascript
// Debugger statement
function processData(data) {
  debugger;  // Execution will pause here
  return data.map(item => item * 2);
}

// Conditional breakpoint in DevTools:
// Right-click line number → Add conditional breakpoint
// Condition: data.length > 100
```

### VS Code Debugging

**launch.json for React:**

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "chrome",
      "request": "launch",
      "name": "Launch Chrome",
      "url": "http://localhost:3000",
      "webRoot": "${workspaceFolder}/src/controller/frontend/src"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Jest Tests",
      "program": "${workspaceFolder}/src/controller/frontend/node_modules/.bin/jest",
      "args": ["--runInBand", "--no-cache"],
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    }
  ]
}
```

### React DevTools

```bash
# Install React DevTools browser extension
# Features:
# - Component tree inspection
# - Props and state viewing
# - Performance profiling
# - Hook inspection
```

## Database Debugging

### PostgreSQL Query Logging

```sql
-- Enable query logging
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_duration = on;
ALTER SYSTEM SET log_min_duration_statement = 100;  -- Log queries > 100ms
SELECT pg_reload_conf();

-- View slow queries
SELECT
    query,
    calls,
    total_time,
    mean_time,
    max_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 20;

-- Explain query plan
EXPLAIN ANALYZE
SELECT * FROM services
WHERE vm_id = 'vm_abc123'
AND status = 'healthy';
```

### SQLAlchemy Debugging

```python
# Enable SQL logging
import logging
logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)

# Or in engine creation
from sqlalchemy import create_engine

engine = create_engine(
    DATABASE_URL,
    echo=True,  # Log all SQL
    echo_pool=True  # Log connection pool events
)

# Profile queries
from sqlalchemy import event
from sqlalchemy.engine import Engine
import time

@event.listens_for(Engine, "before_cursor_execute")
def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    conn.info.setdefault('query_start_time', []).append(time.time())

@event.listens_for(Engine, "after_cursor_execute")
def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    total = time.time() - conn.info['query_start_time'].pop(-1)
    if total > 0.1:  # Log slow queries
        logger.warning(f"Slow query ({total:.2f}s): {statement}")
```

## API Debugging

### Request/Response Logging

```python
# FastAPI middleware for request logging
from fastapi import Request
import time

@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    
    # Log request
    logger.info(
        f"Request: {request.method} {request.url}",
        extra={
            "method": request.method,
            "url": str(request.url),
            "client": request.client.host
        }
    )
    
    # Process request
    response = await call_next(request)
    
    # Log response
    process_time = time.time() - start_time
    logger.info(
        f"Response: {response.status_code} ({process_time:.2f}s)",
        extra={
            "status_code": response.status_code,
            "process_time": process_time
        }
    )
    
    return response
```

### Using Postman/Insomnia

```javascript
// Pre-request script (Postman)
pm.environment.set("timestamp", new Date().toISOString());

// Test script (Postman)
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response time is less than 200ms", function () {
    pm.expect(pm.response.responseTime).to.be.below(200);
});

pm.test("Response has connection ID", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.connection).to.have.property('id');
});
```

### Using curl for API Testing

```bash
# Basic request with verbose output
curl -v http://localhost:8000/api/v1/services

# With authentication
curl -H "Authorization: Bearer TOKEN" \
     http://localhost:8000/api/v1/services

# POST request with JSON
curl -X POST \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer TOKEN" \
     -d '{"service_id":"svc_123","duration":3600}' \
     http://localhost:8000/api/v1/connections

# Save response to file
curl -o response.json \
     http://localhost:8000/api/v1/services

# Follow redirects
curl -L http://localhost:8000/api/v1/services

# Show timing information
curl -w "@curl-format.txt" -o /dev/null -s \
     http://localhost:8000/api/v1/services

# curl-format.txt:
#     time_namelookup:  %{time_namelookup}\n
#        time_connect:  %{time_connect}\n
#     time_appconnect:  %{time_appconnect}\n
#    time_pretransfer:  %{time_pretransfer}\n
#       time_redirect:  %{time_redirect}\n
#  time_starttransfer:  %{time_starttransfer}\n
#                     ----------\n
#          time_total:  %{time_total}\n
```

## Network Debugging

### Monitoring Network Traffic

```bash
# Using tcpdump
sudo tcpdump -i any port 8000 -A

# Capture HTTP traffic
sudo tcpdump -i any port 8000 -w capture.pcap

# Using Wireshark
# Open capture.pcap in Wireshark for analysis

# Monitor WebSocket connections
wscat -c ws://localhost:8000/api/v1/connections/stream
```

### Testing Connectivity

```bash
# Test port connectivity
nc -zv localhost 8000

# Test HTTP endpoint
telnet localhost 8000
GET /api/v1/health HTTP/1.1
Host: localhost

# Check DNS resolution
nslookup gateway.example.com
dig gateway.example.com

# Trace route
traceroute gateway.example.com

# Check SSL certificate
openssl s_client -connect gateway.example.com:443 -servername gateway.example.com
```

## Docker Debugging

### Container Logs

```bash
# View logs
docker logs vm-gateway-controller

# Follow logs
docker logs -f vm-gateway-controller

# Last 100 lines
docker logs --tail 100 vm-gateway-controller

# With timestamps
docker logs -t vm-gateway-controller

# Logs from specific time
docker logs --since 2025-11-08T15:00:00 vm-gateway-controller
```

### Container Inspection

```bash
# Inspect container
docker inspect vm-gateway-controller

# Get specific field
docker inspect -f '{{.State.Status}}' vm-gateway-controller

# Execute command in container
docker exec -it vm-gateway-controller bash

# Check processes
docker top vm-gateway-controller

# Resource usage
docker stats vm-gateway-controller
```

## Common Issues and Solutions

### Issue: Database Connection Refused

**Symptoms:**
```
sqlalchemy.exc.OperationalError: could not connect to server
```

**Solutions:**
```bash
# Check if PostgreSQL is running
sudo systemctl status postgresql
docker ps | grep postgres

# Check connection parameters
psql -U vm_gateway -d vm_gateway_dev -h localhost -p 5432

# Check firewall
sudo ufw status
sudo iptables -L

# Check PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-14-main.log
```

### Issue: Redis Connection Timeout

**Symptoms:**
```
redis.exceptions.ConnectionError: Error connecting to Redis
```

**Solutions:**
```bash
# Check if Redis is running
redis-cli ping
sudo systemctl status redis

# Check Redis configuration
redis-cli CONFIG GET bind
redis-cli CONFIG GET protected-mode

# Monitor Redis
redis-cli MONITOR

# Check Redis logs
sudo tail -f /var/log/redis/redis-server.log
```

### Issue: High Memory Usage

**Symptoms:**
- Application becomes slow
- Out of memory errors

**Solutions:**
```python
# Profile memory usage
from memory_profiler import profile

@profile
def function_to_profile():
    pass

# Check for memory leaks
import tracemalloc

tracemalloc.start()
# ... code ...
snapshot = tracemalloc.take_snapshot()
top_stats = snapshot.statistics('lineno')

for stat in top_stats[:10]:
    print(stat)

# Monitor with psutil
import psutil
import os

process = psutil.Process(os.getpid())
print(f"Memory: {process.memory_info().rss / 1024 / 1024:.2f} MB")
```

### Issue: Slow API Responses

**Symptoms:**
- API requests take > 1 second
- Timeouts

**Solutions:**
```python
# Add timing middleware
import time
from fastapi import Request

@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    if process_time > 1.0:
        logger.warning(f"Slow request: {request.url} ({process_time:.2f}s)")
    return response

# Profile with py-spy
# Install: pip install py-spy
# Run: py-spy top --pid <PID>
# Or: py-spy record -o profile.svg --pid <PID>

# Check database query performance
# Enable SQLAlchemy logging
# Use EXPLAIN ANALYZE on slow queries
```

## Debugging Tools

### Python Tools

- **pdb**: Built-in debugger
- **ipdb**: IPython-enhanced debugger
- **pudb**: Full-screen console debugger
- **py-spy**: Sampling profiler
- **memory_profiler**: Memory usage profiler
- **line_profiler**: Line-by-line profiler

### JavaScript Tools

- **Chrome DevTools**: Browser debugging
- **React DevTools**: React component debugging
- **Redux DevTools**: State management debugging
- **VS Code Debugger**: IDE debugging

### Network Tools

- **Wireshark**: Packet analyzer
- **tcpdump**: Command-line packet analyzer
- **curl**: HTTP client
- **Postman**: API testing
- **wscat**: WebSocket client

### Database Tools

- **pgAdmin**: PostgreSQL GUI
- **DBeaver**: Universal database tool
- **redis-cli**: Redis command-line client

## Best Practices

1. **Use Logging**: Add comprehensive logging before debugging
2. **Reproduce Consistently**: Ensure you can reproduce the issue
3. **Isolate the Problem**: Narrow down to specific component
4. **Check Logs First**: Review application and system logs
5. **Use Version Control**: Compare with working versions
6. **Test Incrementally**: Make small changes and test
7. **Document Findings**: Keep notes on what you've tried
8. **Ask for Help**: Don't hesitate to ask team members

## Related Documentation

- [Development Setup](./01-setup.md) - Environment setup
- [Testing Guide](./03-testing.md) - Testing practices
- [Performance Guide](./06-performance.md) - Performance optimization
- [Security Guide](./07-security.md) - Security best practices
