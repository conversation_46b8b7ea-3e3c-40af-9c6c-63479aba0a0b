---
title: "General FAQ"
section: "FAQ"
order: 1
tags: ["faq", "questions", "answers"]
last_updated: "2025-11-09"
---

# Frequently Asked Questions - General

## Platform Overview

### What is VM Gateway?

VM Gateway is a comprehensive, self-hosted platform for managing, monitoring, and securing access to services running across virtual machine infrastructure. It provides automatic service discovery, granular access control, and secure remote connectivity through a centralized web interface.

Think of it as a self-hosted alternative to commercial solutions like Twingate, but with enhanced capabilities for service discovery, real-time monitoring, and infrastructure management.

### How is VM Gateway different from a VPN?

Key differences:

| Feature | Traditional VPN | VM Gateway |
|---------|----------------|------------|
| Access Level | Network-level (all-or-nothing) | Service-level (granular) |
| Configuration | Manual for each service | Automatic discovery |
| Monitoring | Separate tools required | Built-in comprehensive monitoring |
| Access Control | Basic (network segments) | Advanced RBAC with conditions |
| Audit Trail | Limited | Comprehensive |
| User Experience | Complex (client config) | Simple (web-based or one-click) |

### Is VM Gateway open source?

Yes! The core platform is open source under the MIT License. Advanced features are available through paid tiers, but the source code remains open and auditable.

### What platforms does VM Gateway support?

**Controller**: Linux (Ubuntu, Debian, CentOS, RHEL), Docker, Kubernetes

**Agent**: Linux, Windows, macOS

**Client**: Web browsers (Chrome, Firefox, Safari, Edge), Desktop apps (Windows, macOS, Linux), Mobile apps (iOS, Android - Professional tier+)

### How many VMs can VM Gateway manage?

- **Community Edition**: Up to 25 VMs
- **Professional Edition**: Unlimited
- **Enterprise Edition**: Unlimited with multi-region support

In practice, a single controller can handle 500-1000 VMs depending on hardware. For larger deployments, use multiple controllers with load balancing (Enterprise Edition).

### Does VM Gateway require internet connectivity?

No! VM Gateway is fully self-hosted and can operate in air-gapped environments. The controller, agents, and clients only need connectivity to each other, not to external services.

### Can I use VM Gateway in production?

Absolutely! VM Gateway is production-ready and used by organizations managing critical infrastructure. We recommend:
- Professional or Enterprise Edition for production
- High availability setup (Enterprise)
- Regular backups
- Monitoring and alerting
- Security hardening

---

## Installation & Setup

### How long does it take to set up VM Gateway?

**Quick Start (Docker)**: 5-10 minutes
- Deploy controller with Docker Compose
- Access web interface
- Install agent on first VM

**Production Setup**: 2-4 hours
- Install and configure controller
- Set up database and Redis
- Configure authentication (SSO, MFA)
- Install agents on VMs
- Configure access policies
- Set up monitoring and alerts

### What are the minimum system requirements?

**Controller**:
- 2 CPU cores
- 4GB RAM
- 20GB disk space
- Linux or Docker

**Agent**:
- 1 CPU core
- 512MB RAM
- 1GB disk space
- Linux, Windows, or macOS

**Database (PostgreSQL)**:
- 2 CPU cores
- 4GB RAM
- 50GB disk space (grows with metrics/logs)

### Can I run VM Gateway on Windows?

The controller is designed for Linux but can run on Windows using:
- Docker Desktop
- WSL2 (Windows Subsystem for Linux)
- Windows Server with Docker

Agents run natively on Windows.

### Do I need to install anything on my VMs?

Yes, you need to install the lightweight agent software on each VM you want to monitor. The agent:
- Runs as a background service
- Uses minimal resources (<50MB RAM, <1% CPU)
- Requires no configuration after initial setup
- Automatically discovers and reports services

### Can I monitor VMs without installing an agent?

No, the agent is required for:
- Service discovery
- Metrics collection
- Secure tunneling
- Health monitoring

However, the agent is lightweight and non-intrusive.

---

## Security & Authentication

### Is VM Gateway secure?

Yes! VM Gateway implements enterprise-grade security:
- TLS 1.3 for all communication
- mTLS between agents and controller
- End-to-end encryption for tunnels
- AES-256-GCM for data at rest
- Regular security audits
- No hardcoded credentials
- Secure defaults

### What authentication methods are supported?

- **Local authentication** with strong password policies
- **Multi-factor authentication** (TOTP, WebAuthn, SMS)
- **SSO** (SAML 2.0, OAuth 2.0, OIDC) - Professional+
- **LDAP/Active Directory** - Professional+
- **Custom providers** - Enterprise

### How does VM Gateway handle secrets?

VM Gateway integrates with enterprise secrets management:
- Built-in encrypted storage (all tiers)
- HashiCorp Vault (Professional+)
- AWS Secrets Manager (Professional+)
- Azure Key Vault (Professional+)
- HSM support (Enterprise)

Secrets are never logged or exposed in APIs.

### Can I use VM Gateway with my existing SSO?

Yes! Professional and Enterprise editions support:
- Okta
- Azure AD
- Google Workspace
- OneLogin
- Auth0
- Any SAML 2.0 or OIDC provider

### What compliance standards does VM Gateway support?

VM Gateway helps meet requirements for:
- SOC 2 Type II
- HIPAA
- PCI-DSS
- GDPR
- ISO 27001
- FedRAMP (with proper configuration)

Professional and Enterprise editions include compliance reporting templates.

---

## Features & Functionality

### What types of services can VM Gateway discover?

VM Gateway automatically discovers:
- **Web services**: HTTP, HTTPS, WebSocket
- **Databases**: PostgreSQL, MySQL, MongoDB, Redis, etc.
- **Message queues**: RabbitMQ, Kafka, Redis
- **Caching**: Redis, Memcached
- **APIs**: REST, GraphQL, gRPC
- **Custom services**: Any TCP/UDP service

### How accurate is service classification?

VM Gateway uses a multi-tier classification engine:
- **Tier 1**: Process name matching (95% accuracy)
- **Tier 2**: Port conventions (85% accuracy)
- **Tier 3**: Protocol detection (90% accuracy)
- **Tier 4**: ML-powered (98% accuracy - Professional+)

You can also define custom classification rules.

### Can I access services through my browser?

Yes! For HTTP/HTTPS services:
- Click "Open in Browser" in the service catalog
- Service opens through authenticated proxy
- No VPN or client software needed
- Works on any device with a browser

For other services (databases, APIs), use the desktop client for port forwarding.

### Does VM Gateway support approval workflows?

Yes! Professional and Enterprise editions support:
- Multi-level approval workflows
- Time-based approvals
- Conditional approvals
- Automatic expiration
- Notification integrations
- Audit trail

Example: Require DBA approval for production database access.

### Can I set time-based access restrictions?

Yes! Professional and Enterprise editions support:
- Business hours only access
- Temporary access with auto-expiration
- Scheduled access windows
- Emergency access with approval
- Time-zone aware restrictions

### How does monitoring work?

VM Gateway provides comprehensive monitoring:
- **System metrics**: CPU, memory, disk, network
- **Service metrics**: Response time, error rate, throughput
- **Custom metrics**: Application-specific metrics
- **Health checks**: Automatic service health monitoring
- **Alerting**: Configurable alerts with integrations
- **Dashboards**: Real-time and historical views

### Can I integrate VM Gateway with other tools?

Yes! VM Gateway integrates with:
- **Monitoring**: Datadog, Splunk, Prometheus, Grafana
- **Alerting**: PagerDuty, Slack, email, webhooks
- **Ticketing**: Jira, ServiceNow
- **SIEM**: Splunk, ELK Stack
- **CI/CD**: Jenkins, GitLab CI, GitHub Actions
- **Custom**: REST API and webhooks

---

## Performance & Scalability

### How much overhead does the agent add?

Minimal! The agent typically uses:
- **CPU**: <1% on average, <5% during scans
- **Memory**: 30-50MB
- **Network**: <1KB/s for metrics, <10KB/s during scans
- **Disk**: <100MB for agent + logs

### How often does the agent scan for services?

Default: Every 60 seconds

Configurable from 10 seconds to 1 hour. More frequent scans increase CPU usage slightly but provide faster service discovery.

### How much data does VM Gateway generate?

Depends on number of VMs and services:
- **Metrics**: ~1MB per VM per day
- **Audit logs**: ~100KB per user per day
- **Service catalog**: ~10KB per service

Example: 100 VMs, 500 services, 50 users = ~150MB/day = ~4.5GB/month

### Can VM Gateway handle thousands of VMs?

Yes! Scalability options:
- **Single controller**: 500-1000 VMs
- **Multi-controller (Enterprise)**: 10,000+ VMs
- **Database optimization**: Partitioning, replication
- **Metrics aggregation**: Reduce storage requirements
- **Geographic distribution**: Reduce latency

### Does VM Gateway affect service performance?

No! VM Gateway:
- Monitors services passively (no interference)
- Proxies/tunnels add <5ms latency
- Agent uses minimal resources
- No impact on service availability

---

## Licensing & Pricing

### Is VM Gateway free?

The Community Edition is free forever with:
- Up to 25 VMs
- Unlimited users
- Core features
- Community support

Professional and Enterprise editions add advanced features.

### Do you charge per user?

No! All editions support unlimited users. Pricing is based on feature tier only.

### What happens if I exceed 25 VMs on Community Edition?

The system continues to work, but:
- New VMs won't be added
- You'll see a notification to upgrade
- Existing VMs continue to function normally

### Can I try Professional/Enterprise before buying?

Yes! We offer:
- 30-day free trial of Professional
- 14-day free trial of Enterprise
- No credit card required
- Full feature access during trial

### What's included in support?

**Community**: Forums, GitHub issues, documentation

**Professional**: Email support (48-hour response), quarterly updates

**Enterprise**: 24/7 phone + email (4-hour response), dedicated account manager, on-site training

### Can I get a discount?

Yes! Discounts available for:
- Multi-year commitments (10-30% off)
- Multiple deployments (15-35% off)
- Non-profits (50% off)
- Education (50% off)
- Open source projects (free Enterprise)

---

## Troubleshooting

### Why can't I see any services?

Common causes:
1. Agent not connected - check agent status
2. Agent lacks permissions - run with appropriate privileges
3. Services on non-standard ports - adjust scan range
4. Firewall blocking scans - check firewall rules
5. Scan interval too long - reduce interval

See [Troubleshooting Guide](/docs/11-troubleshooting/01-common-issues.md) for details.

### Why is the web interface slow?

Common causes:
1. Too many services - enable pagination
2. No caching - enable Redis caching
3. Database not optimized - run VACUUM ANALYZE
4. Insufficient resources - upgrade hardware
5. Network latency - check connectivity

See [Performance Tuning](/docs/10-development/06-performance.md) for optimization tips.

### How do I reset my password?

```bash
# Via CLI
python -m vm_gateway.cli reset-password --email <EMAIL>

# Or contact your administrator
```

### How do I backup VM Gateway?

Backup these components:
1. **Database**: `pg_dump vmgateway > backup.sql`
2. **Configuration**: `.env` file and config files
3. **Secrets**: Encryption keys and certificates
4. **Redis** (optional): `redis-cli --rdb dump.rdb`

See [Backup & Recovery](/docs/08-deployment/04-backup-recovery.md) for details.

### Where are the logs?

Default locations:
- **Controller**: `logs/controller/controller.log`
- **Agent**: `/var/log/vm-gateway-agent/agent.log` (Linux)
- **Agent**: `C:\ProgramData\VM Gateway\logs\agent.log` (Windows)
- **Database**: PostgreSQL logs directory
- **Web server**: Nginx/Apache logs

---

## Migration & Integration

### Can I migrate from my current VPN solution?

Yes! VM Gateway can run alongside your existing VPN during migration:
1. Install VM Gateway
2. Migrate services gradually
3. Update access policies
4. Train users
5. Decommission VPN

We offer migration services for complex deployments.

### Can I import existing user accounts?

Yes! Options:
- **SSO**: Automatic user provisioning
- **LDAP/AD**: Sync users from directory
- **CSV import**: Bulk import via CLI
- **API**: Programmatic user creation

### Does VM Gateway work with Kubernetes?

Yes! VM Gateway can:
- Monitor services in Kubernetes pods
- Deploy controller in Kubernetes
- Integrate with Kubernetes RBAC
- Auto-discover services via Kubernetes API

See [Kubernetes Deployment](/docs/08-deployment/03-kubernetes.md) for details.

### Can I use VM Gateway with cloud providers?

Yes! VM Gateway works with:
- AWS (EC2, ECS, EKS)
- Azure (VMs, AKS)
- Google Cloud (GCE, GKE)
- DigitalOcean
- Any cloud provider with VMs

---

## Related Documentation

- [Getting Started Guide](/docs/01-overview/05-getting-started.md)
- [Troubleshooting](/docs/11-troubleshooting/01-common-issues.md)
- [Architecture Overview](/docs/02-architecture/01-system-overview.md)
- [Pricing & Tiers](/docs/01-overview/06-pricing-tiers.md)
- [Security Best Practices](/docs/10-development/07-security.md)

## Still Have Questions?

- Check our [comprehensive documentation](/docs/01-overview/01-introduction.md)
- Visit the community forums
- Review [GitHub issues](https://github.com/your-org/vm-gateway/issues)
- For Professional/Enterprise support, contact your account manager

