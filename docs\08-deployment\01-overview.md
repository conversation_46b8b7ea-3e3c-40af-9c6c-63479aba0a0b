# Deployment Overview

The VM Gateway platform supports multiple deployment models to accommodate different organizational needs, from simple single-server setups to highly available distributed architectures.

## Deployment Models

### 1. Single Server Deployment

Simplest deployment for small environments:

```
┌─────────────────────────────────────┐
│      Single Server                  │
│  ┌───────────────────────────────┐  │
│  │  Controller                   │  │
│  │  - Web Interface              │  │
│  │  - API Server                 │  │
│  │  - PostgreSQL                 │  │
│  │  - Redis                      │  │
│  └───────────────────────────────┘  │
└─────────────────────────────────────┘
```

**Use Cases**:
- Development environments
- Small teams (< 50 users)
- Proof of concept
- Testing and evaluation

**Requirements**:
- 4 CPU cores
- 8 GB RAM
- 100 GB storage
- Ubuntu 22.04 or similar

### 2. Multi-Server Deployment

Separate components for better performance:

```
┌──────────────┐  ┌──────────────┐  ┌──────────────┐
│  Controller  │  │  Database    │  │  Cache       │
│  - Web UI    │  │  PostgreSQL  │  │  Redis       │
│  - API       │  │              │  │              │
└──────────────┘  └──────────────┘  └──────────────┘
```

**Use Cases**:
- Medium teams (50-500 users)
- Production environments
- Better resource isolation
- Improved performance

### 3. High Availability Deployment

Redundant components for maximum uptime:

```
┌──────────────┐  ┌──────────────┐
│ Controller 1 │  │ Controller 2 │
└──────┬───────┘  └──────┬───────┘
       │                 │
       └────────┬────────┘
                │
       ┌────────▼────────┐
       │  Load Balancer  │
       └────────┬────────┘
                │
    ┌───────────┼───────────┐
    │           │           │
┌───▼───┐  ┌───▼───┐  ┌───▼───┐
│ DB 1  │  │ DB 2  │  │ DB 3  │
│Primary│  │Replica│  │Replica│
└───────┘  └───────┘  └───────┘
```

**Use Cases**:
- Large teams (500+ users)
- Mission-critical environments
- 99.9%+ uptime requirements
- Geographic distribution


### 4. Kubernetes Deployment

Container orchestration for cloud-native deployments:

```
┌─────────────────────────────────────────────┐
│           Kubernetes Cluster                │
│  ┌───────────────────────────────────────┐  │
│  │  Controller Deployment (3 replicas)   │  │
│  └───────────────────────────────────────┘  │
│  ┌───────────────────────────────────────┐  │
│  │  PostgreSQL StatefulSet               │  │
│  └───────────────────────────────────────┘  │
│  ┌───────────────────────────────────────┐  │
│  │  Redis Deployment                     │  │
│  └───────────────────────────────────────┘  │
│  ┌───────────────────────────────────────┐  │
│  │  Ingress Controller                   │  │
│  └───────────────────────────────────────┘  │
└─────────────────────────────────────────────┘
```

**Use Cases**:
- Cloud deployments
- Auto-scaling requirements
- Multi-region deployments
- DevOps-focused teams

## Deployment Strategies

### Blue-Green Deployment

Zero-downtime deployments:

1. Deploy new version (green) alongside current (blue)
2. Test green environment
3. Switch traffic to green
4. Keep blue as rollback option
5. Decommission blue after validation

### Rolling Deployment

Gradual rollout:

1. Update one instance at a time
2. Verify health after each update
3. Continue to next instance
4. Rollback if issues detected

### Canary Deployment

Test with subset of users:

1. Deploy new version to small percentage
2. Monitor metrics and errors
3. Gradually increase percentage
4. Full rollout or rollback based on results

## Infrastructure Requirements

### Minimum Requirements (Single Server)

- **CPU**: 4 cores
- **RAM**: 8 GB
- **Storage**: 100 GB SSD
- **Network**: 1 Gbps
- **OS**: Ubuntu 22.04 LTS

### Recommended Requirements (Production)

- **CPU**: 8+ cores per controller
- **RAM**: 16+ GB per controller
- **Storage**: 500+ GB SSD
- **Network**: 10 Gbps
- **OS**: Ubuntu 22.04 LTS or RHEL 8+

### Database Requirements

- **CPU**: 4+ cores
- **RAM**: 16+ GB
- **Storage**: 1+ TB SSD (IOPS optimized)
- **Backup**: Automated daily backups
- **Replication**: Streaming replication for HA

## Network Requirements

### Ports

**Controller**:
- 80/443: HTTP/HTTPS (web interface, API)
- 8080: WebSocket (real-time updates)
- 5432: PostgreSQL (if external)
- 6379: Redis (if external)

**Agent**:
- 8443: Agent API (controller communication)
- Dynamic: Service proxying

**Client**:
- Dynamic: SSH tunnels

### Firewall Rules

```bash
# Allow HTTP/HTTPS
ufw allow 80/tcp
ufw allow 443/tcp

# Allow WebSocket
ufw allow 8080/tcp

# Allow agent communication
ufw allow 8443/tcp

# Allow PostgreSQL (internal only)
ufw allow from 10.0.0.0/8 to any port 5432

# Allow Redis (internal only)
ufw allow from 10.0.0.0/8 to any port 6379
```

## Security Considerations

### TLS/SSL

- Use TLS 1.3 for all external connections
- Valid certificates from trusted CA
- Automatic certificate renewal
- HSTS enabled

### Network Segmentation

- Controller in DMZ or protected network
- Database in private network
- Agents in monitored networks
- Clients from any network (authenticated)

### Access Control

- Firewall rules limiting access
- VPN for administrative access
- MFA for all administrative accounts
- Regular security audits

## Monitoring and Observability

### Metrics Collection

- Prometheus for metrics
- Grafana for visualization
- AlertManager for alerting
- Custom dashboards

### Logging

- Centralized logging (ELK, Loki)
- Log retention policies
- Log analysis and alerting
- Audit log preservation

### Health Checks

- HTTP health endpoints
- Database connectivity checks
- Redis connectivity checks
- Agent connectivity monitoring

## Backup and Recovery

### Backup Strategy

- **Database**: Daily full backups, hourly incrementals
- **Configuration**: Version controlled
- **Secrets**: Encrypted backups
- **Logs**: Archived to object storage

### Recovery Procedures

- Documented recovery steps
- Regular recovery testing
- RTO: < 1 hour
- RPO: < 15 minutes

## Deployment Checklist

### Pre-Deployment

- [ ] Infrastructure provisioned
- [ ] Network configured
- [ ] Firewall rules applied
- [ ] TLS certificates obtained
- [ ] DNS records configured
- [ ] Backup system configured
- [ ] Monitoring system ready

### Deployment

- [ ] Install dependencies
- [ ] Deploy database
- [ ] Deploy controller
- [ ] Configure secrets
- [ ] Run database migrations
- [ ] Deploy agents
- [ ] Configure load balancer
- [ ] Verify health checks

### Post-Deployment

- [ ] Smoke tests passed
- [ ] Monitoring active
- [ ] Alerts configured
- [ ] Documentation updated
- [ ] Team trained
- [ ] Backup verified
- [ ] Disaster recovery tested

## Next Steps

- [Docker](02-docker.md) - Docker containerization
- [Kubernetes](03-kubernetes.md) - Kubernetes deployment
- [Multi-VM](04-multi-vm.md) - Multi-VM architecture
- [DNS Routing](05-dns-routing.md) - Domain-based routing
- [TLS Certificates](06-tls-certificates.md) - Certificate management
- [High Availability](07-high-availability.md) - HA configuration
- [Backup Recovery](08-backup-recovery.md) - Backup and disaster recovery
