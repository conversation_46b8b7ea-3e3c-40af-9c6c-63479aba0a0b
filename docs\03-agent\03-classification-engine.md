---
title: "Service Classification Engine"
section: "Agent"
order: 3
tags: ["agent", "classification", "machine-learning", "detection"]
last_updated: "2025-11-08"
---

# Service Classification Engine

## Introduction

The Service Classification Engine is a sophisticated multi-tier system that automatically identifies and categorizes discovered services. While the Service Discovery Engine tells us *what ports are listening*, the Classification Engine tells us *what those services actually are*—transforming raw port and process data into meaningful service identities.

The engine uses a cascading approach, starting with high-confidence exact matches and progressively falling back to heuristic methods, protocol detection, and optional machine learning when needed. This ensures maximum accuracy while maintaining performance and reliability.

## Classification Architecture

### Multi-Tier Approach

```
┌─────────────────────────────────────────────────────────────┐
│           Service Classification Engine                     │
│                                                             │
│  ┌───────────────────────────────────────────────────────┐ │
│  │  Tier 1: Exact Process Match                          │ │
│  │  - Match executable name against known patterns       │ │
│  │  - Highest confidence (95-100%)                       │ │
│  │  - Examples: nginx, postgres, redis-server            │ │
│  └───────────────────┬───────────────────────────────────┘ │
│                      │ No Match                             │
│                      ▼                                       │
│  ┌───────────────────────────────────────────────────────┐ │
│  │  Tier 2: Port Convention Matching                     │ │
│  │  - Match port number against common conventions       │ │
│  │  - Medium-high confidence (70-90%)                    │ │
│  │  - Examples: 3306→MySQL, 5432→PostgreSQL              │ │
│  └───────────────────┬───────────────────────────────────┘ │
│                      │ No Match                             │
│                      ▼                                       │
│  ┌───────────────────────────────────────────────────────┐ │
│  │  Tier 3: Protocol Detection                           │ │
│  │  - Active probing and banner grabbing                 │ │
│  │  - Medium confidence (60-80%)                         │ │
│  │  - Examples: HTTP headers, TLS handshake              │ │
│  └───────────────────┬───────────────────────────────────┘ │
│                      │ No Match                             │
│                      ▼                                       │
│  ┌───────────────────────────────────────────────────────┐ │
│  │  Tier 4: Deep Packet Inspection (Optional)            │ │
│  │  - Analyze packet payloads                            │ │
│  │  - Pattern matching against protocol signatures       │ │
│  │  - Low-medium confidence (40-60%)                     │ │
│  └───────────────────┬───────────────────────────────────┘ │
│                      │ No Match                             │
│                      ▼                                       │
│  ┌───────────────────────────────────────────────────────┐ │
│  │  Tier 5: Manual Override / Custom Rules               │ │
│  │  - User-defined classification rules                  │ │
│  │  - Highest priority (overrides all tiers)             │ │
│  │  - Confidence: 100% (user-specified)                  │ │
│  └───────────────────────────────────────────────────────┘ │
│                                                             │
│  ┌───────────────────────────────────────────────────────┐ │
│  │  Classification Cache                                 │ │
│  │  - Store classification results                       │ │
│  │  - Invalidate on version change                       │ │
│  │  - Dramatically improve performance                   │ │
│  └───────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Tier 1: Exact Process Match

### Process Name Patterns

The highest confidence classification comes from matching process names against known patterns:


**Web Servers:**
- `nginx`, `httpd`, `apache2` → Web Server (Nginx/Apache)
- `caddy` → Caddy Web Server
- `lighttpd` → Lighttpd Web Server
- `traefik` → Traefik Reverse Proxy

**Databases:**
- `postgres`, `postmaster` → PostgreSQL Database
- `mysqld`, `mariadbd` → MySQL/MariaDB Database
- `mongod` → MongoDB Database
- `redis-server` → Redis Cache/Database
- `memcached` → Memcached Cache
- `cassandra` → Cassandra Database
- `influxd` → InfluxDB Time-Series Database
- `clickhouse-server` → ClickHouse Database

**Message Brokers:**
- `rabbitmq-server`, `beam.smp` (with rabbitmq) → RabbitMQ
- `kafka` → Apache Kafka
- `redis-server` (with pub/sub config) → Redis Pub/Sub
- `nats-server` → NATS Message Broker

**Application Runtimes:**
- `node` → Node.js Application
- `python`, `python3` → Python Application
- `java` → Java Application
- `ruby` → Ruby Application
- `php-fpm` → PHP-FPM
- `dotnet` → .NET Application

**Container Runtimes:**
- `docker-proxy` → Docker Container Port Mapping
- `containerd` → Containerd Runtime
- `podman` → Podman Container

**Other Services:**
- `sshd` → SSH Server
- `elasticsearch` → Elasticsearch
- `grafana-server` → Grafana
- `prometheus` → Prometheus Monitoring
- `vault` → HashiCorp Vault

### Implementation

```python
from typing import Optional, Dict, Any
from dataclasses import dataclass

@dataclass
class ServiceClassification:
    service_type: str
    service_name: str
    confidence: float
    tier: int
    metadata: Dict[str, Any]

class ProcessMatcher:
    # Process name to service type mapping
    PROCESS_PATTERNS = {
        'nginx': {'type': 'web_server', 'name': 'Nginx', 'category': 'web'},
        'httpd': {'type': 'web_server', 'name': 'Apache HTTP Server', 'category': 'web'},
        'apache2': {'type': 'web_server', 'name': 'Apache HTTP Server', 'category': 'web'},
        'postgres': {'type': 'database', 'name': 'PostgreSQL', 'category': 'database'},
        'mysqld': {'type': 'database', 'name': 'MySQL', 'category': 'database'},
        'mongod': {'type': 'database', 'name': 'MongoDB', 'category': 'database'},
        'redis-server': {'type': 'cache', 'name': 'Redis', 'category': 'cache'},
        'node': {'type': 'runtime', 'name': 'Node.js Application', 'category': 'application'},
        'python': {'type': 'runtime', 'name': 'Python Application', 'category': 'application'},
        'java': {'type': 'runtime', 'name': 'Java Application', 'category': 'application'},
    }
    
    def classify(self, process_name: str, cmdline: list) -> Optional[ServiceClassification]:
        """
        Classify service based on process name.
        """
        # Normalize process name
        process_name = process_name.lower().strip()
        
        # Direct match
        if process_name in self.PROCESS_PATTERNS:
            pattern = self.PROCESS_PATTERNS[process_name]
            return ServiceClassification(
                service_type=pattern['type'],
                service_name=pattern['name'],
                confidence=0.95,
                tier=1,
                metadata={'category': pattern['category'], 'method': 'exact_match'}
            )
        
        # Partial match (e.g., "postgres: writer process")
        for key, pattern in self.PROCESS_PATTERNS.items():
            if key in process_name:
                return ServiceClassification(
                    service_type=pattern['type'],
                    service_name=pattern['name'],
                    confidence=0.90,
                    tier=1,
                    metadata={'category': pattern['category'], 'method': 'partial_match'}
                )
        
        return None
```

### Version Detection

When a process is matched, the engine attempts to extract version information:

```python
def extract_version(process_name: str, cmdline: list, exe_path: str) -> Optional[str]:
    """
    Extract version information from process.
    """
    # Method 1: Check command line for --version flag
    if '--version' in cmdline:
        version_idx = cmdline.index('--version')
        if version_idx + 1 < len(cmdline):
            return cmdline[version_idx + 1]
    
    # Method 2: Execute with --version flag
    try:
        result = subprocess.run(
            [exe_path, '--version'],
            capture_output=True,
            text=True,
            timeout=2
        )
        # Parse version from output (e.g., "nginx version: nginx/1.18.0")
        version_match = re.search(r'(\d+\.\d+\.\d+)', result.stdout)
        if version_match:
            return version_match.group(1)
    except (subprocess.TimeoutExpired, FileNotFoundError):
        pass
    
    # Method 3: Check package manager
    version = get_version_from_package_manager(process_name)
    if version:
        return version
    
    return None
```

## Tier 2: Port Convention Matching

### Common Port Assignments

When process name matching fails, the engine falls back to port number conventions:

**Web Services:**
- 80, 8080, 8000, 8888 → HTTP Service
- 443, 8443 → HTTPS Service
- 3000 → Node.js Development Server
- 4200 → Angular Development Server
- 5000 → Flask Development Server
- 8080 → Tomcat/Jenkins

**Databases:**
- 3306 → MySQL/MariaDB
- 5432 → PostgreSQL
- 27017 → MongoDB
- 6379 → Redis
- 11211 → Memcached
- 9042 → Cassandra
- 8086 → InfluxDB
- 9000 → ClickHouse

**Message Brokers:**
- 5672 → RabbitMQ (AMQP)
- 9092 → Kafka
- 4222 → NATS

**Monitoring & Observability:**
- 9090 → Prometheus
- 3000 → Grafana
- 9200 → Elasticsearch
- 5601 → Kibana
- 8125 → StatsD
- 2003 → Graphite

**Other Services:**
- 22 → SSH
- 21 → FTP
- 25 → SMTP
- 53 → DNS
- 3389 → RDP (Remote Desktop)
- 5900 → VNC

### Implementation

```python
class PortMatcher:
    PORT_CONVENTIONS = {
        80: {'type': 'web_server', 'name': 'HTTP Service', 'protocol': 'http'},
        443: {'type': 'web_server', 'name': 'HTTPS Service', 'protocol': 'https'},
        3306: {'type': 'database', 'name': 'MySQL/MariaDB', 'protocol': 'mysql'},
        5432: {'type': 'database', 'name': 'PostgreSQL', 'protocol': 'postgresql'},
        6379: {'type': 'cache', 'name': 'Redis', 'protocol': 'redis'},
        27017: {'type': 'database', 'name': 'MongoDB', 'protocol': 'mongodb'},
        9200: {'type': 'search', 'name': 'Elasticsearch', 'protocol': 'http'},
    }
    
    def classify(self, port: int) -> Optional[ServiceClassification]:
        """
        Classify service based on port number.
        """
        if port in self.PORT_CONVENTIONS:
            convention = self.PORT_CONVENTIONS[port]
            return ServiceClassification(
                service_type=convention['type'],
                service_name=convention['name'],
                confidence=0.75,
                tier=2,
                metadata={'protocol': convention['protocol'], 'method': 'port_convention'}
            )
        
        return None
```

### Port Range Patterns

Some services use port ranges:

```python
def classify_by_port_range(port: int) -> Optional[ServiceClassification]:
    """
    Classify based on port ranges.
    """
    # Ephemeral ports (usually client-side, ignore)
    if 49152 <= port <= 65535:
        return None
    
    # Common development server range
    if 3000 <= port <= 3999:
        return ServiceClassification(
            service_type='web_server',
            service_name='Development Web Server',
            confidence=0.60,
            tier=2,
            metadata={'method': 'port_range', 'range': '3000-3999'}
        )
    
    # Common application server range
    if 8000 <= port <= 8999:
        return ServiceClassification(
            service_type='web_server',
            service_name='Application Server',
            confidence=0.65,
            tier=2,
            metadata={'method': 'port_range', 'range': '8000-8999'}
        )
    
    return None
```

## Tier 3: Protocol Detection

### Active Probing

When process and port matching fail, the engine actively probes the service:

**HTTP/HTTPS Detection:**

```python
import aiohttp
import asyncio

async def detect_http_service(host: str, port: int) -> Optional[ServiceClassification]:
    """
    Detect HTTP/HTTPS service by sending HTTP request.
    """
    for scheme in ['http', 'https']:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f'{scheme}://{host}:{port}',
                    timeout=aiohttp.ClientTimeout(total=2),
                    allow_redirects=False
                ) as response:
                    # Analyze response headers
                    server_header = response.headers.get('Server', '')
                    powered_by = response.headers.get('X-Powered-By', '')
                    
                    # Identify specific web servers
                    if 'nginx' in server_header.lower():
                        return ServiceClassification(
                            service_type='web_server',
                            service_name='Nginx',
                            confidence=0.85,
                            tier=3,
                            metadata={'server_header': server_header, 'scheme': scheme}
                        )
                    elif 'apache' in server_header.lower():
                        return ServiceClassification(
                            service_type='web_server',
                            service_name='Apache HTTP Server',
                            confidence=0.85,
                            tier=3,
                            metadata={'server_header': server_header, 'scheme': scheme}
                        )
                    elif powered_by:
                        return ServiceClassification(
                            service_type='web_server',
                            service_name=f'{powered_by} Application',
                            confidence=0.75,
                            tier=3,
                            metadata={'powered_by': powered_by, 'scheme': scheme}
                        )
                    else:
                        # Generic HTTP service
                        return ServiceClassification(
                            service_type='web_server',
                            service_name='HTTP Service',
                            confidence=0.70,
                            tier=3,
                            metadata={'scheme': scheme}
                        )
        except (aiohttp.ClientError, asyncio.TimeoutError):
            continue
    
    return None
```

**TLS Certificate Inspection:**

```python
import ssl
import socket

def inspect_tls_certificate(host: str, port: int) -> Optional[Dict[str, Any]]:
    """
    Inspect TLS certificate to extract service information.
    """
    try:
        context = ssl.create_default_context()
        with socket.create_connection((host, port), timeout=2) as sock:
            with context.wrap_socket(sock, server_hostname=host) as ssock:
                cert = ssock.getpeercert()
                
                return {
                    'subject': dict(x[0] for x in cert['subject']),
                    'issuer': dict(x[0] for x in cert['issuer']),
                    'version': cert['version'],
                    'serial_number': cert['serialNumber'],
                    'not_before': cert['notBefore'],
                    'not_after': cert['notAfter'],
                    'san': cert.get('subjectAltName', [])
                }
    except (socket.timeout, ssl.SSLError, ConnectionRefusedError):
        return None
```

**Database Protocol Detection:**

```python
async def detect_database_protocol(host: str, port: int) -> Optional[ServiceClassification]:
    """
    Detect database by analyzing handshake.
    """
    try:
        reader, writer = await asyncio.wait_for(
            asyncio.open_connection(host, port),
            timeout=2
        )
        
        # Read initial handshake/banner
        banner = await asyncio.wait_for(reader.read(1024), timeout=1)
        
        writer.close()
        await writer.wait_closed()
        
        # MySQL/MariaDB: Starts with protocol version (0x0a)
        if banner[0:1] == b'\x0a':
            return ServiceClassification(
                service_type='database',
                service_name='MySQL/MariaDB',
                confidence=0.80,
                tier=3,
                metadata={'method': 'protocol_handshake'}
            )
        
        # PostgreSQL: Specific handshake pattern
        if b'PostgreSQL' in banner or banner[0:1] == b'N':
            return ServiceClassification(
                service_type='database',
                service_name='PostgreSQL',
                confidence=0.80,
                tier=3,
                metadata={'method': 'protocol_handshake'}
            )
        
        # Redis: Responds to PING with +PONG
        if banner.startswith(b'+PONG') or banner.startswith(b'-ERR'):
            return ServiceClassification(
                service_type='cache',
                service_name='Redis',
                confidence=0.85,
                tier=3,
                metadata={'method': 'protocol_handshake'}
            )
        
    except (asyncio.TimeoutError, ConnectionRefusedError, OSError):
        pass
    
    return None
```

**SSH Banner Grabbing:**

```python
async def detect_ssh_service(host: str, port: int) -> Optional[ServiceClassification]:
    """
    Detect SSH service by reading banner.
    """
    try:
        reader, writer = await asyncio.wait_for(
            asyncio.open_connection(host, port),
            timeout=2
        )
        
        # SSH server sends banner immediately
        banner = await asyncio.wait_for(reader.readline(), timeout=1)
        
        writer.close()
        await writer.wait_closed()
        
        if banner.startswith(b'SSH-'):
            banner_str = banner.decode('utf-8', errors='ignore').strip()
            return ServiceClassification(
                service_type='remote_access',
                service_name='SSH Server',
                confidence=0.95,
                tier=3,
                metadata={'banner': banner_str, 'method': 'banner_grab'}
            )
    except (asyncio.TimeoutError, ConnectionRefusedError, OSError):
        pass
    
    return None
```

## Tier 4: Deep Packet Inspection

### Packet Analysis

For truly unknown services, optional deep packet inspection can be enabled:

```python
from scapy.all import sniff, TCP, UDP

class PacketInspector:
    def __init__(self):
        self.signatures = self.load_protocol_signatures()
    
    def analyze_traffic(self, port: int, duration: int = 10) -> Optional[ServiceClassification]:
        """
        Capture and analyze packets to identify protocol.
        """
        packets = sniff(
            filter=f'port {port}',
            timeout=duration,
            count=100
        )
        
        for packet in packets:
            if TCP in packet or UDP in packet:
                payload = bytes(packet[TCP].payload if TCP in packet else packet[UDP].payload)
                
                # Match against known protocol signatures
                for signature in self.signatures:
                    if signature['pattern'] in payload:
                        return ServiceClassification(
                            service_type=signature['type'],
                            service_name=signature['name'],
                            confidence=0.60,
                            tier=4,
                            metadata={'method': 'packet_inspection', 'signature': signature['id']}
                        )
        
        return None
```

### Protocol Signatures

Common protocol signatures for identification:

```python
PROTOCOL_SIGNATURES = [
    {
        'id': 'http',
        'pattern': b'HTTP/',
        'type': 'web_server',
        'name': 'HTTP Service'
    },
    {
        'id': 'smtp',
        'pattern': b'220 ',
        'type': 'email',
        'name': 'SMTP Server'
    },
    {
        'id': 'ftp',
        'pattern': b'220-',
        'type': 'file_transfer',
        'name': 'FTP Server'
    },
    {
        'id': 'dns',
        'pattern': b'\x00\x01\x00\x00',
        'type': 'dns',
        'name': 'DNS Server'
    }
]
```

## Tier 5: Manual Override

### Custom Classification Rules

Users can define custom rules that override all automatic classification:

```yaml
classification_rules:
  - name: "Internal API Server"
    match:
      port: 9999
      process: "custom-app"
    classification:
      type: "api"
      name: "Internal API Server"
      category: "internal"
      tags: ["internal", "critical"]
    confidence: 1.0
  
  - name: "Legacy Database"
    match:
      port: 10000
      bind_address: "127.0.0.1"
    classification:
      type: "database"
      name: "Legacy Oracle Database"
      category: "database"
      tags: ["legacy", "deprecated"]
    confidence: 1.0
  
  - name: "Development Servers"
    match:
      port_range: [3000, 3999]
      username: "developer"
    classification:
      type: "web_server"
      name: "Development Server"
      category: "development"
      tags: ["dev", "non-production"]
    confidence: 1.0
```

### Rule Implementation

```python
from typing import List
import yaml

class ManualClassifier:
    def __init__(self, rules_file: str):
        with open(rules_file, 'r') as f:
            config = yaml.safe_load(f)
            self.rules = config.get('classification_rules', [])
    
    def classify(self, service_info: Dict[str, Any]) -> Optional[ServiceClassification]:
        """
        Apply manual classification rules.
        """
        for rule in self.rules:
            if self.matches_rule(service_info, rule['match']):
                classification = rule['classification']
                return ServiceClassification(
                    service_type=classification['type'],
                    service_name=classification['name'],
                    confidence=rule.get('confidence', 1.0),
                    tier=5,
                    metadata={
                        'category': classification.get('category'),
                        'tags': classification.get('tags', []),
                        'method': 'manual_override',
                        'rule_name': rule['name']
                    }
                )
        
        return None
    
    def matches_rule(self, service_info: Dict[str, Any], match_criteria: Dict[str, Any]) -> bool:
        """
        Check if service matches rule criteria.
        """
        # Check port
        if 'port' in match_criteria:
            if service_info.get('port') != match_criteria['port']:
                return False
        
        # Check port range
        if 'port_range' in match_criteria:
            port_range = match_criteria['port_range']
            if not (port_range[0] <= service_info.get('port', 0) <= port_range[1]):
                return False
        
        # Check process name
        if 'process' in match_criteria:
            if match_criteria['process'] not in service_info.get('process_name', ''):
                return False
        
        # Check bind address
        if 'bind_address' in match_criteria:
            if service_info.get('bind_address') != match_criteria['bind_address']:
                return False
        
        # Check username
        if 'username' in match_criteria:
            if service_info.get('username') != match_criteria['username']:
                return False
        
        return True
```

## Classification Orchestration

### Unified Classification Pipeline

The engine orchestrates all tiers in sequence:

```python
class ClassificationEngine:
    def __init__(self, config: Dict[str, Any]):
        self.manual_classifier = ManualClassifier(config.get('rules_file'))
        self.process_matcher = ProcessMatcher()
        self.port_matcher = PortMatcher()
        self.protocol_detector = ProtocolDetector()
        self.packet_inspector = PacketInspector() if config.get('enable_dpi') else None
        self.cache = ClassificationCache()
    
    async def classify(self, service_info: Dict[str, Any]) -> ServiceClassification:
        """
        Classify a service using all available tiers.
        """
        # Check cache first
        cache_key = self.get_cache_key(service_info)
        cached = self.cache.get(cache_key)
        if cached:
            return cached
        
        # Tier 5: Manual override (highest priority)
        result = self.manual_classifier.classify(service_info)
        if result:
            self.cache.set(cache_key, result)
            return result
        
        # Tier 1: Exact process match
        result = self.process_matcher.classify(
            service_info.get('process_name', ''),
            service_info.get('cmdline', [])
        )
        if result and result.confidence >= 0.90:
            self.cache.set(cache_key, result)
            return result
        
        # Tier 2: Port convention
        port_result = self.port_matcher.classify(service_info.get('port'))
        if port_result and port_result.confidence >= 0.75:
            # Combine with process match if available
            if result:
                result.confidence = (result.confidence + port_result.confidence) / 2
                result.metadata['port_convention'] = port_result.service_name
            else:
                result = port_result
            self.cache.set(cache_key, result)
            return result
        
        # Tier 3: Protocol detection
        result = await self.protocol_detector.detect(
            service_info.get('bind_address', '127.0.0.1'),
            service_info.get('port')
        )
        if result:
            self.cache.set(cache_key, result)
            return result
        
        # Tier 4: Deep packet inspection (if enabled)
        if self.packet_inspector:
            result = self.packet_inspector.analyze_traffic(service_info.get('port'))
            if result:
                self.cache.set(cache_key, result)
                return result
        
        # Unknown service
        return ServiceClassification(
            service_type='unknown',
            service_name='Unknown Service',
            confidence=0.0,
            tier=0,
            metadata={'method': 'unclassified'}
        )
```

## Performance and Caching

### Classification Cache

```python
from datetime import datetime, timedelta
from typing import Optional

class ClassificationCache:
    def __init__(self, ttl_seconds: int = 3600):
        self.cache = {}
        self.ttl = timedelta(seconds=ttl_seconds)
    
    def get(self, key: str) -> Optional[ServiceClassification]:
        """
        Get cached classification if still valid.
        """
        if key in self.cache:
            entry = self.cache[key]
            if datetime.now() - entry['timestamp'] < self.ttl:
                return entry['classification']
            else:
                del self.cache[key]
        return None
    
    def set(self, key: str, classification: ServiceClassification):
        """
        Cache classification result.
        """
        self.cache[key] = {
            'classification': classification,
            'timestamp': datetime.now()
        }
    
    def invalidate(self, key: str):
        """
        Invalidate cached classification.
        """
        if key in self.cache:
            del self.cache[key]
```

### Cache Invalidation

Cache is invalidated when:
- Service version changes
- Process restarts (new PID)
- Port changes
- Manual classification rules updated
- TTL expires (default: 1 hour)

## Configuration

### Classification Settings

```yaml
classification:
  # Enable/disable tiers
  enable_process_matching: true
  enable_port_matching: true
  enable_protocol_detection: true
  enable_deep_packet_inspection: false  # Resource intensive
  
  # Confidence thresholds
  min_confidence_tier1: 0.90
  min_confidence_tier2: 0.75
  min_confidence_tier3: 0.60
  
  # Protocol detection settings
  protocol_detection_timeout: 2s
  max_concurrent_probes: 10
  
  # Caching
  cache_ttl: 3600s
  cache_max_entries: 10000
  
  # Custom rules
  rules_file: "/etc/vm-gateway/classification-rules.yaml"
  
  # Performance
  async_classification: true
  classification_timeout: 10s
```

## Summary

The Service Classification Engine transforms raw port and process data into meaningful service identities through a sophisticated multi-tier approach. By cascading from high-confidence exact matches to heuristic methods and optional machine learning, the engine achieves both accuracy and performance.

Key features:

- **Multi-Tier Approach**: Five classification tiers with decreasing confidence
- **High Accuracy**: 95%+ confidence for common services
- **Extensible**: Custom rules override automatic classification
- **Performance**: Intelligent caching and async operations
- **Comprehensive**: Covers 500+ common services out of the box
- **Flexible**: Optional deep packet inspection for unknown services

The classification engine is essential for providing meaningful service names and types in the VM Gateway platform, enabling intuitive service catalogs, intelligent access control, and effective monitoring.

## Next Steps

- **[Metrics Collection](04-metrics-collection.md)**: Learn how metrics are gathered for classified services
- **[Communication Protocol](05-communication-protocol.md)**: See how classification results are transmitted to the controller
- **[Installation](06-installation.md)**: Configure classification rules during agent installation
