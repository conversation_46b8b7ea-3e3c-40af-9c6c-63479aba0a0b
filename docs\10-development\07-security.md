---
title: "Security Best Practices"
section: "Development"
order: 7
tags: ["development", "security", "best-practices"]
last_updated: "2025-11-08"
---

# Security Best Practices

This guide covers security best practices for developing the VM Network Gateway & Access Control Platform. Security is a critical aspect of the platform and must be considered at every stage of development.

## Security Principles

### Core Tenets

1. **Defense in Depth**: Multiple layers of security controls
2. **Least Privilege**: Minimum necessary permissions
3. **Fail Secure**: Default to deny access
4. **Zero Trust**: Never trust, always verify
5. **Security by Design**: Build security in from the start
6. **Assume Breach**: Plan for compromise
7. **Audit Everything**: Comprehensive logging

## Authentication & Authorization

### Password Security

```python
# Use strong password hashing
from passlib.context import CryptContext

pwd_context = CryptContext(
    schemes=["argon2", "bcrypt"],
    deprecated="auto",
    argon2__memory_cost=65536,
    argon2__time_cost=3,
    argon2__parallelism=4
)

def hash_password(password: str) -> str:
    """Hash password using Argon2."""
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password against hash."""
    return pwd_context.verify(plain_password, hashed_password)

# Password validation
import re

def validate_password(password: str) -> tuple[bool, str]:
    """Validate password strength."""
    if len(password) < 12:
        return False, "Password must be at least 12 characters"
    
    if not re.search(r"[A-Z]", password):
        return False, "Password must contain uppercase letter"
    
    if not re.search(r"[a-z]", password):
        return False, "Password must contain lowercase letter"
    
    if not re.search(r"\d", password):
        return False, "Password must contain number"
    
    if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", password):
        return False, "Password must contain special character"
    
    # Check against common passwords
    if password.lower() in COMMON_PASSWORDS:
        return False, "Password is too common"
    
    return True, "Password is valid"
```

### JWT Token Security

```python
from datetime import datetime, timedelta
from jose import JWTError, jwt
from typing import Optional

SECRET_KEY = "your-secret-key-min-32-chars"  # Use environment variable
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 15

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token."""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({
        "exp": expire,
        "iat": datetime.utcnow(),
        "jti": generate_unique_id()  # JWT ID for revocation
    })
    
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> dict:
    """Verify and decode JWT token."""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        # Check if token is revoked
        if is_token_revoked(payload.get("jti")):
            raise JWTError("Token has been revoked")
        
        return payload
    except JWTError:
        raise HTTPException(
            status_code=401,
            detail="Could not validate credentials"
        )
```

### API Key Security

```python
import secrets
import hashlib

def generate_api_key() -> tuple[str, str]:
    """Generate API key and its hash."""
    # Generate random key
    key = f"pk_live_{secrets.token_urlsafe(32)}"
    
    # Hash for storage
    key_hash = hashlib.sha256(key.encode()).hexdigest()
    
    return key, key_hash

def verify_api_key(provided_key: str, stored_hash: str) -> bool:
    """Verify API key against stored hash."""
    provided_hash = hashlib.sha256(provided_key.encode()).hexdigest()
    return secrets.compare_digest(provided_hash, stored_hash)
```

## Input Validation

### SQL Injection Prevention

```python
# ALWAYS use parameterized queries
from sqlalchemy import text

# Bad - SQL injection vulnerable
user_id = request.args.get('user_id')
query = f"SELECT * FROM users WHERE id = '{user_id}'"
result = db.execute(query)

# Good - parameterized query
user_id = request.args.get('user_id')
query = text("SELECT * FROM users WHERE id = :user_id")
result = db.execute(query, {"user_id": user_id})

# Good - ORM
user = db.query(User).filter(User.id == user_id).first()
```

### XSS Prevention

```python
from markupsafe import escape

# Escape user input
def sanitize_input(user_input: str) -> str:
    """Sanitize user input to prevent XSS."""
    return escape(user_input)

# In templates, use auto-escaping
# Jinja2 auto-escapes by default
# {{ user_input }}  # Automatically escaped

# React auto-escapes by default
# <div>{userInput}</div>  // Automatically escaped
```

### Path Traversal Prevention

```python
from pathlib import Path
import os

def safe_join(base_dir: str, user_path: str) -> str:
    """Safely join paths to prevent traversal."""
    base = Path(base_dir).resolve()
    target = (base / user_path).resolve()
    
    # Ensure target is within base directory
    if not str(target).startswith(str(base)):
        raise ValueError("Path traversal detected")
    
    return str(target)

# Example usage
try:
    file_path = safe_join("/var/data", user_provided_path)
    with open(file_path, 'r') as f:
        content = f.read()
except ValueError:
    return {"error": "Invalid path"}
```

### Command Injection Prevention

```python
import subprocess
import shlex

# Bad - command injection vulnerable
filename = request.args.get('filename')
subprocess.run(f"cat {filename}", shell=True)

# Good - use list and avoid shell
filename = request.args.get('filename')
subprocess.run(["cat", filename], shell=False)

# If shell is necessary, sanitize input
filename = shlex.quote(request.args.get('filename'))
subprocess.run(f"cat {filename}", shell=True)
```

## Data Protection

### Encryption at Rest

```python
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2
import base64

class DataEncryption:
    """Encrypt and decrypt sensitive data."""
    
    def __init__(self, master_key: str):
        # Derive encryption key from master key
        kdf = PBKDF2(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'static_salt',  # Use unique salt per installation
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(master_key.encode()))
        self.cipher = Fernet(key)
    
    def encrypt(self, data: str) -> str:
        """Encrypt data."""
        return self.cipher.encrypt(data.encode()).decode()
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt data."""
        return self.cipher.decrypt(encrypted_data.encode()).decode()

# Usage
encryptor = DataEncryption(os.getenv("MASTER_KEY"))

# Encrypt sensitive data before storing
encrypted_secret = encryptor.encrypt(api_secret)
db.execute("INSERT INTO secrets (value) VALUES (:value)", 
           {"value": encrypted_secret})

# Decrypt when retrieving
encrypted_value = db.execute("SELECT value FROM secrets WHERE id = :id",
                             {"id": secret_id}).scalar()
decrypted_secret = encryptor.decrypt(encrypted_value)
```

### Encryption in Transit

```python
# Force HTTPS
from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware

app.add_middleware(HTTPSRedirectMiddleware)

# Set security headers
from fastapi.middleware.trustedhost import TrustedHostMiddleware

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["gateway.example.com", "*.gateway.example.com"]
)

@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Content-Security-Policy"] = "default-src 'self'"
    return response
```

## Secrets Management

### Environment Variables

```python
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """Application settings from environment."""
    
    database_url: str
    redis_url: str
    secret_key: str
    jwt_secret_key: str
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# Never hardcode secrets
# Bad
DATABASE_URL = "postgresql://user:password@localhost/db"

# Good
settings = Settings()
DATABASE_URL = settings.database_url
```

### Secrets Rotation

```python
from datetime import datetime, timedelta

class SecretRotation:
    """Manage secret rotation."""
    
    def should_rotate(self, secret_created_at: datetime) -> bool:
        """Check if secret should be rotated."""
        rotation_period = timedelta(days=90)
        return datetime.utcnow() - secret_created_at > rotation_period
    
    def rotate_secret(self, secret_id: str) -> str:
        """Rotate a secret."""
        # Generate new secret
        new_secret = generate_secure_secret()
        
        # Store new secret
        db.execute(
            "UPDATE secrets SET value = :value, rotated_at = :rotated_at WHERE id = :id",
            {
                "value": new_secret,
                "rotated_at": datetime.utcnow(),
                "id": secret_id
            }
        )
        
        # Notify systems using this secret
        notify_secret_rotation(secret_id)
        
        return new_secret
```

## Audit Logging

### Comprehensive Logging

```python
import logging
from datetime import datetime
from typing import Optional

class AuditLogger:
    """Audit logging for security events."""
    
    def __init__(self):
        self.logger = logging.getLogger("audit")
    
    def log_authentication(
        self,
        user_id: Optional[str],
        email: str,
        success: bool,
        ip_address: str,
        user_agent: str
    ):
        """Log authentication attempt."""
        self.logger.info(
            "Authentication attempt",
            extra={
                "event_type": "authentication",
                "user_id": user_id,
                "email": email,
                "success": success,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
    
    def log_authorization(
        self,
        user_id: str,
        resource_type: str,
        resource_id: str,
        action: str,
        granted: bool,
        reason: Optional[str] = None
    ):
        """Log authorization check."""
        self.logger.info(
            "Authorization check",
            extra={
                "event_type": "authorization",
                "user_id": user_id,
                "resource_type": resource_type,
                "resource_id": resource_id,
                "action": action,
                "granted": granted,
                "reason": reason,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
    
    def log_data_access(
        self,
        user_id: str,
        resource_type: str,
        resource_id: str,
        action: str
    ):
        """Log data access."""
        self.logger.info(
            "Data access",
            extra={
                "event_type": "data_access",
                "user_id": user_id,
                "resource_type": resource_type,
                "resource_id": resource_id,
                "action": action,
                "timestamp": datetime.utcnow().isoformat()
            }
        )

# Usage
audit_logger = AuditLogger()

@app.post("/api/v1/auth/login")
async def login(credentials: LoginCredentials, request: Request):
    user = authenticate(credentials.email, credentials.password)
    
    audit_logger.log_authentication(
        user_id=user.id if user else None,
        email=credentials.email,
        success=user is not None,
        ip_address=request.client.host,
        user_agent=request.headers.get("user-agent")
    )
    
    if not user:
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    return {"token": create_token(user)}
```

## Rate Limiting

### API Rate Limiting

```python
from slowapi import Limiter
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

# Apply rate limits
@app.post("/api/v1/auth/login")
@limiter.limit("5/minute")  # 5 attempts per minute
async def login(request: Request):
    pass

@app.get("/api/v1/services")
@limiter.limit("100/minute")  # 100 requests per minute
async def list_services(request: Request):
    pass

# Custom rate limit key (by user ID)
def get_user_id(request: Request) -> str:
    token = request.headers.get("Authorization")
    payload = verify_token(token)
    return payload.get("sub")

@app.post("/api/v1/connections")
@limiter.limit("10/minute", key_func=get_user_id)
async def create_connection(request: Request):
    pass
```

## Dependency Security

### Vulnerability Scanning

```bash
# Scan Python dependencies
pip install safety
safety check

# Scan with pip-audit
pip install pip-audit
pip-audit

# Scan JavaScript dependencies
npm audit
npm audit fix

# Use Snyk
npm install -g snyk
snyk test
```

### Dependency Pinning

```toml
# pyproject.toml - pin exact versions
[project]
dependencies = [
    "fastapi==0.104.1",
    "uvicorn==0.24.0",
    "sqlalchemy==2.0.23",
    "pydantic==2.5.0",
]

# package.json - pin exact versions
{
  "dependencies": {
    "react": "18.2.0",
    "react-dom": "18.2.0"
  }
}
```

## Security Testing

### Automated Security Tests

```python
# tests/security/test_authentication.py
import pytest
from fastapi.testclient import TestClient

class TestAuthenticationSecurity:
    """Security tests for authentication."""
    
    def test_sql_injection_in_login(self, client: TestClient):
        """Test SQL injection prevention in login."""
        response = client.post("/api/v1/auth/login", json={
            "email": "admin' OR '1'='1",
            "password": "password"
        })
        assert response.status_code == 401
    
    def test_rate_limiting(self, client: TestClient):
        """Test rate limiting on login endpoint."""
        for i in range(6):
            response = client.post("/api/v1/auth/login", json={
                "email": "<EMAIL>",
                "password": "wrong"
            })
        
        # 6th request should be rate limited
        assert response.status_code == 429
    
    def test_password_complexity(self, client: TestClient):
        """Test password complexity requirements."""
        weak_passwords = [
            "short",
            "alllowercase",
            "ALLUPPERCASE",
            "NoNumbers!",
            "NoSpecial123"
        ]
        
        for password in weak_passwords:
            response = client.post("/api/v1/users", json={
                "email": "<EMAIL>",
                "password": password
            })
            assert response.status_code == 400
    
    def test_xss_prevention(self, client: TestClient):
        """Test XSS prevention in user input."""
        response = client.post("/api/v1/services", json={
            "name": "<script>alert('XSS')</script>",
            "type": "web"
        })
        
        service = response.json()
        # Script tags should be escaped
        assert "<script>" not in service["name"]
```

## Security Checklist

### Pre-Deployment

- [ ] All secrets in environment variables
- [ ] HTTPS enforced
- [ ] Security headers configured
- [ ] Rate limiting enabled
- [ ] Input validation implemented
- [ ] SQL injection prevention verified
- [ ] XSS prevention verified
- [ ] CSRF protection enabled
- [ ] Authentication required for all endpoints
- [ ] Authorization checks implemented
- [ ] Audit logging enabled
- [ ] Dependencies scanned for vulnerabilities
- [ ] Security tests passing
- [ ] Penetration testing completed
- [ ] Security review conducted

### Regular Maintenance

- [ ] Rotate secrets every 90 days
- [ ] Update dependencies monthly
- [ ] Review audit logs weekly
- [ ] Scan for vulnerabilities weekly
- [ ] Review access permissions monthly
- [ ] Update security policies quarterly
- [ ] Conduct security training annually

## Incident Response

### Security Incident Procedure

1. **Detect**: Monitor for security events
2. **Contain**: Isolate affected systems
3. **Investigate**: Determine scope and impact
4. **Eradicate**: Remove threat
5. **Recover**: Restore normal operations
6. **Learn**: Document and improve

### Breach Notification

```python
def handle_security_breach(incident_details: dict):
    """Handle security breach."""
    # Log incident
    logger.critical("Security breach detected", extra=incident_details)
    
    # Notify security team
    notify_security_team(incident_details)
    
    # Revoke compromised credentials
    revoke_affected_credentials(incident_details)
    
    # Notify affected users
    notify_affected_users(incident_details)
    
    # Document incident
    create_incident_report(incident_details)
```

## Resources

### Security Tools

- **OWASP ZAP**: Web application security scanner
- **Bandit**: Python security linter
- **Safety**: Python dependency vulnerability scanner
- **npm audit**: JavaScript dependency scanner
- **Snyk**: Comprehensive security platform

### Security Standards

- **OWASP Top 10**: Web application security risks
- **CWE Top 25**: Most dangerous software weaknesses
- **NIST Cybersecurity Framework**: Security best practices
- **ISO 27001**: Information security management

## Related Documentation

- [Development Setup](./01-setup.md) - Environment setup
- [Coding Standards](./02-coding-standards.md) - Code style
- [Testing Guide](./03-testing.md) - Testing practices
- [Authentication System](../06-authentication/01-overview.md) - Authentication details
