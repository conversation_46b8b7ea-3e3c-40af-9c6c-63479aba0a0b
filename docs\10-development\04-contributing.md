---
title: "Contributing Guide"
section: "Development"
order: 4
tags: ["development", "contributing", "workflow", "collaboration"]
last_updated: "2025-11-08"
---

# Contributing Guide

Thank you for your interest in contributing to the VM Network Gateway & Access Control Platform! This guide will help you understand our development workflow and how to submit contributions.

## Getting Started

### Prerequisites

Before contributing, ensure you have:

1. Read the [Development Setup](./01-setup.md) guide
2. Set up your development environment
3. Familiarized yourself with the [Coding Standards](./02-coding-standards.md)
4. Reviewed the [Testing Guide](./03-testing.md)

### Finding Work

**Good First Issues:**
- Look for issues labeled `good-first-issue` on GitHub
- These are beginner-friendly tasks with clear requirements
- Great for getting familiar with the codebase

**Help Wanted:**
- Issues labeled `help-wanted` need community contributions
- May require more experience with the codebase
- Often include detailed specifications

**Feature Requests:**
- Check the roadmap for planned features
- Discuss new features in GitHub Discussions before implementing
- Ensure alignment with project goals

## Development Workflow

### 1. <PERSON> and <PERSON><PERSON>

```bash
# Fork the repository on GitHub
# Then clone your fork
git clone https://github.com/YOUR_USERNAME/vm-gateway.git
cd vm-gateway

# Add upstream remote
git remote add upstream https://github.com/your-org/vm-gateway.git

# Verify remotes
git remote -v
```

### 2. Create a Branch

```bash
# Update your main branch
git checkout main
git pull upstream main

# Create a feature branch
git checkout -b feature/your-feature-name

# Or for bug fixes
git checkout -b fix/issue-description
```

**Branch Naming Conventions:**
- `feature/` - New features
- `fix/` - Bug fixes
- `docs/` - Documentation changes
- `refactor/` - Code refactoring
- `test/` - Test additions or fixes
- `chore/` - Maintenance tasks

### 3. Make Changes

```bash
# Make your changes
# Follow coding standards
# Write tests for new functionality
# Update documentation as needed

# Run linters
black src/ tests/
ruff check --fix src/ tests/

# Run tests
pytest
npm test

# Check coverage
pytest --cov=src
```

### 4. Commit Changes

```bash
# Stage changes
git add .

# Commit with descriptive message
git commit -m "feat(api): add webhook support for service events

Implement webhook subscription API endpoints allowing users to
receive real-time notifications for service status changes.

- Add POST /api/v1/webhooks endpoint
- Add webhook delivery queue with retry logic
- Add HMAC signature verification

Closes #123"
```

**Commit Message Format:**

```
<type>(<scope>): <subject>

<body>

<footer>
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Formatting
- `refactor`: Code restructuring
- `perf`: Performance improvement
- `test`: Tests
- `chore`: Maintenance

### 5. Push Changes

```bash
# Push to your fork
git push origin feature/your-feature-name
```

### 6. Create Pull Request

1. Go to your fork on GitHub
2. Click "New Pull Request"
3. Select your branch
4. Fill out the PR template
5. Link related issues
6. Request reviews

**PR Title Format:**

```
feat(api): add webhook support for service events
```

**PR Description Template:**

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Related Issues
Closes #123

## Changes Made
- Added webhook subscription endpoints
- Implemented delivery queue
- Added signature verification

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed

## Screenshots (if applicable)
[Add screenshots here]

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Comments added for complex code
- [ ] Documentation updated
- [ ] Tests pass locally
- [ ] No new warnings generated
```

### 7. Code Review

**As an Author:**
- Respond to feedback promptly
- Make requested changes
- Push updates to the same branch
- Mark conversations as resolved
- Be open to suggestions

**Review Process:**
- At least one approval required
- All CI checks must pass
- No unresolved conversations
- Up-to-date with main branch

### 8. Merge

Once approved:
- Squash and merge (preferred)
- Rebase and merge (for clean history)
- Merge commit (for feature branches)

## Code Review Guidelines

### For Reviewers

**What to Look For:**
1. **Correctness**: Does the code work as intended?
2. **Tests**: Are there adequate tests?
3. **Style**: Does it follow coding standards?
4. **Performance**: Are there performance concerns?
5. **Security**: Are there security vulnerabilities?
6. **Documentation**: Is documentation updated?

**How to Review:**
1. **Read the Description**: Understand the context
2. **Check Tests**: Run tests locally if needed
3. **Review Code**: Look at the diff carefully
4. **Test Manually**: Try the feature if applicable
5. **Provide Feedback**: Be constructive and specific
6. **Approve or Request Changes**: Make a decision

**Feedback Guidelines:**
- Be respectful and constructive
- Explain the "why" behind suggestions
- Distinguish between required changes and suggestions
- Praise good work
- Use code suggestions feature

**Example Comments:**

```
Good:
"Consider using a context manager here to ensure the file is properly closed. 
This would prevent resource leaks if an exception occurs."

Bad:
"This is wrong."
```

### For Authors

**Responding to Feedback:**
- Thank reviewers for their time
- Ask questions if feedback is unclear
- Explain your reasoning if you disagree
- Make changes promptly
- Update the PR description if scope changes

**Example Responses:**

```
"Good catch! I've updated the code to use a context manager."

"I considered that approach, but chose this one because [reason]. 
What do you think?"

"Could you clarify what you mean by [feedback]?"
```

## Testing Requirements

### Before Submitting PR

- [ ] All existing tests pass
- [ ] New tests added for new functionality
- [ ] Code coverage maintained or improved
- [ ] Manual testing completed
- [ ] No linting errors

### Test Coverage

- Minimum 80% overall coverage
- Minimum 90% for new code
- 100% for critical security code

## Documentation Requirements

### Code Documentation

- Docstrings for all public functions/classes
- Comments for complex logic
- Type hints for Python code
- JSDoc for JavaScript/TypeScript

### User Documentation

- Update relevant documentation files
- Add examples for new features
- Update API documentation
- Add migration guides for breaking changes

## Release Process

### Version Numbering

Format: `[PHASE].[MAJOR].[MINOR]-[BUILD]`

- **PHASE**: a (alpha), b (beta), c (RC), r (release)
- **MAJOR**: Breaking changes
- **MINOR**: New features
- **BUILD**: Incremental build number

### Creating a Release

1. Update version in `pyproject.toml`
2. Update CHANGELOG.md
3. Create release branch
4. Run full test suite
5. Create GitHub release
6. Tag with version number
7. Deploy to staging
8. Deploy to production

## Communication

### Channels

- **GitHub Issues**: Bug reports, feature requests
- **GitHub Discussions**: General questions, ideas
- **Slack**: Real-time communication
- **Email**: <EMAIL>

### Asking Questions

**Before Asking:**
1. Search existing issues/discussions
2. Check documentation
3. Review FAQ

**When Asking:**
- Provide context
- Include error messages
- Share relevant code
- Describe what you've tried

## Community Guidelines

### Code of Conduct

We are committed to providing a welcoming and inclusive environment. All contributors must:

- Be respectful and professional
- Welcome newcomers
- Accept constructive criticism
- Focus on what's best for the community
- Show empathy towards others

### Unacceptable Behavior

- Harassment or discrimination
- Trolling or insulting comments
- Personal or political attacks
- Publishing others' private information
- Other unprofessional conduct

### Reporting Issues

Report <NAME_EMAIL>

## Recognition

### Contributors

All contributors are recognized in:
- CONTRIBUTORS.md file
- Release notes
- Project website

### Maintainers

Active contributors may be invited to become maintainers with:
- Commit access
- Review privileges
- Decision-making authority

## License

By contributing, you agree that your contributions will be licensed under the MIT License.

## Getting Help

If you need help:

1. Check the [Development Setup](./01-setup.md)
2. Review the [Debugging Guide](./05-debugging.md)
3. Ask in GitHub Discussions
4. Join our Slack channel
5. Email <EMAIL>

## Related Documentation

- [Development Setup](./01-setup.md) - Environment setup
- [Coding Standards](./02-coding-standards.md) - Code style
- [Testing Guide](./03-testing.md) - Testing practices
- [Debugging Guide](./05-debugging.md) - Debugging techniques
