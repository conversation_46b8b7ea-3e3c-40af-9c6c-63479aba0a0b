---
title: "Metrics Collection"
section: "Agent"
order: 4
tags: ["agent", "metrics", "monitoring", "prometheus"]
last_updated: "2025-11-08"
---

# Metrics Collection

## Introduction

The Metrics Collection system is responsible for gathering comprehensive performance and health data from both the VM and individual services. This data powers the monitoring dashboards, alerting system, capacity planning, and troubleshooting capabilities of the VM Gateway platform.

The collector operates continuously with minimal overhead, gathering system-wide metrics, per-service metrics, and custom application metrics. All metrics are stored locally for offline operation and transmitted to the controller in efficient batches.

## Metrics Architecture

### Overview

```
┌─────────────────────────────────────────────────────────────┐
│              Metrics Collection System                      │
│                                                             │
│  ┌───────────────────────────────────────────────────────┐ │
│  │        System Metrics Collector                       │ │
│  │  - CPU usage (overall and per-core)                   │ │
│  │  - Memory usage (RAM, swap, cache)                    │ │
│  │  - Disk I/O and usage                                 │ │
│  │  - Network I/O and connections                        │ │
│  │  - System load and processes                          │ │
│  └───────────────────┬───────────────────────────────────┘ │
│                      │                                       │
│  ┌───────────────────▼───────────────────────────────────┐ │
│  │        Service Metrics Collector                      │ │
│  │  - Per-process CPU and memory                         │ │
│  │  - Network I/O per service                            │ │
│  │  - Disk I/O per service                               │ │
│  │  - File descriptors and threads                       │ │
│  │  - Service-specific metrics                           │ │
│  └───────────────────┬───────────────────────────────────┘ │
│                      │                                       │
│  ┌───────────────────▼───────────────────────────────────┐ │
│  │        Health Check Engine                            │ │
│  │  - Connectivity checks                                │ │
│  │  - Response time measurement                          │ │
│  │  - Protocol-specific health checks                    │ │
│  │  - Custom health check scripts                        │ │
│  └───────────────────┬───────────────────────────────────┘ │
│                      │                                       │
│  ┌───────────────────▼───────────────────────────────────┐ │
│  │        Metrics Aggregator                             │ │
│  │  - Combine metrics from all sources                   │ │
│  │  - Calculate derived metrics                          │ │
│  │  - Apply retention policies                           │ │
│  │  - Format for storage and transmission                │ │
│  └───────────────────┬───────────────────────────────────┘ │
│                      │                                       │
│         ┌────────────┼────────────┐                         │
│         │            │            │                          │
│         ▼            ▼            ▼                          │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐                    │
│  │  Local   │ │Prometheus│ │Controller│                    │
│  │ Storage  │ │ Endpoint │ │  Batch   │                    │
│  │ (SQLite) │ │  (HTTP)  │ │  Upload  │                    │
│  └──────────┘ └──────────┘ └──────────┘                    │
└─────────────────────────────────────────────────────────────┘
```

## System Metrics

### CPU Metrics

The collector gathers comprehensive CPU utilization data:


**Overall CPU Usage:**
- Total CPU percentage across all cores
- User time, system time, idle time
- I/O wait time
- Steal time (for virtualized environments)
- Guest time (for hypervisors)

**Per-Core Metrics:**
- Individual core utilization
- Core frequency (current and max)
- Core temperature (if available)
- Core throttling events

**Process-Level CPU:**
- Top processes by CPU usage
- CPU time per process
- Context switches per process

### Implementation

```python
import psutil
from typing import Dict, Any
from dataclasses import dataclass, asdict
from datetime import datetime

@dataclass
class CPUMetrics:
    timestamp: datetime
    total_percent: float
    user_percent: float
    system_percent: float
    idle_percent: float
    iowait_percent: float
    per_core_percent: list
    load_average_1m: float
    load_average_5m: float
    load_average_15m: float
    context_switches: int
    interrupts: int

class SystemMetricsCollector:
    def __init__(self):
        self.last_cpu_times = None
    
    def collect_cpu_metrics(self) -> CPUMetrics:
        """
        Collect comprehensive CPU metrics.
        """
        # Overall CPU percentage
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Detailed CPU times
        cpu_times_percent = psutil.cpu_times_percent(interval=0)
        
        # Per-core utilization
        per_core = psutil.cpu_percent(interval=0, percpu=True)
        
        # Load average
        load_avg = psutil.getloadavg()
        
        # CPU stats
        cpu_stats = psutil.cpu_stats()
        
        return CPUMetrics(
            timestamp=datetime.now(),
            total_percent=cpu_percent,
            user_percent=cpu_times_percent.user,
            system_percent=cpu_times_percent.system,
            idle_percent=cpu_times_percent.idle,
            iowait_percent=getattr(cpu_times_percent, 'iowait', 0),
            per_core_percent=per_core,
            load_average_1m=load_avg[0],
            load_average_5m=load_avg[1],
            load_average_15m=load_avg[2],
            context_switches=cpu_stats.ctx_switches,
            interrupts=cpu_stats.interrupts
        )
```

### Memory Metrics

**RAM Usage:**
- Total memory
- Available memory
- Used memory
- Free memory
- Cached memory
- Buffers
- Shared memory
- Memory percentage

**Swap Usage:**
- Total swap
- Used swap
- Free swap
- Swap in/out rates

**Memory by Process:**
- Top processes by memory usage
- RSS (Resident Set Size) per process
- VMS (Virtual Memory Size) per process
- Shared memory per process

### Implementation

```python
@dataclass
class MemoryMetrics:
    timestamp: datetime
    total_bytes: int
    available_bytes: int
    used_bytes: int
    free_bytes: int
    cached_bytes: int
    buffers_bytes: int
    percent_used: float
    swap_total_bytes: int
    swap_used_bytes: int
    swap_free_bytes: int
    swap_percent_used: float

def collect_memory_metrics() -> MemoryMetrics:
    """
    Collect memory usage metrics.
    """
    mem = psutil.virtual_memory()
    swap = psutil.swap_memory()
    
    return MemoryMetrics(
        timestamp=datetime.now(),
        total_bytes=mem.total,
        available_bytes=mem.available,
        used_bytes=mem.used,
        free_bytes=mem.free,
        cached_bytes=getattr(mem, 'cached', 0),
        buffers_bytes=getattr(mem, 'buffers', 0),
        percent_used=mem.percent,
        swap_total_bytes=swap.total,
        swap_used_bytes=swap.used,
        swap_free_bytes=swap.free,
        swap_percent_used=swap.percent
    )
```

### Disk Metrics

**Disk Usage:**
- Total space per mount point
- Used space per mount point
- Free space per mount point
- Percentage used per mount point
- Inode usage (Linux)

**Disk I/O:**
- Read bytes per second
- Write bytes per second
- Read operations per second
- Write operations per second
- I/O wait time
- Queue depth
- Busy time percentage

**Per-Disk Metrics:**
- Individual disk statistics
- SMART data (if available)
- Disk temperature
- Error counts

### Implementation

```python
@dataclass
class DiskMetrics:
    timestamp: datetime
    partitions: list
    io_counters: Dict[str, Any]

def collect_disk_metrics() -> DiskMetrics:
    """
    Collect disk usage and I/O metrics.
    """
    # Disk usage per partition
    partitions = []
    for partition in psutil.disk_partitions():
        try:
            usage = psutil.disk_usage(partition.mountpoint)
            partitions.append({
                'device': partition.device,
                'mountpoint': partition.mountpoint,
                'fstype': partition.fstype,
                'total_bytes': usage.total,
                'used_bytes': usage.used,
                'free_bytes': usage.free,
                'percent_used': usage.percent
            })
        except PermissionError:
            continue
    
    # Disk I/O counters
    io_counters = {}
    disk_io = psutil.disk_io_counters(perdisk=True)
    for disk, counters in disk_io.items():
        io_counters[disk] = {
            'read_bytes': counters.read_bytes,
            'write_bytes': counters.write_bytes,
            'read_count': counters.read_count,
            'write_count': counters.write_count,
            'read_time_ms': counters.read_time,
            'write_time_ms': counters.write_time
        }
    
    return DiskMetrics(
        timestamp=datetime.now(),
        partitions=partitions,
        io_counters=io_counters
    )
```

### Network Metrics

**Network I/O:**
- Bytes sent per second
- Bytes received per second
- Packets sent per second
- Packets received per second
- Errors in/out
- Drops in/out

**Per-Interface Metrics:**
- Individual interface statistics
- Interface speed and duplex
- MTU size
- Interface status (up/down)

**Connection Statistics:**
- Total connections
- Connections by state (ESTABLISHED, LISTEN, etc.)
- Connections by protocol (TCP, UDP)
- Top connections by bandwidth

### Implementation

```python
@dataclass
class NetworkMetrics:
    timestamp: datetime
    interfaces: Dict[str, Any]
    connections_by_state: Dict[str, int]
    total_connections: int

def collect_network_metrics() -> NetworkMetrics:
    """
    Collect network I/O and connection metrics.
    """
    # Per-interface I/O
    interfaces = {}
    net_io = psutil.net_io_counters(pernic=True)
    for interface, counters in net_io.items():
        interfaces[interface] = {
            'bytes_sent': counters.bytes_sent,
            'bytes_recv': counters.bytes_recv,
            'packets_sent': counters.packets_sent,
            'packets_recv': counters.packets_recv,
            'errin': counters.errin,
            'errout': counters.errout,
            'dropin': counters.dropin,
            'dropout': counters.dropout
        }
    
    # Connection statistics
    connections = psutil.net_connections()
    connections_by_state = {}
    for conn in connections:
        state = conn.status
        connections_by_state[state] = connections_by_state.get(state, 0) + 1
    
    return NetworkMetrics(
        timestamp=datetime.now(),
        interfaces=interfaces,
        connections_by_state=connections_by_state,
        total_connections=len(connections)
    )
```

## Service Metrics

### Per-Service Resource Usage

For each discovered service, the collector gathers:

**CPU Metrics:**
- CPU percentage
- CPU time (user + system)
- Number of threads
- Context switches

**Memory Metrics:**
- RSS (Resident Set Size)
- VMS (Virtual Memory Size)
- Shared memory
- Memory percentage
- Page faults

**I/O Metrics:**
- Read bytes
- Write bytes
- Read operations
- Write operations

**File Descriptors:**
- Number of open files
- Number of open sockets
- File descriptor limit

### Implementation

```python
@dataclass
class ServiceMetrics:
    timestamp: datetime
    service_id: str
    pid: int
    cpu_percent: float
    cpu_time_user: float
    cpu_time_system: float
    memory_rss_bytes: int
    memory_vms_bytes: int
    memory_percent: float
    num_threads: int
    num_fds: int
    io_read_bytes: int
    io_write_bytes: int
    io_read_count: int
    io_write_count: int
    connections_count: int

class ServiceMetricsCollector:
    def collect_service_metrics(self, pid: int, service_id: str) -> ServiceMetrics:
        """
        Collect metrics for a specific service process.
        """
        try:
            proc = psutil.Process(pid)
            
            # CPU metrics
            cpu_percent = proc.cpu_percent(interval=0.1)
            cpu_times = proc.cpu_times()
            
            # Memory metrics
            mem_info = proc.memory_info()
            mem_percent = proc.memory_percent()
            
            # I/O metrics
            try:
                io_counters = proc.io_counters()
                io_read_bytes = io_counters.read_bytes
                io_write_bytes = io_counters.write_bytes
                io_read_count = io_counters.read_count
                io_write_count = io_counters.write_count
            except (AttributeError, PermissionError):
                io_read_bytes = io_write_bytes = 0
                io_read_count = io_write_count = 0
            
            # File descriptors
            try:
                num_fds = proc.num_fds()
            except (AttributeError, PermissionError):
                num_fds = 0
            
            # Connections
            try:
                connections = proc.connections()
                connections_count = len(connections)
            except (PermissionError, psutil.AccessDenied):
                connections_count = 0
            
            return ServiceMetrics(
                timestamp=datetime.now(),
                service_id=service_id,
                pid=pid,
                cpu_percent=cpu_percent,
                cpu_time_user=cpu_times.user,
                cpu_time_system=cpu_times.system,
                memory_rss_bytes=mem_info.rss,
                memory_vms_bytes=mem_info.vms,
                memory_percent=mem_percent,
                num_threads=proc.num_threads(),
                num_fds=num_fds,
                io_read_bytes=io_read_bytes,
                io_write_bytes=io_write_bytes,
                io_read_count=io_read_count,
                io_write_count=io_write_count,
                connections_count=connections_count
            )
        except psutil.NoSuchProcess:
            return None
```

### Service-Specific Metrics

For certain service types, additional metrics are collected:

**Web Servers (Nginx, Apache):**
- Request rate
- Response time
- Active connections
- Status code distribution
- Upstream response times

**Databases (PostgreSQL, MySQL, MongoDB):**
- Query rate
- Query latency
- Active connections
- Cache hit rate
- Replication lag
- Table/collection sizes

**Caches (Redis, Memcached):**
- Hit rate
- Miss rate
- Eviction rate
- Memory usage
- Key count
- Command rate

**Message Queues (RabbitMQ, Kafka):**
- Message rate
- Queue depth
- Consumer lag
- Publish rate
- Consume rate

### Custom Metrics Collection

```python
class CustomMetricsCollector:
    """
    Collect application-specific metrics.
    """
    
    async def collect_nginx_metrics(self, stub_status_url: str) -> Dict[str, Any]:
        """
        Collect Nginx metrics from stub_status module.
        """
        async with aiohttp.ClientSession() as session:
            async with session.get(stub_status_url) as response:
                text = await response.text()
                
                # Parse stub_status output
                metrics = {}
                for line in text.split('\n'):
                    if 'Active connections' in line:
                        metrics['active_connections'] = int(line.split(':')[1].strip())
                    elif 'server accepts handled requests' in line:
                        continue
                    elif line.strip() and line[0].isdigit():
                        parts = line.split()
                        if len(parts) >= 3:
                            metrics['accepts'] = int(parts[0])
                            metrics['handled'] = int(parts[1])
                            metrics['requests'] = int(parts[2])
                
                return metrics
    
    async def collect_postgres_metrics(self, connection_string: str) -> Dict[str, Any]:
        """
        Collect PostgreSQL metrics via SQL queries.
        """
        import asyncpg
        
        conn = await asyncpg.connect(connection_string)
        
        try:
            # Active connections
            active_conns = await conn.fetchval(
                "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'"
            )
            
            # Database size
            db_size = await conn.fetchval(
                "SELECT pg_database_size(current_database())"
            )
            
            # Cache hit rate
            cache_hit_rate = await conn.fetchval("""
                SELECT 
                    sum(blks_hit) / nullif(sum(blks_hit + blks_read), 0) * 100
                FROM pg_stat_database
                WHERE datname = current_database()
            """)
            
            return {
                'active_connections': active_conns,
                'database_size_bytes': db_size,
                'cache_hit_rate_percent': cache_hit_rate or 0
            }
        finally:
            await conn.close()
    
    async def collect_redis_metrics(self, host: str, port: int) -> Dict[str, Any]:
        """
        Collect Redis metrics via INFO command.
        """
        import aioredis
        
        redis = await aioredis.create_redis_pool(f'redis://{host}:{port}')
        
        try:
            info = await redis.info()
            
            return {
                'connected_clients': info['connected_clients'],
                'used_memory_bytes': info['used_memory'],
                'used_memory_rss_bytes': info['used_memory_rss'],
                'total_commands_processed': info['total_commands_processed'],
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'evicted_keys': info.get('evicted_keys', 0)
            }
        finally:
            redis.close()
            await redis.wait_closed()
```

## Health Checks

### Health Check Engine

The agent performs regular health checks on all services:

**Connectivity Checks:**
- TCP connection test
- UDP packet test
- ICMP ping (if applicable)

**Protocol-Specific Checks:**
- HTTP: GET request with expected status code
- HTTPS: TLS handshake validation
- Database: Connection test and simple query
- Redis: PING command
- SMTP: EHLO command

**Response Time Measurement:**
- Connection establishment time
- First byte time
- Total response time
- Percentile tracking (p50, p95, p99)

### Implementation

```python
from enum import Enum
from typing import Optional

class HealthStatus(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

@dataclass
class HealthCheckResult:
    timestamp: datetime
    service_id: str
    status: HealthStatus
    response_time_ms: Optional[float]
    error_message: Optional[str]
    consecutive_failures: int

class HealthCheckEngine:
    def __init__(self):
        self.failure_counts = {}
    
    async def check_tcp_service(
        self,
        host: str,
        port: int,
        service_id: str,
        timeout: float = 2.0
    ) -> HealthCheckResult:
        """
        Perform TCP connectivity health check.
        """
        start_time = datetime.now()
        
        try:
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection(host, port),
                timeout=timeout
            )
            
            writer.close()
            await writer.wait_closed()
            
            response_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # Reset failure count on success
            self.failure_counts[service_id] = 0
            
            return HealthCheckResult(
                timestamp=datetime.now(),
                service_id=service_id,
                status=HealthStatus.HEALTHY,
                response_time_ms=response_time,
                error_message=None,
                consecutive_failures=0
            )
        except (asyncio.TimeoutError, ConnectionRefusedError, OSError) as e:
            # Increment failure count
            self.failure_counts[service_id] = self.failure_counts.get(service_id, 0) + 1
            
            return HealthCheckResult(
                timestamp=datetime.now(),
                service_id=service_id,
                status=HealthStatus.UNHEALTHY,
                response_time_ms=None,
                error_message=str(e),
                consecutive_failures=self.failure_counts[service_id]
            )
    
    async def check_http_service(
        self,
        url: str,
        service_id: str,
        expected_status: int = 200,
        timeout: float = 5.0
    ) -> HealthCheckResult:
        """
        Perform HTTP health check.
        """
        start_time = datetime.now()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url,
                    timeout=aiohttp.ClientTimeout(total=timeout),
                    allow_redirects=False
                ) as response:
                    response_time = (datetime.now() - start_time).total_seconds() * 1000
                    
                    if response.status == expected_status:
                        self.failure_counts[service_id] = 0
                        status = HealthStatus.HEALTHY
                        error_message = None
                    else:
                        self.failure_counts[service_id] = self.failure_counts.get(service_id, 0) + 1
                        status = HealthStatus.DEGRADED
                        error_message = f"Unexpected status code: {response.status}"
                    
                    return HealthCheckResult(
                        timestamp=datetime.now(),
                        service_id=service_id,
                        status=status,
                        response_time_ms=response_time,
                        error_message=error_message,
                        consecutive_failures=self.failure_counts[service_id]
                    )
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            self.failure_counts[service_id] = self.failure_counts.get(service_id, 0) + 1
            
            return HealthCheckResult(
                timestamp=datetime.now(),
                service_id=service_id,
                status=HealthStatus.UNHEALTHY,
                response_time_ms=None,
                error_message=str(e),
                consecutive_failures=self.failure_counts[service_id]
            )
```

### Custom Health Check Scripts

Users can define custom health check scripts:

```yaml
health_checks:
  - service_id: "my-api-server"
    type: "script"
    script: "/opt/health-checks/api-check.sh"
    interval: 30s
    timeout: 10s
    expected_exit_code: 0
  
  - service_id: "database-primary"
    type: "http"
    url: "http://localhost:5432/health"
    interval: 60s
    timeout: 5s
    expected_status: 200
  
  - service_id: "redis-cache"
    type: "tcp"
    host: "127.0.0.1"
    port: 6379
    interval: 15s
    timeout: 2s
```

## Metrics Storage and Transmission

### Local Storage

Metrics are stored locally in SQLite for offline operation:

```sql
CREATE TABLE system_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME NOT NULL,
    metric_type VARCHAR(50) NOT NULL,
    metric_data JSON NOT NULL,
    uploaded BOOLEAN DEFAULT FALSE,
    INDEX idx_timestamp (timestamp),
    INDEX idx_uploaded (uploaded)
);

CREATE TABLE service_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME NOT NULL,
    service_id VARCHAR(100) NOT NULL,
    metric_data JSON NOT NULL,
    uploaded BOOLEAN DEFAULT FALSE,
    INDEX idx_service_timestamp (service_id, timestamp),
    INDEX idx_uploaded (uploaded)
);

CREATE TABLE health_checks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME NOT NULL,
    service_id VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL,
    response_time_ms FLOAT,
    error_message TEXT,
    consecutive_failures INTEGER,
    uploaded BOOLEAN DEFAULT FALSE,
    INDEX idx_service_timestamp (service_id, timestamp),
    INDEX idx_uploaded (uploaded)
);
```

### Retention Policy

```python
class MetricsRetentionManager:
    def __init__(self, db_path: str):
        self.db = sqlite3.connect(db_path)
        self.retention_days = 7  # Keep 7 days locally
    
    def cleanup_old_metrics(self):
        """
        Remove metrics older than retention period.
        """
        cutoff_date = datetime.now() - timedelta(days=self.retention_days)
        
        # Only delete uploaded metrics
        self.db.execute("""
            DELETE FROM system_metrics
            WHERE timestamp < ? AND uploaded = TRUE
        """, (cutoff_date,))
        
        self.db.execute("""
            DELETE FROM service_metrics
            WHERE timestamp < ? AND uploaded = TRUE
        """, (cutoff_date,))
        
        self.db.execute("""
            DELETE FROM health_checks
            WHERE timestamp < ? AND uploaded = TRUE
        """, (cutoff_date,))
        
        self.db.commit()
```

### Batch Transmission

Metrics are transmitted to the controller in efficient batches:

```python
class MetricsBatchUploader:
    def __init__(self, controller_url: str, batch_size: int = 1000):
        self.controller_url = controller_url
        self.batch_size = batch_size
    
    async def upload_pending_metrics(self):
        """
        Upload all pending metrics to controller.
        """
        # Fetch pending system metrics
        system_metrics = self.fetch_pending_metrics('system_metrics', self.batch_size)
        
        # Fetch pending service metrics
        service_metrics = self.fetch_pending_metrics('service_metrics', self.batch_size)
        
        # Fetch pending health checks
        health_checks = self.fetch_pending_metrics('health_checks', self.batch_size)
        
        # Prepare batch payload
        payload = {
            'agent_id': self.agent_id,
            'timestamp': datetime.now().isoformat(),
            'system_metrics': system_metrics,
            'service_metrics': service_metrics,
            'health_checks': health_checks
        }
        
        # Upload to controller
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f'{self.controller_url}/api/metrics/batch',
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        # Mark metrics as uploaded
                        self.mark_as_uploaded(system_metrics, service_metrics, health_checks)
                        return True
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            logger.error(f"Failed to upload metrics: {e}")
            return False
```

### Prometheus Endpoint

The agent exposes a Prometheus-compatible metrics endpoint:

```python
from prometheus_client import Counter, Gauge, Histogram, generate_latest

class PrometheusExporter:
    def __init__(self):
        # System metrics
        self.cpu_usage = Gauge('vm_gateway_cpu_usage_percent', 'CPU usage percentage')
        self.memory_usage = Gauge('vm_gateway_memory_usage_bytes', 'Memory usage in bytes')
        self.disk_usage = Gauge('vm_gateway_disk_usage_bytes', 'Disk usage in bytes', ['mountpoint'])
        
        # Service metrics
        self.service_cpu = Gauge('vm_gateway_service_cpu_percent', 'Service CPU usage', ['service_id'])
        self.service_memory = Gauge('vm_gateway_service_memory_bytes', 'Service memory usage', ['service_id'])
        
        # Health checks
        self.service_health = Gauge('vm_gateway_service_health', 'Service health status (1=healthy, 0=unhealthy)', ['service_id'])
        self.service_response_time = Histogram('vm_gateway_service_response_time_ms', 'Service response time', ['service_id'])
    
    def update_metrics(self, system_metrics, service_metrics, health_checks):
        """
        Update Prometheus metrics with latest data.
        """
        # Update system metrics
        self.cpu_usage.set(system_metrics.cpu_percent)
        self.memory_usage.set(system_metrics.memory_used_bytes)
        
        # Update service metrics
        for service_id, metrics in service_metrics.items():
            self.service_cpu.labels(service_id=service_id).set(metrics.cpu_percent)
            self.service_memory.labels(service_id=service_id).set(metrics.memory_rss_bytes)
        
        # Update health check metrics
        for service_id, health in health_checks.items():
            self.service_health.labels(service_id=service_id).set(
                1 if health.status == HealthStatus.HEALTHY else 0
            )
            if health.response_time_ms:
                self.service_response_time.labels(service_id=service_id).observe(health.response_time_ms)
    
    def get_metrics(self) -> bytes:
        """
        Generate Prometheus-formatted metrics.
        """
        return generate_latest()
```

## Configuration

### Metrics Collection Settings

```yaml
metrics:
  # Collection intervals
  system_metrics_interval: 30s
  service_metrics_interval: 60s
  health_check_interval: 60s
  
  # Storage
  local_storage_path: "/var/lib/vm-gateway/metrics.db"
  retention_days: 7
  max_db_size_mb: 500
  
  # Transmission
  batch_size: 1000
  upload_interval: 300s  # 5 minutes
  max_retry_attempts: 3
  retry_backoff_seconds: 60
  
  # Prometheus
  enable_prometheus_endpoint: true
  prometheus_port: 9100
  
  # Performance
  max_concurrent_health_checks: 10
  health_check_timeout: 5s
  
  # Custom metrics
  enable_custom_metrics: true
  custom_collectors:
    - type: "nginx"
      stub_status_url: "http://localhost/nginx_status"
    - type: "postgres"
      connection_string: "postgresql://monitor@localhost/postgres"
```

## Performance Optimization

### Collection Overhead

Typical resource usage for metrics collection:

- **CPU**: 0.2-0.5% average
- **Memory**: 20-40 MB
- **Disk I/O**: Minimal (batch writes)
- **Network**: 5-20 KB/s (depends on upload frequency)

### Optimization Strategies

**Adaptive Collection:**
- Reduce frequency during low activity
- Increase frequency when anomalies detected
- Skip collection for stopped services

**Efficient Storage:**
- Use JSON for flexible schema
- Compress old metrics
- Batch database writes
- Use indexes for fast queries

**Smart Transmission:**
- Compress payloads (gzip)
- Batch multiple metrics
- Use delta encoding for unchanged values
- Prioritize recent metrics

## Summary

The Metrics Collection system provides comprehensive visibility into system and service performance with minimal overhead. By gathering system-wide metrics, per-service metrics, and health check data, the platform enables effective monitoring, alerting, and capacity planning.

Key features:

- **Comprehensive**: System, service, and custom metrics
- **Low Overhead**: < 0.5% CPU, ~30 MB RAM
- **Resilient**: Local storage for offline operation
- **Efficient**: Batch transmission and compression
- **Flexible**: Prometheus endpoint and custom collectors
- **Intelligent**: Adaptive collection and health checks

The metrics collection system is essential for the monitoring and alerting capabilities of the VM Gateway platform, providing the data foundation for operational excellence.

## Next Steps

- **[Communication Protocol](05-communication-protocol.md)**: Learn how metrics are transmitted to the controller
- **[Installation](06-installation.md)**: Configure metrics collection during agent installation
- **[API Reference](07-api-reference.md)**: Explore the metrics API endpoints
