---
title: "Agent Overview"
section: "Agent"
order: 1
tags: ["agent", "overview", "architecture"]
last_updated: "2025-11-08"
---

# Agent Overview

## Introduction

The VM Gateway Agent is a lightweight, high-performance Python daemon that runs on each monitored virtual machine. It serves as the eyes and ears of the platform, continuously discovering services, collecting metrics, monitoring health, and facilitating secure connections. The agent operates autonomously with minimal resource overhead while maintaining constant communication with the controller for real-time updates and coordination.

Think of the agent as an intelligent observer that understands what's running on your VM, how it's performing, and whether it's healthy—all without requiring manual configuration or intervention.

## Purpose and Capabilities

### Core Responsibilities

The agent fulfills several critical functions:

**Service Discovery**
- Automatically scans for all listening TCP and UDP ports on the VM
- Identifies processes associated with each listening port
- Extracts comprehensive process metadata (executable path, command-line arguments, owner, parent process)
- Detects containerized services (<PERSON><PERSON>, <PERSON><PERSON>, LXC)
- Monitors for service changes in real-time (new services, stopped services, configuration changes)

**Service Classification**
- Intelligently classifies discovered services using a multi-tier approach
- Matches process names against known service patterns
- Applies port convention rules for common services
- Performs protocol detection through active probing
- Supports custom classification rules and manual overrides
- Optionally uses machine learning for unknown service types

**Metrics Collection**
- Gathers system-level metrics (CPU, memory, disk, network)
- Collects service-specific metrics (resource usage per process)
- Monitors performance indicators (response times, error rates)
- Tracks health status with configurable health checks
- Stores metrics locally for offline operation
- Batches and transmits metrics to the controller efficiently

**Connection Management**
- Facilitates secure tunneled connections from clients to services
- Validates connection authorization with the controller
- Manages active connection lifecycle
- Monitors connection health and bandwidth usage
- Handles graceful connection termination
- Supports connection multiplexing for efficiency

**Communication**
- Maintains persistent WebSocket connection to the controller
- Sends periodic heartbeats to indicate agent health
- Pushes real-time updates when services change
- Receives commands from the controller (rescan, update config, restart)
- Implements automatic reconnection with exponential backoff
- Operates in offline mode when controller is unreachable

## Architecture Overview

### High-Level Design

```
┌─────────────────────────────────────────────────────────────┐
│                    VM Gateway Agent                         │
│                                                             │
│  ┌───────────────────────────────────────────────────────┐ │
│  │              Core Agent Service                       │ │
│  │         (Python FastAPI Application)                  │ │
│  └───────────────────────────────────────────────────────┘ │
│                           │                                 │
│         ┌─────────────────┼─────────────────┐              │
│         │                 │                 │               │
│         ▼                 ▼                 ▼               │
│  ┌─────────────┐   ┌─────────────┐  ┌─────────────┐       │
│  │   Scanner   │   │ Classifier  │  │   Metrics   │       │
│  │   Module    │──→│   Engine    │  │  Collector  │       │
│  │  (psutil)   │   │             │  │(Prometheus) │       │
│  └─────────────┘   └─────────────┘  └─────────────┘       │
│         │                 │                 │               │
│         └─────────────────┼─────────────────┘               │
│                           ▼                                 │
│  ┌───────────────────────────────────────────────────────┐ │
│  │         Local Database (SQLite)                       │ │
│  │  - Service catalog cache                              │ │
│  │  - Historical metrics (7 days)                        │ │
│  │  - Configuration and state                            │ │
│  │  - Pending updates queue                              │ │
│  └───────────────────────────────────────────────────────┘ │
│                           │                                 │
│         ┌─────────────────┼─────────────────┐              │
│         │                 │                 │               │
│         ▼                 ▼                 ▼               │
│  ┌─────────────┐   ┌─────────────┐  ┌─────────────┐       │
│  │   Tunnel    │   │  API Server │  │ Controller  │       │
│  │   Manager   │   │(REST/gRPC)  │  │Communicator │       │
│  │             │   │             │  │ (WebSocket) │       │
│  └─────────────┘   └─────────────┘  └─────────────┘       │
│         │                 │                 │               │
└─────────┼─────────────────┼─────────────────┼───────────────┘
          │                 │                 │
          │                 │                 │
          ▼                 ▼                 ▼
    Client Tunnels    Local Services    Controller
```

### Component Breakdown

**Scanner Module**
- Uses `psutil` library for cross-platform system access
- Scans listening ports using native OS APIs
- Maps ports to process IDs (PIDs)
- Extracts process details and metadata
- Detects changes through periodic scanning
- Implements intelligent scan scheduling to minimize overhead

**Classifier Engine**
- Multi-tier classification system for maximum accuracy
- Tier 1: Exact process name matching
- Tier 2: Port convention matching
- Tier 3: Protocol detection and banner grabbing
- Tier 4: Deep packet inspection (optional)
- Tier 5: Manual override rules
- Caches classification results for performance

**Metrics Collector**
- Collects system-wide metrics (CPU, memory, disk, network)
- Gathers per-service metrics (resource usage by process)
- Implements Prometheus-compatible metric format
- Supports custom metric collection via plugins
- Aggregates metrics locally before transmission
- Handles metric retention and cleanup

**Tunnel Manager**
- Establishes secure tunnels for client connections
- Validates authorization tokens with controller
- Manages tunnel lifecycle (creation, monitoring, termination)
- Implements connection multiplexing
- Monitors bandwidth and connection health
- Handles graceful shutdown and cleanup

**API Server**
- Exposes REST API for local operations
- Provides gRPC interface for high-performance operations
- Handles health check endpoints
- Supports administrative commands
- Implements rate limiting and authentication
- Serves metrics endpoint for Prometheus scraping

**Controller Communicator**
- Maintains persistent WebSocket connection to controller
- Implements automatic reconnection with exponential backoff
- Sends periodic heartbeats (default: 30 seconds)
- Pushes real-time service updates
- Receives and executes controller commands
- Queues updates during offline periods

**Local Database (SQLite)**
- Stores discovered service catalog
- Caches configuration from controller
- Maintains historical metrics (default: 7 days)
- Queues pending updates during offline operation
- Provides fast local queries
- Implements automatic cleanup and compaction

## Key Features

### Lightweight and Efficient

The agent is designed to have minimal impact on the host VM:

- **Low CPU Usage**: Typically < 1% CPU utilization during normal operation
- **Small Memory Footprint**: ~50-100 MB RAM depending on service count
- **Efficient Scanning**: Intelligent scheduling reduces unnecessary scans
- **Batch Operations**: Groups updates to minimize network overhead
- **Resource Limits**: Configurable CPU and memory limits prevent runaway usage
- **Optimized Storage**: SQLite database with automatic cleanup

### Autonomous Operation

The agent operates independently when needed:

- **Offline Mode**: Continues functioning when controller is unreachable
- **Local Storage**: Caches data for later synchronization
- **Self-Monitoring**: Detects and reports its own health issues
- **Automatic Recovery**: Restarts failed components automatically
- **Graceful Degradation**: Reduces functionality rather than failing completely
- **State Persistence**: Maintains state across restarts

### Security Hardened

Security is built into every aspect of the agent:

- **mTLS Communication**: Mutual TLS authentication with controller
- **Certificate Management**: Automatic certificate rotation
- **Isolated Execution**: Runs as dedicated user with minimal permissions
- **Encrypted Storage**: Sensitive data encrypted at rest (AES-256-GCM)
- **Secure Defaults**: Security-first configuration out of the box
- **Audit Logging**: Comprehensive logging of all security-relevant events

### Cross-Platform Support

The agent runs on multiple operating systems:

- **Linux**: Full support for all major distributions (Ubuntu, Debian, RHEL, CentOS, Fedora, etc.)
- **Windows**: Native Windows service support (Windows Server 2016+, Windows 10+)
- **macOS**: Support for macOS 10.15+ (primarily for development/testing)
- **Containerized**: Can run in Docker containers for container-based VMs
- **Architecture**: Supports x86_64, ARM64, and ARM32 architectures

### Extensible Design

The agent can be extended and customized:

- **Plugin System**: Add custom scanners, classifiers, or metric collectors
- **Custom Rules**: Define service classification rules via configuration
- **Webhook Integration**: Send events to external systems
- **Script Execution**: Run custom scripts on service events
- **API Extensions**: Add custom API endpoints
- **Metric Plugins**: Collect application-specific metrics

## Operational Characteristics

### Installation and Deployment

The agent is designed for easy deployment:

- **Package Formats**: Available as DEB, RPM, MSI, and Docker image
- **Automated Installation**: One-line install script for quick deployment
- **Configuration Management**: Supports Ansible, Puppet, Chef, SaltStack
- **Container Support**: Official Docker image with health checks
- **Kubernetes**: Helm chart for Kubernetes deployments
- **Cloud Init**: Integration with cloud-init for automatic VM provisioning

### Service Management

The agent runs as a system service:

- **Linux**: systemd service with automatic restart
- **Windows**: Windows Service with recovery actions
- **Auto-Start**: Starts automatically on boot
- **Graceful Shutdown**: Cleans up resources on stop
- **Status Monitoring**: Service status visible via system tools
- **Log Management**: Integrated with system logging (journald, Event Log)

### Configuration

Agent configuration is flexible and manageable:

- **Configuration File**: YAML or TOML format for easy editing
- **Environment Variables**: Override settings via environment
- **Controller-Managed**: Receive configuration updates from controller
- **Hot Reload**: Apply configuration changes without restart (where possible)
- **Validation**: Configuration validation on startup
- **Defaults**: Sensible defaults for zero-configuration deployment

### Monitoring and Observability

The agent provides comprehensive observability:

- **Health Endpoint**: HTTP endpoint for health checks
- **Metrics Endpoint**: Prometheus-compatible metrics
- **Structured Logging**: JSON-formatted logs for easy parsing
- **Log Levels**: Configurable log verbosity (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- **Self-Diagnostics**: Built-in diagnostic commands
- **Performance Profiling**: Optional profiling for troubleshooting

## Use Cases

### Automatic Service Inventory

Organizations use the agent to maintain an accurate, real-time inventory of all services:

- Eliminate manual documentation that becomes outdated
- Discover shadow IT and unauthorized services
- Track service versions for security patching
- Identify unused services for decommissioning
- Support compliance audits with accurate service lists

### Real-Time Monitoring

Operations teams rely on the agent for comprehensive monitoring:

- Monitor service health across entire VM fleet
- Detect service failures immediately
- Track resource usage trends for capacity planning
- Identify performance bottlenecks
- Correlate service health with infrastructure metrics

### Secure Access Facilitation

The agent enables secure, controlled access to services:

- Facilitate encrypted tunnels for client connections
- Validate authorization before allowing connections
- Monitor active connections for security
- Audit all connection attempts
- Support temporary access with automatic expiration

### Change Detection

Security and operations teams use the agent to detect changes:

- Alert when new services appear
- Notify when services stop unexpectedly
- Detect version changes (potential unauthorized updates)
- Identify configuration drift
- Track service lifecycle events

## Performance Characteristics

### Resource Usage

Typical resource consumption on a VM with 50 services:

- **CPU**: 0.5-1% average, 2-3% during scans
- **Memory**: 60-80 MB resident set size
- **Disk**: 100-200 MB (includes database and logs)
- **Network**: 10-50 KB/s average (depends on metric frequency)
- **Disk I/O**: Minimal (mostly reads during scans)

### Scalability

The agent scales to handle various scenarios:

- **Services**: Efficiently handles 1-1000+ services per VM
- **Scan Frequency**: Configurable from 10 seconds to 1 hour
- **Metric Retention**: Stores 7-30 days locally (configurable)
- **Concurrent Connections**: Supports 100+ simultaneous tunnels
- **Database Size**: Typically 10-100 MB depending on service count and retention

### Latency

Response times for common operations:

- **Service Discovery Scan**: 1-5 seconds for typical VM
- **Metric Collection**: < 100ms per collection cycle
- **Health Check**: < 50ms per service
- **Connection Establishment**: 100-500ms (includes authorization)
- **Heartbeat**: < 10ms to send

## Comparison with Alternatives

### vs. Manual Service Documentation

- **Agent**: Automatic, always up-to-date, comprehensive
- **Manual**: Time-consuming, quickly outdated, often incomplete

### vs. Network Scanning Tools (nmap, etc.)

- **Agent**: Real-time, process-aware, low overhead, continuous
- **Network Scanners**: Point-in-time, network-only, higher overhead, periodic

### vs. APM Agents (New Relic, Datadog, etc.)

- **Agent**: Service discovery focus, access control integration, self-hosted
- **APM**: Application performance focus, no access control, SaaS-based

### vs. Configuration Management (Ansible, Puppet, etc.)

- **Agent**: Discovers actual state, real-time monitoring, no configuration needed
- **Config Management**: Enforces desired state, requires configuration, periodic runs

## Summary

The VM Gateway Agent is a sophisticated yet lightweight component that brings intelligence and automation to VM infrastructure management. By automatically discovering services, collecting comprehensive metrics, and facilitating secure connections, the agent eliminates manual overhead while providing unprecedented visibility and control.

Key takeaways:

- **Autonomous**: Operates independently with minimal configuration
- **Lightweight**: Minimal resource overhead (< 1% CPU, ~60 MB RAM)
- **Intelligent**: Multi-tier classification for accurate service identification
- **Secure**: mTLS communication, encrypted storage, isolated execution
- **Resilient**: Offline operation, automatic recovery, graceful degradation
- **Extensible**: Plugin system, custom rules, API extensions

The agent forms the foundation of the VM Gateway platform, enabling all higher-level features like granular access control, comprehensive monitoring, and secure remote connectivity.

## Next Steps

To learn more about the agent:

- **[Service Discovery](02-service-discovery.md)**: Deep dive into the discovery engine architecture
- **[Classification Engine](03-classification-engine.md)**: How services are intelligently classified
- **[Metrics Collection](04-metrics-collection.md)**: Comprehensive metrics gathering and storage
- **[Communication Protocol](05-communication-protocol.md)**: Agent-controller communication details
- **[Installation](06-installation.md)**: Step-by-step installation and configuration guide
- **[API Reference](07-api-reference.md)**: Complete API documentation for the agent
