---
title: "Getting Started Guide"
section: "Overview"
order: 5
tags: ["getting-started", "quickstart", "installation", "setup"]
last_updated: "2025-11-08"
---

# Getting Started Guide

## Overview

This guide will walk you through setting up your first VM Gateway deployment, from installing the controller to connecting your first VM and accessing services. By the end of this guide, you'll have a working VM Gateway installation with automatic service discovery and secure access control.

## Prerequisites

Before you begin, ensure you have:

### System Requirements

**Controller**:
- Linux server (Ubuntu 22.04 LTS or similar recommended)
- 2+ CPU cores
- 4GB+ RAM
- 20GB+ disk space
- Python 3.11 or higher
- PostgreSQL 14 or higher
- Redis 7.0 or higher

**Agent VMs**:
- Linux, Windows, or macOS
- 1+ CPU core
- 512MB+ RAM
- 1GB+ disk space
- Python 3.11 or higher
- Network connectivity to controller

**Client Workstation**:
- Linux, Windows, or macOS
- Modern web browser (Chrome, Firefox, Safari, Edge)
- For desktop client: 2GB+ RAM

### Network Requirements

- Controller accessible from agent VMs (HTTPS, typically port 443)
- Controller accessible from user workstations (HTTPS)
- Agents can communicate with controller (WebSocket, typically port 443)
- Firewall rules allowing required traffic

### Knowledge Requirements

- Basic Linux command line
- Understanding of networking concepts (ports, protocols, firewalls)
- Basic understanding of web applications
- Familiarity with Docker (optional, for containerized deployment)

## Quick Start (5 Minutes)

For the fastest path to a working system, use Docker Compose:

### 1. Clone Repository

```bash
git clone https://github.com/your-org/vm-gateway.git
cd vm-gateway
```

### 2. Configure Environment

```bash
cp .env.example .env
# Edit .env with your settings
nano .env
```

**Minimum required settings**:
```bash
# Database
DATABASE_URL=*********************************************/vmgateway

# Redis
REDIS_URL=redis://redis:6379/0

# Security
SECRET_KEY=your-secret-key-here-change-this
JWT_SECRET=your-jwt-secret-here-change-this

# Admin user
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=changeme
```

### 3. Start Services

```bash
docker-compose up -d
```

This starts:
- PostgreSQL database
- Redis cache
- VM Gateway controller
- Nginx reverse proxy

### 4. Access Web Interface

Open your browser to `http://localhost` (or your server's IP)

Login with:
- Email: `<EMAIL>`
- Password: (what you set in .env)

### 5. Install Agent on First VM

On the VM you want to monitor:

```bash
# Download agent installer
curl -O https://your-controller/downloads/install-agent.sh

# Run installer
sudo bash install-agent.sh --controller https://your-controller --token YOUR_TOKEN
```

The agent will:
- Install Python dependencies
- Configure systemd service
- Start discovering services
- Report to controller

### 6. View Discovered Services

In the web interface:
1. Navigate to "Service Catalog"
2. See automatically discovered services
3. Click any service to view details

**Congratulations!** You now have a working VM Gateway installation.

## Detailed Installation

For production deployments or custom configurations, follow these detailed steps:

### Step 1: Install Controller

#### Option A: Docker Deployment (Recommended)

**1.1 Install Docker and Docker Compose**

```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo apt-get install docker-compose-plugin

# Verify installation
docker --version
docker compose version
```

**1.2 Prepare Configuration**

```bash
# Create directory structure
mkdir -p /opt/vm-gateway
cd /opt/vm-gateway

# Download docker-compose.yml
curl -O https://raw.githubusercontent.com/your-org/vm-gateway/main/docker-compose.yml

# Create environment file
cp .env.example .env
```

**1.3 Configure Environment Variables**

Edit `/opt/vm-gateway/.env`:

```bash
# Application
APP_NAME=VM Gateway
APP_ENV=production
DEBUG=false
LOG_LEVEL=INFO

# Database
DATABASE_URL=*********************************************************/vmgateway
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=10

# Redis
REDIS_URL=redis://redis:6379/0
REDIS_MAX_CONNECTIONS=50

# Security
SECRET_KEY=GENERATE_RANDOM_STRING_HERE
JWT_SECRET=GENERATE_RANDOM_STRING_HERE
JWT_EXPIRATION=28800  # 8 hours in seconds
SESSION_TIMEOUT=28800

# Admin User (created on first run)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=CHANGE_THIS_PASSWORD
ADMIN_NAME=Administrator

# Controller
CONTROLLER_HOST=0.0.0.0
CONTROLLER_PORT=8000
CONTROLLER_WORKERS=4

# TLS (if using)
TLS_ENABLED=true
TLS_CERT_PATH=/etc/ssl/certs/controller.crt
TLS_KEY_PATH=/etc/ssl/private/controller.key

# Features
FEATURE_FLAGS_ENABLED=true
METRICS_ENABLED=true
AUDIT_LOGGING_ENABLED=true
```

**Generate secure secrets**:
```bash
# Generate SECRET_KEY
python3 -c "import secrets; print(secrets.token_urlsafe(32))"

# Generate JWT_SECRET
python3 -c "import secrets; print(secrets.token_urlsafe(32))"
```

**1.4 Start Services**

```bash
# Start all services
docker compose up -d

# Check status
docker compose ps

# View logs
docker compose logs -f controller
```

**1.5 Initialize Database**

```bash
# Run database migrations
docker compose exec controller python -m alembic upgrade head

# Create admin user (if not auto-created)
docker compose exec controller python -m vm_gateway.cli create-admin \
  --email <EMAIL> \
  --password YOUR_PASSWORD \
  --name Administrator
```

#### Option B: Manual Installation

**1.1 Install System Dependencies**

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y \
  python3.11 \
  python3.11-venv \
  python3-pip \
  postgresql-14 \
  redis-server \
  nginx \
  git

# Verify Python version
python3.11 --version
```

**1.2 Install PostgreSQL**

```bash
# Start PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql << EOF
CREATE DATABASE vmgateway;
CREATE USER vmgateway WITH PASSWORD 'your_password_here';
GRANT ALL PRIVILEGES ON DATABASE vmgateway TO vmgateway;
\q
EOF
```

**1.3 Install Redis**

```bash
# Start Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Test Redis
redis-cli ping  # Should return PONG
```

**1.4 Install VM Gateway**

```bash
# Create application user
sudo useradd -r -s /bin/bash -d /opt/vm-gateway vmgateway

# Create directory
sudo mkdir -p /opt/vm-gateway
sudo chown vmgateway:vmgateway /opt/vm-gateway

# Switch to application user
sudo -u vmgateway -i

# Clone repository
cd /opt/vm-gateway
git clone https://github.com/your-org/vm-gateway.git .

# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate

# Install dependencies
pip install --upgrade pip
pip install -e .

# Or install from requirements
pip install -r requirements.txt
```

**1.5 Configure Application**

```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

Use the same environment variables as Docker deployment.

**1.6 Initialize Database**

```bash
# Run migrations
python -m alembic upgrade head

# Create admin user
python -m vm_gateway.cli create-admin \
  --email <EMAIL> \
  --password YOUR_PASSWORD \
  --name Administrator
```

**1.7 Create Systemd Service**

Create `/etc/systemd/system/vm-gateway-controller.service`:

```ini
[Unit]
Description=VM Gateway Controller
After=network.target postgresql.service redis.service
Requires=postgresql.service redis.service

[Service]
Type=notify
User=vmgateway
Group=vmgateway
WorkingDirectory=/opt/vm-gateway
Environment="PATH=/opt/vm-gateway/venv/bin"
EnvironmentFile=/opt/vm-gateway/.env
ExecStart=/opt/vm-gateway/venv/bin/uvicorn \
  vm_gateway.controller.main:app \
  --host 0.0.0.0 \
  --port 8000 \
  --workers 4 \
  --loop uvloop
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

**Start service**:
```bash
sudo systemctl daemon-reload
sudo systemctl start vm-gateway-controller
sudo systemctl enable vm-gateway-controller
sudo systemctl status vm-gateway-controller
```

**1.8 Configure Nginx**

Create `/etc/nginx/sites-available/vm-gateway`:

```nginx
upstream vm_gateway {
    server 127.0.0.1:8000;
}

server {
    listen 80;
    server_name your-domain.com;

    # Redirect to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # TLS configuration
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;

    # Proxy settings
    location / {
        proxy_pass http://vm_gateway;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket support
    location /ws {
        proxy_pass http://vm_gateway;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # Static files
    location /static {
        alias /opt/vm-gateway/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

**Enable site**:
```bash
sudo ln -s /etc/nginx/sites-available/vm-gateway /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Step 2: Install Agent on VMs

#### Linux Installation

**2.1 Download Agent**

```bash
# Download installer
curl -O https://your-controller/downloads/install-agent.sh

# Or download agent package
curl -O https://your-controller/downloads/vm-gateway-agent-latest.tar.gz
```

**2.2 Run Installer**

```bash
# Interactive installation
sudo bash install-agent.sh

# Or automated installation
sudo bash install-agent.sh \
  --controller https://your-controller \
  --token YOUR_REGISTRATION_TOKEN \
  --name "Production Web Server" \
  --tags "production,web,frontend"
```

**2.3 Manual Installation**

```bash
# Create user
sudo useradd -r -s /bin/bash -d /opt/vm-gateway-agent vmagent

# Extract agent
sudo mkdir -p /opt/vm-gateway-agent
sudo tar -xzf vm-gateway-agent-latest.tar.gz -C /opt/vm-gateway-agent
sudo chown -R vmagent:vmagent /opt/vm-gateway-agent

# Install dependencies
cd /opt/vm-gateway-agent
sudo -u vmagent python3.11 -m venv venv
sudo -u vmagent venv/bin/pip install -r requirements.txt

# Configure agent
sudo -u vmagent cp config.example.yaml config.yaml
sudo -u vmagent nano config.yaml
```

**Configuration** (`config.yaml`):
```yaml
controller:
  url: https://your-controller
  token: YOUR_REGISTRATION_TOKEN
  verify_ssl: true

agent:
  name: Production Web Server
  tags:
    - production
    - web
    - frontend
  scan_interval: 60  # seconds
  metrics_interval: 30  # seconds

logging:
  level: INFO
  file: /var/log/vm-gateway-agent/agent.log
  max_size: 10485760  # 10MB
  backup_count: 5
```

**2.4 Create Systemd Service**

Create `/etc/systemd/system/vm-gateway-agent.service`:

```ini
[Unit]
Description=VM Gateway Agent
After=network.target

[Service]
Type=simple
User=vmagent
Group=vmagent
WorkingDirectory=/opt/vm-gateway-agent
Environment="PATH=/opt/vm-gateway-agent/venv/bin"
ExecStart=/opt/vm-gateway-agent/venv/bin/python -m vm_gateway.agent.main
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

**Start agent**:
```bash
sudo systemctl daemon-reload
sudo systemctl start vm-gateway-agent
sudo systemctl enable vm-gateway-agent
sudo systemctl status vm-gateway-agent
```

#### Windows Installation

**2.1 Download Agent**

Download `vm-gateway-agent-windows.msi` from controller

**2.2 Run Installer**

Double-click installer or run:
```powershell
msiexec /i vm-gateway-agent-windows.msi /qn `
  CONTROLLER_URL=https://your-controller `
  REGISTRATION_TOKEN=YOUR_TOKEN `
  AGENT_NAME="Windows Server 01"
```

**2.3 Configure Agent**

Edit `C:\Program Files\VM Gateway Agent\config.yaml`

**2.4 Start Service**

```powershell
# Start service
Start-Service VMGatewayAgent

# Set to start automatically
Set-Service VMGatewayAgent -StartupType Automatic

# Check status
Get-Service VMGatewayAgent
```

### Step 3: Configure Authentication

#### Local Authentication

Already configured with admin user. To add more users:

**Via Web Interface**:
1. Login as admin
2. Navigate to "Users" → "Add User"
3. Fill in details (name, email, password)
4. Assign roles
5. Click "Create User"

**Via CLI**:
```bash
docker compose exec controller python -m vm_gateway.cli create-user \
  --email <EMAIL> \
  --password PASSWORD \
  --name "John Doe" \
  --roles developer,viewer
```

#### Enable MFA (Recommended)

**For users**:
1. Login to web interface
2. Go to "Settings" → "Security"
3. Click "Enable Two-Factor Authentication"
4. Scan QR code with authenticator app
5. Enter verification code
6. Save backup codes

**Enforce MFA for all users** (admin):
1. Go to "Settings" → "Security Policy"
2. Enable "Require MFA for all users"
3. Set grace period (e.g., 7 days)
4. Save settings

#### Configure SSO (Optional)

**SAML 2.0 Example (Okta)**:

1. In Okta, create new SAML application
2. Set ACS URL: `https://your-controller/auth/saml/acs`
3. Set Entity ID: `https://your-controller`
4. Download metadata XML

5. In VM Gateway:
   - Go to "Settings" → "Authentication" → "SSO"
   - Select "SAML 2.0"
   - Upload metadata XML
   - Configure attribute mapping
   - Enable SSO

### Step 4: Configure Access Control

#### Create Roles

**Example: Developer Role**

1. Go to "Access Control" → "Roles" → "Create Role"
2. Name: "Developer"
3. Description: "Access to development and staging services"
4. Permissions:
   - View all services
   - Connect to services in development and staging
   - View metrics and logs
   - Cannot modify configurations
5. Save role

**Via API**:
```bash
curl -X POST https://your-controller/api/roles \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Developer",
    "description": "Access to development and staging services",
    "permissions": [
      {
        "resource": "service:env:development",
        "actions": ["view", "connect"]
      },
      {
        "resource": "service:env:staging",
        "actions": ["view", "connect"]
      }
    ]
  }'
```

#### Assign Roles to Users

1. Go to "Users"
2. Click user
3. Click "Edit Roles"
4. Select roles
5. Save

### Step 5: Access Services

#### Web-Based Services (HTTP/HTTPS)

1. Navigate to "Service Catalog"
2. Find your web service
3. Click "Open in Browser"
4. Service opens in new tab through secure proxy

**Direct URL**:
```
https://your-controller/proxy/SERVICE_ID/
```

#### Other Services (Databases, APIs, etc.)

**Option A: Desktop Client**

1. Download desktop client from controller
2. Install and login
3. Browse service catalog
4. Click "Connect" on desired service
5. Client establishes secure tunnel
6. Connect to `localhost:LOCAL_PORT`

**Option B: CLI Client**

```bash
# Install CLI client
pip install vm-gateway-client

# Login
vm-gateway login https://your-controller

# List services
vm-gateway services list

# Connect to service
vm-gateway connect SERVICE_ID --local-port 5432

# In another terminal, connect to localhost:5432
psql -h localhost -p 5432 -U dbuser -d dbname
```

## Common Tasks

### Add New VM

1. Generate registration token:
   - Web UI: "VMs" → "Add VM" → Copy token
   - CLI: `vm-gateway-cli generate-token`

2. Install agent on new VM (see Step 2)

3. Verify in web interface:
   - Go to "VMs"
   - New VM should appear within 1 minute
   - Services will be discovered automatically

### Grant User Access to Service

1. Go to "Service Catalog"
2. Click service
3. Go to "Access Control" tab
4. Click "Grant Access"
5. Select user or role
6. Set permissions (view, connect, configure)
7. Optional: Set time restrictions
8. Save

### Create Approval Workflow

1. Go to "Settings" → "Approval Workflows"
2. Click "Create Workflow"
3. Configure:
   - Name: "Production Database Access"
   - Trigger: Resource pattern `service:type:database AND env:production`
   - Approvers: Select roles (e.g., DBA Team, Security Team)
   - Timeout: 1 hour
   - Access duration: 2 hours
4. Save workflow

### Set Up Alerts

1. Go to "Monitoring" → "Alerts" → "Create Alert"
2. Configure:
   - Name: "High CPU Usage"
   - Metric: `cpu.usage.percent`
   - Condition: `> 80` for `5 minutes`
   - Severity: Warning
   - Notification: Email to `<EMAIL>`
3. Save alert

## Troubleshooting

### Controller Not Starting

**Check logs**:
```bash
# Docker
docker compose logs controller

# Systemd
sudo journalctl -u vm-gateway-controller -f
```

**Common issues**:
- Database connection failed: Check DATABASE_URL
- Redis connection failed: Check REDIS_URL
- Port already in use: Change CONTROLLER_PORT
- Permission denied: Check file permissions

### Agent Not Connecting

**Check agent logs**:
```bash
# Linux
sudo journalctl -u vm-gateway-agent -f

# Windows
Get-EventLog -LogName Application -Source "VM Gateway Agent"
```

**Common issues**:
- Invalid token: Generate new registration token
- Network connectivity: Check firewall rules
- TLS certificate error: Check certificate validity or disable SSL verification (not recommended for production)
- Controller URL wrong: Verify URL in config

### Services Not Discovered

**Check**:
- Agent is running: `systemctl status vm-gateway-agent`
- Agent has permissions: Agent user needs permission to read process information
- Scan interval: Wait for next scan (default 60 seconds)
- Firewall: Agent needs to access local ports

**Force rescan**:
```bash
# Via API
curl -X POST https://your-controller/api/agents/AGENT_ID/rescan \
  -H "Authorization: Bearer YOUR_TOKEN"

# Via CLI
vm-gateway-cli agent rescan AGENT_ID
```

### Cannot Access Service

**Check**:
- User has permission: Verify in "Service Catalog" → Service → "Access Control"
- Service is healthy: Check service status in catalog
- Agent is online: Verify agent status
- Network connectivity: Agent must be able to reach service

## Next Steps

Now that you have VM Gateway running:

1. **Explore the Documentation**:
   - [Architecture](../02-architecture/01-system-overview.md): Understand system design
   - [Agent Documentation](../03-agent/01-overview.md): Deep dive into agent functionality
   - [Controller Documentation](../04-controller/01-overview.md): Learn about controller features
   - [Authentication](../06-authentication/01-overview.md): Advanced authentication setup

2. **Configure Advanced Features**:
   - Set up SSO integration
   - Configure secrets management (Vault, AWS Secrets Manager)
   - Enable advanced monitoring and alerting
   - Set up high availability

3. **Customize for Your Environment**:
   - Create custom roles for your organization
   - Set up approval workflows
   - Configure compliance reporting
   - Integrate with existing tools (Slack, PagerDuty, etc.)

4. **Scale Your Deployment**:
   - Add more VMs
   - Deploy additional controller instances
   - Set up database replication
   - Configure load balancing

## Getting Help

- **Documentation**: Full documentation at `https://your-controller/docs`
- **API Reference**: Interactive API docs at `https://your-controller/api/docs`
- **Community**: Join our community forum
- **Issues**: Report bugs on GitHub
- **Support**: Contact <EMAIL>

## Summary

You've successfully set up VM Gateway! You now have:

- ✅ Controller running and accessible
- ✅ At least one agent discovering services
- ✅ User authentication configured
- ✅ Access control policies in place
- ✅ Ability to access services securely

VM Gateway will continue to automatically discover services, collect metrics, and provide secure access according to your configured policies. The system requires minimal ongoing maintenance and scales easily as you add more VMs and users.

Welcome to VM Gateway!
