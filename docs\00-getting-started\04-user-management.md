# User Management

This guide covers creating and managing user accounts, roles, and permissions in VM Gateway.

## Overview

VM Gateway provides comprehensive user management with:
- Local user accounts with strong password policies
- Role-based access control (RBAC)
- Multi-factor authentication (MFA)
- Single sign-on (SSO) integration
- Session management
- Audit logging of all user actions

## User Account Types

### Administrator Accounts

Administrators have full access to the platform:
- Manage all users and roles
- Configure system settings
- View all services and VMs
- Access all audit logs
- Manage integrations

### Standard User Accounts

Regular users have limited access based on their assigned roles:
- View assigned services
- Create port forwards to authorized services
- View their own audit logs
- Manage their own profile and MFA settings

### Service Accounts

Service accounts are for API access and automation:
- API-only access (no web interface login)
- Long-lived API tokens
- Specific permission scopes
- Used for integrations and scripts

## Creating Users

### Via Web Interface

1. Log into the controller as an administrator
2. Navigate to **Access Control** > **Users**
3. Click **Create User**
4. Fill in user details:
   - **Username**: Unique username (alphanumeric, hyphens, underscores)
   - **Email**: User's email address
   - **Full Name**: User's display name
   - **Password**: Initial password (user should change on first login)
   - **Roles**: Assign one or more roles
   - **Status**: Active or Inactive
5. Optional settings:
   - **Require Password Change**: Force password change on first login
   - **MFA Required**: Require multi-factor authentication
   - **Account Expiration**: Set expiration date for temporary accounts
6. Click **Create User**

The user will receive an email with their login credentials and instructions.

### Via API

```bash
curl -X POST https://your-controller:8443/api/v1/users \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "jdoe",
    "email": "<EMAIL>",
    "full_name": "John Doe",
    "password": "TempPassword123!",
    "roles": ["developer", "database_user"],
    "require_password_change": true,
    "mfa_required": false,
    "status": "active"
  }'
```

Response:
```json
{
  "user_id": "usr_1234567890",
  "username": "jdoe",
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "roles": ["developer", "database_user"],
  "status": "active",
  "created_at": "2025-11-09T10:30:00Z",
  "mfa_enabled": false
}
```

### Bulk User Import

Import multiple users from CSV:

1. Navigate to **Access Control** > **Users**
2. Click **Import Users**
3. Download the CSV template
4. Fill in user details:
   ```csv
   username,email,full_name,roles,mfa_required
   jdoe,<EMAIL>,John Doe,developer,false
   asmith,<EMAIL>,Alice Smith,"developer,admin",true
   bj ones,<EMAIL>,Bob Jones,operator,false
   ```
5. Upload the CSV file
6. Review the import preview
7. Click **Import**

Users will be created and receive welcome emails.

## Managing Roles

Roles define what users can access and do in the system.

### Built-in Roles

VM Gateway includes several pre-defined roles:

**Administrator:**
- Full system access
- User and role management
- System configuration
- All service access

**Operator:**
- View all services and VMs
- Create port forwards
- View metrics and logs
- No configuration changes

**Developer:**
- View development/staging services
- Create port forwards to dev services
- View service documentation
- No production access

**Database Administrator:**
- Access to database services
- View database metrics
- Create database port forwards
- Manage database secrets

**Read-Only:**
- View service catalog
- View metrics and dashboards
- No port forwarding
- No configuration changes

### Creating Custom Roles

Create roles tailored to your organization:

1. Navigate to **Access Control** > **Roles**
2. Click **Create Role**
3. Configure role details:
   - **Name**: Role name (e.g., "Production Support")
   - **Description**: What this role is for
   - **Permissions**: Select specific permissions
4. Add resource access:
   - **Services**: Which services this role can access
   - **VMs**: Which VMs this role can view
   - **Actions**: What actions are allowed (view, connect, manage)
5. Set conditions (optional):
   - **Time-based**: Only allow access during business hours
   - **IP-based**: Restrict to specific IP ranges
   - **Approval required**: Require approval for certain actions
6. Click **Create Role**

### Permission Types

**Service Permissions:**
- `service:view` - View service details
- `service:connect` - Create port forwards
- `service:manage` - Modify service configuration
- `service:delete` - Remove services

**VM Permissions:**
- `vm:view` - View VM details
- `vm:manage` - Modify VM configuration
- `vm:metrics` - View VM metrics

**User Permissions:**
- `user:view` - View user accounts
- `user:create` - Create new users
- `user:manage` - Modify user accounts
- `user:delete` - Delete user accounts

**System Permissions:**
- `system:view` - View system settings
- `system:configure` - Modify system settings
- `system:audit` - View audit logs
- `system:integrate` - Manage integrations

## Assigning Roles to Users

### Single User

1. Navigate to **Access Control** > **Users**
2. Click on a user
3. Click **Edit Roles**
4. Select roles to assign
5. Click **Save**

### Bulk Role Assignment

Assign roles to multiple users:

1. Navigate to **Access Control** > **Users**
2. Select multiple users (checkboxes)
3. Click **Bulk Actions** > **Assign Roles**
4. Select roles to add
5. Click **Apply**

### Role Inheritance

Roles can inherit permissions from other roles:

```yaml
# Example role hierarchy
roles:
  base_user:
    permissions:
      - service:view
      - vm:view
      
  developer:
    inherits: base_user
    permissions:
      - service:connect
      - service:view_metrics
      
  senior_developer:
    inherits: developer
    permissions:
      - service:manage
```

## Password Policies

Configure strong password requirements:

1. Navigate to **Settings** > **Security** > **Password Policy**
2. Configure requirements:
   - **Minimum Length**: 12 characters (recommended)
   - **Complexity**: Require uppercase, lowercase, numbers, symbols
   - **Password History**: Prevent reuse of last N passwords
   - **Expiration**: Force password change every N days
   - **Lockout**: Lock account after N failed attempts
3. Click **Save**

Example policy:
```yaml
password_policy:
  min_length: 12
  require_uppercase: true
  require_lowercase: true
  require_numbers: true
  require_symbols: true
  password_history: 5
  expiration_days: 90
  max_failed_attempts: 5
  lockout_duration_minutes: 30
```

## Multi-Factor Authentication (MFA)

### Enabling MFA for Users

**Require MFA for all users:**
1. Navigate to **Settings** > **Security** > **MFA Settings**
2. Enable **Require MFA for all users**
3. Set grace period for enrollment (e.g., 7 days)
4. Click **Save**

**Require MFA for specific roles:**
1. Navigate to **Access Control** > **Roles**
2. Edit a role (e.g., "Administrator")
3. Enable **Require MFA**
4. Click **Save**

**Require MFA for individual users:**
1. Navigate to **Access Control** > **Users**
2. Click on a user
3. Enable **MFA Required**
4. Click **Save**

### MFA Methods

VM Gateway supports multiple MFA methods:

**TOTP (Time-based One-Time Password):**
- Use apps like Google Authenticator, Authy, 1Password
- Most common and recommended method

**WebAuthn / FIDO2:**
- Hardware security keys (YubiKey, etc.)
- Biometric authentication
- Most secure method

**SMS (Not Recommended):**
- Text message codes
- Less secure, use only if other methods unavailable

### User MFA Setup

Users set up MFA on first login (if required):

1. Log in with username and password
2. Choose MFA method
3. For TOTP:
   - Scan QR code with authenticator app
   - Enter verification code
   - Save backup codes
4. For WebAuthn:
   - Insert security key
   - Follow browser prompts
   - Register key
5. Complete setup

## Session Management

### Session Settings

Configure session behavior:

1. Navigate to **Settings** > **Security** > **Session Management**
2. Configure:
   - **Session Timeout**: Auto-logout after inactivity (default: 1 hour)
   - **Absolute Timeout**: Force logout after duration (default: 8 hours)
   - **Concurrent Sessions**: Max sessions per user (default: 3)
   - **Remember Me**: Allow "remember me" option (default: disabled)
3. Click **Save**

### Viewing Active Sessions

**As Administrator:**
1. Navigate to **Access Control** > **Sessions**
2. View all active sessions:
   - Username
   - Login time
   - Last activity
   - IP address
   - User agent
3. Revoke sessions if needed

**As User:**
1. Navigate to **Profile** > **Active Sessions**
2. View your own sessions
3. Revoke sessions from unknown devices

### Revoking Sessions

**Revoke specific session:**
1. Find the session in the list
2. Click **Revoke**
3. Confirm

**Revoke all user sessions:**
1. Navigate to user profile
2. Click **Revoke All Sessions**
3. User must log in again

## Service Accounts

Service accounts are for API access and automation.

### Creating Service Accounts

1. Navigate to **Access Control** > **Service Accounts**
2. Click **Create Service Account**
3. Configure:
   - **Name**: Descriptive name (e.g., "Terraform Automation")
   - **Description**: What this account is used for
   - **Permissions**: Specific API scopes
   - **IP Whitelist**: Restrict to specific IPs
   - **Expiration**: Optional expiration date
4. Click **Create**
5. **Copy the API token immediately** - it won't be shown again

### Service Account Permissions

Grant only necessary permissions:

```yaml
# Example: Terraform service account
permissions:
  - vm:view
  - vm:manage
  - service:view
  - agent:manage

# Example: Monitoring service account
permissions:
  - vm:view
  - vm:metrics
  - service:view
  - service:metrics

# Example: Backup service account
permissions:
  - system:backup
  - system:view
```

### Using Service Accounts

```bash
# API request with service account token
curl -X GET https://your-controller:8443/api/v1/vms \
  -H "Authorization: Bearer svc_token_1234567890abcdef"
```

## User Groups

Organize users into groups for easier management.

### Creating Groups

1. Navigate to **Access Control** > **Groups**
2. Click **Create Group**
3. Configure:
   - **Name**: Group name (e.g., "Engineering Team")
   - **Description**: Group purpose
   - **Members**: Add users to the group
   - **Roles**: Assign roles to all group members
4. Click **Create**

### Group-Based Access Control

Assign permissions to groups instead of individual users:

1. Create a group (e.g., "Database Team")
2. Add users to the group
3. Assign roles to the group
4. All group members inherit the roles

Benefits:
- Easier to manage large numbers of users
- Consistent permissions across team members
- Simpler onboarding/offboarding

## SSO Integration

Integrate with your identity provider for centralized authentication.

### Supported SSO Providers

- SAML 2.0 (Okta, Azure AD, Google Workspace, etc.)
- OAuth 2.0 / OpenID Connect
- LDAP / Active Directory

### Configuring SAML SSO

1. Navigate to **Settings** > **Authentication** > **SSO**
2. Click **Configure SAML**
3. Enter IdP details:
   - **IdP Entity ID**: Your identity provider's entity ID
   - **SSO URL**: SAML SSO endpoint
   - **Certificate**: IdP signing certificate
4. Configure attribute mapping:
   - **Username**: SAML attribute for username
   - **Email**: SAML attribute for email
   - **Groups**: SAML attribute for groups (optional)
5. Download SP metadata for your IdP
6. Click **Save**

See [SSO Configuration](/docs/06-authentication/04-sso.md) for detailed setup.

### Automatic User Provisioning

Automatically create users on first SSO login:

1. Navigate to **Settings** > **Authentication** > **SSO**
2. Enable **Auto-Provision Users**
3. Configure:
   - **Default Roles**: Roles assigned to new users
   - **Group Mapping**: Map SSO groups to VM Gateway roles
4. Click **Save**

Example group mapping:
```yaml
group_mapping:
  "Engineering": ["developer"]
  "DevOps": ["operator", "developer"]
  "Admins": ["administrator"]
```

## Audit Logging

All user actions are logged for security and compliance.

### Viewing Audit Logs

1. Navigate to **Audit** > **User Activity**
2. Filter logs:
   - **User**: Specific user or all users
   - **Action**: Type of action (login, access, modify, etc.)
   - **Resource**: What was accessed
   - **Time Range**: Date/time range
   - **Result**: Success or failure
3. Export logs if needed (CSV, JSON)

### Audit Log Events

**Authentication Events:**
- User login (success/failure)
- User logout
- MFA verification
- Password change
- Session revoked

**Authorization Events:**
- Permission granted/denied
- Role assigned/removed
- Access attempt (success/failure)

**Resource Events:**
- Service accessed
- Port forward created/closed
- Configuration changed
- User created/modified/deleted

### Audit Log Retention

Configure retention policies:

1. Navigate to **Settings** > **Audit** > **Retention**
2. Set retention periods:
   - **Authentication logs**: 90 days (minimum)
   - **Authorization logs**: 1 year (recommended)
   - **Resource access logs**: 1 year (recommended)
   - **Configuration changes**: Indefinite (recommended)
3. Configure archival:
   - **Archive to**: S3, Azure Blob, or local storage
   - **Archive after**: Days before archiving
4. Click **Save**

## User Lifecycle Management

### Onboarding New Users

1. Create user account
2. Assign appropriate roles
3. Send welcome email with instructions
4. User logs in and sets up MFA
5. User changes initial password
6. User completes any required training

### Offboarding Users

When a user leaves:

1. Navigate to **Access Control** > **Users**
2. Find the user
3. Click **Deactivate**
4. This will:
   - Revoke all active sessions
   - Disable login
   - Close all port forwards
   - Revoke API tokens
   - Preserve audit logs

**Do not delete users** - deactivate instead to preserve audit trail.

### Temporary Access

Grant temporary elevated access:

1. Navigate to user profile
2. Click **Grant Temporary Access**
3. Configure:
   - **Additional Roles**: Roles to grant temporarily
   - **Duration**: How long access lasts
   - **Reason**: Why access is needed (for audit)
   - **Approval**: Require approval from another admin
4. Click **Grant**

Access automatically expires after the duration.

## Best Practices

### Security

- Enforce strong password policies
- Require MFA for all users (especially admins)
- Use SSO when possible for centralized control
- Regularly review user access and remove unnecessary permissions
- Use service accounts for automation (not personal accounts)
- Monitor audit logs for suspicious activity

### Organization

- Create roles that match your organizational structure
- Use groups to manage teams
- Document role purposes and permissions
- Regular access reviews (quarterly recommended)
- Automate user provisioning/deprovisioning when possible

### Compliance

- Enable audit logging for all actions
- Set appropriate retention periods
- Export and archive logs regularly
- Document access control policies
- Regular compliance audits

## Troubleshooting

### User Can't Log In

**Check account status:**
- Verify account is active (not deactivated)
- Check if account is locked (too many failed attempts)
- Verify password hasn't expired

**Check MFA:**
- Ensure MFA device is synced (time-based codes)
- Verify backup codes if primary MFA unavailable
- Admin can temporarily disable MFA if needed

**Check SSO:**
- Verify SSO configuration is correct
- Check IdP is accessible
- Review SSO error messages in logs

### User Can't Access Service

**Check role permissions:**
- Verify user has appropriate roles
- Check role has permission for the service
- Review any conditional access rules (time, IP, etc.)

**Check service status:**
- Verify service is discovered and active
- Check agent is connected
- Verify service isn't in maintenance mode

### Session Issues

**Session expires too quickly:**
- Increase session timeout in settings
- Check if "remember me" is disabled
- Verify user isn't hitting concurrent session limit

**Can't revoke session:**
- Check admin permissions
- Verify session ID is correct
- Check if session already expired

## Next Steps

- [Configure RBAC](/docs/06-authentication/07-rbac.md) - Detailed access control setup
- [Set Up SSO](/docs/06-authentication/04-sso.md) - Integrate with identity provider
- [Approval Workflows](/docs/06-authentication/08-approval-workflows.md) - Require approval for sensitive access
- [Audit Logging](/docs/07-secrets-management/07-audit.md) - Comprehensive audit configuration

## Related Documentation

- [Authentication Overview](/docs/06-authentication/01-overview.md) - Authentication architecture
- [RBAC System](/docs/06-authentication/07-rbac.md) - Role-based access control
- [SSO Integration](/docs/06-authentication/04-sso.md) - Single sign-on setup
- [Security Best Practices](/docs/10-development/07-security.md) - Security guidelines
