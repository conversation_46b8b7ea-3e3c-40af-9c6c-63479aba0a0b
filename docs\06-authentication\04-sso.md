---
title: "Single Sign-On Integration"
section: "Authentication & Authorization"
order: 4
tags: ["sso", "saml", "oidc", "ldap", "authentication"]
last_updated: "2025-11-08"
---

# Single Sign-On (SSO) Integration

Single Sign-On allows users to authenticate using their existing corporate identity provider, eliminating the need for separate credentials and enabling centralized user management. The platform supports SAML 2.0, OAuth 2.0/OpenID Connect, and LDAP/Active Directory integration.

## Overview

SSO integration provides several benefits for enterprise deployments:

**Benefits:**
- Centralized identity management
- Reduced password fatigue
- Simplified user onboarding/offboarding
- Compliance with corporate security policies
- Audit trail integration with corporate IdP
- Support for existing MFA policies

**Supported Protocols:**
- SAML 2.0 (Security Assertion Markup Language)
- OAuth 2.0 / OpenID Connect (OIDC)
- LDAP / Active Directory

**Supported Identity Providers:**
- Okta
- Azure Active Directory (Azure AD / Entra ID)
- Google Workspace
- OneLogin
- Auth0
- Keycloak
- Generic SAML 2.0 providers
- Generic OIDC providers

## SAML 2.0 Integration

### Overview

SAML 2.0 is an XML-based protocol for exchanging authentication and authorization data between an identity provider (IdP) and a service provider (SP). The platform acts as a SAML service provider.

**SAML Flow:**
1. User attempts to access the platform
2. Platform redirects to IdP with SAML authentication request
3. User authenticates with IdP
4. IdP sends SAML assertion back to platform
5. Platform validates assertion and creates session

### Configuration

```yaml
saml:
  enabled: true
  
  # Service Provider (SP) configuration
  sp:
    entity_id: "https://gateway.example.com/saml/metadata"
    assertion_consumer_service_url: "https://gateway.example.com/saml/acs"
    single_logout_service_url: "https://gateway.example.com/saml/sls"
    
    # Certificate for signing requests (optional)
    certificate: "/path/to/sp-cert.pem"
    private_key: "/path/to/sp-key.pem"
  
  # Identity Provider (IdP) configuration
  idp:
    entity_id: "https://idp.example.com/saml/metadata"
    sso_url: "https://idp.example.com/saml/sso"
    slo_url: "https://idp.example.com/saml/slo"
    
    # IdP certificate for validating assertions
    certificate: "/path/to/idp-cert.pem"
  
  # Attribute mapping
  attribute_mapping:
    email: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
    username: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"
    first_name: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname"
    last_name: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname"
    groups: "http://schemas.xmlsoap.org/claims/Group"
  
  # Just-In-Time provisioning
  jit_provisioning:
    enabled: true
    default_role: "viewer"
    update_on_login: true
    
  # Security settings
  security:
    want_assertions_signed: true
    want_messages_signed: false
    signature_algorithm: "http://www.w3.org/2001/04/xmldsig-more#rsa-sha256"
    digest_algorithm: "http://www.w3.org/2001/04/xmlenc#sha256"
```



### SAML Implementation

```python
from onelogin.saml2.auth import OneLogin_Saml2_Auth
from onelogin.saml2.settings import OneLogin_Saml2_Settings

class SAMLAuthManager:
    """Manage SAML 2.0 authentication"""
    
    def __init__(self, config: dict):
        self.config = config
    
    async def initiate_login(self, request: Request) -> str:
        """Initiate SAML login flow"""
        
        # Prepare SAML request
        saml_auth = OneLogin_Saml2_Auth(
            self._prepare_request(request),
            self.config
        )
        
        # Generate and return SSO URL
        sso_url = saml_auth.login()
        
        # Store relay state for post-login redirect
        relay_state = request.query_params.get('next', '/')
        await redis.setex(
            f"saml_relay:{saml_auth.get_last_request_id()}",
            600,
            relay_state
        )
        
        return sso_url
    
    async def handle_assertion(self, request: Request) -> User:
        """Handle SAML assertion from IdP"""
        
        saml_auth = OneLogin_Saml2_Auth(
            self._prepare_request(request),
            self.config
        )
        
        # Process SAML response
        saml_auth.process_response()
        errors = saml_auth.get_errors()
        
        if errors:
            await log_security_event(
                event_type="saml_assertion_failed",
                errors=errors
            )
            raise HTTPException(status_code=401, detail="SAML authentication failed")
        
        # Validate assertion
        if not saml_auth.is_authenticated():
            raise HTTPException(status_code=401, detail="Not authenticated")
        
        # Extract user attributes
        attributes = saml_auth.get_attributes()
        name_id = saml_auth.get_nameid()
        
        # Map attributes to user fields
        user_data = self._map_attributes(attributes, name_id)
        
        # Get or create user (JIT provisioning)
        user = await self._get_or_create_user(user_data)
        
        # Update user attributes if configured
        if self.config.get('jit_provisioning', {}).get('update_on_login'):
            await self._update_user_attributes(user, user_data)
        
        # Log successful SSO login
        await log_audit_event(
            actor=user.username,
            action="sso_login",
            details={"provider": "saml", "name_id": name_id}
        )
        
        return user
    
    def _map_attributes(self, attributes: dict, name_id: str) -> dict:
        """Map SAML attributes to user fields"""
        
        mapping = self.config.get('attribute_mapping', {})
        
        return {
            'email': self._get_attribute(attributes, mapping.get('email')),
            'username': self._get_attribute(attributes, mapping.get('username')) or name_id,
            'first_name': self._get_attribute(attributes, mapping.get('first_name')),
            'last_name': self._get_attribute(attributes, mapping.get('last_name')),
            'groups': self._get_attribute(attributes, mapping.get('groups'), multiple=True)
        }
    
    def _get_attribute(self, attributes: dict, key: str, multiple: bool = False):
        """Extract attribute value from SAML response"""
        if not key or key not in attributes:
            return [] if multiple else None
        
        values = attributes[key]
        if multiple:
            return values if isinstance(values, list) else [values]
        return values[0] if isinstance(values, list) else values
    
    async def _get_or_create_user(self, user_data: dict) -> User:
        """Get existing user or create new one (JIT provisioning)"""
        
        user = await get_user_by_email(user_data['email'])
        
        if not user:
            # Create new user
            if not self.config.get('jit_provisioning', {}).get('enabled'):
                raise HTTPException(
                    status_code=403,
                    detail="User does not exist and JIT provisioning is disabled"
                )
            
            # Assign default role
            default_role = self.config.get('jit_provisioning', {}).get('default_role', 'viewer')
            
            user = await create_user(
                username=user_data['username'],
                email=user_data['email'],
                first_name=user_data.get('first_name'),
                last_name=user_data.get('last_name'),
                is_active=True,
                email_verified=True,
                auth_method='saml'
            )
            
            # Assign role
            await assign_role(user.id, default_role)
            
            # Sync groups to roles
            if user_data.get('groups'):
                await self._sync_groups_to_roles(user, user_data['groups'])
            
            await log_audit_event(
                actor="system",
                action="user_created_jit",
                details={"user_id": user.id, "email": user.email, "provider": "saml"}
            )
        
        return user
    
    async def _sync_groups_to_roles(self, user: User, groups: List[str]) -> None:
        """Sync SAML groups to platform roles"""
        
        group_role_mapping = self.config.get('group_role_mapping', {})
        
        for group in groups:
            if group in group_role_mapping:
                role_name = group_role_mapping[group]
                await assign_role(user.id, role_name)
```

## OAuth 2.0 / OpenID Connect

### Overview

OAuth 2.0 with OpenID Connect (OIDC) provides modern, token-based authentication. It's widely supported by cloud providers and social identity providers.

**Supported Providers:**
- Google Workspace
- Microsoft Azure AD
- GitHub
- GitLab
- Generic OIDC providers

### Configuration

```yaml
oidc:
  enabled: true
  
  providers:
    - name: "google"
      display_name: "Google Workspace"
      client_id: "your-client-id"
      client_secret: "your-client-secret"
      discovery_url: "https://accounts.google.com/.well-known/openid-configuration"
      scopes: ["openid", "email", "profile"]
      
    - name: "azure"
      display_name: "Microsoft"
      client_id: "your-client-id"
      client_secret: "your-client-secret"
      authority: "https://login.microsoftonline.com/your-tenant-id"
      scopes: ["openid", "email", "profile"]
      
    - name: "github"
      display_name: "GitHub"
      client_id: "your-client-id"
      client_secret: "your-client-secret"
      authorization_endpoint: "https://github.com/login/oauth/authorize"
      token_endpoint: "https://github.com/login/oauth/access_token"
      userinfo_endpoint: "https://api.github.com/user"
      scopes: ["read:user", "user:email"]
  
  # JIT provisioning
  jit_provisioning:
    enabled: true
    default_role: "viewer"
    update_on_login: true
  
  # Attribute mapping
  attribute_mapping:
    email: "email"
    username: "preferred_username"
    first_name: "given_name"
    last_name: "family_name"
```

### OIDC Implementation

```python
from authlib.integrations.starlette_client import OAuth
from authlib.integrations.starlette_client import OAuthError

class OIDCAuthManager:
    """Manage OpenID Connect authentication"""
    
    def __init__(self, config: dict):
        self.oauth = OAuth()
        self._register_providers(config)
    
    def _register_providers(self, config: dict):
        """Register OIDC providers"""
        for provider_config in config.get('providers', []):
            self.oauth.register(
                name=provider_config['name'],
                client_id=provider_config['client_id'],
                client_secret=provider_config['client_secret'],
                server_metadata_url=provider_config.get('discovery_url'),
                client_kwargs={
                    'scope': ' '.join(provider_config.get('scopes', []))
                }
            )
    
    async def initiate_login(self, provider: str, request: Request) -> str:
        """Initiate OIDC login flow"""
        
        client = self.oauth.create_client(provider)
        if not client:
            raise HTTPException(status_code=400, detail="Unknown provider")
        
        # Generate redirect URI
        redirect_uri = request.url_for('oidc_callback', provider=provider)
        
        # Initiate authorization
        return await client.authorize_redirect(request, redirect_uri)
    
    async def handle_callback(self, provider: str, request: Request) -> User:
        """Handle OIDC callback"""
        
        client = self.oauth.create_client(provider)
        
        try:
            # Exchange code for token
            token = await client.authorize_access_token(request)
            
            # Get user info
            userinfo = token.get('userinfo')
            if not userinfo:
                userinfo = await client.userinfo(token=token)
            
            # Map attributes
            user_data = self._map_userinfo(userinfo)
            
            # Get or create user
            user = await self._get_or_create_user(user_data, provider)
            
            # Log SSO login
            await log_audit_event(
                actor=user.username,
                action="sso_login",
                details={"provider": provider, "sub": userinfo.get('sub')}
            )
            
            return user
            
        except OAuthError as e:
            await log_security_event(
                event_type="oidc_auth_failed",
                provider=provider,
                error=str(e)
            )
            raise HTTPException(status_code=401, detail="OIDC authentication failed")
```

## LDAP / Active Directory

### Overview

LDAP integration allows authentication against existing directory services like Active Directory, OpenLDAP, or FreeIPA.

**Features:**
- User authentication via LDAP bind
- Group synchronization
- Nested group support
- Attribute mapping
- Connection pooling

### Configuration

```yaml
ldap:
  enabled: true
  
  # Server configuration
  server:
    url: "ldaps://ldap.example.com:636"
    bind_dn: "cn=service-account,ou=services,dc=example,dc=com"
    bind_password: "service-password"
    use_tls: true
    tls_verify: true
    ca_cert_file: "/path/to/ca-cert.pem"
  
  # User search
  user_search:
    base_dn: "ou=users,dc=example,dc=com"
    filter: "(uid={username})"
    attributes:
      - "uid"
      - "mail"
      - "givenName"
      - "sn"
      - "memberOf"
  
  # Group search
  group_search:
    base_dn: "ou=groups,dc=example,dc=com"
    filter: "(member={user_dn})"
    attributes:
      - "cn"
      - "description"
  
  # Attribute mapping
  attribute_mapping:
    username: "uid"
    email: "mail"
    first_name: "givenName"
    last_name: "sn"
    groups: "memberOf"
  
  # Group to role mapping
  group_role_mapping:
    "cn=admins,ou=groups,dc=example,dc=com": "admin"
    "cn=developers,ou=groups,dc=example,dc=com": "developer"
    "cn=operators,ou=groups,dc=example,dc=com": "operator"
  
  # Sync settings
  sync:
    enabled: true
    interval: 3600  # 1 hour
    create_users: true
    update_users: true
    deactivate_removed_users: true
```

### LDAP Implementation

```python
import ldap3
from ldap3 import Server, Connection, ALL, SUBTREE

class LDAPAuthManager:
    """Manage LDAP authentication"""
    
    def __init__(self, config: dict):
        self.config = config
        self.server = Server(
            config['server']['url'],
            get_info=ALL,
            use_ssl=config['server'].get('use_tls', True)
        )
    
    async def authenticate(self, username: str, password: str) -> Optional[User]:
        """Authenticate user against LDAP"""
        
        # Search for user
        user_dn, user_attrs = await self._search_user(username)
        if not user_dn:
            return None
        
        # Attempt bind with user credentials
        try:
            conn = Connection(
                self.server,
                user=user_dn,
                password=password,
                auto_bind=True
            )
            conn.unbind()
        except ldap3.core.exceptions.LDAPBindError:
            await log_security_event(
                event_type="ldap_bind_failed",
                username=username
            )
            return None
        
        # Get user groups
        groups = await self._get_user_groups(user_dn)
        
        # Map attributes
        user_data = self._map_attributes(user_attrs, groups)
        
        # Get or create user
        user = await self._get_or_create_user(user_data)
        
        # Sync groups to roles
        await self._sync_groups_to_roles(user, groups)
        
        return user
    
    async def _search_user(self, username: str) -> Tuple[Optional[str], Optional[dict]]:
        """Search for user in LDAP"""
        
        # Bind with service account
        conn = Connection(
            self.server,
            user=self.config['server']['bind_dn'],
            password=self.config['server']['bind_password'],
            auto_bind=True
        )
        
        # Search for user
        search_filter = self.config['user_search']['filter'].format(username=username)
        conn.search(
            search_base=self.config['user_search']['base_dn'],
            search_filter=search_filter,
            search_scope=SUBTREE,
            attributes=self.config['user_search']['attributes']
        )
        
        if not conn.entries:
            conn.unbind()
            return None, None
        
        entry = conn.entries[0]
        user_dn = entry.entry_dn
        user_attrs = entry.entry_attributes_as_dict
        
        conn.unbind()
        return user_dn, user_attrs
    
    async def _get_user_groups(self, user_dn: str) -> List[str]:
        """Get groups for user"""
        
        conn = Connection(
            self.server,
            user=self.config['server']['bind_dn'],
            password=self.config['server']['bind_password'],
            auto_bind=True
        )
        
        # Search for groups
        search_filter = self.config['group_search']['filter'].format(user_dn=user_dn)
        conn.search(
            search_base=self.config['group_search']['base_dn'],
            search_filter=search_filter,
            search_scope=SUBTREE,
            attributes=self.config['group_search']['attributes']
        )
        
        groups = [entry.entry_dn for entry in conn.entries]
        conn.unbind()
        
        return groups
```

## SSO Session Management

### Single Logout (SLO)

```python
async def handle_logout(user: User, session_token: str) -> str:
    """Handle logout with SSO provider"""
    
    # Get user's auth method
    if user.auth_method == 'saml':
        # Initiate SAML SLO
        saml_auth = OneLogin_Saml2_Auth(request, saml_config)
        slo_url = saml_auth.logout()
        
        # Invalidate local session
        await invalidate_session(session_token)
        
        return slo_url
    
    elif user.auth_method == 'oidc':
        # Get OIDC end session endpoint
        client = oauth.create_client(user.oidc_provider)
        metadata = await client.load_server_metadata()
        end_session_endpoint = metadata.get('end_session_endpoint')
        
        if end_session_endpoint:
            # Redirect to IdP logout
            post_logout_redirect = request.url_for('logout_complete')
            logout_url = f"{end_session_endpoint}?post_logout_redirect_uri={post_logout_redirect}"
            
            await invalidate_session(session_token)
            return logout_url
    
    # Local logout only
    await invalidate_session(session_token)
    return request.url_for('login')
```

## Related Documentation

- [Authentication Overview](./01-overview.md) - System architecture
- [Local Authentication](./02-local-auth.md) - Password authentication
- [Session Management](./06-session-management.md) - Session handling
- [RBAC System](./07-rbac.md) - Role-based access control
