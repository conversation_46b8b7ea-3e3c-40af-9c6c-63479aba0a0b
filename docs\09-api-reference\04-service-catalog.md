---
title: "Service Catalog API"
section: "API Reference"
order: 4
tags: ["api", "services", "catalog", "endpoints"]
last_updated: "2025-11-08"
---

# Service Catalog API

The Service Catalog API provides endpoints for discovering, managing, and accessing services running on monitored VMs. These endpoints allow you to query the service catalog, update service metadata, and manage service access.

## Base URL

```
https://your-gateway.example.com/api/v1/services
```

## Endpoints

### GET /services

List all discovered services with filtering and pagination.

**Request:**

```http
GET /api/v1/services?type=database&status=healthy&vm_id=vm_abc123&page=1&limit=50 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `type` | string | Service type: `web`, `database`, `cache`, `queue`, `custom` | all |
| `status` | string | Health status: `healthy`, `warning`, `critical`, `unknown` | all |
| `vm_id` | string | Filter by VM ID | all |
| `environment` | string | Filter by environment tag | all |
| `search` | string | Search by name or description | - |
| `tags` | string | Comma-separated list of tags | - |
| `port` | integer | Filter by port number | - |
| `page` | integer | Page number (1-indexed) | 1 |
| `limit` | integer | Results per page (max 100) | 50 |
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "services": [
    {
      "id": "svc_abc123def456",
      "name": "postgres-main",
      "type": "database",
      "subtype": "postgresql",
      "version": "14.5",
      "status": "healthy",
      "vm": {
        "id": "vm_abc123",
        "name": "prod-db-01",
        "hostname": "prod-db-01.internal"
      },
      "network": {
        "port": 5432,
        "protocol": "tcp",
        "bind_address": "0.0.0.0"
      },
      "process": {
        "pid": 1234,
        "user": "postgres",
        "command": "/usr/lib/postgresql/14/bin/postgres"
      },
      "health": {
        "status": "healthy",
        "response_time_ms": 12,
        "last_check": "2025-11-08T14:59:00Z",
        "uptime_percent": 99.98
      },
      "metrics": {
        "cpu_percent": 15.2,
        "memory_mb": 2048,
        "connections": 45,
        "requests_per_second": 120
      },
      "tags": ["production", "primary", "critical"],
      "discovered_at": "2025-10-01T10:00:00Z",
      "last_seen": "2025-11-08T14:59:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 1,
    "total_pages": 1
  }
}
```

---

### GET /services/{service_id}

Get detailed information about a specific service.

**Request:**

```http
GET /api/v1/services/svc_abc123def456 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "service": {
    "id": "svc_abc123def456",
    "name": "postgres-main",
    "description": "Primary PostgreSQL database for production",
    "type": "database",
    "subtype": "postgresql",
    "version": "14.5",
    "status": "healthy",
    "vm": {
      "id": "vm_abc123",
      "name": "prod-db-01",
      "hostname": "prod-db-01.internal",
      "ip_addresses": ["*********"]
    },
    "network": {
      "port": 5432,
      "protocol": "tcp",
      "bind_address": "0.0.0.0",
      "tls_enabled": true,
      "tls_version": "TLSv1.3"
    },
    "process": {
      "pid": 1234,
      "user": "postgres",
      "command": "/usr/lib/postgresql/14/bin/postgres -D /var/lib/postgresql/14/main",
      "working_directory": "/var/lib/postgresql/14/main",
      "started_at": "2025-11-05T10:00:00Z"
    },
    "health": {
      "status": "healthy",
      "response_time_ms": 12,
      "last_check": "2025-11-08T14:59:00Z",
      "consecutive_failures": 0,
      "uptime_percent": 99.98,
      "check_interval": 60
    },
    "metrics": {
      "cpu_percent": 15.2,
      "memory_mb": 2048,
      "connections": 45,
      "max_connections": 100,
      "requests_per_second": 120,
      "error_rate": 0.01
    },
    "access": {
      "web_accessible": false,
      "requires_authentication": true,
      "connection_string": "postgresql://prod-db-01.internal:5432/maindb"
    },
    "tags": ["production", "primary", "critical"],
    "metadata": {
      "owner": "database-team",
      "cost_center": "engineering",
      "backup_enabled": true
    },
    "discovered_at": "2025-10-01T10:00:00Z",
    "last_seen": "2025-11-08T14:59:00Z",
    "updated_at": "2025-11-08T14:00:00Z"
  }
}
```

---

### PATCH /services/{service_id}

Update service metadata and configuration.

**Request:**

```http
PATCH /api/v1/services/svc_abc123def456 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "name": "postgres-main-updated",
  "description": "Primary PostgreSQL database for production (replicated)",
  "tags": ["production", "primary", "critical", "replicated"],
  "metadata": {
    "owner": "database-team",
    "cost_center": "engineering",
    "backup_enabled": true,
    "replication_enabled": true
  },
  "health_check": {
    "enabled": true,
    "interval": 30,
    "timeout": 5,
    "threshold": 3
  }
}
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "service": {
    "id": "svc_abc123def456",
    "name": "postgres-main-updated",
    "description": "Primary PostgreSQL database for production (replicated)",
    "tags": ["production", "primary", "critical", "replicated"],
    "updated_at": "2025-11-08T15:10:00Z"
  },
  "message": "Service updated successfully"
}
```

---

### GET /services/{service_id}/metrics

Get historical metrics for a service.

**Request:**

```http
GET /api/v1/services/svc_abc123def456/metrics?metric=connections&from=2025-11-08T00:00:00Z&to=2025-11-08T23:59:59Z&resolution=5m HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "metric": "connections",
  "resolution": "5m",
  "data_points": [
    {
      "timestamp": "2025-11-08T00:00:00Z",
      "value": 42,
      "min": 38,
      "max": 48,
      "avg": 42
    }
  ],
  "total_points": 288
}
```

---

### POST /services/{service_id}/actions/health-check

Trigger an immediate health check for a service.

**Request:**

```http
POST /api/v1/services/svc_abc123def456/actions/health-check HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "health": {
    "status": "healthy",
    "response_time_ms": 11,
    "checked_at": "2025-11-08T15:15:00Z"
  }
}
```

---

## Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `SERVICE_NOT_FOUND` | 404 | Service with specified ID does not exist |
| `SERVICE_UNAVAILABLE` | 503 | Service is not responding |
| `INVALID_SERVICE_CONFIG` | 400 | Service configuration is invalid |
| `INSUFFICIENT_PERMISSIONS` | 403 | User lacks permission to access service |

## Code Examples

### Python

```python
import requests

headers = {"Authorization": "Bearer YOUR_ACCESS_TOKEN"}

# List all database services
response = requests.get(
    "https://your-gateway.example.com/api/v1/services",
    headers=headers,
    params={"type": "database", "status": "healthy"}
)

services = response.json()["services"]
for service in services:
    print(f"{service['name']}: {service['version']} on {service['vm']['name']}")
```

## Related Documentation

- [API Overview](./01-overview.md) - API design principles
- [VM Management API](./03-vm-management.md) - VM management
- [Connections API](./05-connections.md) - Connection management
