<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VM Gateway Documentation</title>
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css" id="highlight-light">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css" id="highlight-dark" disabled>
</head>
<body>
    <!-- Mobile header with hamburger menu -->
    <header class="mobile-header">
        <button id="menu-toggle" class="menu-toggle" aria-label="Toggle navigation menu">
            <span class="hamburger"></span>
            <span class="hamburger"></span>
            <span class="hamburger"></span>
        </button>
        <h1 class="mobile-title">VM Gateway Docs</h1>
    </header>

    <!-- Sidebar navigation -->
    <aside id="sidebar" class="sidebar">
        <div class="sidebar-header">
            <h1 class="sidebar-title">VM Gateway</h1>
            <p class="sidebar-subtitle">Documentation</p>
        </div>

        <!-- Search box -->
        <div class="search-container">
            <input 
                type="search" 
                id="search-input" 
                class="search-input" 
                placeholder="Search documentation..."
                aria-label="Search documentation"
            >
            <div id="search-results" class="search-results hidden"></div>
        </div>

        <!-- Navigation tree -->
        <nav id="nav-tree" class="nav-tree" aria-label="Documentation navigation">
            <div class="loading">Loading documentation...</div>
        </nav>

        <!-- Footer with version -->
        <div class="sidebar-footer">
            <p class="version">Version a.0.0-1</p>
        </div>
    </aside>

    <!-- Main content area -->
    <main id="main-content" class="main-content">
        <!-- Breadcrumb navigation -->
        <nav id="breadcrumb" class="breadcrumb" aria-label="Breadcrumb"></nav>

        <!-- Content area -->
        <article id="content" class="content">
            <div class="welcome">
                <h1>Welcome to VM Gateway Documentation</h1>
                <p>Select a topic from the navigation menu to get started.</p>
            </div>
        </article>

        <!-- Table of contents (auto-generated) -->
        <aside id="toc-sidebar" class="toc-sidebar">
            <div class="toc-header">On This Page</div>
            <nav id="toc" class="toc" aria-label="Table of contents"></nav>
        </aside>
    </main>

    <!-- Overlay for mobile menu -->
    <div id="overlay" class="overlay"></div>

    <!-- Theme Toggle Button -->
    <button class="theme-toggle" id="themeToggle" aria-label="Toggle theme">
        <span id="themeIcon">🌙</span>
    </button>

    <!-- Scripts -->
    <script src="/static/js/theme.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="/static/js/nav.js"></script>
    <script src="/static/js/search.js"></script>
    <script src="/static/js/app.js"></script>
</body>
</html>
