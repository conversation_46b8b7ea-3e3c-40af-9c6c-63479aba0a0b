---
title: "Tutorial: Secure Database Access Workflow"
section: "Tutorials"
order: 3
tags: ["tutorial", "database", "access", "workflow", "security"]
last_updated: "2025-11-09"
---

# Tutorial: Secure Database Access Workflow

## Overview

This comprehensive tutorial demonstrates how to set up a secure, auditable workflow for database access using VM Gateway. We'll implement a complete solution with approval workflows, time-based access, and comprehensive audit logging.

**Scenario**: Your organization has production databases that require:
- Approval from DBA team before access
- Time-limited access (auto-disconnect after 2 hours)
- Comprehensive audit logging
- Emergency access procedure
- Read-only access for most users
- Write access only for DBAs

**Time Required**: 45-60 minutes

**Prerequisites**:
- VM Gateway Professional or Enterprise Edition
- PostgreSQL database (or any database)
- Admin access to VM Gateway
- Basic understanding of RBAC

---

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Setting Up Database Discovery](#setting-up-database-discovery)
3. [Creating Roles](#creating-roles)
4. [Configuring Approval Workflows](#configuring-approval-workflows)
5. [Setting Up Time-Based Access](#setting-up-time-based-access)
6. [Testing the Workflow](#testing-the-workflow)
7. [Emergency Access](#emergency-access)
8. [Audit and Compliance](#audit-and-compliance)

---

## Architecture Overview

### Access Levels

We'll implement three access levels:

**1. Read-Only Access** (Developers):
- View database schema
- Run SELECT queries
- No approval required
- 8-hour access window

**2. Read-Write Access** (Senior Developers):
- All read-only permissions
- Run INSERT/UPDATE/DELETE queries
- Requires DBA approval
- 2-hour access window
- Auto-disconnect after time limit

**3. Admin Access** (DBAs):
- Full database access
- No approval required
- No time limit
- Can approve others' requests

### Workflow Diagram

```
Developer Requests Access
         ↓
   Check Access Level
         ↓
    ┌────┴────┐
    │         │
Read-Only  Read-Write
    │         │
    │    Approval Required
    │         ↓
    │    DBA Approves
    │         │
    └────┬────┘
         ↓
   Access Granted
         ↓
   Time-Limited Session
         ↓
   Auto-Disconnect
         ↓
   Audit Log Created
```

---

## Setting Up Database Discovery

### Step 1: Install Agent on Database Server

```bash
# SSH to database server
ssh prod-db-01

# Download and install agent
curl -O https://vmgateway.example.com/downloads/install-agent.sh
sudo bash install-agent.sh \
  --controller https://vmgateway.example.com \
  --token YOUR_REGISTRATION_TOKEN \
  --name "Production Database Server" \
  --tags "production,database,postgresql"
```

### Step 2: Verify Discovery

```bash
# Check agent status
sudo systemctl status vm-gateway-agent

# Check logs
sudo journalctl -u vm-gateway-agent -f
```

Look for:
```
INFO: Service discovered: PostgreSQL (port 5432)
INFO: Service classified as: database/postgresql
INFO: Service reported to controller
```

### Step 3: Verify in Web Interface

1. Log in to VM Gateway web interface
2. Navigate to **Service Catalog**
3. Find "PostgreSQL" service
4. Verify details:
   - Name: PostgreSQL
   - Type: Database
   - VM: prod-db-01
   - Port: 5432
   - Status: Healthy

### Step 4: Add Service Metadata

Add additional metadata to help users:

```bash
# Via API
curl -X PATCH https://vmgateway.example.com/api/v1/services/svc_123 \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "display_name": "Production PostgreSQL Database",
    "description": "Main production database for application",
    "tags": ["production", "postgresql", "critical"],
    "metadata": {
      "database_name": "myapp_production",
      "version": "14.5",
      "owner": "<EMAIL>",
      "runbook": "https://wiki.example.com/runbooks/postgres",
      "connection_info": {
        "default_database": "myapp_production",
        "default_user": "app_readonly"
      }
    }
  }'
```

---

## Creating Roles

### Step 1: Create Database Read-Only Role

```yaml
# Via web interface or API
name: "Database Reader"
description: "Read-only access to databases"

permissions:
  # Can view all databases
  - resource: "service:type:database"
    actions: ["view"]
  
  # Can connect to non-production databases without approval
  - resource: "service:type:database:env:development"
    actions: ["connect", "query:select"]
  
  - resource: "service:type:database:env:staging"
    actions: ["connect", "query:select"]
  
  # Can connect to production databases (read-only)
  - resource: "service:type:database:env:production"
    actions: ["connect", "query:select"]
    conditions:
      time_window:
        start: "09:00"
        end: "17:00"
        timezone: "America/New_York"
        days: ["monday", "tuesday", "wednesday", "thursday", "friday"]

# Time limits
session_duration_minutes: 480  # 8 hours
auto_disconnect: true
```

**Create via CLI**:
```bash
python -m vm_gateway.cli create-role \
  --name "Database Reader" \
  --description "Read-only access to databases" \
  --permissions-file db-reader-permissions.yaml
```

### Step 2: Create Database Read-Write Role

```yaml
name: "Database Writer"
description: "Read-write access to databases (requires approval)"

permissions:
  # Can view all databases
  - resource: "service:type:database"
    actions: ["view"]
  
  # Full access to non-production (no approval)
  - resource: "service:type:database:env:development"
    actions: ["connect", "query:*"]
  
  - resource: "service:type:database:env:staging"
    actions: ["connect", "query:*"]
  
  # Read-write access to production (requires approval)
  - resource: "service:type:database:env:production"
    actions: ["connect", "query:select", "query:insert", "query:update", "query:delete"]
    requires_approval: true
    approval_workflow: "database-write-access"

# Time limits
session_duration_minutes: 120  # 2 hours
auto_disconnect: true
warn_before_disconnect_minutes: 10
```

### Step 3: Create DBA Role

```yaml
name: "Database Administrator"
description: "Full database access (no approval required)"

permissions:
  # Full access to all databases
  - resource: "service:type:database"
    actions: ["*"]
  
  # Can approve access requests
  - resource: "approval:database-write-access"
    actions: ["approve", "deny"]
  
  # Can view all audit logs
  - resource: "audit:database"
    actions: ["view"]

# No time limits for DBAs
session_duration_minutes: null
auto_disconnect: false
```

### Step 4: Assign Roles to Users

```bash
# Assign Database Reader role to developers
python -m vm_gateway.cli assign-role \
  --user <EMAIL> \
  --role "Database Reader"

# Assign Database Writer role to senior developers
python -m vm_gateway.cli assign-role \
  --user <EMAIL> \
  --role "Database Writer"

# Assign DBA role to database administrators
python -m vm_gateway.cli assign-role \
  --user <EMAIL> \
  --role "Database Administrator"
```

---

## Configuring Approval Workflows

### Step 1: Create Approval Workflow

```yaml
# config/workflows/database-write-access.yaml
name: "database-write-access"
display_name: "Production Database Write Access"
description: "Approval required for write access to production databases"

# Trigger conditions
triggers:
  - resource_pattern: "service:type:database:env:production"
    action_pattern: "query:(insert|update|delete)"

# Approval levels
approval_levels:
  - level: 1
    name: "DBA Approval"
    required_approvals: 1
    eligible_approvers:
      - role: "Database Administrator"
    timeout_minutes: 60  # Request expires after 1 hour
    escalation:
      after_minutes: 30
      notify:
        - role: "Database Administrator"
        - email: "<EMAIL>"

# Notifications
notifications:
  request_created:
    - approvers
    - requester
    - slack_channel: "#database-approvals"
  
  approved:
    - requester
    - slack_channel: "#database-approvals"
  
  denied:
    - requester
    - approvers
  
  expired:
    - requester
    - approvers

# Access duration after approval
access_duration_minutes: 120  # 2 hours
auto_revoke: true

# Required information from requester
required_fields:
  - name: "reason"
    type: "text"
    label: "Reason for access"
    required: true
    min_length: 20
  
  - name: "ticket_number"
    type: "text"
    label: "Ticket/Issue Number"
    required: true
    pattern: "^(JIRA|INC)-[0-9]+"
  
  - name: "queries"
    type: "textarea"
    label: "Queries to run (optional)"
    required: false
```

**Create via CLI**:
```bash
python -m vm_gateway.cli create-workflow \
  --file config/workflows/database-write-access.yaml
```

### Step 2: Configure Notifications

**Slack Integration**:
```yaml
# In config/slack-config.yaml
routing:
  approval_requested:
    webhook: "approvals"
    enabled: true
    mention_users: ["@dba-team"]
    interactive: true  # Enable approve/deny buttons
```

**Email Notifications**:
```yaml
# In config/email-config.yaml
notifications:
  approval_requested:
    enabled: true
    recipients:
      - role: "Database Administrator"
      - email: "<EMAIL>"
    template: "approval-request"
    priority: "high"
```

---

## Setting Up Time-Based Access

### Step 1: Configure Business Hours

```yaml
# In role configuration
permissions:
  - resource: "service:type:database:env:production"
    actions: ["connect", "query:select"]
    conditions:
      time_window:
        # Only during business hours
        start: "09:00"
        end: "17:00"
        timezone: "America/New_York"
        days: ["monday", "tuesday", "wednesday", "thursday", "friday"]
        
        # Exceptions (holidays)
        exclude_dates:
          - "2025-12-25"  # Christmas
          - "2025-01-01"  # New Year
```

### Step 2: Configure Session Duration

```yaml
# In role configuration
session_duration_minutes: 120  # 2 hours
auto_disconnect: true
warn_before_disconnect_minutes: 10

# Warning notification
disconnect_warning:
  enabled: true
  methods:
    - desktop_notification
    - email
    - slack_dm
```

### Step 3: Configure Auto-Disconnect

```python
# In controller configuration
# Auto-disconnect implementation
async def check_session_timeouts():
    """Check for expired sessions and disconnect."""
    
    while True:
        # Check every minute
        await asyncio.sleep(60)
        
        # Get all active connections
        connections = await get_active_connections()
        
        for conn in connections:
            # Check if session expired
            if conn.expires_at and conn.expires_at < datetime.now():
                # Warn user 10 minutes before disconnect
                if conn.expires_at - datetime.now() < timedelta(minutes=10):
                    if not conn.warning_sent:
                        await send_disconnect_warning(conn)
                        conn.warning_sent = True
                
                # Disconnect if expired
                if conn.expires_at < datetime.now():
                    await disconnect_session(conn)
                    await log_auto_disconnect(conn)
                    await notify_user_disconnected(conn)
```

---

## Testing the Workflow

### Test 1: Read-Only Access (No Approval)

**As Developer (<EMAIL>)**:

1. **Log in to VM Gateway**
2. **Navigate to Service Catalog**
3. **Find "Production PostgreSQL Database"**
4. **Click "Connect"**
5. **Verify connection established**:
   ```
   Connection established to localhost:5432
   Session expires in 8 hours
   ```

6. **Test read-only access**:
   ```sql
   -- This should work
   SELECT * FROM users LIMIT 10;
   
   -- This should fail
   INSERT INTO users (name) VALUES ('test');
   -- Error: permission denied
   ```

7. **Verify session info**:
   - Check "Active Connections" in web interface
   - See expiration time
   - See connection details

### Test 2: Read-Write Access (With Approval)

**As Senior Developer (<EMAIL>)**:

1. **Log in to VM Gateway**
2. **Navigate to Service Catalog**
3. **Find "Production PostgreSQL Database"**
4. **Click "Request Write Access"**
5. **Fill in approval form**:
   ```
   Reason: Fix data corruption in user table
   Ticket Number: JIRA-1234
   Queries: UPDATE users SET status='active' WHERE id=123
   ```

6. **Submit request**
7. **Wait for approval** (notification sent to DBAs)

**As DBA (<EMAIL>)**:

1. **Receive notification** (email, Slack, web interface)
2. **Review request**:
   - Requester: <EMAIL>
   - Reason: Fix data corruption
   - Ticket: JIRA-1234
   - Queries: UPDATE users...
3. **Verify ticket exists** and is valid
4. **Click "Approve"** (or "Deny" with reason)

**Back to Senior Developer**:

1. **Receive approval notification**
2. **Connection automatically established**
3. **Run queries**:
   ```sql
   -- This now works
   UPDATE users SET status='active' WHERE id=123;
   ```

4. **Monitor time remaining**:
   ```
   Session expires in 1 hour 45 minutes
   ```

5. **Receive warning** 10 minutes before disconnect:
   ```
   Warning: Your database session will expire in 10 minutes.
   Please save your work and disconnect.
   ```

6. **Auto-disconnect** after 2 hours:
   ```
   Session expired. Connection closed.
   ```

### Test 3: Emergency Access

**Scenario**: Production issue requires immediate database access

**As Developer**:

1. **Request emergency access**:
   ```bash
   curl -X POST https://vmgateway.example.com/api/v1/access/emergency \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "service_id": "svc_123",
       "reason": "Production outage - users cannot login",
       "severity": "critical",
       "incident_id": "INC-5678"
     }'
   ```

2. **Emergency access granted** (with reduced approval requirements)
3. **Access logged as emergency**
4. **Post-incident review required**

---

## Emergency Access

### Configuring Emergency Access

```yaml
# config/emergency-access.yaml
enabled: true

# Reduced approval requirements
emergency_approval:
  required_approvals: 1  # Instead of normal 2
  timeout_minutes: 15  # Faster timeout
  eligible_approvers:
    - role: "Database Administrator"
    - role: "On-Call Engineer"
  
  # Auto-approve for critical incidents
  auto_approve_conditions:
    - severity: "critical"
      incident_system: "pagerduty"
      incident_status: "triggered"

# Emergency access duration
access_duration_minutes: 60  # 1 hour
max_extensions: 2  # Can extend twice

# Notifications
notifications:
  emergency_access_granted:
    - all_admins
    - security_team
    - slack_channel: "#security-alerts"
  
  emergency_access_used:
    - security_team
    - audit_log

# Post-incident requirements
post_incident:
  review_required: true
  review_deadline_hours: 24
  review_template: "emergency-access-review"
```

### Emergency Access Procedure

1. **Request emergency access** with incident details
2. **Reduced approval** (1 approver instead of 2)
3. **Faster timeout** (15 minutes instead of 60)
4. **Access granted** with emergency flag
5. **Enhanced logging** (all queries logged)
6. **Post-incident review** required within 24 hours

---

## Audit and Compliance

### Audit Logging

All database access is comprehensively logged:

```json
{
  "event_id": "evt_abc123",
  "timestamp": "2025-11-09T15:30:00Z",
  "event_type": "database_access",
  "user": {
    "id": "usr_123",
    "email": "<EMAIL>",
    "name": "Jane Smith",
    "roles": ["Database Writer"]
  },
  "service": {
    "id": "svc_123",
    "name": "Production PostgreSQL",
    "type": "database",
    "vm": "prod-db-01"
  },
  "connection": {
    "id": "conn_xyz789",
    "established_at": "2025-11-09T15:30:00Z",
    "disconnected_at": "2025-11-09T17:30:00Z",
    "duration_minutes": 120,
    "auto_disconnected": true
  },
  "approval": {
    "workflow": "database-write-access",
    "request_id": "req_456",
    "approved_by": "<EMAIL>",
    "approved_at": "2025-11-09T15:25:00Z",
    "reason": "Fix data corruption in user table",
    "ticket": "JIRA-1234"
  },
  "queries": [
    {
      "timestamp": "2025-11-09T15:31:00Z",
      "query": "SELECT * FROM users WHERE id=123",
      "duration_ms": 15,
      "rows_affected": 1
    },
    {
      "timestamp": "2025-11-09T15:32:00Z",
      "query": "UPDATE users SET status='active' WHERE id=123",
      "duration_ms": 25,
      "rows_affected": 1
    }
  ],
  "metadata": {
    "ip_address": "*************",
    "user_agent": "VM Gateway Desktop Client 1.0",
    "session_id": "sess_789"
  }
}
```

### Compliance Reports

Generate compliance reports:

```bash
# Database access report for last month
python -m vm_gateway.cli generate-report \
  --type database-access \
  --start-date 2025-10-01 \
  --end-date 2025-10-31 \
  --output database-access-october-2025.pdf

# Approval workflow report
python -m vm_gateway.cli generate-report \
  --type approval-workflows \
  --workflow database-write-access \
  --start-date 2025-10-01 \
  --end-date 2025-10-31 \
  --output approvals-october-2025.pdf
```

**Report Contents**:
- Total database connections
- Connections by user
- Connections by service
- Approval requests (approved/denied/expired)
- Average approval time
- Emergency access usage
- Policy violations
- Audit log completeness

### Alerting on Suspicious Activity

```yaml
# config/security-alerts.yaml
alerts:
  # Alert on unusual query patterns
  - name: "Unusual Database Activity"
    condition: |
      query_count > 1000 in 1 hour
      OR
      rows_affected > 10000 in 1 query
    severity: "warning"
    notify:
      - security_team
      - dba_team
  
  # Alert on access outside business hours
  - name: "After-Hours Database Access"
    condition: |
      connection_time NOT IN business_hours
      AND user_role != "Database Administrator"
    severity: "info"
    notify:
      - security_team
  
  # Alert on failed approval attempts
  - name: "Multiple Approval Denials"
    condition: |
      approval_denials > 3 in 24 hours
      FOR same_user
    severity: "warning"
    notify:
      - security_team
      - user_manager
```

---

## Related Documentation

- **[RBAC Configuration](/docs/06-authentication/07-rbac.md)**: Detailed RBAC setup
- **[Approval Workflows](/docs/06-authentication/08-approval-workflows.md)**: Workflow configuration
- **[Audit Logging](/docs/07-secrets-management/07-audit.md)**: Audit and compliance
- **[Client Usage](/docs/05-client/01-overview.md)**: Using the desktop client
- **[API Reference](/docs/09-api-reference/01-overview.md)**: API for automation
- **[Security Best Practices](/docs/10-development/07-security.md)**: Security recommendations

## Summary

You've successfully implemented a secure, auditable database access workflow! This setup provides:

**Security**:
- Approval required for write access
- Time-limited sessions
- Auto-disconnect after time limit
- Emergency access procedure
- Comprehensive audit logging

**Usability**:
- Simple request process
- Clear approval workflow
- Automatic connection establishment
- Warning before disconnect
- Self-service for read-only access

**Compliance**:
- Complete audit trail
- Approval documentation
- Query logging
- Compliance reports
- Security alerting

**Best Practices**:
- Principle of least privilege
- Separation of duties
- Time-based access control
- Approval workflows for sensitive access
- Comprehensive logging
- Regular access reviews

This workflow can be adapted for other sensitive resources (SSH access, cloud consoles, etc.) using the same principles!

