---
title: "Performance Optimization"
section: "Development"
order: 6
tags: ["development", "performance", "optimization"]
last_updated: "2025-11-08"
---

# Performance Optimization

This guide covers performance optimization techniques for the VM Network Gateway & Access Control Platform, including profiling, caching strategies, database optimization, and scalability considerations.

## Performance Goals

### Target Metrics

- **API Response Time**: < 200ms (p95)
- **Database Queries**: < 50ms (p95)
- **Page Load Time**: < 2 seconds
- **Time to Interactive**: < 3 seconds
- **Throughput**: > 1000 requests/second
- **Memory Usage**: < 512MB per process
- **CPU Usage**: < 70% under normal load

## Python Performance

### Profiling

**CPU Profiling with cProfile:**

```python
import cProfile
import pstats
from pstats import SortKey

# Profile a function
profiler = cProfile.Profile()
profiler.enable()

result = expensive_function()

profiler.disable()
stats = pstats.Stats(profiler)
stats.sort_stats(SortKey.CUMULATIVE)
stats.print_stats(20)

# Profile entire application
# python -m cProfile -o output.prof script.py
# python -m pstats output.prof
```

**Memory Profiling:**

```python
from memory_profiler import profile

@profile
def memory_intensive_function():
    large_list = [i for i in range(1000000)]
    return sum(large_list)

# Run: python -m memory_profiler script.py
```

### Optimization Techniques

**1. Use Generators for Large Datasets:**

```python
# Bad - loads everything into memory
def get_all_services():
    return db.query(Service).all()

# Good - yields one at a time
def get_all_services():
    for service in db.query(Service).yield_per(100):
        yield service
```

**2. Batch Database Operations:**

```python
# Bad - N+1 queries
for service_id in service_ids:
    service = db.query(Service).filter(Service.id == service_id).first()
    process(service)

# Good - single query
services = db.query(Service).filter(Service.id.in_(service_ids)).all()
for service in services:
    process(service)
```

**3. Use Async for I/O Operations:**

```python
import asyncio
import httpx

# Bad - sequential
def fetch_metrics(service_ids):
    results = []
    for sid in service_ids:
        response = requests.get(f"/api/services/{sid}/metrics")
        results.append(response.json())
    return results

# Good - concurrent
async def fetch_metrics(service_ids):
    async with httpx.AsyncClient() as client:
        tasks = [
            client.get(f"/api/services/{sid}/metrics")
            for sid in service_ids
        ]
        responses = await asyncio.gather(*tasks)
        return [r.json() for r in responses]
```

**4. Cache Expensive Computations:**

```python
from functools import lru_cache

@lru_cache(maxsize=128)
def calculate_service_score(service_id: str) -> float:
    # Expensive calculation
    return score

# Clear cache when needed
calculate_service_score.cache_clear()
```

## Database Optimization

### Query Optimization

**1. Use Indexes:**

```sql
-- Create indexes for frequently queried columns
CREATE INDEX idx_services_vm_id ON services(vm_id);
CREATE INDEX idx_services_type ON services(type);
CREATE INDEX idx_services_status ON services(status);
CREATE INDEX idx_connections_user_id ON connections(user_id);
CREATE INDEX idx_connections_service_id ON connections(service_id);

-- Composite indexes for common query patterns
CREATE INDEX idx_services_vm_type ON services(vm_id, type);
CREATE INDEX idx_connections_user_status ON connections(user_id, status);
```

**2. Optimize Queries:**

```python
# Bad - loads all columns
services = db.query(Service).all()

# Good - only load needed columns
services = db.query(Service.id, Service.name, Service.type).all()

# Bad - N+1 problem
services = db.query(Service).all()
for service in services:
    vm = service.vm  # Triggers additional query

# Good - eager loading
services = db.query(Service).options(
    joinedload(Service.vm)
).all()
```

**3. Use Connection Pooling:**

```python
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=40,
    pool_pre_ping=True,
    pool_recycle=3600
)
```

### Database Maintenance

```sql
-- Analyze tables for query planner
ANALYZE services;
ANALYZE connections;

-- Vacuum to reclaim space
VACUUM ANALYZE services;

-- Reindex
REINDEX TABLE services;

-- Check table bloat
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## Caching Strategies

### Redis Caching

```python
import redis
import json
from functools import wraps

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_result(ttl=300):
    """Cache function result in Redis."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = f"{func.__name__}:{args}:{kwargs}"
            
            # Try to get from cache
            cached = redis_client.get(cache_key)
            if cached:
                return json.loads(cached)
            
            # Execute function
            result = await func(*args, **kwargs)
            
            # Store in cache
            redis_client.setex(
                cache_key,
                ttl,
                json.dumps(result)
            )
            
            return result
        return wrapper
    return decorator

@cache_result(ttl=300)
async def get_service_metrics(service_id: str):
    # Expensive operation
    return metrics
```

### Application-Level Caching

```python
from cachetools import TTLCache, cached

# In-memory cache with TTL
cache = TTLCache(maxsize=1000, ttl=300)

@cached(cache)
def get_user_permissions(user_id: str):
    # Expensive permission calculation
    return permissions

# Cache invalidation
def update_user_permissions(user_id: str, new_permissions):
    # Update database
    db.update(...)
    
    # Invalidate cache
    cache_key = (user_id,)
    if cache_key in cache:
        del cache[cache_key]
```

## Frontend Performance

### React Optimization

**1. Use React.memo:**

```typescript
import React from 'react';

// Memoize component to prevent unnecessary re-renders
export const ServiceCard = React.memo(({ service, onConnect }) => {
  return (
    <div className="service-card">
      <h3>{service.name}</h3>
      <button onClick={() => onConnect(service.id)}>Connect</button>
    </div>
  );
});
```

**2. Use useMemo and useCallback:**

```typescript
import { useMemo, useCallback } from 'react';

function ServiceList({ services, onConnect }) {
  // Memoize expensive computation
  const sortedServices = useMemo(() => {
    return services.sort((a, b) => a.name.localeCompare(b.name));
  }, [services]);
  
  // Memoize callback to prevent child re-renders
  const handleConnect = useCallback((serviceId) => {
    onConnect(serviceId);
  }, [onConnect]);
  
  return (
    <div>
      {sortedServices.map(service => (
        <ServiceCard
          key={service.id}
          service={service}
          onConnect={handleConnect}
        />
      ))}
    </div>
  );
}
```

**3. Code Splitting:**

```typescript
import { lazy, Suspense } from 'react';

// Lazy load components
const Dashboard = lazy(() => import('./pages/Dashboard'));
const Services = lazy(() => import('./pages/Services'));
const Connections = lazy(() => import('./pages/Connections'));

function App() {
  return (
    <Suspense fallback={<Loading />}>
      <Routes>
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/services" element={<Services />} />
        <Route path="/connections" element={<Connections />} />
      </Routes>
    </Suspense>
  );
}
```

### Bundle Optimization

```javascript
// vite.config.ts
import { defineConfig } from 'vite';

export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          'ui-vendor': ['@headlessui/react', '@heroicons/react'],
        },
      },
    },
    chunkSizeWarningLimit: 1000,
  },
  optimizeDeps: {
    include: ['react', 'react-dom'],
  },
});
```

## API Performance

### Response Compression

```python
from fastapi import FastAPI
from fastapi.middleware.gzip import GZIPMiddleware

app = FastAPI()

# Enable gzip compression
app.add_middleware(GZIPMiddleware, minimum_size=1000)
```

### Pagination

```python
from fastapi import Query

@app.get("/api/v1/services")
async def list_services(
    page: int = Query(1, ge=1),
    limit: int = Query(50, ge=1, le=100)
):
    offset = (page - 1) * limit
    
    services = db.query(Service)\
        .offset(offset)\
        .limit(limit)\
        .all()
    
    total = db.query(Service).count()
    
    return {
        "services": services,
        "pagination": {
            "page": page,
            "limit": limit,
            "total": total,
            "total_pages": (total + limit - 1) // limit
        }
    }
```

### Rate Limiting

```python
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

@app.get("/api/v1/services")
@limiter.limit("100/minute")
async def list_services(request: Request):
    return {"services": []}
```

## Monitoring and Profiling

### Application Metrics

```python
from prometheus_client import Counter, Histogram, Gauge
import time

# Define metrics
request_count = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status']
)

request_duration = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint']
)

active_connections = Gauge(
    'active_connections',
    'Number of active connections'
)

# Middleware to collect metrics
@app.middleware("http")
async def metrics_middleware(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    duration = time.time() - start_time
    
    request_count.labels(
        method=request.method,
        endpoint=request.url.path,
        status=response.status_code
    ).inc()
    
    request_duration.labels(
        method=request.method,
        endpoint=request.url.path
    ).observe(duration)
    
    return response
```

### Performance Testing

```python
# Using locust for load testing
from locust import HttpUser, task, between

class VMGatewayUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        # Login
        response = self.client.post("/api/v1/auth/login", json={
            "username": "<EMAIL>",
            "password": "password123"
        })
        self.token = response.json()["tokens"]["access_token"]
        self.headers = {"Authorization": f"Bearer {self.token}"}
    
    @task(3)
    def list_services(self):
        self.client.get("/api/v1/services", headers=self.headers)
    
    @task(1)
    def create_connection(self):
        self.client.post(
            "/api/v1/connections",
            headers=self.headers,
            json={
                "service_id": "svc_abc123",
                "duration": 3600
            }
        )

# Run: locust -f locustfile.py --host=http://localhost:8000
```

## Scalability

### Horizontal Scaling

```yaml
# docker-compose.yml for multiple instances
version: '3.8'

services:
  controller:
    image: vm-gateway-controller
    deploy:
      replicas: 3
    environment:
      - DATABASE_URL=postgresql://...
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis

  nginx:
    image: nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - controller
```

**nginx.conf:**

```nginx
upstream controller {
    least_conn;
    server controller:8000 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    
    location / {
        proxy_pass http://controller;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

### Database Scaling

```python
# Read replicas
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Write database
write_engine = create_engine(PRIMARY_DATABASE_URL)
WriteSession = sessionmaker(bind=write_engine)

# Read replicas
read_engine = create_engine(REPLICA_DATABASE_URL)
ReadSession = sessionmaker(bind=read_engine)

# Use read replica for queries
def get_services():
    session = ReadSession()
    return session.query(Service).all()

# Use primary for writes
def create_service(data):
    session = WriteSession()
    service = Service(**data)
    session.add(service)
    session.commit()
    return service
```

## Best Practices

1. **Profile Before Optimizing**: Measure to find bottlenecks
2. **Optimize Hot Paths**: Focus on frequently executed code
3. **Cache Wisely**: Cache expensive operations with appropriate TTL
4. **Use Async**: Leverage async/await for I/O operations
5. **Batch Operations**: Reduce round trips to database/API
6. **Monitor Continuously**: Track performance metrics in production
7. **Load Test**: Test under realistic load conditions
8. **Scale Horizontally**: Design for horizontal scaling
9. **Optimize Queries**: Use indexes and efficient queries
10. **Minimize Payload**: Return only necessary data

## Related Documentation

- [Development Setup](./01-setup.md) - Environment setup
- [Debugging Guide](./05-debugging.md) - Debugging techniques
- [Security Guide](./07-security.md) - Security best practices
