---
title: "Webhooks API"
section: "API Reference"
order: 8
tags: ["api", "webhooks", "events", "endpoints"]
last_updated: "2025-11-08"
---

# Webhooks API

The Webhooks API allows you to subscribe to platform events and receive real-time notifications via HTTP callbacks. This enables integration with external systems, custom automation workflows, and event-driven architectures.

## Base URL

```
https://your-gateway.example.com/api/v1/webhooks
```

## Webhook Events

The platform emits events for various activities:

### VM Events
- `vm.registered` - New VM registered
- `vm.unregistered` - VM removed from platform
- `vm.status_changed` - VM status changed (online/offline/degraded)
- `vm.agent_updated` - Agent software updated

### Service Events
- `service.discovered` - New service discovered
- `service.removed` - Service no longer detected
- `service.status_changed` - Service health status changed
- `service.version_changed` - Service version updated

### Connection Events
- `connection.established` - New connection created
- `connection.terminated` - Connection ended
- `connection.extended` - Connection duration extended
- `connection.failed` - Connection attempt failed

### Authentication Events
- `auth.login_success` - User logged in successfully
- `auth.login_failed` - Login attempt failed
- `auth.logout` - User logged out
- `auth.mfa_enabled` - MFA enabled for user
- `auth.password_changed` - User password changed

### Access Control Events
- `access.granted` - Access permission granted
- `access.denied` - Access attempt denied
- `access.approval_requested` - Approval workflow initiated
- `access.approval_granted` - Access request approved
- `access.approval_denied` - Access request denied

### Alert Events
- `alert.triggered` - Alert rule triggered
- `alert.resolved` - Alert condition resolved
- `alert.acknowledged` - Alert acknowledged by user

### Configuration Events
- `config.updated` - Configuration changed
- `config.feature_flag_changed` - Feature flag toggled

## Endpoints

### POST /webhooks

Create a new webhook subscription.

**Request:**

```http
POST /api/v1/webhooks HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "name": "Slack Production Alerts",
  "url": "https://hooks.slack.com/services/T00000000/B00000000/XXXXXXXXXXXXXXXXXXXX",
  "events": [
    "service.status_changed",
    "alert.triggered",
    "connection.failed"
  ],
  "filters": {
    "environment": "production",
    "severity": ["warning", "critical"]
  },
  "enabled": true,
  "secret": "webhook_secret_abc123",
  "headers": {
    "X-Custom-Header": "value"
  },
  "retry_policy": {
    "max_attempts": 3,
    "backoff_multiplier": 2,
    "initial_delay_seconds": 1
  }
}
```

**Request Body Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `name` | string | Yes | Descriptive name for the webhook |
| `url` | string | Yes | HTTPS endpoint to receive events |
| `events` | array | Yes | List of event types to subscribe to |
| `filters` | object | No | Event filtering criteria |
| `enabled` | boolean | No | Enable/disable webhook (default: true) |
| `secret` | string | No | Secret for HMAC signature verification |
| `headers` | object | No | Custom HTTP headers to include |
| `retry_policy` | object | No | Retry configuration for failed deliveries |

**Response:**

```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,
  "webhook": {
    "id": "webhook_abc123def456",
    "name": "Slack Production Alerts",
    "url": "https://hooks.slack.com/services/T00000000/B00000000/XXXXXXXXXXXXXXXXXXXX",
    "events": [
      "service.status_changed",
      "alert.triggered",
      "connection.failed"
    ],
    "filters": {
      "environment": "production",
      "severity": ["warning", "critical"]
    },
    "enabled": true,
    "status": "active",
    "created_at": "2025-11-08T16:00:00Z",
    "created_by": "usr_1234567890"
  },
  "message": "Webhook created successfully"
}
```

---

### GET /webhooks

List all webhook subscriptions.

**Request:**

```http
GET /api/v1/webhooks?status=active HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `status` | string | Filter by status: `active`, `disabled`, `failed` | all |
| `event` | string | Filter by event type | all |
| `page` | integer | Page number (1-indexed) | 1 |
| `limit` | integer | Results per page (max 100) | 50 |

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "webhooks": [
    {
      "id": "webhook_abc123def456",
      "name": "Slack Production Alerts",
      "url": "https://hooks.slack.com/services/T00000000/B00000000/XXXXXXXXXXXXXXXXXXXX",
      "events": [
        "service.status_changed",
        "alert.triggered",
        "connection.failed"
      ],
      "enabled": true,
      "status": "active",
      "statistics": {
        "total_deliveries": 1250,
        "successful_deliveries": 1245,
        "failed_deliveries": 5,
        "last_delivery": "2025-11-08T15:55:00Z",
        "last_success": "2025-11-08T15:55:00Z",
        "last_failure": "2025-11-07T10:30:00Z"
      },
      "created_at": "2025-10-01T10:00:00Z",
      "updated_at": "2025-11-08T16:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 1,
    "total_pages": 1
  }
}
```

---

### GET /webhooks/{webhook_id}

Get detailed information about a specific webhook.

**Request:**

```http
GET /api/v1/webhooks/webhook_abc123def456 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "webhook": {
    "id": "webhook_abc123def456",
    "name": "Slack Production Alerts",
    "description": "Send critical production alerts to Slack",
    "url": "https://hooks.slack.com/services/T00000000/B00000000/XXXXXXXXXXXXXXXXXXXX",
    "events": [
      "service.status_changed",
      "alert.triggered",
      "connection.failed"
    ],
    "filters": {
      "environment": "production",
      "severity": ["warning", "critical"]
    },
    "enabled": true,
    "status": "active",
    "secret_configured": true,
    "headers": {
      "X-Custom-Header": "value"
    },
    "retry_policy": {
      "max_attempts": 3,
      "backoff_multiplier": 2,
      "initial_delay_seconds": 1
    },
    "statistics": {
      "total_deliveries": 1250,
      "successful_deliveries": 1245,
      "failed_deliveries": 5,
      "success_rate": 99.6,
      "average_response_time_ms": 145,
      "last_delivery": "2025-11-08T15:55:00Z",
      "last_success": "2025-11-08T15:55:00Z",
      "last_failure": "2025-11-07T10:30:00Z",
      "last_failure_reason": "Connection timeout"
    },
    "created_at": "2025-10-01T10:00:00Z",
    "created_by": "usr_1234567890",
    "updated_at": "2025-11-08T16:00:00Z",
    "updated_by": "usr_1234567890"
  }
}
```

---

### PATCH /webhooks/{webhook_id}

Update a webhook subscription.

**Request:**

```http
PATCH /api/v1/webhooks/webhook_abc123def456 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "name": "Slack Production Alerts (Updated)",
  "events": [
    "service.status_changed",
    "alert.triggered",
    "connection.failed",
    "vm.status_changed"
  ],
  "enabled": true
}
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "webhook": {
    "id": "webhook_abc123def456",
    "name": "Slack Production Alerts (Updated)",
    "events": [
      "service.status_changed",
      "alert.triggered",
      "connection.failed",
      "vm.status_changed"
    ],
    "updated_at": "2025-11-08T16:05:00Z"
  },
  "message": "Webhook updated successfully"
}
```

---

### DELETE /webhooks/{webhook_id}

Delete a webhook subscription.

**Request:**

```http
DELETE /api/v1/webhooks/webhook_abc123def456 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "message": "Webhook deleted successfully"
}
```

---

### POST /webhooks/{webhook_id}/test

Send a test event to verify webhook configuration.

**Request:**

```http
POST /api/v1/webhooks/webhook_abc123def456/test HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "event_type": "service.status_changed"
}
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "test_delivery": {
    "webhook_id": "webhook_abc123def456",
    "event_type": "service.status_changed",
    "delivered_at": "2025-11-08T16:10:00Z",
    "response_code": 200,
    "response_time_ms": 142,
    "response_body": "ok"
  },
  "message": "Test event delivered successfully"
}
```

---

### GET /webhooks/{webhook_id}/deliveries

Get delivery history for a webhook.

**Request:**

```http
GET /api/v1/webhooks/webhook_abc123def456/deliveries?status=failed&limit=100 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `status` | string | Filter by status: `success`, `failed`, `pending` | all |
| `event_type` | string | Filter by event type | all |
| `from` | string | Start time (ISO 8601) | 24 hours ago |
| `to` | string | End time (ISO 8601) | now |
| `page` | integer | Page number (1-indexed) | 1 |
| `limit` | integer | Results per page (max 100) | 50 |

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "deliveries": [
    {
      "id": "delivery_abc123",
      "webhook_id": "webhook_abc123def456",
      "event_type": "service.status_changed",
      "event_id": "event_xyz789",
      "status": "success",
      "attempt": 1,
      "request": {
        "url": "https://hooks.slack.com/services/T00000000/B00000000/XXXXXXXXXXXXXXXXXXXX",
        "method": "POST",
        "headers": {
          "Content-Type": "application/json",
          "X-Webhook-Signature": "sha256=abc123...",
          "X-Webhook-ID": "webhook_abc123def456",
          "X-Event-Type": "service.status_changed"
        },
        "body_size": 512
      },
      "response": {
        "status_code": 200,
        "headers": {
          "Content-Type": "text/plain"
        },
        "body": "ok",
        "response_time_ms": 142
      },
      "delivered_at": "2025-11-08T15:55:00Z"
    },
    {
      "id": "delivery_def456",
      "webhook_id": "webhook_abc123def456",
      "event_type": "alert.triggered",
      "event_id": "event_abc456",
      "status": "failed",
      "attempt": 3,
      "request": {
        "url": "https://hooks.slack.com/services/T00000000/B00000000/XXXXXXXXXXXXXXXXXXXX",
        "method": "POST",
        "headers": {
          "Content-Type": "application/json"
        },
        "body_size": 645
      },
      "response": {
        "status_code": 0,
        "error": "Connection timeout after 30 seconds"
      },
      "delivered_at": "2025-11-07T10:30:00Z",
      "next_retry_at": null
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 100,
    "total": 2,
    "total_pages": 1
  }
}
```

---

### POST /webhooks/{webhook_id}/deliveries/{delivery_id}/retry

Manually retry a failed webhook delivery.

**Request:**

```http
POST /api/v1/webhooks/webhook_abc123def456/deliveries/delivery_def456/retry HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 202 Accepted
Content-Type: application/json

{
  "success": true,
  "delivery": {
    "id": "delivery_def456",
    "status": "pending",
    "retry_scheduled_at": "2025-11-08T16:15:00Z"
  },
  "message": "Delivery retry scheduled"
}
```

---

## Webhook Payload Format

All webhook events are delivered as HTTP POST requests with the following structure:

```json
{
  "event_id": "event_abc123def456",
  "event_type": "service.status_changed",
  "timestamp": "2025-11-08T15:55:00Z",
  "webhook_id": "webhook_abc123def456",
  "data": {
    "service": {
      "id": "svc_abc123def456",
      "name": "postgres-main",
      "type": "database",
      "vm": {
        "id": "vm_abc123",
        "name": "prod-db-01"
      }
    },
    "previous_status": "healthy",
    "current_status": "warning",
    "reason": "High memory usage (85%)",
    "details": {
      "memory_percent": 85.2,
      "threshold": 80
    }
  },
  "metadata": {
    "environment": "production",
    "severity": "warning"
  }
}
```

### Payload Fields

| Field | Type | Description |
|-------|------|-------------|
| `event_id` | string | Unique identifier for the event |
| `event_type` | string | Type of event (e.g., `service.status_changed`) |
| `timestamp` | string | ISO 8601 timestamp when event occurred |
| `webhook_id` | string | ID of the webhook subscription |
| `data` | object | Event-specific data payload |
| `metadata` | object | Additional context and filtering metadata |

## Security

### Signature Verification

All webhook requests include an `X-Webhook-Signature` header containing an HMAC-SHA256 signature of the request body. Verify this signature to ensure the request came from the platform:

**Python Example:**

```python
import hmac
import hashlib

def verify_webhook_signature(payload, signature, secret):
    expected_signature = hmac.new(
        secret.encode('utf-8'),
        payload.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(
        f"sha256={expected_signature}",
        signature
    )

# In your webhook handler
payload = request.body
signature = request.headers.get('X-Webhook-Signature')
secret = 'webhook_secret_abc123'

if verify_webhook_signature(payload, signature, secret):
    # Process webhook
    pass
else:
    # Reject webhook
    return 401
```

**Node.js Example:**

```javascript
const crypto = require('crypto');

function verifyWebhookSignature(payload, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(`sha256=${expectedSignature}`),
    Buffer.from(signature)
  );
}

// In your webhook handler
const payload = req.body;
const signature = req.headers['x-webhook-signature'];
const secret = 'webhook_secret_abc123';

if (verifyWebhookSignature(payload, signature, secret)) {
  // Process webhook
} else {
  // Reject webhook
  res.status(401).send('Invalid signature');
}
```

### Best Practices

1. **Always verify signatures** to prevent spoofed webhooks
2. **Use HTTPS endpoints** for webhook URLs
3. **Implement idempotency** using the `event_id` to handle duplicate deliveries
4. **Respond quickly** (within 5 seconds) to avoid timeouts
5. **Process asynchronously** - acknowledge receipt immediately, process later
6. **Monitor delivery failures** and investigate patterns
7. **Rotate secrets** periodically
8. **Implement rate limiting** on your webhook endpoint
9. **Log all webhook deliveries** for debugging
10. **Test webhooks** before enabling in production

## Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `WEBHOOK_NOT_FOUND` | 404 | Webhook with specified ID does not exist |
| `INVALID_WEBHOOK_URL` | 400 | Webhook URL is invalid or not HTTPS |
| `INVALID_EVENT_TYPE` | 400 | Event type is not supported |
| `WEBHOOK_DELIVERY_FAILED` | 500 | Failed to deliver webhook after all retries |
| `INSUFFICIENT_PERMISSIONS` | 403 | User lacks permission to manage webhooks |

## Code Examples

### Python Flask Webhook Handler

```python
from flask import Flask, request, jsonify
import hmac
import hashlib
import json

app = Flask(__name__)
WEBHOOK_SECRET = 'webhook_secret_abc123'

def verify_signature(payload, signature):
    expected = hmac.new(
        WEBHOOK_SECRET.encode(),
        payload,
        hashlib.sha256
    ).hexdigest()
    return hmac.compare_digest(f"sha256={expected}", signature)

@app.route('/webhook', methods=['POST'])
def handle_webhook():
    # Verify signature
    signature = request.headers.get('X-Webhook-Signature')
    if not verify_signature(request.data, signature):
        return jsonify({'error': 'Invalid signature'}), 401
    
    # Parse event
    event = request.json
    event_type = event['event_type']
    event_id = event['event_id']
    
    # Check for duplicate (idempotency)
    if is_duplicate_event(event_id):
        return jsonify({'status': 'already_processed'}), 200
    
    # Process event asynchronously
    process_event_async(event)
    
    # Respond immediately
    return jsonify({'status': 'accepted'}), 200

def process_event_async(event):
    # Queue for background processing
    event_queue.put(event)

if __name__ == '__main__':
    app.run(port=5000)
```

### Node.js Express Webhook Handler

```javascript
const express = require('express');
const crypto = require('crypto');
const app = express();

const WEBHOOK_SECRET = 'webhook_secret_abc123';
const processedEvents = new Set();

app.use(express.json());

function verifySignature(payload, signature) {
  const expected = crypto
    .createHmac('sha256', WEBHOOK_SECRET)
    .update(JSON.stringify(payload))
    .digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(`sha256=${expected}`),
    Buffer.from(signature)
  );
}

app.post('/webhook', (req, res) => {
  // Verify signature
  const signature = req.headers['x-webhook-signature'];
  if (!verifySignature(req.body, signature)) {
    return res.status(401).json({ error: 'Invalid signature' });
  }
  
  const event = req.body;
  const eventId = event.event_id;
  
  // Check for duplicate (idempotency)
  if (processedEvents.has(eventId)) {
    return res.json({ status: 'already_processed' });
  }
  
  // Mark as processed
  processedEvents.add(eventId);
  
  // Process event asynchronously
  processEventAsync(event);
  
  // Respond immediately
  res.json({ status: 'accepted' });
});

async function processEventAsync(event) {
  // Queue for background processing
  await eventQueue.add(event);
}

app.listen(5000, () => {
  console.log('Webhook handler listening on port 5000');
});
```

## Related Documentation

- [API Overview](./01-overview.md) - API design principles
- [Authentication API](./02-authentication.md) - Authentication methods
- [Metrics API](./06-metrics.md) - Metrics and monitoring
