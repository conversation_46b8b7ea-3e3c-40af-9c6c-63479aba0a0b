# VM Network Gateway & Access Control Platform

A self-hosted VM networking and access management platform that automatically discovers services, provides granular RBAC-controlled access, and enables secure remote port forwarding through a centralized web interface.

## Overview

The VM Network Gateway & Access Control Platform is a comprehensive solution for managing and securing access to services across multiple virtual machines. It functions as a self-hosted alternative to commercial solutions like Twingate, with enhanced service discovery, monitoring, and management capabilities.

## Core Components

### 1. Agent Software
Lightweight daemon installed on VMs to:
- Automatically discover all running services
- Intelligently classify services based on process names and port conventions
- Collect comprehensive metrics and health data
- Manage secure connections

### 2. Controller & Web Interface
Centralized management system providing:
- Enterprise-grade RBAC with granular permission controls
- Web-based access to HTTP/HTTPS services through built-in proxy
- Real-time monitoring and alerting
- Comprehensive audit logging
- Advanced secrets management

### 3. Client Software
Desktop application enabling:
- Secure port forwarding of any TCP/UDP service
- Cross-platform support (Windows, macOS, Linux)
- Connection management and profiles
- Local authentication and session handling

## Key Features

- **Automatic Service Discovery**: Scans and identifies all services running on monitored VMs
- **Intelligent Classification**: Multi-tier classification engine with ML-based detection
- **Zero-Trust Security**: mTLS authentication, encrypted tunnels, and comprehensive access controls
- **RBAC System**: Fine-grained permissions with approval workflows and time-based access
- **Multi-VM Deployment**: Support for central, distributed, and hybrid deployment models
- **Real-Time Monitoring**: Live metrics, health checks, and alerting
- **Secrets Management**: Integrated secrets storage with vault support
- **Responsive Web UI**: Clean, professional interface accessible from any device

## Technology Stack

- **Backend**: Python 3.11+ with FastAPI
- **Frontend**: React with TypeScript and Tailwind CSS
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Real-Time**: WebSockets and Redis
- **Security**: mTLS, JWT, AES-256-GCM encryption
- **Deployment**: Docker, Kubernetes (optional)

## Version

Current Version: **a.0.0-1** (Alpha)

This version establishes the foundational project structure and comprehensive documentation system.

## Version Numbering

Format: `[PHASE].[MAJOR].[MINOR]-[BUILD]`

- **Phase**: a (alpha), b (beta), c (release candidate), r (release)
- **Major**: Increments for breaking changes or complete platform overhauls
- **Minor**: Increments for new features or significant enhancements
- **Build**: Incremental build number

## Getting Started

### Prerequisites

- Python 3.11 or higher
- PostgreSQL 14+
- Redis 7+
- Node.js 18+ (for frontend development)

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd vm-gateway

# Install Python dependencies
pip install -e .

# View documentation
python scripts/serve_docs.py
```

Visit `http://localhost:80` to access the documentation viewer.

## Project Structure

```
vm-gateway/
├── docs/              # Comprehensive documentation
├── src/               # Source code
│   ├── agent/        # Agent component
│   ├── controller/   # Controller component
│   ├── client/       # Client component
│   ├── shared/       # Shared utilities
│   └── doc_viewer/   # Documentation viewer
├── tests/            # Test suite
├── config/           # Configuration files
├── scripts/          # Utility scripts
└── logs/             # Application logs
```

## Documentation

Comprehensive documentation is available through the built-in documentation viewer. The documentation covers:

- System architecture and design patterns
- Component deep-dives (Agent, Controller, Client)
- Authentication and authorization
- Secrets management
- Deployment strategies
- API reference
- Development guidelines

## Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Security

For security concerns, please review our security documentation in `docs/10-development/security.md` and follow responsible disclosure practices.

## Support

For issues, questions, or contributions, please use the GitHub issue tracker.

---

**Note**: This is alpha software under active development. Features and APIs may change without notice.
