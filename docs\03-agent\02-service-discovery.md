---
title: "Service Discovery Engine"
section: "Agent"
order: 2
tags: ["agent", "discovery", "scanning", "ports"]
last_updated: "2025-11-08"
---

# Service Discovery Engine

## Introduction

The Service Discovery Engine is the core component of the VM Gateway Agent responsible for automatically identifying all services running on a monitored VM. Unlike traditional network scanning tools that operate externally, the discovery engine runs locally on each VM, providing deep visibility into processes, ports, and service configurations with minimal overhead.

The engine continuously monitors the VM for listening ports, maps them to running processes, and detects changes in real-time—all without requiring any manual configuration or service registration.

## Discovery Architecture

### Overview

```
┌─────────────────────────────────────────────────────────────┐
│              Service Discovery Engine                       │
│                                                             │
│  ┌───────────────────────────────────────────────────────┐ │
│  │           Port Scanner                                │ │
│  │  - Enumerate listening TCP/UDP ports                  │ │
│  │  - Identify binding addresses                         │ │
│  │  - Support IPv4 and IPv6                              │ │
│  └───────────────────┬───────────────────────────────────┘ │
│                      │                                       │
│                      ▼                                       │
│  ┌───────────────────────────────────────────────────────┐ │
│  │        Process Identifier                             │ │
│  │  - Map ports to PIDs                                  │ │
│  │  - Extract process metadata                           │ │
│  │  - Detect containerized processes                     │ │
│  └───────────────────┬───────────────────────────────────┘ │
│                      │                                       │
│                      ▼                                       │
│  ┌───────────────────────────────────────────────────────┐ │
│  │        Metadata Collector                             │ │
│  │  - Executable path and name                           │ │
│  │  - Command-line arguments                             │ │
│  │  - Process owner/user                                 │ │
│  │  - Parent process information                         │ │
│  │  - Working directory                                  │ │
│  │  - Environment variables (optional)                   │ │
│  └───────────────────┬───────────────────────────────────┘ │
│                      │                                       │
│                      ▼                                       │
│  ┌───────────────────────────────────────────────────────┐ │
│  │        Change Detector                                │ │
│  │  - Compare with previous scan                         │ │
│  │  - Identify new services                              │ │
│  │  - Detect stopped services                            │ │
│  │  - Track configuration changes                        │ │
│  └───────────────────┬───────────────────────────────────┘ │
│                      │                                       │
│                      ▼                                       │
│  ┌───────────────────────────────────────────────────────┐ │
│  │        Local Cache (SQLite)                           │ │
│  │  - Store discovered services                          │ │
│  │  - Track service history                              │ │
│  │  - Enable change detection                            │ │
│  └───────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Port Scanning

### Implementation

The port scanner uses the `psutil` library to access native OS APIs for maximum efficiency and cross-platform compatibility.

**Key Features:**
- **Non-Intrusive**: Uses OS-provided APIs rather than network probes
- **Fast**: Completes full scan in 1-5 seconds on typical VMs
- **Accurate**: Sees all listening ports, including localhost-only bindings
- **Low Overhead**: Minimal CPU and memory usage during scans

### Scanning Process

```python
import psutil
from typing import List, Dict, Any

def scan_listening_ports() -> List[Dict[str, Any]]:
    """
    Scan for all listening TCP and UDP ports on the system.
    
    Returns:
        List of dictionaries containing port information
    """
    listening_ports = []
    
    # Get all network connections
    connections = psutil.net_connections(kind='inet')
    
    for conn in connections:
        # Only interested in listening sockets
        if conn.status != psutil.CONN_LISTEN:
            continue
            
        port_info = {
            'protocol': 'tcp' if conn.type == 1 else 'udp',
            'port': conn.laddr.port,
            'bind_address': conn.laddr.ip,
            'pid': conn.pid,
            'family': 'ipv4' if conn.family == 2 else 'ipv6'
        }
        
        listening_ports.append(port_info)
    
    return listening_ports
```

### Port Information Captured

For each listening port, the scanner captures:

**Network Details:**
- **Port Number**: The TCP or UDP port number (1-65535)
- **Protocol**: TCP or UDP
- **Bind Address**: The IP address the service is bound to:
  - `0.0.0.0` or `::` - Listening on all interfaces
  - `127.0.0.1` or `::1` - Localhost only
  - Specific IP - Bound to specific interface
- **IP Version**: IPv4 or IPv6

**Process Association:**
- **PID**: Process ID owning the listening socket
- **Socket State**: LISTEN, ESTABLISHED, etc.
- **Socket Options**: SO_REUSEADDR, SO_REUSEPORT, etc. (if available)

### Handling Special Cases

**Containerized Services:**

The scanner detects services running in containers:

```python
def detect_container_type(pid: int) -> Optional[str]:
    """
    Detect if a process is running in a container.
    
    Returns:
        Container type ('docker', 'podman', 'lxc') or None
    """
    try:
        proc = psutil.Process(pid)
        
        # Check cgroup for container indicators
        with open(f'/proc/{pid}/cgroup', 'r') as f:
            cgroup_content = f.read()
            
        if 'docker' in cgroup_content:
            return 'docker'
        elif 'lxc' in cgroup_content:
            return 'lxc'
        elif 'podman' in cgroup_content:
            return 'podman'
            
        # Check for container-specific environment variables
        env = proc.environ()
        if 'KUBERNETES_SERVICE_HOST' in env:
            return 'kubernetes'
            
    except (psutil.NoSuchProcess, PermissionError, FileNotFoundError):
        pass
        
    return None
```

**Permission-Restricted Processes:**

Some processes may not be accessible due to permissions:

```python
def safe_get_process_info(pid: int) -> Optional[Dict[str, Any]]:
    """
    Safely retrieve process information, handling permission errors.
    """
    try:
        proc = psutil.Process(pid)
        return {
            'name': proc.name(),
            'exe': proc.exe(),
            'cmdline': proc.cmdline(),
            'username': proc.username(),
            'cwd': proc.cwd(),
            'create_time': proc.create_time()
        }
    except psutil.AccessDenied:
        # Log that we couldn't access this process
        return {
            'name': 'ACCESS_DENIED',
            'exe': None,
            'cmdline': [],
            'username': None,
            'cwd': None,
            'create_time': None,
            'access_denied': True
        }
    except psutil.NoSuchProcess:
        # Process terminated during scan
        return None
```

**Ephemeral Ports:**

The scanner filters out ephemeral (client-side) connections:

- Only captures LISTEN state sockets
- Ignores ESTABLISHED connections (client connections)
- Focuses on server-side listening ports

## Process Identification

### Mapping Ports to Processes

Once listening ports are identified, the engine maps each port to its owning process:

```python
def map_port_to_process(port_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    Map a listening port to detailed process information.
    """
    pid = port_info['pid']
    
    if pid is None:
        return {**port_info, 'process': None}
    
    process_info = safe_get_process_info(pid)
    
    if process_info is None:
        return {**port_info, 'process': None}
    
    # Enrich with additional details
    process_info['container_type'] = detect_container_type(pid)
    process_info['parent_pid'] = get_parent_pid(pid)
    process_info['parent_name'] = get_parent_name(pid)
    
    return {**port_info, 'process': process_info}
```

### Process Metadata Extraction

The engine extracts comprehensive metadata for each process:

**Basic Information:**
- **Process Name**: Executable name (e.g., `nginx`, `postgres`, `node`)
- **Executable Path**: Full path to the executable (e.g., `/usr/sbin/nginx`)
- **Process ID (PID)**: Unique process identifier
- **Parent PID**: Parent process ID
- **Process Owner**: Username running the process
- **Create Time**: When the process started (Unix timestamp)

**Command-Line Details:**
- **Command-Line Arguments**: Full command with all arguments
- **Working Directory**: Current working directory of the process
- **Environment Variables**: Selected environment variables (configurable)

**Process Hierarchy:**
- **Parent Process**: Information about the parent process
- **Child Processes**: List of child processes (if any)
- **Process Tree**: Full ancestry chain

**Resource Usage:**
- **CPU Usage**: Current CPU percentage
- **Memory Usage**: RSS (Resident Set Size) and VMS (Virtual Memory Size)
- **Thread Count**: Number of threads
- **File Descriptors**: Number of open file descriptors

### Example Process Information

```json
{
  "port": 8080,
  "protocol": "tcp",
  "bind_address": "0.0.0.0",
  "family": "ipv4",
  "process": {
    "pid": 12345,
    "name": "node",
    "exe": "/usr/bin/node",
    "cmdline": ["node", "/app/server.js", "--port", "8080"],
    "username": "webapp",
    "cwd": "/app",
    "create_time": 1699459200,
    "parent_pid": 1,
    "parent_name": "systemd",
    "container_type": "docker",
    "cpu_percent": 2.5,
    "memory_rss": 157286400,
    "memory_vms": 1073741824,
    "num_threads": 8,
    "num_fds": 42,
    "environment": {
      "NODE_ENV": "production",
      "PORT": "8080",
      "DATABASE_URL": "***REDACTED***"
    }
  }
}
```

## Change Detection

### Real-Time Monitoring

The discovery engine continuously monitors for changes:

**Detection Mechanism:**
1. Perform periodic scans (default: every 60 seconds)
2. Compare current scan results with previous scan
3. Identify differences (new, stopped, modified services)
4. Generate change events
5. Push updates to controller immediately

### Types of Changes Detected

**New Service Detected:**
- A new port is listening that wasn't in the previous scan
- Triggers: Service started, new application deployed, configuration change

**Service Stopped:**
- A previously listening port is no longer listening
- Triggers: Service stopped, crashed, or reconfigured

**Port Changed:**
- Same service (same PID) now listening on different port
- Triggers: Configuration change, dynamic port allocation

**Process Restarted:**
- Same port, but different PID
- Triggers: Service restart, crash and auto-restart

**Version Changed:**
- Same service, but version information differs
- Triggers: Software update, rollback

**Configuration Changed:**
- Command-line arguments or environment variables changed
- Triggers: Configuration update, feature flag change

### Change Event Structure

```python
from enum import Enum
from datetime import datetime
from typing import Optional, Dict, Any

class ChangeType(Enum):
    SERVICE_ADDED = "service_added"
    SERVICE_REMOVED = "service_removed"
    SERVICE_MODIFIED = "service_modified"
    PORT_CHANGED = "port_changed"
    PROCESS_RESTARTED = "process_restarted"
    VERSION_CHANGED = "version_changed"

class ChangeEvent:
    def __init__(
        self,
        change_type: ChangeType,
        service_id: str,
        timestamp: datetime,
        old_state: Optional[Dict[str, Any]] = None,
        new_state: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.change_type = change_type
        self.service_id = service_id
        self.timestamp = timestamp
        self.old_state = old_state
        self.new_state = new_state
        self.metadata = metadata or {}
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'change_type': self.change_type.value,
            'service_id': self.service_id,
            'timestamp': self.timestamp.isoformat(),
            'old_state': self.old_state,
            'new_state': self.new_state,
            'metadata': self.metadata
        }
```

### Intelligent Scan Scheduling

The engine optimizes scan frequency based on system activity:

**Adaptive Scanning:**
- **Stable State**: Scan every 60 seconds (default)
- **High Activity**: Increase to every 30 seconds when changes detected
- **Quiet Period**: Decrease to every 120 seconds after 1 hour of no changes
- **Manual Trigger**: Immediate scan on demand from controller

**Scan Optimization:**
- **Incremental Scans**: Only check for changes, not full re-scan
- **Smart Caching**: Cache process information that rarely changes
- **Batch Updates**: Group multiple changes into single update message
- **Debouncing**: Wait for service to stabilize before reporting (avoid flapping)

## Container Detection

### Docker Container Support

The engine provides special handling for Docker containers:

**Detection Methods:**
1. Check cgroup for docker indicators
2. Inspect `/proc/<pid>/cgroup` for container ID
3. Query Docker API for container metadata (if available)
4. Check for Docker-specific environment variables

**Container Metadata Extraction:**
```python
def get_docker_container_info(pid: int) -> Optional[Dict[str, Any]]:
    """
    Extract Docker container information for a process.
    """
    try:
        # Read cgroup to get container ID
        with open(f'/proc/{pid}/cgroup', 'r') as f:
            for line in f:
                if 'docker' in line:
                    # Extract container ID from cgroup path
                    parts = line.strip().split('/')
                    container_id = parts[-1]
                    
                    # Try to get more info from Docker API
                    container_info = query_docker_api(container_id)
                    
                    return {
                        'container_id': container_id,
                        'container_name': container_info.get('name'),
                        'image': container_info.get('image'),
                        'image_tag': container_info.get('image_tag'),
                        'labels': container_info.get('labels', {})
                    }
    except (FileNotFoundError, PermissionError):
        pass
    
    return None
```

### Kubernetes Pod Detection

For services running in Kubernetes pods:

```python
def get_kubernetes_pod_info(pid: int) -> Optional[Dict[str, Any]]:
    """
    Extract Kubernetes pod information for a process.
    """
    try:
        proc = psutil.Process(pid)
        env = proc.environ()
        
        # Check for Kubernetes environment variables
        if 'KUBERNETES_SERVICE_HOST' not in env:
            return None
        
        return {
            'pod_name': env.get('HOSTNAME'),
            'namespace': env.get('POD_NAMESPACE'),
            'pod_ip': env.get('POD_IP'),
            'service_account': env.get('KUBERNETES_SERVICE_ACCOUNT'),
            'node_name': env.get('NODE_NAME')
        }
    except (psutil.NoSuchProcess, PermissionError):
        pass
    
    return None
```

## Performance Optimization

### Scan Performance

The discovery engine is optimized for minimal overhead:

**Benchmarks (typical VM with 50 services):**
- **Full Scan Time**: 1-3 seconds
- **Incremental Scan**: 200-500ms
- **CPU Usage During Scan**: 2-3%
- **Memory Overhead**: ~10 MB additional during scan
- **Network Impact**: None (local-only operations)

### Caching Strategy

The engine implements intelligent caching:

**Process Information Cache:**
- Cache process metadata for 60 seconds
- Invalidate on PID change
- Reduce repeated `psutil` calls

**Port Scan Cache:**
- Cache listening ports for scan interval
- Invalidate on manual rescan request
- Enable fast change detection

**Classification Cache:**
- Cache classification results indefinitely
- Invalidate only on version change
- Dramatically speeds up subsequent scans

### Resource Limits

The engine respects configurable resource limits:

```yaml
discovery:
  max_scan_duration: 30s  # Kill scan if it takes too long
  max_cpu_percent: 5      # Throttle if CPU usage exceeds limit
  max_memory_mb: 100      # Limit memory usage during scan
  scan_interval: 60s      # Time between scans
  batch_size: 100         # Process ports in batches
```

## Error Handling

### Graceful Degradation

The engine handles errors gracefully:

**Permission Errors:**
- Log inaccessible processes
- Continue scanning other processes
- Report partial results
- Mark services as "limited visibility"

**Process Termination:**
- Handle processes that terminate during scan
- Retry once on transient errors
- Skip and continue with remaining processes

**System Overload:**
- Detect high system load
- Automatically reduce scan frequency
- Defer non-critical operations
- Resume normal operation when load decreases

### Error Recovery

```python
class DiscoveryEngine:
    def __init__(self):
        self.consecutive_failures = 0
        self.max_failures = 3
        
    async def scan_with_retry(self):
        """
        Perform scan with automatic retry on failure.
        """
        try:
            results = await self.perform_scan()
            self.consecutive_failures = 0
            return results
        except Exception as e:
            self.consecutive_failures += 1
            logger.error(f"Scan failed: {e}")
            
            if self.consecutive_failures >= self.max_failures:
                logger.critical("Multiple scan failures, entering degraded mode")
                await self.enter_degraded_mode()
            else:
                # Exponential backoff
                await asyncio.sleep(2 ** self.consecutive_failures)
                return await self.scan_with_retry()
```

## Configuration

### Discovery Settings

```yaml
discovery:
  # Scan configuration
  scan_interval: 60s
  scan_timeout: 30s
  enable_ipv6: true
  
  # Process information
  collect_environment_vars: false
  collect_command_line: true
  collect_working_directory: true
  
  # Container detection
  detect_docker: true
  detect_kubernetes: true
  detect_lxc: true
  
  # Performance tuning
  max_concurrent_scans: 1
  batch_size: 100
  cache_ttl: 60s
  
  # Change detection
  debounce_interval: 5s
  min_uptime_before_report: 10s
  
  # Filtering
  exclude_ports: [22]  # Don't report SSH
  exclude_processes: ["sshd"]
  include_localhost_only: true
```

## Summary

The Service Discovery Engine provides automatic, continuous, and comprehensive visibility into all services running on a VM. By leveraging native OS APIs through the `psutil` library, the engine achieves high accuracy with minimal overhead.

Key capabilities:

- **Automatic Discovery**: No manual configuration required
- **Real-Time Updates**: Immediate notification of service changes
- **Process-Aware**: Maps ports to processes with full metadata
- **Container Support**: Detects and enriches containerized services
- **Low Overhead**: < 1% CPU, completes scans in 1-3 seconds
- **Intelligent Caching**: Optimizes repeated scans
- **Graceful Degradation**: Handles errors without failing completely

The discovery engine forms the foundation for service classification, monitoring, and access control—providing the raw data that powers the entire VM Gateway platform.

## Next Steps

- **[Classification Engine](03-classification-engine.md)**: Learn how discovered services are intelligently classified
- **[Metrics Collection](04-metrics-collection.md)**: Understand how metrics are gathered for discovered services
- **[Communication Protocol](05-communication-protocol.md)**: See how discovery results are transmitted to the controller
