---
title: "Feature Gating System"
section: "Overview"
order: 4
tags: ["feature-flags", "feature-gating", "deployment", "configuration"]
last_updated: "2025-11-08"
---

# Feature Gating System

## Overview

VM Gateway implements a comprehensive feature gating system (also known as feature flags or feature toggles) that enables safe, incremental rollout of new functionality, A/B testing, and rapid response to issues without requiring code deployments. This system is fundamental to the platform's ability to iterate quickly while maintaining stability in production environments.

## What is Feature Gating?

Feature gating is a software development technique that allows you to enable or disable features at runtime without deploying new code. Features are wrapped in conditional logic that checks whether the feature is enabled before executing the code.

### Benefits

**For Development**:
- Deploy code to production before it's ready for users
- Test features in production with real data
- Separate deployment from release
- Reduce risk of long-lived feature branches
- Enable trunk-based development

**For Operations**:
- Quickly disable problematic features without rollback
- Gradual rollout to detect issues early
- A/B testing for performance comparison
- Environment-specific features (dev, staging, prod)
- Reduce blast radius of bugs

**For Business**:
- Control feature access by user segment
- Beta testing with selected users
- Premium features for specific tiers
- Regional feature availability
- Time-based feature releases

## Architecture

### Storage Options

VM Gateway supports multiple storage backends for feature flags:

#### 1. Database-Backed (Default)

**Storage**: PostgreSQL table

**Advantages**:
- Centralized configuration
- Real-time updates across all instances
- Audit trail of changes
- User-friendly admin interface
- Supports complex targeting rules

**Schema**:
```sql
CREATE TABLE feature_flags (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    enabled BOOLEAN DEFAULT FALSE,
    rollout_percentage INTEGER DEFAULT 0,
    target_users TEXT[],
    target_roles TEXT[],
    environments TEXT[],
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by VARCHAR(255),
    metadata JSONB
);
```

#### 2. Configuration File

**Storage**: YAML or TOML file

**Advantages**:
- Version controlled with code
- Easy to review in pull requests
- No database dependency
- Fast reads (no network calls)

**Example** (YAML):
```yaml
feature_flags:
  advanced_monitoring:
    enabled: true
    description: "Enhanced metrics collection and visualization"
    environments: ["staging", "production"]
    
  new_authentication_flow:
    enabled: false
    rollout_percentage: 10
    target_roles: ["beta_testers"]
    
  experimental_ai_classification:
    enabled: true
    environments: ["development"]
    expires_at: "2025-12-31T23:59:59Z"
```

#### 3. Environment Variables

**Storage**: Environment variables

**Advantages**:
- Simple deployment configuration
- Container-friendly
- No additional infrastructure

**Format**:
```bash
FEATURE_ADVANCED_MONITORING=true
FEATURE_NEW_AUTH_FLOW=false
FEATURE_AI_CLASSIFICATION=true
```

#### 4. External Service

**Storage**: LaunchDarkly, Unleash, or custom service

**Advantages**:
- Advanced targeting capabilities
- Real-time updates
- Analytics and insights
- Multi-application support
- Professional support

## Flag Types

### 1. Boolean Flags

Simple on/off switches:

```python
if feature_enabled("advanced_monitoring"):
    metrics = collect_advanced_metrics()
else:
    metrics = collect_basic_metrics()
```

**Use Cases**:
- Enable/disable entire features
- Kill switches for problematic code
- Environment-specific features

### 2. Percentage Rollout

Gradually enable features for a percentage of users:

```python
@feature_flag("new_dashboard", rollout_percentage=25)
async def render_dashboard():
    return new_dashboard_template()
```

**How It Works**:
- User ID hashed to determine bucket (0-99)
- If hash < rollout_percentage, feature enabled
- Consistent per user (same user always gets same result)

**Use Cases**:
- Gradual rollout (5% → 25% → 50% → 100%)
- Canary deployments
- Load testing new features

**Example Rollout Plan**:
```
Day 1: 5%   (internal users + early adopters)
Day 3: 10%  (monitor metrics)
Day 5: 25%  (if no issues)
Day 7: 50%  (if metrics look good)
Day 10: 100% (full rollout)
```

### 3. User-Based Flags

Enable for specific users:

```python
if feature_enabled("beta_features", user=current_user):
    show_beta_features()
```

**Configuration**:
```yaml
beta_features:
  enabled: true
  target_users:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
```

**Use Cases**:
- Beta testing with specific users
- VIP features
- Internal testing
- Customer-specific features

### 4. Role-Based Flags

Enable for users with specific roles:

```python
if feature_enabled("admin_analytics", user=current_user):
    return admin_analytics_dashboard()
```

**Configuration**:
```yaml
admin_analytics:
  enabled: true
  target_roles:
    - "admin"
    - "analyst"
```

**Use Cases**:
- Role-specific features
- Premium tier features
- Administrative tools
- Compliance features

### 5. Environment-Based Flags

Different flags per environment:

```python
if feature_enabled("debug_logging"):
    enable_verbose_logging()
```

**Configuration**:
```yaml
debug_logging:
  enabled: true
  environments: ["development", "staging"]
```

**Use Cases**:
- Development-only features
- Staging testing
- Production safeguards

### 6. Time-Based Flags

Enable after specific date/time:

```python
if feature_enabled("holiday_theme"):
    apply_holiday_theme()
```

**Configuration**:
```yaml
holiday_theme:
  enabled: true
  start_time: "2025-12-01T00:00:00Z"
  end_time: "2025-12-31T23:59:59Z"
```

**Use Cases**:
- Scheduled feature releases
- Time-limited promotions
- Temporary features
- Automatic feature expiration

## Implementation

### Python Decorator

The most common usage pattern:

```python
from vm_gateway.features import feature_flag

@feature_flag("advanced_monitoring")
async def get_advanced_metrics(vm_id: str):
    """Only executes if feature is enabled"""
    return await collect_advanced_metrics(vm_id)

# If feature is disabled, returns None or raises FeatureDisabled exception
```

### Conditional Check

For inline conditional logic:

```python
from vm_gateway.features import feature_enabled

async def get_metrics(vm_id: str):
    if feature_enabled("advanced_monitoring"):
        return await collect_advanced_metrics(vm_id)
    else:
        return await collect_basic_metrics(vm_id)
```

### Context Manager

For wrapping blocks of code:

```python
from vm_gateway.features import feature_context

async def process_data():
    with feature_context("experimental_processing"):
        # This code only runs if feature is enabled
        result = await experimental_algorithm()
    
    # This code always runs
    return finalize_result(result)
```

### Percentage Rollout

```python
@feature_flag("new_dashboard", rollout_percentage=25)
async def render_new_dashboard():
    return new_dashboard_template()

# Automatically determines if user is in the 25% rollout
```

### User-Specific Check

```python
if feature_enabled("beta_features", user=current_user):
    available_features = get_beta_features()
else:
    available_features = get_standard_features()
```

### Multi-Condition Check

```python
if feature_enabled(
    "premium_analytics",
    user=current_user,
    environment="production"
):
    return premium_analytics()
```

## Feature Flag Service

### Core Service Class

```python
from typing import Optional, List
from datetime import datetime
import hashlib

class FeatureFlagService:
    """Centralized feature flag evaluation"""
    
    def __init__(self, storage_backend):
        self.storage = storage_backend
        self.cache = {}
        self.cache_ttl = 60  # seconds
    
    async def is_enabled(
        self,
        flag_name: str,
        user: Optional[User] = None,
        environment: Optional[str] = None
    ) -> bool:
        """Check if feature flag is enabled"""
        
        # Get flag configuration
        flag = await self._get_flag(flag_name)
        
        if not flag:
            return False  # Unknown flags default to disabled
        
        # Check basic enabled status
        if not flag.enabled:
            return False
        
        # Check environment
        if flag.environments and environment not in flag.environments:
            return False
        
        # Check expiration
        if flag.expires_at and datetime.utcnow() > flag.expires_at:
            return False
        
        # Check user targeting
        if user:
            # Specific user targeting
            if flag.target_users and user.email in flag.target_users:
                return True
            
            # Role-based targeting
            if flag.target_roles:
                user_roles = set(user.roles)
                target_roles = set(flag.target_roles)
                if not user_roles.intersection(target_roles):
                    return False
            
            # Percentage rollout
            if flag.rollout_percentage > 0:
                return self._is_in_rollout(user.id, flag_name, flag.rollout_percentage)
        
        return True
    
    def _is_in_rollout(self, user_id: str, flag_name: str, percentage: int) -> bool:
        """Determine if user is in percentage rollout"""
        # Hash user_id + flag_name for consistent bucketing
        hash_input = f"{user_id}:{flag_name}".encode()
        hash_value = int(hashlib.sha256(hash_input).hexdigest(), 16)
        bucket = hash_value % 100
        return bucket < percentage
    
    async def _get_flag(self, flag_name: str):
        """Get flag from storage with caching"""
        # Check cache
        if flag_name in self.cache:
            cached_flag, cached_time = self.cache[flag_name]
            if (datetime.utcnow() - cached_time).seconds < self.cache_ttl:
                return cached_flag
        
        # Fetch from storage
        flag = await self.storage.get_flag(flag_name)
        
        # Update cache
        self.cache[flag_name] = (flag, datetime.utcnow())
        
        return flag
```

### Decorator Implementation

```python
from functools import wraps

def feature_flag(flag_name: str, rollout_percentage: int = 0):
    """Decorator to gate function execution behind feature flag"""
    
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Get current user from context
            user = get_current_user()
            environment = get_current_environment()
            
            # Check if feature is enabled
            if await feature_service.is_enabled(
                flag_name,
                user=user,
                environment=environment
            ):
                return await func(*args, **kwargs)
            else:
                # Feature disabled - return None or raise exception
                raise FeatureDisabledException(f"Feature '{flag_name}' is not enabled")
        
        return wrapper
    return decorator
```

## Management Interface

### Web Admin Panel

The controller provides a web interface for managing feature flags:

**Features**:
- List all feature flags with status
- Create new flags
- Edit existing flags
- Enable/disable flags with one click
- View flag usage analytics
- Audit log of flag changes
- Schedule flag changes

**UI Components**:

```
┌─────────────────────────────────────────────────────────┐
│ Feature Flags                              [+ New Flag] │
├─────────────────────────────────────────────────────────┤
│                                                          │
│ ┌────────────────────────────────────────────────────┐ │
│ │ advanced_monitoring                    [●] Enabled │ │
│ │ Enhanced metrics collection and visualization      │ │
│ │ Rollout: 100% | Environments: staging, production  │ │
│ │ [Edit] [Disable] [Analytics] [History]             │ │
│ └────────────────────────────────────────────────────┘ │
│                                                          │
│ ┌────────────────────────────────────────────────────┐ │
│ │ new_authentication_flow               [○] Disabled │ │
│ │ New OAuth2 authentication flow                     │ │
│ │ Rollout: 10% | Roles: beta_testers                 │ │
│ │ [Edit] [Enable] [Analytics] [History]              │ │
│ └────────────────────────────────────────────────────┘ │
│                                                          │
│ ┌────────────────────────────────────────────────────┐ │
│ │ experimental_ai_classification        [●] Enabled  │ │
│ │ ML-based service classification                    │ │
│ │ Environments: development | Expires: 2025-12-31    │ │
│ │ [Edit] [Disable] [Analytics] [History]             │ │
│ └────────────────────────────────────────────────────┘ │
│                                                          │
└─────────────────────────────────────────────────────────┘
```

### Flag Editor

```
┌─────────────────────────────────────────────────────────┐
│ Edit Feature Flag: advanced_monitoring                  │
├─────────────────────────────────────────────────────────┤
│                                                          │
│ Name: [advanced_monitoring                           ]  │
│                                                          │
│ Description:                                             │
│ [Enhanced metrics collection and visualization       ]  │
│                                                          │
│ Status: [●] Enabled  [○] Disabled                       │
│                                                          │
│ Rollout Strategy:                                        │
│   [○] All users                                          │
│   [●] Percentage: [100]%                                 │
│   [○] Specific users                                     │
│   [○] Specific roles                                     │
│                                                          │
│ Environments:                                            │
│   [✓] Development                                        │
│   [✓] Staging                                            │
│   [✓] Production                                         │
│                                                          │
│ Expiration (optional):                                   │
│   [Never expires                                      ▼] │
│                                                          │
│ Dependencies (optional):                                 │
│   Requires: [Select flags...                          ▼] │
│                                                          │
│                                    [Cancel]  [Save]      │
└─────────────────────────────────────────────────────────┘
```

### API Endpoints

```python
# List all flags
GET /api/feature-flags

# Get specific flag
GET /api/feature-flags/{flag_name}

# Create flag
POST /api/feature-flags
{
  "name": "new_feature",
  "description": "Description",
  "enabled": false,
  "rollout_percentage": 0
}

# Update flag
PUT /api/feature-flags/{flag_name}
{
  "enabled": true,
  "rollout_percentage": 25
}

# Delete flag
DELETE /api/feature-flags/{flag_name}

# Check if flag is enabled for current user
GET /api/feature-flags/{flag_name}/enabled
```

## Advanced Features

### Flag Dependencies

Flags can depend on other flags:

```yaml
advanced_analytics:
  enabled: true
  requires:
    - advanced_monitoring
    - premium_tier
```

**Behavior**: `advanced_analytics` only enabled if both `advanced_monitoring` and `premium_tier` are enabled.

### Flag Scheduling

Schedule flag changes:

```yaml
holiday_sale:
  enabled: true
  schedule:
    enable_at: "2025-12-01T00:00:00Z"
    disable_at: "2025-12-31T23:59:59Z"
```

**Behavior**: Flag automatically enabled/disabled at specified times.

### Gradual Rollout Automation

Automatically increase rollout percentage:

```yaml
new_dashboard:
  enabled: true
  rollout_percentage: 5
  auto_rollout:
    enabled: true
    increment: 25
    interval: "3 days"
    max_percentage: 100
    rollback_on_errors: true
    error_threshold: 5  # percent
```

**Behavior**: 
- Start at 5%
- Every 3 days, increase by 25%
- Stop at 100%
- Rollback if error rate exceeds 5%

### A/B Testing

Compare two implementations:

```python
variant = get_feature_variant("dashboard_redesign", user=current_user)

if variant == "control":
    return render_old_dashboard()
elif variant == "treatment":
    return render_new_dashboard()
```

**Configuration**:
```yaml
dashboard_redesign:
  enabled: true
  variants:
    control: 50    # 50% of users
    treatment: 50  # 50% of users
  track_metrics: true
```

### Kill Switches

Emergency disable for problematic features:

```python
@feature_flag("new_payment_processor", kill_switch=True)
async def process_payment(amount: float):
    # If kill switch activated, falls back to old processor
    return await new_payment_processor.charge(amount)
```

**Usage**: Quickly disable feature if issues detected in production.

## Best Practices

### Naming Conventions

- Use descriptive names: `advanced_monitoring` not `feature1`
- Use snake_case: `new_auth_flow` not `NewAuthFlow`
- Prefix by category: `ui_new_dashboard`, `api_v2_endpoints`
- Include ticket number: `feat_1234_user_profiles`

### Flag Lifecycle

1. **Creation**: Create flag disabled by default
2. **Development**: Enable in development environment
3. **Testing**: Enable in staging for QA
4. **Rollout**: Gradual rollout in production (5% → 25% → 50% → 100%)
5. **Stabilization**: Monitor for 1-2 weeks at 100%
6. **Cleanup**: Remove flag, make feature permanent

### Flag Cleanup

**Problem**: Accumulation of old flags clutters codebase

**Solution**: Regular cleanup process

1. **Identify old flags**: Flags at 100% for > 30 days
2. **Remove flag checks**: Replace with direct code
3. **Delete flag**: Remove from database/config
4. **Document**: Note in changelog

**Example**:
```python
# Before (with flag)
if feature_enabled("new_dashboard"):
    return render_new_dashboard()
else:
    return render_old_dashboard()

# After (flag removed, feature permanent)
return render_new_dashboard()
```

### Testing with Flags

**Override flags in tests**:

```python
@pytest.mark.feature_flag("advanced_monitoring", enabled=True)
async def test_advanced_metrics():
    metrics = await get_metrics()
    assert "advanced" in metrics

@pytest.mark.feature_flag("advanced_monitoring", enabled=False)
async def test_basic_metrics():
    metrics = await get_metrics()
    assert "advanced" not in metrics
```

### Monitoring Flag Usage

Track flag evaluation:

```python
# Log flag checks
logger.info(f"Feature flag '{flag_name}' evaluated", extra={
    "flag_name": flag_name,
    "enabled": result,
    "user_id": user.id if user else None,
    "environment": environment
})

# Metrics
metrics.increment(f"feature_flag.{flag_name}.checked")
if result:
    metrics.increment(f"feature_flag.{flag_name}.enabled")
```

### Documentation

Document flags in code:

```python
@feature_flag("advanced_monitoring")
async def get_advanced_metrics(vm_id: str):
    """
    Collect advanced metrics for VM.
    
    Feature Flag: advanced_monitoring
    - Added in: v1.5.0
    - Status: Gradual rollout
    - Planned removal: v1.7.0 (when stable)
    - Fallback: Basic metrics collection
    """
    return await collect_advanced_metrics(vm_id)
```

## Security Considerations

### Access Control

- Only admins can modify flags
- Audit log of all flag changes
- Require approval for production flag changes
- Rate limiting on flag updates

### Sensitive Features

For security-critical features:

```yaml
security_feature:
  enabled: true
  require_approval: true
  approval_roles: ["security_team", "cto"]
  audit_level: detailed
```

### Flag Injection Prevention

Validate flag names to prevent injection:

```python
def validate_flag_name(name: str) -> bool:
    # Only allow alphanumeric and underscores
    return bool(re.match(r'^[a-z0-9_]+$', name))
```

## Summary

VM Gateway's feature gating system provides powerful, flexible control over feature rollout and experimentation. By supporting multiple flag types (boolean, percentage, user-based, role-based, environment-based, and time-based), the system enables safe, incremental deployment of new functionality while maintaining the ability to quickly respond to issues.

The combination of database-backed storage, comprehensive management interface, and programmatic API makes feature flags a core part of the development and deployment workflow. Proper use of feature flags enables trunk-based development, reduces deployment risk, and provides fine-grained control over feature availability across different user segments and environments.
