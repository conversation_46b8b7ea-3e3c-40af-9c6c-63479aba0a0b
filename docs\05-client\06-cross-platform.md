---
title: "Cross-Platform Implementation"
section: "Client"
order: 6
tags: ["client", "windows", "macos", "linux", "cross-platform"]
last_updated: "2025-11-08"
---

# Cross-Platform Implementation

## Overview

The VM Gateway Client is designed to provide consistent functionality across Windows, macOS, and Linux while respecting platform-specific conventions and integrating with native OS features. This document details the platform-specific implementations, challenges, and solutions for delivering a native experience on each operating system.

## Platform Support Matrix

### Supported Platforms

| Platform | Versions | Architecture | Status |
|----------|----------|--------------|--------|
| Windows | 10, 11 | x64, ARM64 | Full Support |
| macOS | 11+ (Big Sur and later) | x64, ARM64 (Apple Silicon) | Full Support |
| Linux | Ubuntu 20.04+, Fedora 35+, Debian 11+ | x64, ARM64 | Full Support |

### Feature Parity

All core features are available on all platforms:
- Authentication and authorization
- Tunnel establishment (WireGuard, TLS, WebSocket)
- Local port forwarding
- Connection management
- Settings and preferences
- Automatic updates

Platform-specific features are clearly documented and gracefully degrade when unavailable.

## Windows Implementation

### System Integration

**System Tray:**
```python
import pystray
from PIL import Image

def create_windows_tray():
    # Load icon
    icon_image = Image.open("assets/icon.ico")
    
    # Create menu
    menu = pystray.Menu(
        pystray.MenuItem("Open", on_open),
        pystray.MenuItem("Connect to Service", on_connect),
        pystray.Menu.SEPARATOR,
        pystray.MenuItem("Settings", on_settings),
        pystray.MenuItem("Quit", on_quit)
    )
    
    # Create tray icon
    icon = pystray.Icon(
        "vm-gateway",
        icon_image,
        "VM Gateway",
        menu
    )
    
    return icon
```


**Windows Credential Manager Integration:**
```python
import keyring
from keyring.backends import Windows

def store_token_windows(token: str):
    """Store token in Windows Credential Manager"""
    keyring.set_password("vm-gateway", "access_token", token)

def retrieve_token_windows() -> str:
    """Retrieve token from Windows Credential Manager"""
    return keyring.get_password("vm-gateway", "access_token")
```

**Windows Hello Integration:**
```python
import win32security
import win32api

def authenticate_with_windows_hello():
    """Use Windows Hello for biometric authentication"""
    try:
        # Prompt for Windows Hello authentication
        result = win32security.CredUIPromptForWindowsCredentials(
            None,
            0,
            "VM Gateway",
            "Authenticate to access services",
            None,
            win32security.CREDUIWIN_GENERIC
        )
        
        if result[0] == 0:  # Success
            return True
        return False
    except Exception as e:
        logger.error(f"Windows Hello authentication failed: {e}")
        return False
```

**Auto-Start Configuration:**
```python
import winreg

def enable_autostart_windows():
    """Add to Windows startup registry"""
    key_path = r"Software\Microsoft\Windows\CurrentVersion\Run"
    
    try:
        key = winreg.OpenKey(
            winreg.HKEY_CURRENT_USER,
            key_path,
            0,
            winreg.KEY_SET_VALUE
        )
        
        exe_path = sys.executable
        winreg.SetValueEx(
            key,
            "VMGateway",
            0,
            winreg.REG_SZ,
            f'"{exe_path}" --minimized'
        )
        
        winreg.CloseKey(key)
        logger.info("Auto-start enabled")
    except Exception as e:
        logger.error(f"Failed to enable auto-start: {e}")

def disable_autostart_windows():
    """Remove from Windows startup registry"""
    key_path = r"Software\Microsoft\Windows\CurrentVersion\Run"
    
    try:
        key = winreg.OpenKey(
            winreg.HKEY_CURRENT_USER,
            key_path,
            0,
            winreg.KEY_SET_VALUE
        )
        
        winreg.DeleteValue(key, "VMGateway")
        winreg.CloseKey(key)
        logger.info("Auto-start disabled")
    except FileNotFoundError:
        pass  # Already not in startup
    except Exception as e:
        logger.error(f"Failed to disable auto-start: {e}")
```

**Windows Firewall Integration:**
```python
import subprocess

def add_firewall_rule_windows(port: int):
    """Add Windows Firewall rule for local port"""
    rule_name = f"VM Gateway - Port {port}"
    
    try:
        # Add inbound rule
        subprocess.run([
            "netsh", "advfirewall", "firewall", "add", "rule",
            f"name={rule_name}",
            "dir=in",
            "action=allow",
            "protocol=TCP",
            f"localport={port}",
            "profile=private"
        ], check=True, capture_output=True)
        
        logger.info(f"Firewall rule added for port {port}")
    except subprocess.CalledProcessError as e:
        logger.warning(f"Failed to add firewall rule: {e}")

def remove_firewall_rule_windows(port: int):
    """Remove Windows Firewall rule"""
    rule_name = f"VM Gateway - Port {port}"
    
    try:
        subprocess.run([
            "netsh", "advfirewall", "firewall", "delete", "rule",
            f"name={rule_name}"
        ], check=True, capture_output=True)
        
        logger.info(f"Firewall rule removed for port {port}")
    except subprocess.CalledProcessError as e:
        logger.warning(f"Failed to remove firewall rule: {e}")
```

**Toast Notifications:**
```python
from win10toast import ToastNotifier

def show_notification_windows(title: str, message: str, icon_path: str = None):
    """Show Windows 10/11 toast notification"""
    toaster = ToastNotifier()
    toaster.show_toast(
        title,
        message,
        icon_path=icon_path or "assets/icon.ico",
        duration=5,
        threaded=True
    )
```

### Installation

**MSI Installer:**
- Created with WiX Toolset
- Installs to `C:\Program Files\VM Gateway\`
- Creates Start Menu shortcuts
- Registers custom URL scheme (`vmgateway://`)
- Optionally adds to startup
- Includes uninstaller

**Standalone Executable:**
- Single `.exe` file (PyInstaller or Nuitka)
- Portable, no installation required
- Stores settings in `%APPDATA%\VMGateway\`

### Platform-Specific Challenges

**Challenge: WireGuard Kernel Module**
- Solution: Use WireGuard-NT (Windows native implementation)
- Fallback: TLS tunnel if WireGuard unavailable

**Challenge: Administrator Privileges for Port 80**
- Solution: Warn user, suggest alternative ports
- Fallback: Use ports > 1024

**Challenge: Antivirus False Positives**
- Solution: Code signing certificate
- Documentation: Whitelist instructions for common AV software

## macOS Implementation

### System Integration

**Menu Bar App:**
```python
import rumps

class VMGatewayApp(rumps.App):
    def __init__(self):
        super(VMGatewayApp, self).__init__(
            "VM Gateway",
            icon="assets/icon.png",
            quit_button=None
        )
        
        self.menu = [
            rumps.MenuItem("Open", callback=self.on_open),
            rumps.MenuItem("Connect to Service", callback=self.on_connect),
            rumps.separator,
            rumps.MenuItem("Settings", callback=self.on_settings),
            rumps.MenuItem("Quit", callback=self.on_quit)
        ]
    
    def on_open(self, _):
        # Show main window
        pass
    
    def on_connect(self, _):
        # Show service selector
        pass
    
    def on_settings(self, _):
        # Show settings
        pass
    
    def on_quit(self, _):
        rumps.quit_application()

if __name__ == "__main__":
    VMGatewayApp().run()
```

**Keychain Integration:**
```python
import keyring
from keyring.backends import macOS

def store_token_macos(token: str):
    """Store token in macOS Keychain"""
    keyring.set_password("vm-gateway", "access_token", token)

def retrieve_token_macos() -> str:
    """Retrieve token from macOS Keychain"""
    return keyring.get_password("vm-gateway", "access_token")
```

**Touch ID Integration:**
```python
import LocalAuthentication

def authenticate_with_touch_id():
    """Use Touch ID for biometric authentication"""
    context = LocalAuthentication.LAContext()
    
    # Check if Touch ID is available
    can_evaluate, error = context.canEvaluatePolicy_error_(
        LocalAuthentication.LAPolicyDeviceOwnerAuthenticationWithBiometrics,
        None
    )
    
    if not can_evaluate:
        logger.warning("Touch ID not available")
        return False
    
    # Prompt for Touch ID
    success, error = context.evaluatePolicy_localizedReason_reply_(
        LocalAuthentication.LAPolicyDeviceOwnerAuthenticationWithBiometrics,
        "Authenticate to access services",
        None
    )
    
    return success
```

**Launch Agent Configuration:**
```python
import plistlib
import os

def enable_autostart_macos():
    """Create Launch Agent plist"""
    plist_path = os.path.expanduser(
        "~/Library/LaunchAgents/com.vmgateway.client.plist"
    )
    
    plist_data = {
        "Label": "com.vmgateway.client",
        "ProgramArguments": [
            "/Applications/VM Gateway.app/Contents/MacOS/vmgateway",
            "--minimized"
        ],
        "RunAtLoad": True,
        "KeepAlive": False
    }
    
    with open(plist_path, 'wb') as f:
        plistlib.dump(plist_data, f)
    
    # Load the launch agent
    os.system(f"launchctl load {plist_path}")
    logger.info("Auto-start enabled")

def disable_autostart_macos():
    """Remove Launch Agent"""
    plist_path = os.path.expanduser(
        "~/Library/LaunchAgents/com.vmgateway.client.plist"
    )
    
    if os.path.exists(plist_path):
        os.system(f"launchctl unload {plist_path}")
        os.remove(plist_path)
        logger.info("Auto-start disabled")
```

**Notification Center Integration:**
```python
import objc
from Foundation import NSUserNotification, NSUserNotificationCenter

def show_notification_macos(title: str, message: str):
    """Show macOS Notification Center notification"""
    notification = NSUserNotification.alloc().init()
    notification.setTitle_(title)
    notification.setInformativeText_(message)
    notification.setSoundName_("NSUserNotificationDefaultSoundName")
    
    center = NSUserNotificationCenter.defaultUserNotificationCenter()
    center.deliverNotification_(notification)
```

**Spotlight Integration:**
```python
def create_spotlight_metadata():
    """Create metadata for Spotlight search"""
    metadata_path = "/Applications/VM Gateway.app/Contents/Info.plist"
    
    # Add keywords for Spotlight
    metadata = {
        "CFBundleDisplayName": "VM Gateway",
        "CFBundleIdentifier": "com.vmgateway.client",
        "LSApplicationCategoryType": "public.app-category.developer-tools",
        "NSHumanReadableCopyright": "© 2025 VM Gateway",
        "CFBundleShortVersionString": "1.0.0",
        "CFBundleURLTypes": [{
            "CFBundleURLName": "VM Gateway URL",
            "CFBundleURLSchemes": ["vmgateway"]
        }]
    }
    
    with open(metadata_path, 'wb') as f:
        plistlib.dump(metadata, f)
```

### Installation

**DMG Disk Image:**
- Drag-and-drop installation
- Background image with instructions
- Symlink to Applications folder
- Code signed and notarized

**PKG Installer:**
- Traditional installer package
- Custom installation options
- Post-install scripts for configuration
- Code signed

### Platform-Specific Challenges

**Challenge: Gatekeeper and Code Signing**
- Solution: Apple Developer certificate
- Notarization through Apple's notary service
- Hardened runtime enabled

**Challenge: System Extension Approval**
- Solution: Prompt user to approve in System Preferences
- Clear instructions with screenshots
- Fallback to TLS tunnel if WireGuard blocked

**Challenge: Network Extension Entitlements**
- Solution: Request appropriate entitlements
- Explain permissions to user
- Provide alternative if denied

## Linux Implementation

### System Integration

**System Tray (Multiple Desktop Environments):**
```python
import gi
gi.require_version('Gtk', '3.0')
gi.require_version('AppIndicator3', '0.1')
from gi.repository import Gtk, AppIndicator3

class LinuxTrayIcon:
    def __init__(self):
        self.indicator = AppIndicator3.Indicator.new(
            "vm-gateway",
            "vm-gateway-icon",
            AppIndicator3.IndicatorCategory.APPLICATION_STATUS
        )
        self.indicator.set_status(AppIndicator3.IndicatorStatus.ACTIVE)
        self.indicator.set_menu(self.create_menu())
    
    def create_menu(self):
        menu = Gtk.Menu()
        
        # Open item
        item_open = Gtk.MenuItem(label="Open")
        item_open.connect("activate", self.on_open)
        menu.append(item_open)
        
        # Connect item
        item_connect = Gtk.MenuItem(label="Connect to Service")
        item_connect.connect("activate", self.on_connect)
        menu.append(item_connect)
        
        # Separator
        menu.append(Gtk.SeparatorMenuItem())
        
        # Settings item
        item_settings = Gtk.MenuItem(label="Settings")
        item_settings.connect("activate", self.on_settings)
        menu.append(item_settings)
        
        # Quit item
        item_quit = Gtk.MenuItem(label="Quit")
        item_quit.connect("activate", self.on_quit)
        menu.append(item_quit)
        
        menu.show_all()
        return menu
```

**Secret Service Integration:**
```python
import secretstorage

def store_token_linux(token: str):
    """Store token in Secret Service (GNOME Keyring, KWallet)"""
    connection = secretstorage.dbus_init()
    collection = secretstorage.get_default_collection(connection)
    
    collection.create_item(
        "VM Gateway Access Token",
        {"application": "vm-gateway"},
        token.encode(),
        replace=True
    )

def retrieve_token_linux() -> str:
    """Retrieve token from Secret Service"""
    connection = secretstorage.dbus_init()
    collection = secretstorage.get_default_collection(connection)
    
    items = collection.search_items({"application": "vm-gateway"})
    if items:
        return items[0].get_secret().decode()
    return None
```

**Desktop Entry (Auto-Start):**
```python
import os

def enable_autostart_linux():
    """Create .desktop file in autostart directory"""
    autostart_dir = os.path.expanduser("~/.config/autostart")
    os.makedirs(autostart_dir, exist_ok=True)
    
    desktop_file = os.path.join(autostart_dir, "vmgateway.desktop")
    
    content = """[Desktop Entry]
Type=Application
Name=VM Gateway
Exec=/usr/bin/vmgateway --minimized
Icon=vmgateway
Comment=VM Gateway Client
X-GNOME-Autostart-enabled=true
"""
    
    with open(desktop_file, 'w') as f:
        f.write(content)
    
    os.chmod(desktop_file, 0o755)
    logger.info("Auto-start enabled")

def disable_autostart_linux():
    """Remove .desktop file from autostart"""
    desktop_file = os.path.expanduser("~/.config/autostart/vmgateway.desktop")
    
    if os.path.exists(desktop_file):
        os.remove(desktop_file)
        logger.info("Auto-start disabled")
```

**Desktop Notifications:**
```python
import gi
gi.require_version('Notify', '0.7')
from gi.repository import Notify

def show_notification_linux(title: str, message: str):
    """Show desktop notification via libnotify"""
    Notify.init("VM Gateway")
    
    notification = Notify.Notification.new(
        title,
        message,
        "vmgateway"
    )
    
    notification.show()
```

**Systemd Integration:**
```python
def create_systemd_user_service():
    """Create systemd user service"""
    service_dir = os.path.expanduser("~/.config/systemd/user")
    os.makedirs(service_dir, exist_ok=True)
    
    service_file = os.path.join(service_dir, "vmgateway.service")
    
    content = """[Unit]
Description=VM Gateway Client
After=network.target

[Service]
Type=simple
ExecStart=/usr/bin/vmgateway
Restart=on-failure
RestartSec=5

[Install]
WantedBy=default.target
"""
    
    with open(service_file, 'w') as f:
        f.write(content)
    
    # Reload systemd and enable service
    os.system("systemctl --user daemon-reload")
    os.system("systemctl --user enable vmgateway.service")
    os.system("systemctl --user start vmgateway.service")
```

### Installation

**DEB Package (Debian/Ubuntu):**
```bash
# Package structure
vmgateway_1.0.0_amd64.deb
├── DEBIAN/
│   ├── control
│   ├── postinst
│   └── prerm
├── usr/
│   ├── bin/
│   │   └── vmgateway
│   └── share/
│       ├── applications/
│       │   └── vmgateway.desktop
│       ├── icons/
│       │   └── hicolor/
│       │       └── 256x256/
│       │           └── apps/
│       │               └── vmgateway.png
│       └── doc/
│           └── vmgateway/
│               └── README.md
```

**RPM Package (Fedora/RHEL):**
```spec
Name:           vmgateway
Version:        1.0.0
Release:        1%{?dist}
Summary:        VM Gateway Client

License:        MIT
URL:            https://github.com/vmgateway/client
Source0:        %{name}-%{version}.tar.gz

BuildRequires:  python3-devel
Requires:       python3 >= 3.11

%description
VM Gateway Client for secure service access

%install
mkdir -p %{buildroot}%{_bindir}
install -m 755 vmgateway %{buildroot}%{_bindir}/vmgateway

%files
%{_bindir}/vmgateway
%{_datadir}/applications/vmgateway.desktop
%{_datadir}/icons/hicolor/256x256/apps/vmgateway.png
```

**AppImage (Universal):**
- Self-contained executable
- No installation required
- Includes all dependencies
- Works on any Linux distribution

**Flatpak:**
- Sandboxed application
- Distributed through Flathub
- Automatic updates
- Permission management

### Platform-Specific Challenges

**Challenge: Multiple Desktop Environments**
- Solution: Support AppIndicator (GNOME, Unity) and StatusNotifier (KDE)
- Fallback: GTK system tray
- Test on major DEs: GNOME, KDE, XFCE, Cinnamon

**Challenge: WireGuard Availability**
- Solution: Check for kernel module or wireguard-tools
- Provide installation instructions
- Fallback to TLS tunnel

**Challenge: Permission for Network Operations**
- Solution: Use capabilities instead of setuid
- Provide polkit policy for privileged operations
- Clear error messages with solutions

## Cross-Platform Abstractions

### Platform Detection

```python
import platform
import sys

class Platform:
    @staticmethod
    def get_os():
        """Get current operating system"""
        system = platform.system()
        if system == "Windows":
            return "windows"
        elif system == "Darwin":
            return "macos"
        elif system == "Linux":
            return "linux"
        else:
            return "unknown"
    
    @staticmethod
    def get_version():
        """Get OS version"""
        return platform.version()
    
    @staticmethod
    def get_architecture():
        """Get system architecture"""
        return platform.machine()
    
    @staticmethod
    def is_64bit():
        """Check if 64-bit system"""
        return sys.maxsize > 2**32
```

### Unified API

```python
class PlatformAdapter:
    """Unified API for platform-specific operations"""
    
    def __init__(self):
        self.os = Platform.get_os()
    
    def store_credential(self, key: str, value: str):
        """Store credential in platform keyring"""
        if self.os == "windows":
            return store_token_windows(value)
        elif self.os == "macos":
            return store_token_macos(value)
        elif self.os == "linux":
            return store_token_linux(value)
    
    def retrieve_credential(self, key: str) -> str:
        """Retrieve credential from platform keyring"""
        if self.os == "windows":
            return retrieve_token_windows()
        elif self.os == "macos":
            return retrieve_token_macos()
        elif self.os == "linux":
            return retrieve_token_linux()
    
    def show_notification(self, title: str, message: str):
        """Show platform notification"""
        if self.os == "windows":
            return show_notification_windows(title, message)
        elif self.os == "macos":
            return show_notification_macos(title, message)
        elif self.os == "linux":
            return show_notification_linux(title, message)
    
    def enable_autostart(self):
        """Enable application auto-start"""
        if self.os == "windows":
            return enable_autostart_windows()
        elif self.os == "macos":
            return enable_autostart_macos()
        elif self.os == "linux":
            return enable_autostart_linux()
    
    def disable_autostart(self):
        """Disable application auto-start"""
        if self.os == "windows":
            return disable_autostart_windows()
        elif self.os == "macos":
            return disable_autostart_macos()
        elif self.os == "linux":
            return disable_autostart_linux()
```

## Testing Strategy

### Platform-Specific Testing

**Automated Testing:**
- Unit tests run on all platforms via CI/CD
- Integration tests on virtual machines
- UI tests with platform-specific frameworks

**Manual Testing:**
- Test on physical hardware for each platform
- Test on different OS versions
- Test on different desktop environments (Linux)
- Test with different screen resolutions and DPI settings

**Beta Testing:**
- Platform-specific beta channels
- Collect feedback from users on each platform
- Monitor crash reports and error logs

## Distribution

### Update Mechanism

**Cross-Platform Update System:**
```python
import requests
import hashlib

class UpdateManager:
    def __init__(self, current_version: str):
        self.current_version = current_version
        self.update_url = "https://updates.vmgateway.com"
    
    def check_for_updates(self) -> dict:
        """Check if updates are available"""
        response = requests.get(
            f"{self.update_url}/latest",
            params={"platform": Platform.get_os()}
        )
        
        latest = response.json()
        
        if self.is_newer(latest["version"], self.current_version):
            return latest
        return None
    
    def download_update(self, update_info: dict):
        """Download update package"""
        response = requests.get(update_info["download_url"], stream=True)
        
        # Save to temp file
        temp_file = f"/tmp/vmgateway-update.{self.get_extension()}"
        
        with open(temp_file, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        # Verify checksum
        if not self.verify_checksum(temp_file, update_info["checksum"]):
            raise UpdateError("Checksum verification failed")
        
        return temp_file
    
    def install_update(self, update_file: str):
        """Install downloaded update"""
        if Platform.get_os() == "windows":
            # Run MSI installer
            os.system(f'msiexec /i "{update_file}" /quiet')
        elif Platform.get_os() == "macos":
            # Mount DMG and copy app
            os.system(f'hdiutil attach "{update_file}"')
            # ... copy logic
        elif Platform.get_os() == "linux":
            # Install DEB/RPM package
            if update_file.endswith(".deb"):
                os.system(f'sudo dpkg -i "{update_file}"')
            elif update_file.endswith(".rpm"):
                os.system(f'sudo rpm -U "{update_file}"')
```

## Summary

The VM Gateway Client provides a consistent, native experience across Windows, macOS, and Linux through careful platform-specific implementation and cross-platform abstractions. By integrating with platform-specific features like credential storage, notifications, and auto-start mechanisms, the client feels native on each operating system while maintaining feature parity.

The use of platform adapters and unified APIs ensures that core functionality remains consistent while allowing for platform-specific optimizations and integrations. Comprehensive testing on each platform ensures reliability and a polished user experience regardless of the user's operating system choice.
