# System Requirements

This document outlines the hardware, software, and network requirements for deploying VM Gateway in your environment.

## Controller Requirements

The controller is the central management server and requires the most resources.

### Minimum Requirements (Small Deployments)

For deployments with up to 50 VMs and 500 services:

**Hardware:**
- CPU: 2 cores (2.0 GHz or faster)
- RAM: 4 GB
- Storage: 50 GB SSD
- Network: 100 Mbps

**Software:**
- Operating System: Windows Server 2019+, Ubuntu 20.04+, RHEL 8+, or macOS 11+
- PostgreSQL: 14.0 or higher
- Redis: 7.0 or higher

### Recommended Requirements (Medium Deployments)

For deployments with 50-500 VMs and up to 5,000 services:

**Hardware:**
- CPU: 4 cores (2.5 GHz or faster)
- RAM: 16 GB
- Storage: 200 GB SSD (NVMe preferred)
- Network: 1 Gbps

**Software:**
- Operating System: Ubuntu 22.04 LTS or RHEL 9 (recommended for production)
- PostgreSQL: 15.0 or higher with connection pooling
- Redis: 7.2 or higher with persistence enabled

### Enterprise Requirements (Large Deployments)

For deployments with 500+ VMs and 10,000+ services:

**Hardware:**
- CPU: 8+ cores (3.0 GHz or faster)
- RAM: 32 GB or more
- Storage: 500 GB+ NVMe SSD
- Network: 10 Gbps
- Load Balancer: For distributed controller deployment

**Software:**
- Operating System: Ubuntu 22.04 LTS or RHEL 9
- PostgreSQL: 15.0+ with replication and connection pooling
- Redis: 7.2+ cluster mode
- Kubernetes: Optional, for container orchestration

### Storage Considerations

**Database Storage:**
- Metrics retention: ~100 MB per VM per day
- Audit logs: ~50 MB per 1,000 users per day
- Service catalog: ~1 MB per 1,000 services
- Plan for 3-6 months of retention minimum

**Log Storage:**
- Application logs: ~500 MB per day (varies with log level)
- Implement log rotation to prevent disk exhaustion

## Agent Requirements

Agents are lightweight and have minimal resource overhead.

### Per-Agent Requirements

**Hardware:**
- CPU: 0.1-0.5% of one core (idle to active scanning)
- RAM: 50-100 MB
- Storage: 100 MB for agent binary and local cache
- Network: Minimal (1-5 Mbps for metrics reporting)

**Software:**
- Operating System: Windows Server 2012+, Windows 10+, Ubuntu 18.04+, RHEL 7+, macOS 10.15+
- No additional dependencies (agent is a standalone executable)

### Agent Scalability

A single VM can run the agent alongside hundreds of services with negligible performance impact:
- Scanning overhead: <1% CPU during active scans
- Memory footprint: Scales with number of discovered services (~1 KB per service)
- Network usage: ~10 KB/s average for metrics reporting

## Client Requirements

The desktop client runs on user workstations.

### Desktop Client Requirements

**Hardware:**
- CPU: 1 core (1.5 GHz or faster)
- RAM: 512 MB
- Storage: 200 MB
- Network: Depends on tunneled traffic volume

**Software:**
- Windows: Windows 10 or higher
- macOS: macOS 11 (Big Sur) or higher
- Linux: Ubuntu 20.04+, Fedora 35+, or equivalent with GTK 3.24+

### Client Scalability

- Supports up to 50 simultaneous port forwards per client
- Bandwidth: Limited only by network connection
- Latency: Adds <5ms overhead for tunneled connections

## Database Requirements

### PostgreSQL Configuration

**Minimum Version:** 14.0

**Recommended Configuration:**
```ini
# Memory settings
shared_buffers = 4GB                    # 25% of system RAM
effective_cache_size = 12GB             # 75% of system RAM
work_mem = 64MB
maintenance_work_mem = 1GB

# Connection settings
max_connections = 200
max_prepared_transactions = 200

# Write-ahead log
wal_level = replica
max_wal_size = 4GB
min_wal_size = 1GB

# Query planning
random_page_cost = 1.1                  # For SSD storage
effective_io_concurrency = 200          # For SSD storage

# Logging
log_min_duration_statement = 1000       # Log slow queries (>1s)
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
```

**Storage:**
- Initial database size: ~100 MB
- Growth rate: ~100-500 MB per day (depends on VM count and retention)
- Recommend separate disk/partition for PostgreSQL data

**Backup:**
- Daily full backups recommended
- Point-in-time recovery (PITR) for production deployments
- See [Backup & Recovery](/docs/08-deployment/08-backup-recovery.md)

### Redis Configuration

**Minimum Version:** 7.0

**Recommended Configuration:**
```ini
# Memory
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec

# Performance
tcp-backlog 511
timeout 300
tcp-keepalive 300

# Security
requirepass your_secure_redis_password
```

**Storage:**
- Redis data size: ~50-200 MB (depends on active sessions and cache)
- Persistence files: ~100-500 MB

## Network Requirements

### Bandwidth Requirements

**Controller:**
- Inbound: 1-10 Mbps per 100 VMs (agent metrics)
- Outbound: Depends on web interface usage and tunneled traffic
- Peak: 100+ Mbps for large deployments with many active tunnels

**Agent:**
- Outbound: 10-50 KB/s average per agent
- Peak: 1-5 Mbps during initial service discovery

**Client:**
- Bandwidth: Matches tunneled application traffic
- Overhead: <5% for encryption and protocol overhead

### Port Requirements

**Controller Ports:**
- 8443/TCP: Web interface (HTTPS) - must be accessible to users
- 8444/TCP: Agent API (HTTPS with mTLS) - must be accessible to agents
- 8445/TCP: Client tunnel API (WSS) - must be accessible to clients
- 5432/TCP: PostgreSQL (if not on same host)
- 6379/TCP: Redis (if not on same host)

**Agent Ports:**
- No inbound ports required (agents initiate outbound connections)
- Outbound: 8444/TCP to controller

**Client Ports:**
- No inbound ports required
- Outbound: 8443/TCP and 8445/TCP to controller
- Local: Dynamic ports for port forwarding (configurable)

### Firewall Rules

**Controller Firewall:**
```bash
# Allow web interface
iptables -A INPUT -p tcp --dport 8443 -j ACCEPT

# Allow agent connections
iptables -A INPUT -p tcp --dport 8444 -j ACCEPT

# Allow client tunnels
iptables -A INPUT -p tcp --dport 8445 -j ACCEPT

# Allow PostgreSQL (if remote)
iptables -A INPUT -p tcp --dport 5432 -s <db-server-ip> -j ACCEPT

# Allow Redis (if remote)
iptables -A INPUT -p tcp --dport 6379 -s <redis-server-ip> -j ACCEPT
```

**Agent Firewall:**
```bash
# Allow outbound to controller
iptables -A OUTPUT -p tcp --dport 8444 -d <controller-ip> -j ACCEPT
```

### Network Latency

**Controller to Agent:**
- Recommended: <50ms
- Maximum: <200ms (higher latency increases metric reporting delay)

**Client to Controller:**
- Recommended: <100ms for good user experience
- Maximum: <500ms (higher latency affects tunnel responsiveness)

### DNS Requirements

**Required:**
- Controller must be reachable via hostname or IP
- Agents and clients must be able to resolve controller hostname

**Recommended:**
- Use DNS names instead of IPs for flexibility
- Configure proper reverse DNS for audit logging
- Consider split-horizon DNS for internal/external access

## TLS Certificate Requirements

### Controller Certificate

**Required:**
- Valid TLS certificate for web interface
- Certificate must match controller hostname
- Minimum: TLS 1.2, Recommended: TLS 1.3

**Options:**
1. **Self-signed certificate** (development/testing only)
2. **Internal CA certificate** (recommended for internal deployments)
3. **Public CA certificate** (Let's Encrypt, DigiCert, etc.)

**Certificate Requirements:**
- Key size: 2048-bit RSA minimum, 4096-bit recommended
- Subject Alternative Names (SANs) for all controller hostnames
- Validity period: 1-2 years maximum

### Agent mTLS Certificates

Agents use mutual TLS (mTLS) for authentication:
- Controller acts as CA for agent certificates
- Certificates auto-generated during agent registration
- Automatic rotation every 90 days

### Client Certificates

Optional client certificate authentication:
- Can be enabled for additional security
- Supports smart cards and hardware tokens
- See [Authentication Configuration](/docs/06-authentication/01-overview.md)

## Browser Requirements

### Supported Browsers

**Fully Supported:**
- Google Chrome 100+
- Microsoft Edge 100+
- Mozilla Firefox 100+
- Safari 15+

**Minimum Requirements:**
- JavaScript enabled
- Cookies enabled
- WebSocket support
- TLS 1.2+ support

**Recommended:**
- Latest browser version for security
- Hardware acceleration enabled
- Ad blockers may need to whitelist controller domain

## Operating System Compatibility

### Controller OS Support

**Linux (Recommended for Production):**
- Ubuntu 20.04 LTS, 22.04 LTS
- RHEL 8, 9
- CentOS Stream 8, 9
- Debian 11, 12
- Rocky Linux 8, 9
- AlmaLinux 8, 9

**Windows:**
- Windows Server 2019
- Windows Server 2022

**macOS:**
- macOS 11 (Big Sur)
- macOS 12 (Monterey)
- macOS 13 (Ventura)
- macOS 14 (Sonoma)

### Agent OS Support

**Linux:**
- Ubuntu 18.04+, 20.04+, 22.04+
- RHEL 7+, 8+, 9+
- CentOS 7+, Stream 8+
- Debian 10+, 11+, 12+
- Fedora 35+
- Amazon Linux 2, 2023
- SUSE Linux Enterprise 15+

**Windows:**
- Windows Server 2012 R2+
- Windows Server 2016+
- Windows Server 2019+
- Windows Server 2022+
- Windows 10 (for development VMs)
- Windows 11

**macOS:**
- macOS 10.15 (Catalina)+
- macOS 11 (Big Sur)+
- macOS 12 (Monterey)+
- macOS 13 (Ventura)+
- macOS 14 (Sonoma)+

### Client OS Support

**Windows:**
- Windows 10 (version 1909+)
- Windows 11

**macOS:**
- macOS 11 (Big Sur)+
- macOS 12 (Monterey)+
- macOS 13 (Ventura)+
- macOS 14 (Sonoma)+

**Linux:**
- Ubuntu 20.04+, 22.04+
- Fedora 35+
- Debian 11+
- Arch Linux (latest)
- Pop!_OS 22.04+

## Virtualization Platform Support

VM Gateway works with any virtualization platform. Tested platforms include:

**Cloud Providers:**
- AWS EC2
- Azure Virtual Machines
- Google Compute Engine
- DigitalOcean Droplets
- Linode
- Vultr

**On-Premises Hypervisors:**
- VMware vSphere / ESXi
- Microsoft Hyper-V
- Proxmox VE
- KVM / QEMU
- XenServer / XCP-ng
- Oracle VirtualBox (development only)

**Container Platforms:**
- Docker (controller and agent can run in containers)
- Kubernetes (see [Kubernetes Deployment](/docs/08-deployment/03-kubernetes.md))
- Podman
- LXC / LXD

## Scalability Limits

### Single Controller Limits

- Maximum VMs: 1,000
- Maximum services: 50,000
- Maximum concurrent users: 500
- Maximum concurrent tunnels: 5,000
- Maximum API requests: 10,000/minute

### Distributed Controller Limits

- Maximum VMs: 10,000+
- Maximum services: 500,000+
- Maximum concurrent users: 5,000+
- Maximum concurrent tunnels: 50,000+
- Maximum API requests: 100,000+/minute

See [High Availability Deployment](/docs/08-deployment/07-high-availability.md) for distributed architecture.

## Performance Benchmarks

### Controller Performance

**Service Discovery:**
- 1,000 services discovered: <5 seconds
- 10,000 services discovered: <30 seconds
- 100,000 services discovered: <5 minutes

**API Response Times:**
- Service catalog query: <100ms (p95)
- Metrics query: <200ms (p95)
- Tunnel establishment: <500ms (p95)

**Database Performance:**
- Metrics ingestion: 10,000+ points/second
- Audit log writes: 1,000+ events/second
- Service catalog queries: <50ms (p95)

### Agent Performance

**Scanning Performance:**
- Full port scan (65,535 ports): 5-30 seconds
- Smart scan (common ports): 1-5 seconds
- Service classification: <100ms per service

**Resource Usage:**
- CPU: <0.5% average, <5% during scans
- Memory: 50-100 MB
- Disk I/O: Minimal (<1 MB/s)
- Network: 10-50 KB/s average

## Security Requirements

### Minimum Security Standards

**Encryption:**
- TLS 1.2 minimum (TLS 1.3 recommended)
- AES-256-GCM for data at rest
- Perfect forward secrecy (PFS) for all connections

**Authentication:**
- Strong password policy (12+ characters, complexity requirements)
- Multi-factor authentication (MFA) recommended
- API key rotation every 90 days

**Network Security:**
- Firewall rules limiting access to required ports only
- Network segmentation recommended
- DDoS protection for internet-facing controllers

**Compliance:**
- Audit logging enabled
- Log retention per compliance requirements
- Regular security updates

See [Security Model](/docs/02-architecture/04-security-model.md) for comprehensive security documentation.

## Monitoring Requirements

### Required Monitoring

**Controller Health:**
- CPU, memory, disk usage
- Database connection pool status
- Redis connection status
- API response times
- Error rates

**Agent Health:**
- Agent connectivity status
- Last successful scan time
- Metric reporting lag
- Error rates

**System Health:**
- PostgreSQL performance metrics
- Redis performance metrics
- Network connectivity
- TLS certificate expiration

### Recommended Monitoring Tools

- Prometheus + Grafana (metrics and dashboards)
- ELK Stack (log aggregation)
- Nagios / Icinga (infrastructure monitoring)
- PagerDuty (alerting)

See [Monitoring Configuration](/docs/04-controller/07-monitoring.md) for setup details.

## Backup Requirements

### What to Back Up

**Critical Data:**
- PostgreSQL database (full backup)
- Controller configuration files
- TLS certificates and private keys
- Agent API keys and secrets

**Optional Data:**
- Redis data (can be rebuilt)
- Application logs (if needed for compliance)

### Backup Frequency

**Production:**
- Database: Daily full backup + continuous WAL archiving
- Configuration: After every change
- Certificates: After generation/renewal

**Development/Staging:**
- Database: Weekly full backup
- Configuration: After major changes

See [Backup & Recovery](/docs/08-deployment/08-backup-recovery.md) for detailed procedures.

## Related Documentation

- [Quick Start Guide](/docs/00-getting-started/01-quick-start.md) - Get started with installation
- [Controller Installation](/docs/04-controller/01-overview.md) - Detailed controller setup
- [Agent Installation](/docs/03-agent/06-installation.md) - Detailed agent setup
- [High Availability](/docs/08-deployment/07-high-availability.md) - Production deployment architecture
- [Performance Tuning](/docs/10-development/06-performance.md) - Optimize your deployment
