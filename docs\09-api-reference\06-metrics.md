---
title: "Metrics API"
section: "API Reference"
order: 6
tags: ["api", "metrics", "monitoring", "endpoints"]
last_updated: "2025-11-08"
---

# Metrics API

The Metrics API provides endpoints for querying system and service metrics, creating custom dashboards, and exporting monitoring data. This API supports both real-time and historical metric queries with flexible aggregation options.

## Base URL

```
https://your-gateway.example.com/api/v1/metrics
```

## Metric Types

The platform collects and exposes several categories of metrics:

1. **System Metrics**: CPU, memory, disk, network usage at VM level
2. **Service Metrics**: Service-specific performance indicators
3. **Connection Metrics**: Tunnel and proxy connection statistics
4. **Platform Metrics**: Controller and agent health metrics
5. **Custom Metrics**: User-defined application metrics

## Endpoints

### GET /metrics/query

Query metrics with flexible filtering and aggregation.

**Request:**

```http
GET /api/v1/metrics/query?metric=cpu_percent&resource_type=vm&resource_id=vm_abc123&from=2025-11-08T00:00:00Z&to=2025-11-08T23:59:59Z&resolution=5m&aggregation=avg HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `metric` | string | Yes | Metric name (e.g., `cpu_percent`, `memory_percent`, `requests_per_second`) |
| `resource_type` | string | Yes | Resource type: `vm`, `service`, `connection`, `agent` |
| `resource_id` | string | Yes | ID of the resource to query |
| `from` | string | Yes | Start time (ISO 8601 format) |
| `to` | string | Yes | End time (ISO 8601 format) |
| `resolution` | string | No | Data resolution: `1m`, `5m`, `15m`, `1h`, `6h`, `1d` (default: `5m`) |
| `aggregation` | string | No | Aggregation function: `avg`, `min`, `max`, `sum`, `count` (default: `avg`) |
| `fill` | string | No | Fill missing data: `null`, `zero`, `previous`, `linear` (default: `null`) |

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "query": {
    "metric": "cpu_percent",
    "resource_type": "vm",
    "resource_id": "vm_abc123",
    "from": "2025-11-08T00:00:00Z",
    "to": "2025-11-08T23:59:59Z",
    "resolution": "5m",
    "aggregation": "avg"
  },
  "metadata": {
    "unit": "percent",
    "description": "CPU usage percentage",
    "data_points": 288,
    "missing_points": 0
  },
  "data": [
    {
      "timestamp": "2025-11-08T00:00:00Z",
      "value": 42.5,
      "min": 38.2,
      "max": 48.9,
      "count": 60
    },
    {
      "timestamp": "2025-11-08T00:05:00Z",
      "value": 45.1,
      "min": 41.0,
      "max": 52.3,
      "count": 60
    },
    {
      "timestamp": "2025-11-08T00:10:00Z",
      "value": 43.8,
      "min": 39.5,
      "max": 50.1,
      "count": 60
    }
  ],
  "statistics": {
    "average": 44.2,
    "minimum": 38.2,
    "maximum": 52.3,
    "median": 43.8,
    "p95": 50.5,
    "p99": 51.8,
    "standard_deviation": 3.2
  }
}
```

---

### POST /metrics/query/multi

Query multiple metrics in a single request for efficient dashboard loading.

**Request:**

```http
POST /api/v1/metrics/query/multi HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "queries": [
    {
      "id": "cpu",
      "metric": "cpu_percent",
      "resource_type": "vm",
      "resource_id": "vm_abc123",
      "from": "2025-11-08T00:00:00Z",
      "to": "2025-11-08T23:59:59Z",
      "resolution": "5m"
    },
    {
      "id": "memory",
      "metric": "memory_percent",
      "resource_type": "vm",
      "resource_id": "vm_abc123",
      "from": "2025-11-08T00:00:00Z",
      "to": "2025-11-08T23:59:59Z",
      "resolution": "5m"
    },
    {
      "id": "disk",
      "metric": "disk_percent",
      "resource_type": "vm",
      "resource_id": "vm_abc123",
      "from": "2025-11-08T00:00:00Z",
      "to": "2025-11-08T23:59:59Z",
      "resolution": "5m"
    }
  ]
}
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "results": {
    "cpu": {
      "success": true,
      "data": [
        {"timestamp": "2025-11-08T00:00:00Z", "value": 42.5},
        {"timestamp": "2025-11-08T00:05:00Z", "value": 45.1}
      ],
      "statistics": {"average": 44.2, "minimum": 38.2, "maximum": 52.3}
    },
    "memory": {
      "success": true,
      "data": [
        {"timestamp": "2025-11-08T00:00:00Z", "value": 62.8},
        {"timestamp": "2025-11-08T00:05:00Z", "value": 63.2}
      ],
      "statistics": {"average": 63.0, "minimum": 60.5, "maximum": 65.8}
    },
    "disk": {
      "success": true,
      "data": [
        {"timestamp": "2025-11-08T00:00:00Z", "value": 38.5},
        {"timestamp": "2025-11-08T00:05:00Z", "value": 38.5}
      ],
      "statistics": {"average": 38.5, "minimum": 38.5, "maximum": 38.6}
    }
  },
  "execution_time_ms": 145
}
```

---

### GET /metrics/available

List all available metrics for a resource.

**Request:**

```http
GET /api/v1/metrics/available?resource_type=vm&resource_id=vm_abc123 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "resource": {
    "type": "vm",
    "id": "vm_abc123",
    "name": "prod-web-01"
  },
  "metrics": [
    {
      "name": "cpu_percent",
      "display_name": "CPU Usage",
      "description": "CPU usage percentage across all cores",
      "unit": "percent",
      "type": "gauge",
      "category": "system",
      "available_since": "2025-10-01T10:00:00Z",
      "sample_rate": "1s",
      "retention": "7d"
    },
    {
      "name": "memory_percent",
      "display_name": "Memory Usage",
      "description": "Memory usage percentage",
      "unit": "percent",
      "type": "gauge",
      "category": "system",
      "available_since": "2025-10-01T10:00:00Z",
      "sample_rate": "1s",
      "retention": "7d"
    },
    {
      "name": "disk_percent",
      "display_name": "Disk Usage",
      "description": "Disk usage percentage across all mount points",
      "unit": "percent",
      "type": "gauge",
      "category": "system",
      "available_since": "2025-10-01T10:00:00Z",
      "sample_rate": "1s",
      "retention": "7d"
    },
    {
      "name": "network_in_mbps",
      "display_name": "Network In",
      "description": "Incoming network traffic in megabits per second",
      "unit": "mbps",
      "type": "counter",
      "category": "network",
      "available_since": "2025-10-01T10:00:00Z",
      "sample_rate": "1s",
      "retention": "7d"
    },
    {
      "name": "network_out_mbps",
      "display_name": "Network Out",
      "description": "Outgoing network traffic in megabits per second",
      "unit": "mbps",
      "type": "counter",
      "category": "network",
      "available_since": "2025-10-01T10:00:00Z",
      "sample_rate": "1s",
      "retention": "7d"
    }
  ],
  "total": 5
}
```

---

### POST /metrics/custom

Submit custom application metrics.

**Request:**

```http
POST /api/v1/metrics/custom HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "resource_type": "service",
  "resource_id": "svc_abc123def456",
  "metrics": [
    {
      "name": "api_requests_total",
      "value": 1250,
      "timestamp": "2025-11-08T15:00:00Z",
      "tags": {
        "endpoint": "/api/users",
        "method": "GET",
        "status": "200"
      }
    },
    {
      "name": "api_response_time_ms",
      "value": 45.2,
      "timestamp": "2025-11-08T15:00:00Z",
      "tags": {
        "endpoint": "/api/users",
        "method": "GET"
      }
    },
    {
      "name": "database_connections",
      "value": 42,
      "timestamp": "2025-11-08T15:00:00Z"
    }
  ]
}
```

**Response:**

```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,
  "accepted": 3,
  "rejected": 0,
  "message": "3 metrics accepted successfully"
}
```

---

### GET /metrics/export

Export metrics data in various formats.

**Request:**

```http
GET /api/v1/metrics/export?metric=cpu_percent&resource_type=vm&resource_id=vm_abc123&from=2025-11-08T00:00:00Z&to=2025-11-08T23:59:59Z&format=csv HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `metric` | string | Yes | Metric name to export |
| `resource_type` | string | Yes | Resource type |
| `resource_id` | string | Yes | Resource ID |
| `from` | string | Yes | Start time (ISO 8601) |
| `to` | string | Yes | End time (ISO 8601) |
| `format` | string | No | Export format: `json`, `csv`, `prometheus` (default: `json`) |
| `resolution` | string | No | Data resolution (default: `1m`) |

**Response (CSV format):**

```http
HTTP/1.1 200 OK
Content-Type: text/csv
Content-Disposition: attachment; filename="cpu_percent_vm_abc123_2025-11-08.csv"

timestamp,value,min,max,count
2025-11-08T00:00:00Z,42.5,38.2,48.9,60
2025-11-08T00:05:00Z,45.1,41.0,52.3,60
2025-11-08T00:10:00Z,43.8,39.5,50.1,60
```

**Response (Prometheus format):**

```http
HTTP/1.1 200 OK
Content-Type: text/plain

# HELP vm_cpu_percent CPU usage percentage
# TYPE vm_cpu_percent gauge
vm_cpu_percent{vm_id="vm_abc123",vm_name="prod-web-01"} 42.5 1699401600000
vm_cpu_percent{vm_id="vm_abc123",vm_name="prod-web-01"} 45.1 1699401900000
vm_cpu_percent{vm_id="vm_abc123",vm_name="prod-web-01"} 43.8 1699402200000
```

---

### GET /metrics/prometheus

Prometheus-compatible metrics endpoint for scraping.

**Request:**

```http
GET /api/v1/metrics/prometheus HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: text/plain; version=0.0.4

# HELP vm_gateway_vms_total Total number of registered VMs
# TYPE vm_gateway_vms_total gauge
vm_gateway_vms_total{status="online"} 45
vm_gateway_vms_total{status="offline"} 3
vm_gateway_vms_total{status="degraded"} 2

# HELP vm_gateway_services_total Total number of discovered services
# TYPE vm_gateway_services_total gauge
vm_gateway_services_total{type="web"} 120
vm_gateway_services_total{type="database"} 35
vm_gateway_services_total{type="cache"} 18
vm_gateway_services_total{type="queue"} 12

# HELP vm_gateway_connections_active Active connections
# TYPE vm_gateway_connections_active gauge
vm_gateway_connections_active{type="tunnel"} 28
vm_gateway_connections_active{type="proxy"} 15

# HELP vm_gateway_api_requests_total Total API requests
# TYPE vm_gateway_api_requests_total counter
vm_gateway_api_requests_total{method="GET",status="200"} 125430
vm_gateway_api_requests_total{method="POST",status="201"} 8920
vm_gateway_api_requests_total{method="GET",status="404"} 245

# HELP vm_gateway_api_request_duration_seconds API request duration
# TYPE vm_gateway_api_request_duration_seconds histogram
vm_gateway_api_request_duration_seconds_bucket{le="0.01"} 95420
vm_gateway_api_request_duration_seconds_bucket{le="0.05"} 128340
vm_gateway_api_request_duration_seconds_bucket{le="0.1"} 132890
vm_gateway_api_request_duration_seconds_bucket{le="0.5"} 134120
vm_gateway_api_request_duration_seconds_bucket{le="1.0"} 134450
vm_gateway_api_request_duration_seconds_bucket{le="+Inf"} 134595
vm_gateway_api_request_duration_seconds_sum 2845.32
vm_gateway_api_request_duration_seconds_count 134595
```

---

### POST /metrics/alerts

Create a metric-based alert rule.

**Request:**

```http
POST /api/v1/metrics/alerts HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "name": "High CPU Usage on Production VMs",
  "description": "Alert when CPU exceeds 80% for 5 minutes",
  "enabled": true,
  "metric": "cpu_percent",
  "resource_type": "vm",
  "resource_filter": {
    "tags": ["production"]
  },
  "condition": {
    "operator": "greater_than",
    "threshold": 80,
    "duration": "5m",
    "aggregation": "avg"
  },
  "severity": "warning",
  "actions": [
    {
      "type": "email",
      "recipients": ["<EMAIL>"],
      "template": "high_cpu_alert"
    },
    {
      "type": "slack",
      "channel": "#production-alerts",
      "mention": "@oncall"
    }
  ],
  "notification": {
    "grouping_window": "15m",
    "repeat_interval": "4h",
    "auto_resolve": true
  }
}
```

**Response:**

```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,
  "alert": {
    "id": "alert_abc123def456",
    "name": "High CPU Usage on Production VMs",
    "enabled": true,
    "status": "active",
    "created_at": "2025-11-08T15:30:00Z",
    "created_by": "usr_1234567890"
  },
  "message": "Alert rule created successfully"
}
```

---

### GET /metrics/alerts

List all alert rules.

**Request:**

```http
GET /api/v1/metrics/alerts?status=active&severity=warning HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "alerts": [
    {
      "id": "alert_abc123def456",
      "name": "High CPU Usage on Production VMs",
      "description": "Alert when CPU exceeds 80% for 5 minutes",
      "enabled": true,
      "status": "active",
      "severity": "warning",
      "metric": "cpu_percent",
      "condition": {
        "operator": "greater_than",
        "threshold": 80,
        "duration": "5m"
      },
      "triggered_count": 3,
      "last_triggered": "2025-11-08T14:30:00Z",
      "created_at": "2025-11-01T10:00:00Z",
      "updated_at": "2025-11-08T15:30:00Z"
    }
  ],
  "total": 1
}
```

---

### GET /metrics/dashboards

List all custom dashboards.

**Request:**

```http
GET /api/v1/metrics/dashboards HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "dashboards": [
    {
      "id": "dash_abc123",
      "name": "Production Overview",
      "description": "Overview of all production systems",
      "owner": {
        "id": "usr_1234567890",
        "name": "John Doe"
      },
      "visibility": "team",
      "widgets_count": 12,
      "created_at": "2025-10-15T10:00:00Z",
      "updated_at": "2025-11-08T14:00:00Z",
      "last_viewed": "2025-11-08T15:00:00Z"
    }
  ],
  "total": 1
}
```

---

### POST /metrics/dashboards

Create a custom dashboard.

**Request:**

```http
POST /api/v1/metrics/dashboards HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "name": "Database Performance",
  "description": "Monitor all database services",
  "visibility": "private",
  "layout": "grid",
  "refresh_interval": 30,
  "widgets": [
    {
      "type": "line_chart",
      "title": "Database CPU Usage",
      "position": {"x": 0, "y": 0, "width": 6, "height": 4},
      "query": {
        "metric": "cpu_percent",
        "resource_type": "service",
        "resource_filter": {"type": "database"},
        "time_range": "1h",
        "resolution": "1m"
      }
    },
    {
      "type": "gauge",
      "title": "Active Connections",
      "position": {"x": 6, "y": 0, "width": 3, "height": 4},
      "query": {
        "metric": "connections",
        "resource_type": "service",
        "resource_id": "svc_abc123def456",
        "aggregation": "current"
      },
      "thresholds": [
        {"value": 80, "color": "yellow"},
        {"value": 95, "color": "red"}
      ]
    }
  ]
}
```

**Response:**

```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,
  "dashboard": {
    "id": "dash_new123",
    "name": "Database Performance",
    "url": "/dashboards/dash_new123",
    "created_at": "2025-11-08T15:35:00Z"
  },
  "message": "Dashboard created successfully"
}
```

---

## Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `METRIC_NOT_FOUND` | 404 | Metric does not exist for resource |
| `INVALID_TIME_RANGE` | 400 | Invalid time range specified |
| `RESOLUTION_TOO_HIGH` | 400 | Requested resolution exceeds data retention |
| `QUERY_TIMEOUT` | 504 | Query took too long to execute |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many metric queries |
| `INSUFFICIENT_PERMISSIONS` | 403 | User lacks permission to view metrics |

## Code Examples

### Python

```python
import requests
from datetime import datetime, timedelta

headers = {"Authorization": "Bearer YOUR_ACCESS_TOKEN"}

# Query CPU metrics for the last hour
end_time = datetime.utcnow()
start_time = end_time - timedelta(hours=1)

response = requests.get(
    "https://your-gateway.example.com/api/v1/metrics/query",
    headers=headers,
    params={
        "metric": "cpu_percent",
        "resource_type": "vm",
        "resource_id": "vm_abc123",
        "from": start_time.isoformat() + "Z",
        "to": end_time.isoformat() + "Z",
        "resolution": "5m",
        "aggregation": "avg"
    }
)

data = response.json()
print(f"Average CPU: {data['statistics']['average']}%")
print(f"Peak CPU: {data['statistics']['maximum']}%")

# Plot the data
import matplotlib.pyplot as plt

timestamps = [point['timestamp'] for point in data['data']]
values = [point['value'] for point in data['data']]

plt.plot(timestamps, values)
plt.xlabel('Time')
plt.ylabel('CPU %')
plt.title('CPU Usage Over Time')
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()
```

### JavaScript

```javascript
const headers = {
  'Authorization': 'Bearer YOUR_ACCESS_TOKEN'
};

// Query multiple metrics for dashboard
const response = await fetch('https://your-gateway.example.com/api/v1/metrics/query/multi', {
  method: 'POST',
  headers: {
    ...headers,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    queries: [
      {
        id: 'cpu',
        metric: 'cpu_percent',
        resource_type: 'vm',
        resource_id: 'vm_abc123',
        from: new Date(Date.now() - 3600000).toISOString(),
        to: new Date().toISOString(),
        resolution: '5m'
      },
      {
        id: 'memory',
        metric: 'memory_percent',
        resource_type: 'vm',
        resource_id: 'vm_abc123',
        from: new Date(Date.now() - 3600000).toISOString(),
        to: new Date().toISOString(),
        resolution: '5m'
      }
    ]
  })
});

const data = await response.json();
console.log('CPU Average:', data.results.cpu.statistics.average);
console.log('Memory Average:', data.results.memory.statistics.average);
```

## Related Documentation

- [API Overview](./01-overview.md) - API design principles
- [VM Management API](./03-vm-management.md) - VM management
- [Service Catalog API](./04-service-catalog.md) - Service management
- [Controller Monitoring](../04-controller/07-monitoring.md) - Monitoring system details
