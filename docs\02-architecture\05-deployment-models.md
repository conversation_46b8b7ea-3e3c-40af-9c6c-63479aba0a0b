# Deployment Models

## Overview

The platform supports three primary deployment models to accommodate different organizational needs, scale requirements, and infrastructure preferences. Each model offers different trade-offs between simplicity, scalability, and resilience.

## Central Deployment Model

### Architecture

In the central deployment model, all controller components run on a single server or VM:

```
┌─────────────────────────────────────────────────────────┐
│                    Central Server                        │
│  ┌──────────────────────────────────────────────────┐  │
│  │              Controller Components                │  │
│  │  ┌──────────┐  ┌──────────┐  ┌──────────┐       │  │
│  │  │   Web    │  │   API    │  │  Proxy   │       │  │
│  │  │    UI    │  │  Server  │  │  System  │       │  │
│  │  └──────────┘  └──────────┘  └──────────┘       │  │
│  │  ┌──────────┐  ┌──────────┐  ┌──────────┐       │  │
│  │  │PostgreSQL│  │  Secrets │  │   Logs   │       │  │
│  │  │ Database │  │  Vault   │  │  Storage │       │  │
│  │  └──────────┘  └──────────┘  └──────────┘       │  │
│  └──────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────┘
                         │
        ┌────────────────┼────────────────┐
        │                │                │
   ┌────▼────┐      ┌────▼────┐     ┌────▼────┐
   │ Agent 1 │      │ Agent 2 │     │ Agent N │
   │  (VM1)  │      │  (VM2)  │     │  (VMN)  │
   └─────────┘      └─────────┘     └─────────┘
```

### Characteristics

**Advantages:**
- Simple to deploy and manage
- Lower infrastructure costs
- Easier troubleshooting and debugging
- Suitable for small to medium deployments
- Quick setup and configuration

**Disadvantages:**
- Single point of failure
- Limited horizontal scalability
- Resource constraints on single server
- Potential performance bottleneck

### Use Cases

- Development and testing environments
- Small organizations (< 50 VMs)
- Proof of concept deployments
- Budget-constrained deployments
- Low-traffic environments

### Resource Requirements

**Minimum:**
- 4 CPU cores
- 8 GB RAM
- 100 GB SSD storage
- 1 Gbps network

**Recommended:**
- 8 CPU cores
- 16 GB RAM
- 250 GB SSD storage
- 10 Gbps network

## Distributed Deployment Model

### Architecture

In the distributed deployment model, controller components are separated across multiple servers:

```
┌──────────────┐     ┌──────────────┐     ┌──────────────┐
│  Web Server  │     │  API Server  │     │ Proxy Server │
│              │     │              │     │              │
│  ┌────────┐  │     │  ┌────────┐  │     │  ┌────────┐  │
│  │  Web   │  │     │  │  API   │  │     │  │ Proxy  │  │
│  │   UI   │  │     │  │ Server │  │     │  │ System │  │
│  └────────┘  │     │  └────────┘  │     │  └────────┘  │
└──────┬───────┘     └──────┬───────┘     └──────┬───────┘
       │                    │                    │
       └────────────────────┼────────────────────┘
                            │
       ┌────────────────────┼────────────────────┐
       │                    │                    │
┌──────▼───────┐     ┌──────▼───────┐     ┌──────▼───────┐
│  Database    │     │   Secrets    │     │     Logs     │
│   Server     │     │    Server    │     │    Server    │
│              │     │              │     │              │
│ ┌──────────┐ │     │ ┌──────────┐ │     │ ┌──────────┐ │
│ │PostgreSQL│ │     │ │  Vault   │ │     │ │   Log    │ │
│ │          │ │     │ │          │ │     │ │  Store   │ │
│ └──────────┘ │     │ └──────────┘ │     │ └──────────┘ │
└──────────────┘     └──────────────┘     └──────────────┘
                            │
        ┌───────────────────┼───────────────────┐
        │                   │                   │
   ┌────▼────┐         ┌────▼────┐        ┌────▼────┐
   │ Agent 1 │         │ Agent 2 │        │ Agent N │
   │  (VM1)  │         │  (VM2)  │        │  (VMN)  │
   └─────────┘         └─────────┘        └─────────┘
```

### Characteristics

**Advantages:**
- Better scalability and performance
- Component isolation for security
- Independent scaling of components
- Reduced blast radius for failures
- Better resource utilization

**Disadvantages:**
- More complex deployment and management
- Higher infrastructure costs
- More network traffic between components
- Requires load balancing and service discovery

### Use Cases

- Medium to large organizations (50-500 VMs)
- Production environments requiring high availability
- Organizations with compliance requirements
- High-traffic environments
- Multi-tenant deployments

### Resource Requirements

**Per Component:**

Web Server:
- 4 CPU cores
- 8 GB RAM
- 50 GB SSD storage

API Server:
- 8 CPU cores
- 16 GB RAM
- 100 GB SSD storage

Proxy Server:
- 4 CPU cores
- 8 GB RAM
- 50 GB SSD storage

Database Server:
- 8 CPU cores
- 32 GB RAM
- 500 GB SSD storage

Secrets Server:
- 4 CPU cores
- 8 GB RAM
- 100 GB SSD storage

Logs Server:
- 4 CPU cores
- 16 GB RAM
- 1 TB SSD storage

## Hybrid Deployment Model

### Architecture

The hybrid deployment model combines central and distributed approaches with high availability:

```
                    ┌──────────────┐
                    │ Load Balancer│
                    └──────┬───────┘
                           │
        ┌──────────────────┼──────────────────┐
        │                  │                  │
┌───────▼────────┐  ┌──────▼───────┐  ┌──────▼───────┐
│ Controller 1   │  │ Controller 2 │  │ Controller 3 │
│                │  │              │  │              │
│ ┌────┐ ┌────┐ │  │ ┌────┐ ┌────┐│  │ ┌────┐ ┌────┐│
│ │Web │ │API │ │  │ │Web │ │API ││  │ │Web │ │API ││
│ └────┘ └────┘ │  │ └────┘ └────┘│  │ └────┘ └────┘│
│ ┌────┐        │  │ ┌────┐       │  │ ┌────┐       │
│ │Prxy│        │  │ │Prxy│       │  │ │Prxy│       │
│ └────┘        │  │ └────┘       │  │ └────┘       │
└───────┬────────┘  └──────┬───────┘  └──────┬───────┘
        │                  │                  │
        └──────────────────┼──────────────────┘
                           │
        ┌──────────────────┼──────────────────┐
        │                  │                  │
┌───────▼────────┐  ┌──────▼───────┐  ┌──────▼───────┐
│ PostgreSQL     │  │   Secrets    │  │     Logs     │
│   Cluster      │  │   Cluster    │  │   Cluster    │
│  (Primary +    │  │  (HA Vault)  │  │ (Distributed)│
│   Replicas)    │  │              │  │              │
└────────────────┘  └──────────────┘  └──────────────┘
                           │
        ┌──────────────────┼──────────────────┐
        │                  │                  │
   ┌────▼────┐        ┌────▼────┐       ┌────▼────┐
   │ Agent 1 │        │ Agent 2 │       │ Agent N │
   │  (VM1)  │        │  (VM2)  │       │  (VMN)  │
   └─────────┘        └─────────┘       └─────────┘
```

### Characteristics

**Advantages:**
- High availability with no single point of failure
- Horizontal scalability for all components
- Geographic distribution for disaster recovery
- Load balancing for optimal performance
- Rolling updates with zero downtime

**Disadvantages:**
- Most complex deployment and management
- Highest infrastructure costs
- Requires sophisticated orchestration
- Complex troubleshooting across distributed system

### Use Cases

- Large enterprises (500+ VMs)
- Mission-critical production environments
- Multi-region deployments
- Organizations requiring 99.99% uptime
- Compliance-heavy industries

### Resource Requirements

**Minimum 3 Controller Nodes:**
- 8 CPU cores each
- 16 GB RAM each
- 250 GB SSD storage each

**Database Cluster:**
- 1 Primary + 2 Replicas
- 8 CPU cores each
- 32 GB RAM each
- 1 TB SSD storage each

**Load Balancer:**
- 4 CPU cores
- 8 GB RAM
- 50 GB SSD storage

## Deployment Comparison

| Feature | Central | Distributed | Hybrid |
|---------|---------|-------------|--------|
| Complexity | Low | Medium | High |
| Cost | Low | Medium | High |
| Scalability | Limited | Good | Excellent |
| Availability | Single Point | Better | High Availability |
| Performance | Limited | Good | Excellent |
| Management | Simple | Moderate | Complex |
| Suitable Scale | < 50 VMs | 50-500 VMs | 500+ VMs |

## Migration Between Models

### Central to Distributed

1. Deploy separate database server
2. Migrate database with minimal downtime
3. Deploy API servers and configure database connection
4. Deploy web servers and configure API endpoints
5. Update agents to use new API endpoints
6. Decommission central server

### Distributed to Hybrid

1. Deploy additional controller nodes
2. Configure database replication
3. Deploy load balancer
4. Configure health checks and failover
5. Update DNS to point to load balancer
6. Test failover scenarios

### Rollback Procedures

Each migration includes rollback procedures:

- Database backups before migration
- DNS TTL reduced for quick rollback
- Parallel operation during transition
- Automated health checks
- Manual verification steps

## Cloud Provider Considerations

### AWS Deployment

- Use RDS for PostgreSQL
- Use ELB for load balancing
- Use Secrets Manager for secrets
- Use CloudWatch for logging
- Use Auto Scaling Groups for controllers

### Azure Deployment

- Use Azure Database for PostgreSQL
- Use Azure Load Balancer
- Use Azure Key Vault for secrets
- Use Azure Monitor for logging
- Use Virtual Machine Scale Sets

### Google Cloud Deployment

- Use Cloud SQL for PostgreSQL
- Use Cloud Load Balancing
- Use Secret Manager for secrets
- Use Cloud Logging
- Use Managed Instance Groups

### On-Premises Deployment

- Use PostgreSQL with streaming replication
- Use HAProxy or NGINX for load balancing
- Use HashiCorp Vault for secrets
- Use ELK stack for logging
- Use Kubernetes or Docker Swarm for orchestration

## Related Documentation

- [System Overview](01-system-overview.md)
- [High Availability](../08-deployment/07-high-availability.md)
- [Kubernetes Deployment](../08-deployment/03-kubernetes.md)
