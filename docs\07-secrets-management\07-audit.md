# Secret Audit Logging

Comprehensive audit logging tracks all secret-related activities for security, compliance, and troubleshooting purposes.

## Audit Events

### Secret Operations

**Create**:
```json
{
  "event_type": "secret.created",
  "timestamp": "2025-11-08T10:00:00Z",
  "actor": "user_xyz789",
  "secret_id": "secret_abc123",
  "secret_name": "database/prod/password",
  "metadata": {
    "type": "database_credential",
    "tags": ["production", "database"]
  }
}
```

**Read**:
```json
{
  "event_type": "secret.read",
  "timestamp": "2025-11-08T10:30:00Z",
  "actor": "service_agent_001",
  "secret_id": "secret_abc123",
  "secret_name": "database/prod/password",
  "ip_address": "*********",
  "user_agent": "vm-gateway-agent/1.0"
}
```

**Update**:
```json
{
  "event_type": "secret.updated",
  "timestamp": "2025-11-08T11:00:00Z",
  "actor": "user_xyz789",
  "secret_id": "secret_abc123",
  "secret_name": "database/prod/password",
  "changes": {
    "version": {"old": 1, "new": 2},
    "rotation_policy": {"old": null, "new": {"interval_days": 90}}
  }
}
```

**Delete**:
```json
{
  "event_type": "secret.deleted",
  "timestamp": "2025-11-08T12:00:00Z",
  "actor": "user_xyz789",
  "secret_id": "secret_abc123",
  "secret_name": "database/prod/password",
  "deletion_type": "soft"
}
```

### Access Control Events

**Permission Grant**:
```json
{
  "event_type": "secret.permission.granted",
  "timestamp": "2025-11-08T10:15:00Z",
  "actor": "user_xyz789",
  "secret_id": "secret_abc123",
  "principal": "group_developers",
  "permission_level": "reader"
}
```

**Access Denied**:
```json
{
  "event_type": "secret.access.denied",
  "timestamp": "2025-11-08T10:45:00Z",
  "actor": "user_abc456",
  "secret_id": "secret_abc123",
  "reason": "insufficient_permissions",
  "required_permission": "read_value"
}
```


### Rotation Events

**Rotation Started**:
```json
{
  "event_type": "secret.rotation.started",
  "timestamp": "2025-11-08T02:00:00Z",
  "actor": "system",
  "secret_id": "secret_abc123",
  "rotation_policy": {"interval_days": 90}
}
```

**Rotation Completed**:
```json
{
  "event_type": "secret.rotation.completed",
  "timestamp": "2025-11-08T02:05:00Z",
  "actor": "system",
  "secret_id": "secret_abc123",
  "old_version": 4,
  "new_version": 5
}
```

## Audit Log Storage

### Database Schema

```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(100) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    actor_id UUID,
    actor_type VARCHAR(50),
    secret_id UUID,
    secret_name VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    event_data JSONB,
    result VARCHAR(20),
    error_message TEXT
);

CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp DESC);
CREATE INDEX idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX idx_audit_logs_actor_id ON audit_logs(actor_id);
CREATE INDEX idx_audit_logs_secret_id ON audit_logs(secret_id);
```

### Log Retention

Audit logs are retained according to policy:

```yaml
audit:
  retention:
    default_days: 365
    compliance_days: 2555  # 7 years for compliance
    archive_after_days: 90
  storage:
    hot: postgresql
    warm: s3
    cold: glacier
```

## Audit Queries

### Common Audit Queries

**Who accessed a secret?**
```sql
SELECT actor_id, timestamp, ip_address
FROM audit_logs
WHERE secret_id = 'secret_abc123'
  AND event_type = 'secret.read'
ORDER BY timestamp DESC;
```

**What secrets did a user access?**
```sql
SELECT secret_name, timestamp, event_type
FROM audit_logs
WHERE actor_id = 'user_xyz789'
  AND event_type LIKE 'secret.%'
ORDER BY timestamp DESC;
```

**Failed access attempts**:
```sql
SELECT actor_id, secret_name, timestamp, error_message
FROM audit_logs
WHERE event_type = 'secret.access.denied'
  AND timestamp > NOW() - INTERVAL '24 hours'
ORDER BY timestamp DESC;
```

**Bulk secret access (potential data exfiltration)**:
```sql
SELECT actor_id, COUNT(*) as access_count
FROM audit_logs
WHERE event_type = 'secret.read'
  AND timestamp > NOW() - INTERVAL '1 hour'
GROUP BY actor_id
HAVING COUNT(*) > 50
ORDER BY access_count DESC;
```

## Compliance Reporting

### SOC 2 Reports

Generate SOC 2 compliance reports:

```python
def generate_soc2_report(start_date, end_date):
    """Generate SOC 2 audit report"""
    return {
        "period": {"start": start_date, "end": end_date},
        "metrics": {
            "total_secrets": count_secrets(),
            "secrets_accessed": count_secret_accesses(start_date, end_date),
            "failed_access_attempts": count_failed_accesses(start_date, end_date),
            "secrets_rotated": count_rotations(start_date, end_date),
            "permission_changes": count_permission_changes(start_date, end_date)
        },
        "incidents": get_security_incidents(start_date, end_date),
        "controls": verify_security_controls()
    }
```

### PCI DSS Reports

Track cardholder data access:

```python
def generate_pci_report(start_date, end_date):
    """Generate PCI DSS compliance report"""
    pci_secrets = get_secrets_by_tag("pci")
    
    return {
        "period": {"start": start_date, "end": end_date},
        "cardholder_data_secrets": len(pci_secrets),
        "access_logs": get_audit_logs(pci_secrets, start_date, end_date),
        "encryption_status": verify_encryption(pci_secrets),
        "rotation_compliance": verify_rotation_compliance(pci_secrets, 90),
        "access_control_review": review_access_controls(pci_secrets)
    }
```

## Alerting

### Security Alerts

Configure alerts for suspicious activity:

**Multiple Failed Access Attempts**:
```yaml
alert:
  name: multiple_failed_access
  condition: >
    COUNT(event_type = 'secret.access.denied' 
          AND actor_id = $actor_id 
          AND timestamp > NOW() - INTERVAL '5 minutes') > 5
  action:
    - notify: security_team
    - lock_account: $actor_id
```

**Unusual Access Pattern**:
```yaml
alert:
  name: unusual_access_pattern
  condition: >
    COUNT(event_type = 'secret.read' 
          AND actor_id = $actor_id 
          AND timestamp > NOW() - INTERVAL '1 hour') > 100
  action:
    - notify: security_team
    - flag_for_review: $actor_id
```

**Access from New Location**:
```yaml
alert:
  name: access_from_new_location
  condition: >
    ip_address NOT IN (
      SELECT DISTINCT ip_address 
      FROM audit_logs 
      WHERE actor_id = $actor_id 
        AND timestamp > NOW() - INTERVAL '30 days'
    )
  action:
    - notify: user
    - require_mfa: true
```

## Audit Log Export

### Export Formats

Export audit logs for external analysis:

**JSON Export**:
```bash
curl -X GET "https://api.example.com/api/audit/export?format=json&start=2025-01-01&end=2025-12-31" \
  -H "Authorization: Bearer $TOKEN" \
  -o audit_logs_2025.json
```

**CSV Export**:
```bash
curl -X GET "https://api.example.com/api/audit/export?format=csv&start=2025-01-01&end=2025-12-31" \
  -H "Authorization: Bearer $TOKEN" \
  -o audit_logs_2025.csv
```

**SIEM Integration**:
```yaml
siem:
  enabled: true
  provider: splunk
  endpoint: https://splunk.example.com:8088/services/collector
  token: ${SPLUNK_HEC_TOKEN}
  batch_size: 100
  flush_interval: 60
```

## Audit Log Integrity

### Tamper Protection

Protect audit logs from tampering:

**Cryptographic Hashing**:
```python
def create_audit_log_entry(event_data):
    """Create tamper-proof audit log entry"""
    # Get previous log hash
    previous_hash = get_last_log_hash()
    
    # Create log entry
    entry = {
        "id": generate_uuid(),
        "event_data": event_data,
        "timestamp": now(),
        "previous_hash": previous_hash
    }
    
    # Calculate hash
    entry["hash"] = sha256(json.dumps(entry, sort_keys=True))
    
    # Store entry
    store_audit_log(entry)
    
    return entry
```

**Verification**:
```python
def verify_audit_log_integrity():
    """Verify audit log chain integrity"""
    logs = get_all_audit_logs(order_by="timestamp")
    
    for i in range(1, len(logs)):
        current = logs[i]
        previous = logs[i-1]
        
        # Verify hash chain
        if current["previous_hash"] != previous["hash"]:
            alert("Audit log integrity violation detected")
            return False
    
    return True
```

## Best Practices

1. **Log all secret operations**: Never skip audit logging
2. **Protect audit logs**: Prevent tampering and deletion
3. **Monitor for anomalies**: Alert on suspicious patterns
4. **Regular reviews**: Review audit logs periodically
5. **Long retention**: Keep logs for compliance periods
6. **Secure storage**: Encrypt audit logs at rest
7. **Access control**: Limit who can view audit logs
8. **Automated analysis**: Use SIEM for pattern detection

## Performance Considerations

### Asynchronous Logging

Log audit events asynchronously to avoid performance impact:

```python
import asyncio
from queue import Queue

audit_queue = Queue()

async def audit_logger():
    """Background audit logger"""
    while True:
        event = await audit_queue.get()
        try:
            store_audit_log(event)
        except Exception as e:
            logger.error(f"Failed to store audit log: {e}")

def log_audit_event(event):
    """Queue audit event for async logging"""
    audit_queue.put(event)
```

### Batch Inserts

Insert audit logs in batches:

```python
def flush_audit_logs():
    """Flush queued audit logs to database"""
    batch = []
    while not audit_queue.empty() and len(batch) < 100:
        batch.append(audit_queue.get())
    
    if batch:
        db.execute_many(
            "INSERT INTO audit_logs (...) VALUES (...)",
            batch
        )
```

## Next Steps

- [Overview](01-overview.md) - Secrets management overview
- [Storage](02-storage.md) - Encryption and storage
- [Access Control](05-access-control.md) - Permission system
- [Rotation](06-rotation.md) - Automatic rotation
