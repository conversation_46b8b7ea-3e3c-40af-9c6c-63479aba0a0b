---
title: "User Interface Design"
section: "Client"
order: 5
tags: ["client", "ui", "ux", "design"]
last_updated: "2025-11-08"
---

# User Interface Design

## Overview

The VM Gateway Client user interface is designed to be intuitive, unobtrusive, and efficient. The primary interface is a system tray or menu bar application that provides always-accessible controls without cluttering the user's workspace. A main window offers detailed connection management, service browsing, and configuration options when needed.

The UI design prioritizes common workflows, minimizes clicks required for frequent actions, and provides clear visual feedback about connection status and health.

## Design Principles

### 1. Minimal and Unobtrusive

The client should be present when needed but invisible when not:
- Lives in system tray/menu bar
- No persistent windows unless user opens them
- Desktop notifications for important events only
- Automatic cleanup of completed operations

### 2. Clear Status Indication

Users should always know the state of their connections:
- Color-coded status indicators (green=connected, yellow=warning, red=error, gray=disconnected)
- Real-time connection statistics
- Clear error messages with actionable solutions
- Progress indicators for long-running operations

### 3. Efficient Workflows

Common tasks should require minimal interaction:
- One-click connection to favorite services
- Quick disconnect from tray menu
- Keyboard shortcuts for power users
- Connection profiles for complex setups

### 4. Progressive Disclosure

Show simple options by default, advanced options when needed:
- Basic connection interface for most users
- Advanced settings hidden behind "Advanced" section
- Tooltips explain complex options
- Sensible defaults for all settings


## System Tray Interface

### Tray Icon

The tray icon provides at-a-glance status:

**Icon States:**
- **Gray**: Not connected to any services
- **Green**: Connected to one or more services, all healthy
- **Yellow**: Connected but with warnings (high latency, approaching time limit)
- **Red**: Connection errors or authentication required
- **Animated**: Connection in progress

**Badge Indicators:**
- Number badge shows count of active connections
- Exclamation mark for errors requiring attention

### Tray Menu

Right-clicking the tray icon shows a context menu:

```
┌─────────────────────────────────────────┐
│ VM Gateway                              │
│ Connected to 3 services                 │
├─────────────────────────────────────────┤
│ ● postgres-prod (localhost:5432)        │
│   Connected: 1h 23m                     │
│   → Disconnect                          │
│                                         │
│ ● redis-cache (localhost:6379)          │
│   Connected: 15m                        │
│   → Disconnect                          │
│                                         │
│ ● api-staging (localhost:8080)          │
│   Connected: 2m                         │
│   → Disconnect                          │
├─────────────────────────────────────────┤
│ ⭐ Favorites                            │
│   → Production Database                 │
│   → Staging API                         │
│   → Dev Redis                           │
├─────────────────────────────────────────┤
│ Connect to Service...                   │
│ Open Dashboard                          │
│ Settings                                │
├─────────────────────────────────────────┤
│ Check for Updates                       │
│ About                                   │
│ Sign Out                                │
│ Quit                                    │
└─────────────────────────────────────────┘
```

**Menu Sections:**

1. **Status Header**: Shows connection count and overall status
2. **Active Connections**: List of currently connected services with quick disconnect
3. **Favorites**: Quick access to starred services
4. **Actions**: Common operations (connect, open dashboard, settings)
5. **System**: App management (updates, about, quit)

## Main Window

### Window Layout

The main window uses a tabbed interface:

```
┌────────────────────────────────────────────────────────────┐
│ VM Gateway Client                              [_] [□] [X] │
├────────────────────────────────────────────────────────────┤
│ [Active] [Services] [Profiles] [Settings] [About]         │
├────────────────────────────────────────────────────────────┤
│                                                            │
│  [Tab Content Area]                                        │
│                                                            │
│                                                            │
│                                                            │
│                                                            │
│                                                            │
│                                                            │
│                                                            │
│                                                            │
│                                                            │
│                                                            │
├────────────────────────────────────────────────────────────┤
│ Status: Connected to controller.example.com               │
│ User: <EMAIL>                                │
└────────────────────────────────────────────────────────────┘
```

### Active Connections Tab

Shows all active connections with detailed information:

```
┌────────────────────────────────────────────────────────────┐
│ Active Connections                                    [+]  │
├────────────────────────────────────────────────────────────┤
│                                                            │
│ ┌────────────────────────────────────────────────────┐   │
│ │ postgres-prod                          [Details] [X]│   │
│ │ Production PostgreSQL Database                      │   │
│ │                                                      │   │
│ │ Local:  localhost:5432                              │   │
│ │ Remote: vm-prod-db:5432                             │   │
│ │                                                      │   │
│ │ Status: ● Connected                                 │   │
│ │ Duration: 1h 23m 45s                                │   │
│ │ Latency: 12ms                                       │   │
│ │                                                      │   │
│ │ ↓ 1.2 MB    ↑ 450 KB                                │   │
│ │ [▓▓▓▓▓▓▓▓░░░░░░░░░░] Bandwidth                      │   │
│ └────────────────────────────────────────────────────┘   │
│                                                            │
│ ┌────────────────────────────────────────────────────┐   │
│ │ redis-cache                            [Details] [X]│   │
│ │ Redis Cache Server                                  │   │
│ │                                                      │   │
│ │ Local:  localhost:6379                              │   │
│ │ Remote: vm-cache:6379                               │   │
│ │                                                      │   │
│ │ Status: ● Connected                                 │   │
│ │ Duration: 15m 32s                                   │   │
│ │ Latency: 8ms                                        │   │
│ │                                                      │   │
│ │ ↓ 52 KB     ↑ 31 KB                                 │   │
│ │ [▓▓░░░░░░░░░░░░░░░░] Bandwidth                      │   │
│ └────────────────────────────────────────────────────┘   │
│                                                            │
└────────────────────────────────────────────────────────────┘
```

**Connection Card Elements:**
- Service name and description
- Local and remote addresses
- Status indicator with color coding
- Connection duration
- Current latency
- Data transfer statistics
- Bandwidth usage graph
- Details button (opens detailed view)
- Disconnect button

### Services Tab

Browse and connect to available services:

```
┌────────────────────────────────────────────────────────────┐
│ Available Services                                         │
├────────────────────────────────────────────────────────────┤
│ Search: [_________________]  [Type ▼] [VM ▼] [Tag ▼]      │
├────────────────────────────────────────────────────────────┤
│                                                            │
│ ⭐ FAVORITES                                               │
│                                                            │
│ ┌────────────────────────────────────────────────────┐   │
│ │ ⭐ postgres-prod                      [Connect]     │   │
│ │    Production PostgreSQL • vm-prod-db:5432         │   │
│ │    Tags: production, database, critical            │   │
│ └────────────────────────────────────────────────────┘   │
│                                                            │
│ ┌────────────────────────────────────────────────────┐   │
│ │ ⭐ api-staging                        [Connect]     │   │
│ │    Staging API Server • vm-staging:3000            │   │
│ │    Tags: staging, api, development                 │   │
│ └────────────────────────────────────────────────────┘   │
│                                                            │
│ ALL SERVICES                                               │
│                                                            │
│ ┌────────────────────────────────────────────────────┐   │
│ │ redis-cache                           [Connect]     │   │
│ │    Redis Cache Server • vm-cache:6379              │   │
│ │    Tags: production, cache                         │   │
│ │    Status: ● Healthy                               │   │
│ └────────────────────────────────────────────────────┘   │
│                                                            │
│ ┌────────────────────────────────────────────────────┐   │
│ │ mongodb-dev                           [Connect]     │   │
│ │    Development MongoDB • vm-dev:27017              │   │
│ │    Tags: development, database                     │   │
│ │    Status: ● Healthy                               │   │
│ └────────────────────────────────────────────────────┘   │
│                                                            │
└────────────────────────────────────────────────────────────┘
```

**Service Card Elements:**
- Star icon for favorites
- Service name
- Description and location
- Tags for categorization
- Health status indicator
- Connect button (or "Connected" if already connected)

**Filtering and Search:**
- Real-time search across service names and descriptions
- Filter by service type (database, api, cache, etc.)
- Filter by VM or VM group
- Filter by tags
- Sort by name, last used, or frequency

### Connection Profiles Tab

Manage saved connection configurations:

```
┌────────────────────────────────────────────────────────────┐
│ Connection Profiles                               [+ New]  │
├────────────────────────────────────────────────────────────┤
│                                                            │
│ ┌────────────────────────────────────────────────────┐   │
│ │ Full Stack Development                             │   │
│ │ 3 services • Last used: 2 hours ago                │   │
│ │                                                     │   │
│ │ • postgres-dev (localhost:5432)                    │   │
│ │ • redis-dev (localhost:6379)                       │   │
│ │ • api-dev (localhost:8080)                         │   │
│ │                                                     │   │
│ │ [Connect All]  [Edit]  [Delete]                    │   │
│ └────────────────────────────────────────────────────┘   │
│                                                            │
│ ┌────────────────────────────────────────────────────┐   │
│ │ Production Database Access                         │   │
│ │ 1 service • Last used: 1 day ago                   │   │
│ │                                                     │   │
│ │ • postgres-prod (localhost:5432)                   │   │
│ │   Auto-connect: No                                 │   │
│ │   Time limit: 1 hour                               │   │
│ │                                                     │   │
│ │ [Connect]  [Edit]  [Delete]                        │   │
│ └────────────────────────────────────────────────────┘   │
│                                                            │
└────────────────────────────────────────────────────────────┘
```

**Profile Features:**
- Group multiple services into a single profile
- One-click connection to all services in profile
- Configure per-service settings (local port, auto-connect)
- Edit and delete profiles
- Export/import profiles for sharing

### Settings Tab

Configure client preferences:

```
┌────────────────────────────────────────────────────────────┐
│ Settings                                                   │
├────────────────────────────────────────────────────────────┤
│                                                            │
│ GENERAL                                                    │
│                                                            │
│ ☑ Start on system boot                                    │
│ ☑ Start minimized to tray                                 │
│ ☑ Show desktop notifications                              │
│ ☐ Play notification sounds                                │
│                                                            │
│ Theme: [System Default ▼]                                 │
│ Language: [English ▼]                                     │
│                                                            │
│ ─────────────────────────────────────────────────────────│
│                                                            │
│ CONNECTION                                                 │
│                                                            │
│ Tunnel Protocol: [Auto ▼]                                 │
│   Options: Auto, WireGuard, TLS, WebSocket                │
│                                                            │
│ ☑ Auto-reconnect on network change                        │
│ ☑ Reconnect to services on startup                        │
│                                                            │
│ Connection timeout: [30] seconds                          │
│ Keep-alive interval: [30] seconds                         │
│                                                            │
│ Local port assignment: [Same as remote ▼]                 │
│   Options: Same as remote, Automatic, Manual              │
│                                                            │
│ ─────────────────────────────────────────────────────────│
│                                                            │
│ ADVANCED                                                   │
│                                                            │
│ Log level: [Info ▼]                                       │
│ Log file location: [/var/log/vmgateway/]  [Browse]       │
│                                                            │
│ ☐ Enable diagnostic mode                                  │
│ ☐ Use HTTP proxy                                          │
│   Proxy URL: [_____________________]                      │
│                                                            │
│ [Reset to Defaults]                                       │
│                                                            │
└────────────────────────────────────────────────────────────┘
```

**Settings Categories:**

1. **General**: Startup behavior, notifications, theme, language
2. **Connection**: Tunnel protocol, reconnection, timeouts, port assignment
3. **Advanced**: Logging, diagnostics, proxy settings

### Connection Detail View

Clicking "Details" on a connection shows comprehensive information:

```
┌────────────────────────────────────────────────────────────┐
│ Connection Details: postgres-prod                     [X]  │
├────────────────────────────────────────────────────────────┤
│                                                            │
│ SERVICE INFORMATION                                        │
│                                                            │
│ Name:        Production PostgreSQL Database                │
│ Type:        Database (PostgreSQL 14.5)                    │
│ VM:          vm-prod-db (10.0.1.50)                        │
│ Remote Port: 5432                                          │
│ Local Port:  5432                                          │
│                                                            │
│ ─────────────────────────────────────────────────────────│
│                                                            │
│ CONNECTION STATUS                                          │
│                                                            │
│ Status:      ● Connected                                   │
│ Duration:    1h 23m 45s                                    │
│ Established: 2025-11-08 14:30:15                          │
│ Reconnects:  0                                             │
│                                                            │
│ ─────────────────────────────────────────────────────────│
│                                                            │
│ TUNNEL INFORMATION                                         │
│                                                            │
│ Protocol:    WireGuard                                     │
│ Endpoint:    agent.example.com:51820                       │
│ Encryption:  ChaCha20-Poly1305                            │
│ Tunnel IP:   **********                                    │
│                                                            │
│ ─────────────────────────────────────────────────────────│
│                                                            │
│ PERFORMANCE METRICS                                        │
│                                                            │
│ Latency:     12ms (avg)  8ms (min)  45ms (max)            │
│ Packet Loss: 0.0%                                          │
│                                                            │
│ Data Transfer:                                             │
│   Downloaded: 1.2 MB (1,258,291 bytes)                    │
│   Uploaded:   450 KB (461,824 bytes)                      │
│                                                            │
│ Bandwidth:                                                 │
│   Download: 245 KB/s                                       │
│   Upload:   89 KB/s                                        │
│                                                            │
│ [Bandwidth Graph - Last 5 Minutes]                        │
│ ┌──────────────────────────────────────────────────┐     │
│ │     ╱╲                                            │     │
│ │    ╱  ╲      ╱╲                                   │     │
│ │   ╱    ╲    ╱  ╲    ╱╲                           │     │
│ │  ╱      ╲  ╱    ╲  ╱  ╲                          │     │
│ │ ╱        ╲╱      ╲╱    ╲                         │     │
│ └──────────────────────────────────────────────────┘     │
│                                                            │
│ ─────────────────────────────────────────────────────────│
│                                                            │
│ ACTIONS                                                    │
│                                                            │
│ [Copy Connection String]  [Reconnect]  [Disconnect]       │
│                                                            │
└────────────────────────────────────────────────────────────┘
```

## Visual Design

### Color Palette

**Light Theme:**
- Background: #FFFFFF
- Surface: #F5F5F5
- Primary: #2563EB (blue)
- Success: #10B981 (green)
- Warning: #F59E0B (amber)
- Error: #EF4444 (red)
- Text Primary: #1F2937
- Text Secondary: #6B7280

**Dark Theme:**
- Background: #1F2937
- Surface: #374151
- Primary: #3B82F6 (blue)
- Success: #34D399 (green)
- Warning: #FBBF24 (amber)
- Error: #F87171 (red)
- Text Primary: #F9FAFB
- Text Secondary: #D1D5DB

### Typography

**Font Stack:**
```css
font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, 
             "Helvetica Neue", Arial, sans-serif;
```

**Font Sizes:**
- Heading 1: 24px / 1.5rem
- Heading 2: 20px / 1.25rem
- Heading 3: 18px / 1.125rem
- Body: 14px / 0.875rem
- Small: 12px / 0.75rem
- Tiny: 10px / 0.625rem

**Font Weights:**
- Regular: 400
- Medium: 500
- Semibold: 600
- Bold: 700

### Icons

Use consistent iconography throughout:
- **Connected**: Green circle (●)
- **Disconnected**: Gray circle (○)
- **Warning**: Yellow triangle (⚠)
- **Error**: Red X (✕)
- **Favorite**: Star (⭐)
- **Settings**: Gear (⚙)
- **Info**: Information (ℹ)

## Notifications

### Desktop Notifications

Show notifications for important events:

**Connection Established:**
```
┌─────────────────────────────────────┐
│ VM Gateway                          │
├─────────────────────────────────────┤
│ Connected to postgres-prod          │
│ Local port: 5432                    │
│                                     │
│ [View Details]  [Dismiss]           │
└─────────────────────────────────────┘
```

**Connection Failed:**
```
┌─────────────────────────────────────┐
│ VM Gateway                          │
├─────────────────────────────────────┤
│ Failed to connect to postgres-prod  │
│ Service unavailable                 │
│                                     │
│ [Retry]  [View Logs]  [Dismiss]     │
└─────────────────────────────────────┘
```

**Authentication Required:**
```
┌─────────────────────────────────────┐
│ VM Gateway                          │
├─────────────────────────────────────┤
│ Authentication required             │
│ Your session has expired            │
│                                     │
│ [Sign In]  [Dismiss]                │
└─────────────────────────────────────┘
```

**Approaching Time Limit:**
```
┌─────────────────────────────────────┐
│ VM Gateway                          │
├─────────────────────────────────────┤
│ Connection expiring soon            │
│ postgres-prod: 5 minutes remaining  │
│                                     │
│ [Extend]  [Dismiss]                 │
└─────────────────────────────────────┘
```

### In-App Notifications

Show non-intrusive notifications within the app:

```
┌────────────────────────────────────────────────────────────┐
│ ℹ New version available: v1.5.0                      [X]   │
│   [Download]  [View Changelog]  [Remind Me Later]         │
└────────────────────────────────────────────────────────────┘

┌────────────────────────────────────────────────────────────┐
│ ⚠ High latency detected on postgres-prod (125ms)    [X]   │
│   [View Details]  [Run Diagnostics]                        │
└────────────────────────────────────────────────────────────┘

┌────────────────────────────────────────────────────────────┐
│ ✓ Successfully reconnected to redis-cache            [X]   │
└────────────────────────────────────────────────────────────┘
```

## Keyboard Shortcuts

Provide keyboard shortcuts for power users:

**Global Shortcuts:**
- `Ctrl/Cmd + Shift + G`: Show/hide main window
- `Ctrl/Cmd + Q`: Quit application

**Window Shortcuts:**
- `Ctrl/Cmd + 1-5`: Switch between tabs
- `Ctrl/Cmd + F`: Focus search
- `Ctrl/Cmd + N`: New connection
- `Ctrl/Cmd + W`: Close window
- `Ctrl/Cmd + ,`: Open settings
- `Esc`: Close current dialog

**Connection Shortcuts:**
- `Enter`: Connect to selected service
- `Ctrl/Cmd + D`: Disconnect from selected connection
- `Ctrl/Cmd + R`: Reconnect
- `Ctrl/Cmd + I`: Show connection details

## Accessibility

### Screen Reader Support

Ensure all UI elements are accessible:
- Proper ARIA labels for all interactive elements
- Semantic HTML structure
- Keyboard navigation support
- Focus indicators
- Alt text for icons and images

### Keyboard Navigation

Full keyboard navigation support:
- Tab through all interactive elements
- Arrow keys for list navigation
- Enter/Space to activate buttons
- Escape to close dialogs
- Focus visible indicators

### High Contrast Mode

Support high contrast themes:
- Respect OS high contrast settings
- Ensure sufficient color contrast (WCAG AA)
- Don't rely solely on color for information
- Use patterns and text labels

## Responsive Design

While primarily a desktop application, the UI should adapt to different window sizes:

**Minimum Window Size:** 800x600 pixels
**Recommended Size:** 1024x768 pixels

**Breakpoints:**
- Compact: < 900px width (single column layout)
- Normal: 900-1200px width (standard layout)
- Wide: > 1200px width (expanded layout with more details)

## Summary

The VM Gateway Client user interface is designed to be intuitive, efficient, and unobtrusive. The system tray interface provides quick access to common operations, while the main window offers comprehensive connection management and configuration options. Clear visual feedback, desktop notifications, and keyboard shortcuts ensure users can work efficiently while maintaining awareness of connection status and health.

The design follows platform conventions on each operating system, providing a native feel while maintaining consistent functionality across Windows, macOS, and Linux. Accessibility features ensure the client is usable by all users, regardless of their abilities or preferences.
