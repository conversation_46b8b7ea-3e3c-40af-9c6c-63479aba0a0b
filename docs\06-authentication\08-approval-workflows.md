---
title: "Approval Workflows"
section: "Authentication & Authorization"
order: 8
tags: ["approval", "workflows", "access-control", "security"]
last_updated: "2025-11-08"
---

# Approval Workflows

Approval workflows provide an additional layer of security for sensitive resource access by requiring explicit approval from designated approvers before granting access. This is essential for compliance, security, and audit requirements in enterprise environments.

## Overview

Approval workflows enable organizations to implement just-in-time access with proper oversight:

**Key Features:**
- Multi-level approval chains
- Parallel and sequential approval paths
- Auto-approval rules based on context
- Time-limited access grants
- Emergency break-glass procedures
- Approval delegation
- Notification system
- Comprehensive audit trails

**Use Cases:**
- Production database access
- Sensitive service connections
- Administrative operations
- Temporary privilege elevation
- Emergency access
- Compliance-required approvals

## Workflow Configuration

### Workflow Definition

```python
from pydantic import BaseModel
from typing import List, Optional, Dict
from enum import Enum

class ApprovalLevel(BaseModel):
    level: int
    required_count: int  # Number of approvals needed
    eligible_approvers: List[str]  # User IDs, role names, or group names
    timeout: int  # Seconds until this level times out
    allow_self_approval: bool = False

class AutoApproveRule(BaseModel):
    condition: str  # Expression to evaluate
    skip_levels: List[int]  # Which approval levels to skip

class NotificationConfig(BaseModel):
    request_created: List[str]
    approved: List[str]
    denied: List[str]
    expired: List[str]

class AccessGrant(BaseModel):
    duration: int  # Seconds
    extendable: bool = False
    max_extensions: int = 0
    audit_level: str = "detailed"  # "basic" or "detailed"

class ApprovalWorkflow(BaseModel):
    name: str
    description: str
    trigger: Dict  # Resource pattern and action
    approval_levels: List[ApprovalLevel]
    auto_approve_rules: List[AutoApproveRule] = []
    notifications: NotificationConfig
    access_grant: AccessGrant
    require_reason: bool = True
    enabled: bool = True
```

### Example Workflow Configuration

```yaml
approval_workflows:
  - name: "Production Database Access"
    description: "Required approval for connecting to production databases"
    enabled: true
    
    # When to trigger this workflow
    trigger:
      resource_pattern: "service:type:database AND vm:env:production"
      action: "connect"
    
    # Approval chain
    approval_levels:
      - level: 1
        required_count: 1
        eligible_approvers:
          - "role:dba_team"
          - "role:security_team"
        timeout: 3600  # 1 hour
        allow_self_approval: false
      
      - level: 2
        required_count: 1
        eligible_approvers:
          - "role:director"
          - "role:cto"
        timeout: 14400  # 4 hours
        allow_self_approval: false
    
    # Auto-approval rules
    auto_approve_rules:
      - condition: "user.has_role('senior_dba') AND time.is_business_hours()"
        skip_levels: [1]
      
      - condition: "user.has_role('oncall_engineer') AND request.reason.contains('incident')"
        skip_levels: [1]
    
    # Notifications
    notifications:
      request_created:
        - "approvers"
        - "<EMAIL>"
        - "slack:#security-approvals"
      approved:
        - "requester"
        - "<EMAIL>"
      denied:
        - "requester"
        - "requester_manager"
      expired:
        - "requester"
        - "approvers"
    
    # Access grant settings
    access_grant:
      duration: 3600  # 1 hour
      extendable: true
      max_extensions: 2
      audit_level: "detailed"
    
    # Require justification
    require_reason: true
```



## Workflow Engine

### Implementation

```python
from enum import Enum
from typing import List, Optional, Dict
from datetime import datetime, timedelta

class ApprovalStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    DENIED = "denied"
    EXPIRED = "expired"
    CANCELLED = "cancelled"

class ApprovalWorkflowEngine:
    """Manage approval workflows"""
    
    async def create_request(
        self,
        requester: User,
        resource_type: str,
        resource_id: str,
        action: str,
        reason: str,
        duration: int = 3600
    ) -> str:
        """Create new access request"""
        
        # Find matching workflow
        workflow = await self._find_workflow(resource_type, resource_id, action)
        
        if not workflow:
            # No workflow required, grant immediately
            return await self._grant_access_immediately(
                requester, resource_type, resource_id, action, duration
            )
        
        # Check auto-approval rules
        if await self._check_auto_approval(workflow, requester, reason):
            return await self._grant_access_immediately(
                requester, resource_type, resource_id, action, duration
            )
        
        # Create approval request
        request_id = await db.fetchval(
            """
            INSERT INTO approval_requests (
                requester_id, workflow_id, resource_type, resource_id,
                action, reason, duration, status, created_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, 'pending', NOW())
            RETURNING id
            """,
            requester.id, workflow['id'], resource_type, resource_id,
            action, reason, duration
        )
        
        # Create approval levels
        for level in workflow['approval_levels']:
            await db.execute(
                """
                INSERT INTO approval_levels (
                    request_id, level, required_count, eligible_approvers, timeout
                )
                VALUES ($1, $2, $3, $4, $5)
                """,
                request_id, level['level'], level['required_count'],
                level['eligible_approvers'], level['timeout']
            )
        
        # Send notifications
        await self._notify_approvers(request_id, workflow, requester)
        
        # Log request
        await log_audit_event(
            actor=requester.username,
            action="approval_request_created",
            details={
                "request_id": request_id,
                "resource": f"{resource_type}:{resource_id}",
                "action": action
            }
        )
        
        return request_id
    
    async def approve_request(
        self,
        request_id: str,
        approver: User,
        comment: Optional[str] = None
    ) -> bool:
        """Approve an access request"""
        
        # Get request
        request = await db.fetchrow(
            """
            SELECT * FROM approval_requests WHERE id = $1
            """,
            request_id
        )
        
        if not request or request['status'] != 'pending':
            return False
        
        # Check if user is eligible approver
        current_level = await self._get_current_approval_level(request_id)
        if not await self._is_eligible_approver(approver, current_level):
            raise PermissionDenied("Not eligible to approve this request")
        
        # Record approval
        await db.execute(
            """
            INSERT INTO approvals (
                request_id, level, approver_id, decision, comment, created_at
            )
            VALUES ($1, $2, $3, 'approved', $4, NOW())
            """,
            request_id, current_level['level'], approver.id, comment
        )
        
        # Check if level is complete
        approval_count = await db.fetchval(
            """
            SELECT COUNT(*) FROM approvals
            WHERE request_id = $1 AND level = $2 AND decision = 'approved'
            """,
            request_id, current_level['level']
        )
        
        if approval_count >= current_level['required_count']:
            # Level complete, check if more levels exist
            next_level = await self._get_next_approval_level(request_id, current_level['level'])
            
            if next_level:
                # Notify next level approvers
                await self._notify_approvers(request_id, None, None, next_level)
            else:
                # All levels complete, grant access
                await self._grant_access(request_id)
        
        # Log approval
        await log_audit_event(
            actor=approver.username,
            action="approval_granted",
            details={
                "request_id": request_id,
                "level": current_level['level'],
                "comment": comment
            }
        )
        
        return True
    
    async def deny_request(
        self,
        request_id: str,
        approver: User,
        reason: str
    ) -> bool:
        """Deny an access request"""
        
        # Get request
        request = await db.fetchrow(
            """
            SELECT * FROM approval_requests WHERE id = $1
            """,
            request_id
        )
        
        if not request or request['status'] != 'pending':
            return False
        
        # Check if user is eligible approver
        current_level = await self._get_current_approval_level(request_id)
        if not await self._is_eligible_approver(approver, current_level):
            raise PermissionDenied("Not eligible to deny this request")
        
        # Record denial
        await db.execute(
            """
            INSERT INTO approvals (
                request_id, level, approver_id, decision, comment, created_at
            )
            VALUES ($1, $2, $3, 'denied', $4, NOW())
            """,
            request_id, current_level['level'], approver.id, reason
        )
        
        # Update request status
        await db.execute(
            """
            UPDATE approval_requests
            SET status = 'denied', completed_at = NOW()
            WHERE id = $1
            """,
            request_id
        )
        
        # Notify requester
        requester = await get_user_by_id(request['requester_id'])
        await send_email(
            to=requester.email,
            subject="Access Request Denied",
            template="approval_denied",
            context={
                "requester": requester,
                "approver": approver,
                "reason": reason,
                "request": request
            }
        )
        
        # Log denial
        await log_audit_event(
            actor=approver.username,
            action="approval_denied",
            details={
                "request_id": request_id,
                "reason": reason
            }
        )
        
        return True
    
    async def _grant_access(self, request_id: str) -> None:
        """Grant access after all approvals"""
        
        request = await db.fetchrow(
            """
            SELECT * FROM approval_requests WHERE id = $1
            """,
            request_id
        )
        
        # Create temporary permission
        expires_at = datetime.utcnow() + timedelta(seconds=request['duration'])
        
        await db.execute(
            """
            INSERT INTO temporary_permissions (
                user_id, resource_type, resource_id, action,
                granted_by_request, expires_at, created_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, NOW())
            """,
            request['requester_id'],
            request['resource_type'],
            request['resource_id'],
            request['action'],
            request_id,
            expires_at
        )
        
        # Update request status
        await db.execute(
            """
            UPDATE approval_requests
            SET status = 'approved', completed_at = NOW()
            WHERE id = $1
            """,
            request_id
        )
        
        # Clear permission cache
        await redis.delete(f"user_permissions:{request['requester_id']}")
        
        # Notify requester
        requester = await get_user_by_id(request['requester_id'])
        await send_email(
            to=requester.email,
            subject="Access Request Approved",
            template="approval_granted",
            context={
                "requester": requester,
                "request": request,
                "expires_at": expires_at
            }
        )
        
        # Log grant
        await log_audit_event(
            actor="system",
            action="access_granted",
            target=requester.username,
            details={
                "request_id": request_id,
                "resource": f"{request['resource_type']}:{request['resource_id']}",
                "duration": request['duration'],
                "expires_at": expires_at
            }
        )
    
    async def _check_auto_approval(
        self,
        workflow: dict,
        requester: User,
        reason: str
    ) -> bool:
        """Check if request qualifies for auto-approval"""
        
        for rule in workflow.get('auto_approve_rules', []):
            condition = rule['condition']
            
            # Evaluate condition
            if await self._evaluate_auto_approval_condition(condition, requester, reason):
                return True
        
        return False
    
    async def _evaluate_auto_approval_condition(
        self,
        condition: str,
        requester: User,
        reason: str
    ) -> bool:
        """Evaluate auto-approval condition"""
        
        # Simple expression evaluator
        # In production, use a proper expression parser
        
        context = {
            'user': requester,
            'reason': reason,
            'time': datetime.utcnow()
        }
        
        # Example conditions:
        # "user.has_role('senior_dba') AND time.is_business_hours()"
        # "user.has_role('oncall_engineer') AND reason.contains('incident')"
        
        try:
            # This is simplified - use a proper expression evaluator in production
            result = eval(condition, {"__builtins__": {}}, context)
            return bool(result)
        except:
            return False
    
    async def _notify_approvers(
        self,
        request_id: str,
        workflow: Optional[dict],
        requester: Optional[User],
        level: Optional[dict] = None
    ) -> None:
        """Send notifications to eligible approvers"""
        
        if not level:
            level = await self._get_current_approval_level(request_id)
        
        # Get eligible approvers
        approvers = await self._get_eligible_approvers(level['eligible_approvers'])
        
        # Send notifications
        for approver in approvers:
            # Email notification
            await send_email(
                to=approver.email,
                subject="Access Request Pending Approval",
                template="approval_request",
                context={
                    "approver": approver,
                    "requester": requester,
                    "request_id": request_id,
                    "level": level['level']
                }
            )
            
            # In-app notification
            await create_notification(
                user_id=approver.id,
                type="approval_request",
                title="Access Request Pending",
                message=f"{requester.username} is requesting access",
                action_url=f"/approvals/{request_id}"
            )
```

## Break-Glass Emergency Access

### Implementation

```python
class BreakGlassAccess:
    """Manage emergency break-glass access"""
    
    async def request_emergency_access(
        self,
        user: User,
        resource_type: str,
        resource_id: str,
        action: str,
        justification: str,
        duration: int = 1800  # 30 minutes default
    ) -> str:
        """Request emergency access (break-glass)"""
        
        # Verify user is authorized for break-glass
        if not await self._can_use_break_glass(user):
            raise PermissionDenied("Not authorized for emergency access")
        
        # Create emergency access record
        access_id = await db.fetchval(
            """
            INSERT INTO emergency_access (
                user_id, resource_type, resource_id, action,
                justification, duration, granted_at, expires_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW() + INTERVAL '1 second' * $6)
            RETURNING id
            """,
            user.id, resource_type, resource_id, action,
            justification, duration
        )
        
        # Grant temporary permission
        await db.execute(
            """
            INSERT INTO temporary_permissions (
                user_id, resource_type, resource_id, action,
                is_emergency, expires_at, created_at
            )
            VALUES ($1, $2, $3, $4, true, NOW() + INTERVAL '1 second' * $5, NOW())
            """,
            user.id, resource_type, resource_id, action, duration
        )
        
        # Clear permission cache
        await redis.delete(f"user_permissions:{user.id}")
        
        # Send immediate alerts
        await self._alert_security_team(user, resource_type, resource_id, justification)
        
        # Log emergency access
        await log_audit_event(
            actor=user.username,
            action="emergency_access_granted",
            severity="critical",
            details={
                "access_id": access_id,
                "resource": f"{resource_type}:{resource_id}",
                "action": action,
                "justification": justification,
                "duration": duration
            }
        )
        
        return access_id
    
    async def _alert_security_team(
        self,
        user: User,
        resource_type: str,
        resource_id: str,
        justification: str
    ) -> None:
        """Alert security team of emergency access"""
        
        # Email alerts
        security_team = await get_users_with_role("security_team")
        for member in security_team:
            await send_email(
                to=member.email,
                subject="ALERT: Emergency Access Granted",
                template="emergency_access_alert",
                context={
                    "user": user,
                    "resource": f"{resource_type}:{resource_id}",
                    "justification": justification
                },
                priority="high"
            )
        
        # Slack/Teams notification
        await send_slack_message(
            channel="#security-alerts",
            message=f"🚨 Emergency Access: {user.username} accessed {resource_type}:{resource_id}",
            priority="critical"
        )
        
        # PagerDuty incident
        await create_pagerduty_incident(
            title=f"Emergency Access: {user.username}",
            description=f"User {user.username} used break-glass access for {resource_type}:{resource_id}",
            severity="high"
        )
```

## Approval Delegation

### Implementation

```python
class ApprovalDelegation:
    """Manage approval delegation"""
    
    async def delegate_approvals(
        self,
        delegator: User,
        delegate: User,
        start_date: datetime,
        end_date: datetime,
        reason: str
    ) -> str:
        """Delegate approval authority to another user"""
        
        # Create delegation record
        delegation_id = await db.fetchval(
            """
            INSERT INTO approval_delegations (
                delegator_id, delegate_id, start_date, end_date, reason, created_at
            )
            VALUES ($1, $2, $3, $4, $5, NOW())
            RETURNING id
            """,
            delegator.id, delegate.id, start_date, end_date, reason
        )
        
        # Notify delegate
        await send_email(
            to=delegate.email,
            subject="Approval Authority Delegated",
            template="approval_delegated",
            context={
                "delegator": delegator,
                "delegate": delegate,
                "start_date": start_date,
                "end_date": end_date,
                "reason": reason
            }
        )
        
        # Log delegation
        await log_audit_event(
            actor=delegator.username,
            action="approval_delegated",
            target=delegate.username,
            details={
                "delegation_id": delegation_id,
                "start_date": start_date,
                "end_date": end_date,
                "reason": reason
            }
        )
        
        return delegation_id
    
    async def get_effective_approver(
        self,
        original_approver_id: str,
        timestamp: datetime = None
    ) -> str:
        """Get effective approver (considering delegations)"""
        
        timestamp = timestamp or datetime.utcnow()
        
        # Check for active delegation
        delegation = await db.fetchrow(
            """
            SELECT delegate_id FROM approval_delegations
            WHERE delegator_id = $1
            AND start_date <= $2
            AND end_date >= $2
            ORDER BY created_at DESC
            LIMIT 1
            """,
            original_approver_id, timestamp
        )
        
        if delegation:
            return delegation['delegate_id']
        
        return original_approver_id
```

## Access Extension

### Implementation

```python
async def extend_access(
    request_id: str,
    user: User,
    additional_duration: int,
    reason: str
) -> bool:
    """Extend temporary access duration"""
    
    # Get original request
    request = await db.fetchrow(
        """
        SELECT * FROM approval_requests WHERE id = $1
        """,
        request_id
    )
    
    if not request or request['requester_id'] != user.id:
        return False
    
    # Check if extension is allowed
    workflow = await get_workflow(request['workflow_id'])
    if not workflow['access_grant'].get('extendable'):
        raise ValueError("Access extension not allowed for this workflow")
    
    # Check extension limit
    extension_count = await db.fetchval(
        """
        SELECT COUNT(*) FROM access_extensions
        WHERE request_id = $1
        """,
        request_id
    )
    
    max_extensions = workflow['access_grant'].get('max_extensions', 0)
    if extension_count >= max_extensions:
        raise ValueError(f"Maximum extensions ({max_extensions}) reached")
    
    # Create extension request (may require approval)
    if workflow.get('require_approval_for_extension'):
        # Create new approval request
        extension_request_id = await create_approval_request(
            user, request['resource_type'], request['resource_id'],
            request['action'], f"Extension: {reason}", additional_duration
        )
        return extension_request_id
    else:
        # Grant extension immediately
        await db.execute(
            """
            UPDATE temporary_permissions
            SET expires_at = expires_at + INTERVAL '1 second' * $1
            WHERE user_id = $2 AND resource_type = $3 AND resource_id = $4
            """,
            additional_duration, user.id, request['resource_type'], request['resource_id']
        )
        
        # Record extension
        await db.execute(
            """
            INSERT INTO access_extensions (
                request_id, additional_duration, reason, granted_at
            )
            VALUES ($1, $2, $3, NOW())
            """,
            request_id, additional_duration, reason
        )
        
        # Log extension
        await log_audit_event(
            actor=user.username,
            action="access_extended",
            details={
                "request_id": request_id,
                "additional_duration": additional_duration,
                "reason": reason
            }
        )
        
        return True
```

## Approval Analytics

### Metrics and Reporting

```python
class ApprovalAnalytics:
    """Analytics for approval workflows"""
    
    async def get_approval_metrics(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> dict:
        """Get approval workflow metrics"""
        
        metrics = {}
        
        # Total requests
        metrics['total_requests'] = await db.fetchval(
            """
            SELECT COUNT(*) FROM approval_requests
            WHERE created_at BETWEEN $1 AND $2
            """,
            start_date, end_date
        )
        
        # Approval rate
        metrics['approval_rate'] = await db.fetchval(
            """
            SELECT
                COUNT(CASE WHEN status = 'approved' THEN 1 END)::float /
                NULLIF(COUNT(*), 0) * 100
            FROM approval_requests
            WHERE created_at BETWEEN $1 AND $2
            """,
            start_date, end_date
        )
        
        # Average approval time
        metrics['avg_approval_time'] = await db.fetchval(
            """
            SELECT AVG(EXTRACT(EPOCH FROM (completed_at - created_at)))
            FROM approval_requests
            WHERE status = 'approved'
            AND created_at BETWEEN $1 AND $2
            """,
            start_date, end_date
        )
        
        # Requests by status
        metrics['by_status'] = await db.fetch(
            """
            SELECT status, COUNT(*) as count
            FROM approval_requests
            WHERE created_at BETWEEN $1 AND $2
            GROUP BY status
            """,
            start_date, end_date
        )
        
        # Top requesters
        metrics['top_requesters'] = await db.fetch(
            """
            SELECT u.username, COUNT(*) as request_count
            FROM approval_requests ar
            JOIN users u ON ar.requester_id = u.id
            WHERE ar.created_at BETWEEN $1 AND $2
            GROUP BY u.username
            ORDER BY request_count DESC
            LIMIT 10
            """,
            start_date, end_date
        )
        
        # Top approvers
        metrics['top_approvers'] = await db.fetch(
            """
            SELECT u.username, COUNT(*) as approval_count
            FROM approvals a
            JOIN users u ON a.approver_id = u.id
            WHERE a.created_at BETWEEN $1 AND $2
            GROUP BY u.username
            ORDER BY approval_count DESC
            LIMIT 10
            """,
            start_date, end_date
        )
        
        return metrics
```

## Related Documentation

- [Authentication Overview](./01-overview.md) - System architecture
- [RBAC System](./07-rbac.md) - Role-based access control
- [Session Management](./06-session-management.md) - Session handling
- [API Authentication](./05-api-auth.md) - API access methods
