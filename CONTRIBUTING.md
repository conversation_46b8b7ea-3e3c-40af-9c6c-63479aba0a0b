# Contributing to VM Gateway

Thank you for your interest in contributing to the VM Network Gateway & Access Control Platform! This document provides guidelines and instructions for contributing to the project.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Coding Standards](#coding-standards)
- [Commit Guidelines](#commit-guidelines)
- [Pull Request Process](#pull-request-process)
- [Testing](#testing)
- [Documentation](#documentation)
- [Issue Reporting](#issue-reporting)

## Code of Conduct

### Our Pledge

We are committed to providing a welcoming and inclusive environment for all contributors. We expect all participants to:

- Use welcoming and inclusive language
- Be respectful of differing viewpoints and experiences
- Gracefully accept constructive criticism
- Focus on what is best for the community
- Show empathy towards other community members

### Unacceptable Behavior

- Harassment, discrimination, or offensive comments
- Trolling, insulting/derogatory comments, and personal attacks
- Public or private harassment
- Publishing others' private information without permission
- Other conduct which could reasonably be considered inappropriate

## Getting Started

1. **Fork the repository** on GitHub
2. **Clone your fork** locally:
   ```bash
   git clone https://github.com/your-username/vm-gateway.git
   cd vm-gateway
   ```
3. **Add upstream remote**:
   ```bash
   git remote add upstream https://github.com/original-owner/vm-gateway.git
   ```
4. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

## Development Setup

### Prerequisites

- Python 3.11 or higher
- PostgreSQL 14+
- Redis 7+
- Node.js 18+ (for frontend development)
- Git

### Installation

1. **Create a virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies**:
   ```bash
   pip install -e ".[dev]"
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run tests** to verify setup:
   ```bash
   pytest
   ```

## Coding Standards

### Python Code Style

We follow PEP 8 with some modifications:

- **Line length**: 100 characters maximum
- **Indentation**: 4 spaces (no tabs)
- **Quotes**: Double quotes for strings, single quotes for dict keys
- **Imports**: Organized in three groups (standard library, third-party, local)

### Code Formatting

We use **Black** for automatic code formatting:

```bash
black src/ tests/
```

### Linting

We use **Ruff** for fast Python linting:

```bash
ruff check src/ tests/
```

### Type Checking

We use **mypy** for static type checking:

```bash
mypy src/
```

### Pre-commit Checks

Before committing, run:

```bash
# Format code
black src/ tests/

# Check linting
ruff check src/ tests/

# Run type checker
mypy src/

# Run tests
pytest
```

## Commit Guidelines

### Commit Message Format

We follow the Conventional Commits specification:

```
<type>(<scope>): <subject>

<body>

<footer>
```

### Types

- **feat**: A new feature
- **fix**: A bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, missing semicolons, etc.)
- **refactor**: Code refactoring without changing functionality
- **perf**: Performance improvements
- **test**: Adding or updating tests
- **chore**: Maintenance tasks, dependency updates, etc.
- **ci**: CI/CD configuration changes

### Examples

```
feat(agent): add service discovery engine

Implement automatic service discovery using port scanning
and process inspection. Supports TCP/UDP services.

Closes #123
```

```
fix(controller): resolve database connection pool exhaustion

Increase pool size and add connection timeout handling
to prevent pool exhaustion under high load.

Fixes #456
```

### Commit Best Practices

- Use present tense ("add feature" not "added feature")
- Use imperative mood ("move cursor to..." not "moves cursor to...")
- Keep subject line under 72 characters
- Reference issues and pull requests in the footer
- Separate subject from body with a blank line
- Wrap body at 72 characters

## Pull Request Process

### Before Submitting

1. **Update your branch** with the latest upstream changes:
   ```bash
   git fetch upstream
   git rebase upstream/main
   ```

2. **Run all checks**:
   ```bash
   black src/ tests/
   ruff check src/ tests/
   mypy src/
   pytest
   ```

3. **Update documentation** if needed

4. **Add tests** for new features

### Submitting a Pull Request

1. **Push your branch** to your fork:
   ```bash
   git push origin feature/your-feature-name
   ```

2. **Create a pull request** on GitHub

3. **Fill out the PR template** with:
   - Description of changes
   - Related issues
   - Testing performed
   - Screenshots (if UI changes)
   - Breaking changes (if any)

### PR Review Process

- At least one maintainer must approve the PR
- All CI checks must pass
- Code coverage should not decrease
- Documentation must be updated
- Commit history should be clean

### After Approval

- Maintainers will merge your PR
- Your branch will be deleted automatically
- You can delete your local branch:
  ```bash
  git branch -d feature/your-feature-name
  ```

## Testing

### Running Tests

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/agent/test_discovery.py

# Run with coverage
pytest --cov=src --cov-report=html

# Run with verbose output
pytest -v
```

### Writing Tests

- Place tests in the `tests/` directory mirroring the `src/` structure
- Use descriptive test names: `test_<function>_<scenario>_<expected_result>`
- Follow AAA pattern: Arrange, Act, Assert
- Use fixtures for common setup
- Mock external dependencies
- Aim for high code coverage (>80%)

### Test Example

```python
import pytest
from src.agent.discovery import ServiceDiscovery

def test_discover_services_returns_list_of_services():
    # Arrange
    discovery = ServiceDiscovery()
    
    # Act
    services = discovery.discover()
    
    # Assert
    assert isinstance(services, list)
    assert len(services) > 0
    assert all(hasattr(s, 'port') for s in services)
```

## Documentation

### Documentation Standards

- Use clear, concise language
- Include code examples
- Add diagrams where helpful (use Mermaid)
- Keep documentation up-to-date with code changes
- Follow Markdown best practices

### Documentation Structure

- **README.md**: Project overview and quick start
- **docs/**: Comprehensive documentation
- **Docstrings**: All public functions, classes, and modules
- **Comments**: Complex logic and non-obvious code

### Docstring Format

We use Google-style docstrings:

```python
def discover_services(port_range: tuple[int, int]) -> list[Service]:
    """Discover services running on specified port range.
    
    Args:
        port_range: Tuple of (start_port, end_port) to scan
        
    Returns:
        List of discovered Service objects
        
    Raises:
        ValueError: If port_range is invalid
        PermissionError: If insufficient privileges for scanning
        
    Example:
        >>> discovery = ServiceDiscovery()
        >>> services = discovery.discover_services((1, 1024))
        >>> print(len(services))
        42
    """
    pass
```

## Issue Reporting

### Before Creating an Issue

1. **Search existing issues** to avoid duplicates
2. **Check documentation** for solutions
3. **Verify** you're using the latest version

### Creating an Issue

Use the appropriate issue template:

- **Bug Report**: For reporting bugs
- **Feature Request**: For suggesting new features
- **Documentation**: For documentation improvements
- **Question**: For asking questions

### Bug Report Template

```markdown
**Description**
A clear description of the bug

**Steps to Reproduce**
1. Step one
2. Step two
3. Step three

**Expected Behavior**
What you expected to happen

**Actual Behavior**
What actually happened

**Environment**
- OS: [e.g., Ubuntu 22.04]
- Python version: [e.g., 3.11.5]
- VM Gateway version: [e.g., a.0.0-20]

**Additional Context**
Any other relevant information
```

## Questions?

If you have questions about contributing, please:

1. Check the documentation in `docs/`
2. Search existing issues and discussions
3. Create a new issue with the "Question" label
4. Join our community chat (if available)

## License

By contributing to VM Gateway, you agree that your contributions will be licensed under the MIT License.

## Thank You!

Your contributions make this project better. We appreciate your time and effort!
