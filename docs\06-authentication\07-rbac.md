---
title: "Role-Based Access Control (RBAC)"
section: "Authentication & Authorization"
order: 7
tags: ["rbac", "permissions", "authorization", "roles"]
last_updated: "2025-11-08"
---

# Role-Based Access Control (RBAC)

The platform implements a comprehensive Role-Based Access Control system that provides fine-grained control over user permissions. RBAC enables administrators to define who can access what resources under which conditions.

## Overview

RBAC is the foundation of the platform's authorization system:

**Core Concepts:**
- **Resources**: VMs, services, ports, users, roles, settings, secrets
- **Actions**: view, connect, configure, delete, grant, manage
- **Roles**: Collections of permissions assigned to users
- **Conditions**: Context-based restrictions (time, IP, MFA, approval)

**Key Features:**
- Built-in roles for common scenarios
- Custom role creation with inheritance
- Resource-level permissions
- Conditional access policies
- Group-based role assignment
- Temporary role elevation
- Permission testing and simulation
- Comprehensive audit logging

## Permission Model

### Permission Structure

Each permission defines access to a specific resource with specific actions under specific conditions:

```python
from pydantic import BaseModel
from typing import List, Optional, Dict
from enum import Enum

class ResourceType(str, Enum):
    VM = "vm"
    SERVICE = "service"
    PORT = "port"
    USER = "user"
    ROLE = "role"
    SETTINGS = "settings"
    SECRET = "secret"

class Action(str, Enum):
    VIEW = "view"
    CONNECT = "connect"
    CONFIGURE = "configure"
    DELETE = "delete"
    GRANT = "grant"
    MANAGE = "manage"

class TimeWindow(BaseModel):
    days: List[str]  # ["monday", "tuesday", ...]
    hours: str  # "09:00-17:00"
    timezone: str  # "America/New_York"

class AccessConditions(BaseModel):
    ip_whitelist: Optional[List[str]] = None
    ip_blacklist: Optional[List[str]] = None
    time_windows: Optional[List[TimeWindow]] = None
    mfa_required: bool = False
    approval_required: bool = False
    max_session_duration: Optional[int] = None
    allowed_actions_per_hour: Optional[int] = None

class Permission(BaseModel):
    resource_type: ResourceType
    resource_id: str  # Specific ID or "*" for all
    actions: List[Action]
    conditions: AccessConditions = AccessConditions()
    
    def matches_resource(self, resource_type: str, resource_id: str) -> bool:
        """Check if permission applies to given resource"""
        if self.resource_type != resource_type:
            return False
        if self.resource_id == "*":
            return True
        return self.resource_id == resource_id
    
    def allows_action(self, action: Action) -> bool:
        """Check if permission allows specific action"""
        return action in self.actions
```



### Built-in Roles

The platform provides pre-configured roles for common scenarios:

```python
BUILT_IN_ROLES = {
    "super_admin": {
        "name": "Super Admin",
        "description": "Full system access, cannot be restricted",
        "permissions": [
            Permission(
                resource_type=ResourceType.VM,
                resource_id="*",
                actions=[Action.VIEW, Action.CONNECT, Action.CONFIGURE, Action.DELETE, Action.MANAGE]
            ),
            # ... all other resources with full permissions
        ],
        "is_system_role": True,
        "cannot_be_deleted": True
    },
    
    "admin": {
        "name": "Administrator",
        "description": "Manage users, roles, and system settings",
        "permissions": [
            Permission(
                resource_type=ResourceType.USER,
                resource_id="*",
                actions=[Action.VIEW, Action.MANAGE]
            ),
            Permission(
                resource_type=ResourceType.ROLE,
                resource_id="*",
                actions=[Action.VIEW, Action.MANAGE]
            ),
            Permission(
                resource_type=ResourceType.SETTINGS,
                resource_id="*",
                actions=[Action.VIEW, Action.CONFIGURE]
            )
        ]
    },
    
    "operator": {
        "name": "Operator",
        "description": "Deploy and configure agents, manage VMs and services",
        "permissions": [
            Permission(
                resource_type=ResourceType.VM,
                resource_id="*",
                actions=[Action.VIEW, Action.CONFIGURE]
            ),
            Permission(
                resource_type=ResourceType.SERVICE,
                resource_id="*",
                actions=[Action.VIEW, Action.CONFIGURE]
            )
        ]
    },
    
    "developer": {
        "name": "Developer",
        "description": "Connect to development and staging services",
        "permissions": [
            Permission(
                resource_type=ResourceType.SERVICE,
                resource_id="env:development",
                actions=[Action.VIEW, Action.CONNECT, Action.CONFIGURE]
            ),
            Permission(
                resource_type=ResourceType.SERVICE,
                resource_id="env:staging",
                actions=[Action.VIEW, Action.CONNECT]
            ),
            Permission(
                resource_type=ResourceType.SERVICE,
                resource_id="env:production",
                actions=[Action.VIEW]
            )
        ]
    },
    
    "viewer": {
        "name": "Viewer",
        "description": "Read-only access to dashboards and service catalog",
        "permissions": [
            Permission(
                resource_type=ResourceType.VM,
                resource_id="*",
                actions=[Action.VIEW]
            ),
            Permission(
                resource_type=ResourceType.SERVICE,
                resource_id="*",
                actions=[Action.VIEW]
            )
        ]
    }
}
```

## Permission Evaluation

### Authorization Engine

```python
class AuthorizationEngine:
    """Evaluate user permissions"""
    
    async def check_permission(
        self,
        user: User,
        resource_type: ResourceType,
        resource_id: str,
        action: Action,
        context: dict = None
    ) -> Tuple[bool, Optional[str]]:
        """
        Check if user has permission to perform action on resource
        Returns (is_allowed, denial_reason)
        """
        
        # Super admins bypass all checks
        if await self._is_super_admin(user):
            return (True, None)
        
        # Get user's effective permissions
        permissions = await self._get_effective_permissions(user)
        
        # Find matching permissions
        matching_perms = [
            p for p in permissions
            if p.matches_resource(resource_type, resource_id) and p.allows_action(action)
        ]
        
        if not matching_perms:
            return (False, "no_permission")
        
        # Evaluate conditions for each matching permission
        for perm in matching_perms:
            allowed, reason = await self._evaluate_conditions(perm.conditions, context or {})
            if allowed:
                return (True, None)
            # Continue checking other permissions
        
        return (False, "conditions_not_met")
    
    async def _get_effective_permissions(self, user: User) -> List[Permission]:
        """Get all permissions for user (from roles and explicit grants)"""
        
        permissions = []
        
        # Get permissions from roles
        roles = await db.fetch(
            """
            SELECT r.* FROM roles r
            JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = $1 AND ur.expires_at > NOW()
            """,
            user.id
        )
        
        for role in roles:
            role_perms = await self._get_role_permissions(role['id'])
            permissions.extend(role_perms)
        
        # Get explicit permissions
        explicit_perms = await db.fetch(
            """
            SELECT * FROM user_permissions
            WHERE user_id = $1
            """,
            user.id
        )
        
        for perm in explicit_perms:
            permissions.append(Permission(**perm))
        
        # Get permissions from groups
        group_perms = await self._get_group_permissions(user.id)
        permissions.extend(group_perms)
        
        return permissions
    
    async def _evaluate_conditions(
        self,
        conditions: AccessConditions,
        context: dict
    ) -> Tuple[bool, Optional[str]]:
        """Evaluate access conditions"""
        
        # Check IP whitelist
        if conditions.ip_whitelist:
            client_ip = context.get('ip_address')
            if not self._ip_in_list(client_ip, conditions.ip_whitelist):
                return (False, "ip_not_whitelisted")
        
        # Check IP blacklist
        if conditions.ip_blacklist:
            client_ip = context.get('ip_address')
            if self._ip_in_list(client_ip, conditions.ip_blacklist):
                return (False, "ip_blacklisted")
        
        # Check time windows
        if conditions.time_windows:
            if not self._in_time_window(conditions.time_windows):
                return (False, "outside_time_window")
        
        # Check MFA requirement
        if conditions.mfa_required:
            if not context.get('mfa_verified'):
                return (False, "mfa_required")
        
        # Check approval requirement
        if conditions.approval_required:
            if not context.get('approval_granted'):
                return (False, "approval_required")
        
        # Check session duration
        if conditions.max_session_duration:
            session_duration = context.get('session_duration', 0)
            if session_duration > conditions.max_session_duration:
                return (False, "session_duration_exceeded")
        
        # Check rate limit
        if conditions.allowed_actions_per_hour:
            action_count = await self._get_action_count(
                context.get('user_id'),
                context.get('resource_type'),
                context.get('resource_id')
            )
            if action_count >= conditions.allowed_actions_per_hour:
                return (False, "rate_limit_exceeded")
        
        return (True, None)
    
    def _in_time_window(self, time_windows: List[TimeWindow]) -> bool:
        """Check if current time is within allowed windows"""
        from datetime import datetime
        import pytz
        
        now = datetime.utcnow()
        
        for window in time_windows:
            # Check day of week
            current_day = now.strftime('%A').lower()
            if current_day not in [d.lower() for d in window.days]:
                continue
            
            # Check time range
            tz = pytz.timezone(window.timezone)
            local_now = now.astimezone(tz)
            
            start_time, end_time = window.hours.split('-')
            start_hour, start_min = map(int, start_time.split(':'))
            end_hour, end_min = map(int, end_time.split(':'))
            
            current_minutes = local_now.hour * 60 + local_now.minute
            start_minutes = start_hour * 60 + start_min
            end_minutes = end_hour * 60 + end_min
            
            if start_minutes <= current_minutes <= end_minutes:
                return True
        
        return False
```

### Permission Decorator

```python
def require_permission(
    resource_type: ResourceType,
    action: Action,
    resource_id_param: str = "resource_id"
):
    """Decorator to check permissions before executing endpoint"""
    
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Get current user from context
            user = kwargs.get('current_user')
            if not user:
                raise HTTPException(status_code=401, detail="Not authenticated")
            
            # Get resource ID from parameters
            resource_id = kwargs.get(resource_id_param, "*")
            
            # Build context
            context = {
                'user_id': user.id,
                'ip_address': kwargs.get('request').client.host,
                'mfa_verified': getattr(user, 'mfa_verified', False),
                'resource_type': resource_type,
                'resource_id': resource_id
            }
            
            # Check permission
            auth_engine = AuthorizationEngine()
            allowed, reason = await auth_engine.check_permission(
                user, resource_type, resource_id, action, context
            )
            
            if not allowed:
                # Log denial
                await log_audit_event(
                    actor=user.username,
                    action="permission_denied",
                    details={
                        "resource_type": resource_type,
                        "resource_id": resource_id,
                        "action": action,
                        "reason": reason
                    }
                )
                
                raise HTTPException(
                    status_code=403,
                    detail=f"Permission denied: {reason}"
                )
            
            # Execute function
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator

# Usage example
@router.post("/api/services/{service_id}/connect")
@require_permission(ResourceType.SERVICE, Action.CONNECT, "service_id")
async def connect_to_service(
    service_id: str,
    current_user: User = Depends(get_current_user),
    request: Request = None
):
    """Connect to service - requires service:connect permission"""
    return await establish_connection(service_id, current_user)
```

## Role Management

### Creating Custom Roles

```python
class RoleManager:
    """Manage roles and permissions"""
    
    async def create_role(
        self,
        name: str,
        description: str,
        permissions: List[Permission],
        created_by: User,
        parent_role_id: Optional[str] = None
    ) -> str:
        """Create a new role"""
        
        # Check permission
        if not await has_permission(created_by, ResourceType.ROLE, "*", Action.MANAGE):
            raise PermissionDenied("Cannot create roles")
        
        # Create role
        role_id = await db.fetchval(
            """
            INSERT INTO roles (name, description, parent_role_id, created_by, created_at)
            VALUES ($1, $2, $3, $4, NOW())
            RETURNING id
            """,
            name, description, parent_role_id, created_by.id
        )
        
        # Add permissions
        for perm in permissions:
            await db.execute(
                """
                INSERT INTO role_permissions (
                    role_id, resource_type, resource_id, actions, conditions
                )
                VALUES ($1, $2, $3, $4, $5)
                """,
                role_id,
                perm.resource_type,
                perm.resource_id,
                [a.value for a in perm.actions],
                perm.conditions.dict()
            )
        
        # Log creation
        await log_audit_event(
            actor=created_by.username,
            action="role_created",
            details={"role_id": role_id, "name": name}
        )
        
        return role_id
    
    async def assign_role(
        self,
        user_id: str,
        role_id: str,
        assigned_by: User,
        expires_at: Optional[datetime] = None
    ) -> None:
        """Assign role to user"""
        
        # Check permission
        if not await has_permission(assigned_by, ResourceType.USER, user_id, Action.MANAGE):
            raise PermissionDenied("Cannot assign roles")
        
        # Assign role
        await db.execute(
            """
            INSERT INTO user_roles (user_id, role_id, assigned_by, assigned_at, expires_at)
            VALUES ($1, $2, $3, NOW(), $4)
            ON CONFLICT (user_id, role_id) DO UPDATE
            SET expires_at = $4, assigned_by = $3, assigned_at = NOW()
            """,
            user_id, role_id, assigned_by.id, expires_at
        )
        
        # Clear permission cache
        await redis.delete(f"user_permissions:{user_id}")
        
        # Log assignment
        await log_audit_event(
            actor=assigned_by.username,
            action="role_assigned",
            target=user_id,
            details={"role_id": role_id, "expires_at": expires_at}
        )
```

## Resource-Level Permissions

### Dynamic Resource Patterns

```python
class ResourceMatcher:
    """Match resources against permission patterns"""
    
    def matches(self, pattern: str, resource_id: str) -> bool:
        """Check if resource matches pattern"""
        
        # Exact match
        if pattern == resource_id:
            return True
        
        # Wildcard
        if pattern == "*":
            return True
        
        # Prefix match (e.g., "vm:prod-*")
        if "*" in pattern:
            prefix = pattern.replace("*", "")
            if resource_id.startswith(prefix):
                return True
        
        # Tag-based match (e.g., "env:production")
        if ":" in pattern:
            tag_key, tag_value = pattern.split(":", 1)
            resource_tags = self._get_resource_tags(resource_id)
            if resource_tags.get(tag_key) == tag_value:
                return True
        
        # Regex match (e.g., "vm:prod-db-[0-9]+")
        if self._is_regex_pattern(pattern):
            import re
            if re.match(pattern, resource_id):
                return True
        
        return False
    
    def _get_resource_tags(self, resource_id: str) -> dict:
        """Get tags for resource"""
        # Implementation depends on resource type
        pass
```

## Permission Testing

### Test Mode

```python
async def test_permission(
    user_id: str,
    resource_type: ResourceType,
    resource_id: str,
    action: Action
) -> dict:
    """Test what would happen if user tried to perform action"""
    
    user = await get_user_by_id(user_id)
    auth_engine = AuthorizationEngine()
    
    allowed, reason = await auth_engine.check_permission(
        user, resource_type, resource_id, action
    )
    
    # Get detailed explanation
    explanation = await auth_engine.explain_decision(
        user, resource_type, resource_id, action
    )
    
    return {
        "allowed": allowed,
        "reason": reason,
        "explanation": explanation,
        "matching_permissions": explanation.get('matching_permissions', []),
        "failed_conditions": explanation.get('failed_conditions', [])
    }
```

## Related Documentation

- [Authentication Overview](./01-overview.md) - System architecture
- [Approval Workflows](./08-approval-workflows.md) - Access approval processes
- [Session Management](./06-session-management.md) - Session handling
