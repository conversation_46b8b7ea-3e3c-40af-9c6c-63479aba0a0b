---
title: "Controller Overview"
section: "Controller"
order: 1
tags: ["controller", "architecture", "overview"]
last_updated: "2025-11-08"
---

# Controller Overview

The Controller is the central nervous system of the VM Network Gateway & Access Control Platform. It serves as the unified management interface, authentication authority, and coordination hub for all agents, clients, and services across the entire infrastructure. The Controller provides a comprehensive web-based interface for administrators and users to discover, monitor, and access services running on distributed VMs.

## Purpose and Role

The Controller fulfills several critical functions within the platform ecosystem:

### Centralized Management

The Controller acts as the single source of truth for the entire platform. It maintains a complete inventory of all registered VMs, discovered services, user accounts, permissions, and system configurations. This centralized approach eliminates the complexity of managing distributed systems by providing a unified view and control plane.

All configuration changes, whether they affect global settings, per-VM parameters, or individual service configurations, flow through the Controller. This ensures consistency across the infrastructure and provides a clear audit trail for compliance and troubleshooting purposes.

### Authentication Authority

As the authentication authority, the Controller handles all user authentication requests across multiple methods including local username/password authentication, multi-factor authentication (MFA), single sign-on (SSO) via SAML 2.0 or OAuth 2.0/OpenID Connect, and LDAP/Active Directory integration.

The Controller issues and manages authentication tokens (JWT-based) that clients and agents use to verify user identity. It enforces password policies, manages MFA enrollment and verification, handles session lifecycle, and coordinates with external identity providers for SSO scenarios.

### Authorization Engine

Beyond authentication, the Controller implements a sophisticated Role-Based Access Control (RBAC) system with fine-grained permissions. Every access request—whether a user attempting to view a service, connect to a port, or modify a configuration—passes through the Controller's authorization engine.

The authorization engine evaluates:
- User roles and explicit permissions
- Resource-level access controls
- Contextual conditions (time windows, IP restrictions, MFA requirements)
- Approval workflow requirements
- Dynamic risk-based policies

### Service Registry

The Controller maintains a real-time registry of all services discovered by agents across all monitored VMs. This registry includes comprehensive metadata about each service: type, version, health status, resource usage, access permissions, and custom tags.

The service registry is continuously updated as agents report new discoveries, service state changes, or health status updates. Users interact with this registry through the web interface to browse available services, view details, and initiate connections.

### Connection Coordinator

When a client application requests access to a service, the Controller orchestrates the entire connection establishment process. It validates permissions, checks service availability with the appropriate agent, generates ephemeral tunnel credentials, and provides the client with connection parameters.

The Controller tracks all active connections, enforces session time limits, monitors connection health, and can terminate connections based on policy changes or administrative actions.

### Monitoring and Alerting Hub

The Controller aggregates metrics and logs from all agents, providing a unified monitoring dashboard. It processes real-time metrics streams, stores historical data, evaluates alert rules, and dispatches notifications through configured channels (email, Slack, PagerDuty, webhooks).

The monitoring system tracks VM-level metrics (CPU, memory, disk, network), service-level metrics (request rates, response times, error rates), and platform-level metrics (active users, connection counts, agent health).

### Web-Based Proxy

For HTTP and HTTPS services, the Controller provides a built-in web proxy that allows users to access services directly through their browser without installing client software. The proxy handles authentication, authorization, URL rewriting, WebSocket support, and comprehensive audit logging of all proxied traffic.

## Architecture Overview

The Controller is built as a modern, scalable web application using Python and FastAPI. Its architecture follows a layered design pattern that separates concerns and enables horizontal scaling.

### Technology Stack

**Backend Framework**: FastAPI provides the foundation for the Controller's REST API and WebSocket endpoints. FastAPI's async/await support enables high-concurrency handling of simultaneous agent connections, client requests, and real-time updates.

**Database**: PostgreSQL serves as the primary data store for all persistent data including user accounts, service catalog, configurations, audit logs, and historical metrics. PostgreSQL's JSONB support allows flexible storage of service metadata and custom attributes.

**Cache and Session Store**: Redis provides high-performance caching for frequently accessed data (service catalog, user permissions) and stores active session data. Redis pub/sub channels enable real-time communication between Controller instances in multi-instance deployments.

**Task Queue**: Celery with Redis as the message broker handles background tasks such as metric aggregation, report generation, alert evaluation, and scheduled maintenance operations.

**Frontend**: React with TypeScript provides the web interface. The UI uses Tailwind CSS with shadcn/ui components for a modern, responsive design. State management is handled by Zustand or Redux Toolkit, and real-time updates flow through WebSocket connections.

### Component Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Load Balancer / Reverse Proxy            │
│                    (Nginx, Traefik, HAProxy)                │
└────────────────────────────┬────────────────────────────────┘
                             │
        ┌────────────────────┼────────────────────┐
        │                    │                    │
┌───────▼────────┐  ┌────────▼───────┐  ┌────────▼───────┐
│  Controller    │  │  Controller    │  │  Controller    │
│  Instance 1    │  │  Instance 2    │  │  Instance 3    │
│                │  │                │  │                │
│ ┌────────────┐ │  │ ┌────────────┐ │  │ ┌────────────┐ │
│ │ FastAPI    │ │  │ │ FastAPI    │ │  │ │ FastAPI    │ │
│ │ Web Server │ │  │ │ Web Server │ │  │ │ Web Server │ │
│ └────────────┘ │  │ └────────────┘ │  │ └────────────┘ │
│                │  │                │  │                │
│ ┌────────────┐ │  │ ┌────────────┐ │  │ ┌────────────┐ │
│ │ WebSocket  │ │  │ │ WebSocket  │ │  │ │ WebSocket  │ │
│ │ Handler    │ │  │ │ Handler    │ │  │ │ Handler    │ │
│ └────────────┘ │  │ └────────────┘ │  │ └────────────┘ │
│                │  │                │  │                │
│ ┌────────────┐ │  │ ┌────────────┐ │  │ ┌────────────┐ │
│ │ Auth       │ │  │ │ Auth       │ │  │ │ Auth       │ │
│ │ Service    │ │  │ │ Service    │ │  │ │ Service    │ │
│ └────────────┘ │  │ └────────────┘ │  │ └────────────┘ │
│                │  │                │  │                │
│ ┌────────────┐ │  │ ┌────────────┐ │  │ ┌────────────┐ │
│ │ Proxy      │ │  │ │ Proxy      │ │  │ │ Proxy      │ │
│ │ Engine     │ │  │ │ Engine     │ │  │ │ Engine     │ │
│ └────────────┘ │  │ └────────────┘ │  │ └────────────┘ │
└────────┬───────┘  └────────┬───────┘  └────────┬───────┘
         │                   │                   │
         └───────────────────┼───────────────────┘
                             │
        ┌────────────────────┼────────────────────┐
        │                    │                    │
┌───────▼────────┐  ┌────────▼───────┐  ┌────────▼───────┐
│  PostgreSQL    │  │     Redis      │  │    Celery      │
│  Primary DB    │  │   Cluster      │  │   Workers      │
│                │  │                │  │                │
│ - Users        │  │ - Sessions     │  │ - Metrics      │
│ - Services     │  │ - Cache        │  │ - Alerts       │
│ - Config       │  │ - Pub/Sub      │  │ - Reports      │
│ - Audit Logs   │  │ - Queues       │  │ - Maintenance  │
└────────────────┘  └────────────────┘  └────────────────┘
```

### Stateless Design

Controller instances are designed to be stateless, storing all persistent data in PostgreSQL and all session data in Redis. This stateless architecture enables:

- **Horizontal Scaling**: Add more Controller instances to handle increased load
- **Rolling Updates**: Update instances one at a time without downtime
- **Automatic Failover**: If an instance fails, load balancer redirects to healthy instances
- **Geographic Distribution**: Deploy instances in multiple regions for lower latency

### High Availability

For production deployments, the Controller supports high availability configurations:

**Multiple Controller Instances**: Run 3+ instances behind a load balancer for redundancy and load distribution.

**Database Replication**: PostgreSQL primary-replica setup with automatic failover using Patroni or Stolon. Read replicas can handle read-heavy queries like dashboard metrics and service catalog browsing.

**Redis Cluster**: Redis Sentinel or Cluster mode provides automatic failover and data persistence.

**Health Checks**: Load balancer performs regular health checks on Controller instances, removing unhealthy instances from rotation.

## Core Responsibilities

### Agent Management

The Controller maintains persistent WebSocket connections with all registered agents. These bidirectional connections enable:

- **Real-time Service Discovery**: Agents push service discoveries immediately to the Controller
- **Configuration Distribution**: Controller pushes configuration updates to agents
- **Command Execution**: Controller can trigger agent actions (rescan, restart, update)
- **Heartbeat Monitoring**: Controller tracks agent health via periodic heartbeats
- **Offline Detection**: Controller detects when agents go offline and triggers alerts

### User Session Management

The Controller manages the complete lifecycle of user sessions:

**Session Creation**: When a user successfully authenticates, the Controller creates a session record in Redis with a unique session ID. The session stores user identity, roles, permissions, authentication method, device information, and IP address.

**Session Validation**: Every API request includes a session token (HTTP-only cookie or Authorization header). The Controller validates the token, checks expiration, and loads user context.

**Session Extension**: For sliding expiration, the Controller extends session lifetime on each activity, up to an absolute maximum duration.

**Session Termination**: Sessions end on explicit logout, timeout, password change, role modification, or administrative revocation.

**Concurrent Session Control**: The Controller enforces limits on concurrent sessions per user, either preventing new logins or terminating oldest sessions when limits are exceeded.

### Permission Evaluation

Every access request triggers permission evaluation:

1. **Load User Context**: Retrieve user's roles and explicit permissions from cache or database
2. **Identify Resource**: Determine what resource is being accessed (VM, service, configuration)
3. **Check Permissions**: Evaluate if user has required permission for the action
4. **Apply Conditions**: Check contextual conditions (time windows, IP restrictions, MFA status)
5. **Evaluate Approval Requirements**: Determine if approval workflow is required
6. **Return Decision**: Grant or deny access, with detailed reason for audit logging

### Metrics Aggregation

Agents continuously stream metrics to the Controller. The Controller:

- **Receives Metrics**: Accepts metric batches from agents via WebSocket or REST API
- **Validates Data**: Ensures metric data is well-formed and within expected ranges
- **Stores Raw Data**: Writes high-resolution metrics to PostgreSQL or time-series database
- **Aggregates Data**: Celery workers periodically aggregate raw metrics into 5-minute, 1-hour, and 1-day rollups
- **Evaluates Alerts**: Checks metric values against alert rule thresholds
- **Serves Queries**: Provides API endpoints for querying metrics with time ranges and aggregations

### Audit Logging

The Controller maintains comprehensive audit logs for compliance and security:

**Authentication Events**: Login attempts (success and failure), logout, MFA enrollment, password changes, account lockouts

**Authorization Events**: Permission checks, access denials, role assignments, permission grants

**Resource Access**: Service connections, configuration views, data exports, report generation

**Configuration Changes**: Any modification to system settings, user accounts, roles, or service configurations

**Security Events**: Suspicious activity, brute force attempts, privilege escalations, emergency access usage

All audit log entries include timestamp, user identity, action performed, resource affected, result, source IP, user agent, and before/after values for changes.

## Deployment Models

The Controller supports multiple deployment architectures to suit different organizational needs:

### Single Instance (Development/Small Deployments)

A single Controller instance with local PostgreSQL and Redis, suitable for development environments or small deployments with fewer than 10 VMs and minimal user load.

### High Availability (Production)

Multiple Controller instances (typically 3+) behind a load balancer, with PostgreSQL replication and Redis Cluster. This configuration provides redundancy, automatic failover, and horizontal scaling for production workloads.

### Hybrid Control/Data Plane

The Controller serves as the control plane (authentication, authorization, configuration) while data plane traffic (actual service connections) flows directly between clients and agents. This architecture reduces Controller load and improves connection latency.

### Multi-Region

Deploy Controller instances in multiple geographic regions with a global load balancer. Each region has local PostgreSQL replicas for read operations, with a primary database in one region for writes. This configuration minimizes latency for globally distributed users.

## Security Considerations

### Network Security

The Controller should be deployed behind a firewall with only necessary ports exposed:
- Port 443 (HTTPS) for web interface and API
- Port 443 (WSS) for WebSocket connections from agents and clients

All communication uses TLS 1.3 with strong cipher suites. The Controller supports certificate pinning for agent connections to prevent man-in-the-middle attacks.

### Data Security

**Encryption at Rest**: Sensitive data in PostgreSQL (passwords, tokens, secrets) is encrypted using application-level encryption with AES-256-GCM.

**Encryption in Transit**: All network communication uses TLS. Agent connections can optionally use mutual TLS (mTLS) for certificate-based authentication.

**Secret Management**: The Controller integrates with external secret vaults (HashiCorp Vault, AWS Secrets Manager, Azure Key Vault) for storing sensitive credentials.

### Access Control

The Controller itself requires strong access controls:
- Super Admin accounts protected with mandatory MFA
- Admin actions require approval from multiple administrators
- Privileged operations logged with enhanced detail
- Break-glass emergency access with immediate security team notification

### Compliance

The Controller supports compliance requirements for various standards:
- **HIPAA**: Comprehensive audit logging, encryption, access controls
- **SOC 2**: Security monitoring, change management, incident response
- **PCI-DSS**: Network segmentation, access logging, encryption
- **GDPR**: Data retention policies, user data export, right to deletion

## Integration Points

### External Identity Providers

The Controller integrates with enterprise identity systems:
- **SAML 2.0**: Okta, Azure AD, Google Workspace, generic SAML IdPs
- **OAuth 2.0/OpenID Connect**: Authorization code flow with PKCE
- **LDAP/Active Directory**: User authentication and group synchronization

### Monitoring Systems

Export metrics and logs to external monitoring platforms:
- **Prometheus**: Metrics scraping endpoint
- **Grafana**: Pre-built dashboards for visualization
- **Datadog**: Agent integration for metrics and logs
- **ELK Stack**: Log forwarding to Elasticsearch
- **Splunk**: Log forwarding via HTTP Event Collector

### Notification Services

Send alerts and notifications through various channels:
- **Email**: SMTP integration for email notifications
- **Slack**: Webhook and OAuth app integration
- **Microsoft Teams**: Webhook integration
- **PagerDuty**: Incident creation and escalation
- **Webhooks**: Generic HTTP POST for custom integrations

### Secret Vaults

Integrate with external secret management systems:
- **HashiCorp Vault**: KV secrets engine, dynamic secrets
- **AWS Secrets Manager**: Automatic rotation, IAM integration
- **Azure Key Vault**: Managed identities, certificate management
- **Google Secret Manager**: Cloud IAM integration

## Performance Characteristics

### Scalability

A single Controller instance can handle:
- 1,000+ concurrent WebSocket connections (agents + clients)
- 10,000+ HTTP requests per second (with caching)
- 100+ VMs with active agents
- 10,000+ discovered services
- 1,000+ concurrent user sessions

Horizontal scaling by adding more instances increases these limits proportionally.

### Latency

Typical response times:
- API requests: < 50ms (cached data), < 200ms (database queries)
- WebSocket message delivery: < 10ms
- Permission evaluation: < 5ms (cached), < 50ms (database lookup)
- Service catalog queries: < 100ms (10,000 services)

### Resource Requirements

Minimum requirements per Controller instance:
- **CPU**: 2 cores (4+ recommended for production)
- **RAM**: 4 GB (8+ GB recommended for production)
- **Disk**: 20 GB for application, logs scale with retention policy
- **Network**: 1 Gbps network interface

Database requirements scale with data volume:
- **PostgreSQL**: 4 GB RAM minimum, 8+ GB recommended, disk scales with audit log retention
- **Redis**: 2 GB RAM minimum, 4+ GB recommended for large deployments

## Operational Considerations

### Monitoring

Monitor Controller health through:
- **Health Check Endpoint**: `/health` returns 200 OK when healthy
- **Metrics Endpoint**: `/metrics` exposes Prometheus-format metrics
- **Log Aggregation**: Structured JSON logs for centralized collection
- **Database Connection Pool**: Monitor pool utilization and wait times
- **Redis Connection**: Monitor connection count and latency

### Backup and Recovery

**Database Backups**: Regular PostgreSQL backups (daily full, hourly incremental) with point-in-time recovery capability.

**Configuration Backups**: Export system configuration to version-controlled repository.

**Disaster Recovery**: Document and test recovery procedures, including database restoration, configuration restoration, and service resumption.

### Updates and Maintenance

**Rolling Updates**: Update Controller instances one at a time to maintain availability.

**Database Migrations**: Use Alembic for schema migrations with rollback capability.

**Maintenance Windows**: Schedule maintenance for low-traffic periods, notify users in advance.

**Feature Flags**: Use feature flags to gradually roll out new features and quickly disable problematic features.

## Future Enhancements

Planned enhancements for future versions:

- **GraphQL API**: Alternative to REST API for more flexible queries
- **Multi-Tenancy**: Support multiple isolated organizations within single deployment
- **Advanced Analytics**: Machine learning for anomaly detection and capacity planning
- **Mobile App**: Native iOS and Android apps for monitoring and management
- **API Gateway**: Built-in API gateway for exposing services with rate limiting and authentication
- **Service Mesh Integration**: Integration with Istio, Linkerd for advanced traffic management
