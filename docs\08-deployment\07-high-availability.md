# High Availability Configuration

Configure the VM Gateway platform for maximum uptime and fault tolerance.

## HA Architecture

```
┌─────────────────────────────────────────────┐
│         Load Balancer (Active/Passive)      │
│         HAProxy + Keepalived                │
│         VIP: *************                  │
└──────────────┬──────────────────────────────┘
               │
    ┌──────────┼──────────┐
    │          │          │
┌───▼───┐  ┌──▼────┐  ┌──▼────┐
│Ctrl 1 │  │Ctrl 2 │  │Ctrl 3 │
└───┬───┘  └───┬───┘  └───┬───┘
    │          │          │
    └──────────┼──────────┘
               │
    ┌──────────▼──────────┐
    │  PostgreSQL Cluster │
    │  Primary + Replicas │
    │  Patroni + etcd     │
    └─────────────────────┘
```

## Load Balancer HA

### Keepalived Configuration

**Master Node**:
```
# /etc/keepalived/keepalived.conf
vrrp_instance VI_1 {
    state MASTER
    interface eth0
    virtual_router_id 51
    priority 101
    advert_int 1
    
    authentication {
        auth_type PASS
        auth_pass secret123
    }
    
    virtual_ipaddress {
        *************/24
    }
}
```

**Backup Node**:
```
# /etc/keepalived/keepalived.conf
vrrp_instance VI_1 {
    state BACKUP
    interface eth0
    virtual_router_id 51
    priority 100
    advert_int 1
    
    authentication {
        auth_type PASS
        auth_pass secret123
    }
    
    virtual_ipaddress {
        *************/24
    }
}
```

## Database HA

### Patroni Configuration

```yaml
# /etc/patroni/patroni.yml
scope: vm-gateway-cluster
namespace: /service/
name: postgres-01

restapi:
  listen: 0.0.0.0:8008
  connect_address: *********:8008

etcd:
  hosts: *********:2379,*********:2379,*********:2379

bootstrap:
  dcs:
    ttl: 30
    loop_wait: 10
    retry_timeout: 10
    maximum_lag_on_failover: 1048576
    postgresql:
      use_pg_rewind: true
      parameters:
        max_connections: 200
        shared_buffers: 4GB

postgresql:
  listen: 0.0.0.0:5432
  connect_address: *********:5432
  data_dir: /var/lib/postgresql/15/main
  authentication:
    replication:
      username: replicator
      password: repl_password
    superuser:
      username: postgres
      password: postgres_password
```

### Automatic Failover

Patroni handles automatic failover:

1. **Health Check**: Patroni monitors primary
2. **Failure Detection**: Primary becomes unreachable
3. **Leader Election**: etcd elects new primary
4. **Promotion**: Replica promoted to primary
5. **Notification**: Applications reconnect to new primary

## Redis HA

### Redis Sentinel

```
# /etc/redis/sentinel.conf
port 26379
sentinel monitor mymaster ********* 6379 2
sentinel auth-pass mymaster redis_password
sentinel down-after-milliseconds mymaster 5000
sentinel parallel-syncs mymaster 1
sentinel failover-timeout mymaster 10000
```

### Redis Cluster

```bash
# Create cluster
redis-cli --cluster create \
  *********:6379 \
  *********:6379 \
  *********:6379 \
  *********:6379 \
  *********:6379 \
  10.0.3.15:6379 \
  --cluster-replicas 1
```

## Application HA

### Health Checks

```python
# health.py
from fastapi import FastAPI, Response

app = FastAPI()

@app.get("/health")
async def health_check():
    """Basic health check"""
    return {"status": "healthy"}

@app.get("/ready")
async def readiness_check():
    """Readiness check with dependencies"""
    checks = {
        "database": check_database(),
        "redis": check_redis(),
        "disk_space": check_disk_space()
    }
    
    if all(checks.values()):
        return {"status": "ready", "checks": checks}
    else:
        return Response(
            content={"status": "not_ready", "checks": checks},
            status_code=503
        )
```

### Graceful Shutdown

```python
# main.py
import signal
import sys

def signal_handler(sig, frame):
    """Handle shutdown signals"""
    logger.info("Shutting down gracefully...")
    
    # Stop accepting new requests
    stop_accepting_requests()
    
    # Wait for active requests to complete
    wait_for_active_requests(timeout=30)
    
    # Close database connections
    close_database_connections()
    
    # Exit
    sys.exit(0)

signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)
```

## Monitoring and Alerting

### Prometheus Alerts

```yaml
# alerts.yml
groups:
- name: vm-gateway
  rules:
  - alert: InstanceDown
    expr: up == 0
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Instance {{ $labels.instance }} down"
  
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.05
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High error rate on {{ $labels.instance }}"
  
  - alert: DatabaseReplicationLag
    expr: pg_replication_lag_seconds > 60
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Database replication lag > 60s"
```

## Disaster Recovery

### Backup Strategy

```bash
# Daily backups
0 2 * * * /usr/local/bin/backup-database.sh
0 3 * * * /usr/local/bin/backup-config.sh

# Weekly full backups
0 1 * * 0 /usr/local/bin/backup-full.sh
```

### Recovery Procedures

1. **Database Recovery**:
```bash
# Stop application
systemctl stop vm-gateway-controller

# Restore database
pg_restore -d vm_gateway backup.dump

# Start application
systemctl start vm-gateway-controller
```

2. **Configuration Recovery**:
```bash
# Restore from backup
tar xzf config_backup.tar.gz -C /

# Restart services
systemctl restart vm-gateway-controller
```

## Testing HA

### Chaos Engineering

```bash
# Test controller failure
systemctl stop vm-gateway-controller

# Test database failover
systemctl stop postgresql

# Test network partition
iptables -A INPUT -s ********* -j DROP
```

### Failover Testing

```python
# test_failover.py
def test_database_failover():
    """Test database failover"""
    # Kill primary
    kill_primary_database()
    
    # Wait for failover
    time.sleep(10)
    
    # Verify new primary
    assert check_database_writable()
    
    # Verify application still works
    response = requests.get("https://vm-gateway.example.com/health")
    assert response.status_code == 200
```

## Best Practices

1. **No single point of failure**: Redundancy for all components
2. **Automated failover**: Use Patroni, Sentinel
3. **Health checks**: Comprehensive health monitoring
4. **Graceful degradation**: Continue with reduced functionality
5. **Regular testing**: Test failover procedures monthly
6. **Monitoring**: 24/7 monitoring and alerting
7. **Documentation**: Maintain runbooks
8. **Backups**: Regular automated backups

## Next Steps

- [Backup Recovery](08-backup-recovery.md) - Backup strategies
- [Multi-VM](04-multi-vm.md) - Multi-VM deployment
- [Kubernetes](03-kubernetes.md) - Kubernetes HA
