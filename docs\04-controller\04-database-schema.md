---
title: "Database Schema"
section: "Controller"
order: 4
tags: ["controller", "database", "postgresql", "schema"]
last_updated: "2025-11-08"
---

# Database Schema

The Controller uses PostgreSQL as its primary data store for all persistent data including user accounts, service catalog, configurations, audit logs, and metrics. The schema is designed for performance, data integrity, and scalability.

## Database Technology

### PostgreSQL Features

**JSONB Support**: Flexible storage for service metadata, custom attributes, and configuration data without rigid schema constraints.

**Full-Text Search**: Built-in full-text search capabilities for service catalog and log queries.

**Partitioning**: Table partitioning for time-series data (metrics, logs) to improve query performance and enable efficient data retention.

**Replication**: Streaming replication for high availability with primary-replica setup.

**Connection Pooling**: PgBouncer for efficient connection management and reduced overhead.

**Indexes**: Strategic indexing on frequently queried columns and JSONB fields for optimal performance.

## Core Tables

### users

Stores user account information.

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE,
    name VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    mfa_enabled BOOLEAN DEFAULT FALSE,
    mfa_secret VARCHAR(255),
    mfa_backup_codes JSONB,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    last_login TIMESTAMP,
    last_password_change TIMESTAMP NOT NULL DEFAULT NOW(),
    password_history JSONB DEFAULT '[]',
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_last_login ON users(last_login);
```

**Fields**:
- `id`: Unique user identifier (UUID)
- `email`: User email address (unique, used for login)
- `username`: Optional username (alternative login)
- `name`: User's full name
- `password_hash`: Bcrypt or Argon2 hashed password
- `status`: Account status (active, disabled, locked, pending)
- `mfa_enabled`: Whether MFA is enabled
- `mfa_secret`: TOTP secret (encrypted)
- `mfa_backup_codes`: Array of backup codes (hashed)
- `failed_login_attempts`: Counter for failed logins
- `locked_until`: Account lock expiration time
- `last_login`: Last successful login timestamp
- `last_password_change`: When password was last changed
- `password_history`: Array of previous password hashes
- `preferences`: User preferences (theme, language, etc.)
- `created_at`, `updated_at`, `deleted_at`: Timestamps



### roles

Defines roles for RBAC system.

```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    type VARCHAR(20) NOT NULL DEFAULT 'custom',
    parent_role_id UUID REFERENCES roles(id),
    permissions JSONB NOT NULL DEFAULT '[]',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_roles_name ON roles(name);
CREATE INDEX idx_roles_type ON roles(type);
```

**Fields**:
- `id`: Unique role identifier
- `name`: Role name (unique)
- `description`: Role description
- `type`: Role type (built-in, custom)
- `parent_role_id`: Parent role for inheritance
- `permissions`: Array of permission objects
- `created_at`, `updated_at`: Timestamps

### user_roles

Maps users to roles (many-to-many).

```sql
CREATE TABLE user_roles (
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    granted_by UUID REFERENCES users(id),
    granted_at TIMESTAMP NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP,
    PRIMARY KEY (user_id, role_id)
);

CREATE INDEX idx_user_roles_user ON user_roles(user_id);
CREATE INDEX idx_user_roles_role ON user_roles(role_id);
CREATE INDEX idx_user_roles_expires ON user_roles(expires_at) WHERE expires_at IS NOT NULL;
```

### groups

Organizes users into groups.

```sql
CREATE TABLE groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_group_id UUID REFERENCES groups(id),
    type VARCHAR(50),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_groups_name ON groups(name);
CREATE INDEX idx_groups_parent ON groups(parent_group_id);
```

### group_members

Maps users to groups (many-to-many).

```sql
CREATE TABLE group_members (
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    added_by UUID REFERENCES users(id),
    added_at TIMESTAMP NOT NULL DEFAULT NOW(),
    PRIMARY KEY (group_id, user_id)
);

CREATE INDEX idx_group_members_group ON group_members(group_id);
CREATE INDEX idx_group_members_user ON group_members(user_id);
```

### sessions

Stores active user sessions.

```sql
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    device_info JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    last_activity TIMESTAMP NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL
);

CREATE INDEX idx_sessions_user ON sessions(user_id);
CREATE INDEX idx_sessions_token ON sessions(token_hash);
CREATE INDEX idx_sessions_expires ON sessions(expires_at);
```

**Fields**:
- `id`: Session identifier
- `user_id`: User who owns this session
- `token_hash`: Hashed session token
- `device_info`: Device fingerprint and details
- `ip_address`: Client IP address
- `user_agent`: Client user agent string
- `created_at`: Session creation time
- `last_activity`: Last activity timestamp
- `expires_at`: Session expiration time

### vms

Stores information about monitored VMs.

```sql
CREATE TABLE vms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    hostname VARCHAR(255),
    ip_addresses JSONB NOT NULL DEFAULT '[]',
    status VARCHAR(20) NOT NULL DEFAULT 'unknown',
    agent_version VARCHAR(50),
    agent_uptime INTEGER,
    last_heartbeat TIMESTAMP,
    system_info JSONB DEFAULT '{}',
    tags JSONB DEFAULT '[]',
    environment VARCHAR(50),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP
);

CREATE INDEX idx_vms_name ON vms(name);
CREATE INDEX idx_vms_status ON vms(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_vms_environment ON vms(environment);
CREATE INDEX idx_vms_last_heartbeat ON vms(last_heartbeat);
CREATE INDEX idx_vms_tags ON vms USING GIN(tags);
```

**Fields**:
- `id`: Unique VM identifier
- `name`: VM display name
- `hostname`: VM hostname
- `ip_addresses`: Array of IP addresses
- `status`: VM status (online, offline, degraded, unknown)
- `agent_version`: Installed agent version
- `agent_uptime`: Agent uptime in seconds
- `last_heartbeat`: Last heartbeat timestamp
- `system_info`: OS, kernel, CPU, memory, disk info
- `tags`: Array of tags
- `environment`: Environment (production, staging, development)
- `metadata`: Custom metadata
- `created_at`, `updated_at`, `deleted_at`: Timestamps

### services

Stores discovered services.

```sql
CREATE TABLE services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vm_id UUID NOT NULL REFERENCES vms(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL,
    subtype VARCHAR(50),
    version VARCHAR(100),
    port INTEGER NOT NULL,
    protocol VARCHAR(10) NOT NULL,
    bind_address VARCHAR(50),
    status VARCHAR(20) NOT NULL DEFAULT 'unknown',
    health_score INTEGER DEFAULT 0,
    health_check_config JSONB DEFAULT '{}',
    last_health_check TIMESTAMP,
    process_info JSONB DEFAULT '{}',
    tags JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    discovered_at TIMESTAMP NOT NULL DEFAULT NOW(),
    last_seen TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP
);

CREATE INDEX idx_services_vm ON services(vm_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_services_type ON services(type);
CREATE INDEX idx_services_status ON services(status);
CREATE INDEX idx_services_port ON services(port);
CREATE INDEX idx_services_tags ON services USING GIN(tags);
CREATE INDEX idx_services_name_search ON services USING GIN(to_tsvector('english', name || ' ' || COALESCE(description, '')));
```

**Fields**:
- `id`: Unique service identifier
- `vm_id`: VM where service is running
- `name`: Service name
- `description`: Service description
- `type`: Service type (web, database, cache, etc.)
- `subtype`: Specific service (postgresql, redis, nginx)
- `version`: Service version
- `port`: Port number
- `protocol`: Protocol (tcp, udp)
- `bind_address`: Bind address (0.0.0.0, localhost, specific IP)
- `status`: Health status (healthy, warning, critical, unknown)
- `health_score`: Health score (0-100)
- `health_check_config`: Health check configuration
- `last_health_check`: Last health check timestamp
- `process_info`: PID, user, command, working directory
- `tags`: Array of tags
- `metadata`: Custom metadata
- `discovered_at`: When service was first discovered
- `last_seen`: Last time service was detected
- `updated_at`, `deleted_at`: Timestamps

### connections

Tracks active and historical connections.

```sql
CREATE TABLE connections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    service_id UUID NOT NULL REFERENCES services(id),
    vm_id UUID NOT NULL REFERENCES vms(id),
    local_port INTEGER,
    tunnel_type VARCHAR(20),
    tunnel_config JSONB,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    bytes_sent BIGINT DEFAULT 0,
    bytes_received BIGINT DEFAULT 0,
    started_at TIMESTAMP NOT NULL DEFAULT NOW(),
    last_activity TIMESTAMP NOT NULL DEFAULT NOW(),
    ended_at TIMESTAMP,
    expires_at TIMESTAMP,
    termination_reason VARCHAR(100)
);

CREATE INDEX idx_connections_user ON connections(user_id);
CREATE INDEX idx_connections_service ON connections(service_id);
CREATE INDEX idx_connections_status ON connections(status);
CREATE INDEX idx_connections_started ON connections(started_at);
CREATE INDEX idx_connections_active ON connections(status, expires_at) WHERE status = 'active';
```

**Fields**:
- `id`: Connection identifier
- `user_id`: User who initiated connection
- `service_id`: Service being accessed
- `vm_id`: VM where service is running
- `local_port`: Local port on client
- `tunnel_type`: Tunnel type (wireguard, tls, websocket)
- `tunnel_config`: Tunnel configuration details
- `status`: Connection status (active, expired, terminated)
- `bytes_sent`, `bytes_received`: Data transfer statistics
- `started_at`: Connection start time
- `last_activity`: Last activity timestamp
- `ended_at`: Connection end time
- `expires_at`: Connection expiration time
- `termination_reason`: Why connection ended

### metrics

Stores time-series metrics data (partitioned by time).

```sql
CREATE TABLE metrics (
    id BIGSERIAL,
    resource_type VARCHAR(20) NOT NULL,
    resource_id UUID NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    value DOUBLE PRECISION NOT NULL,
    tags JSONB DEFAULT '{}',
    timestamp TIMESTAMP NOT NULL DEFAULT NOW()
) PARTITION BY RANGE (timestamp);

CREATE INDEX idx_metrics_resource ON metrics(resource_type, resource_id, timestamp);
CREATE INDEX idx_metrics_name ON metrics(metric_name, timestamp);

-- Create monthly partitions
CREATE TABLE metrics_2025_11 PARTITION OF metrics
    FOR VALUES FROM ('2025-11-01') TO ('2025-12-01');
```

**Fields**:
- `id`: Metric entry identifier
- `resource_type`: Type of resource (vm, service)
- `resource_id`: Resource identifier
- `metric_name`: Metric name (cpu_usage, memory_usage, etc.)
- `value`: Metric value
- `tags`: Additional tags for filtering
- `timestamp`: Metric timestamp

### alerts

Stores alert definitions and instances.

```sql
CREATE TABLE alert_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    metric_name VARCHAR(100) NOT NULL,
    resource_type VARCHAR(20),
    resource_id UUID,
    condition JSONB NOT NULL,
    severity VARCHAR(20) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    actions JSONB DEFAULT '[]',
    notification_config JSONB DEFAULT '{}',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_alert_rules_enabled ON alert_rules(enabled);
CREATE INDEX idx_alert_rules_resource ON alert_rules(resource_type, resource_id);

CREATE TABLE alert_instances (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rule_id UUID NOT NULL REFERENCES alert_rules(id) ON DELETE CASCADE,
    resource_type VARCHAR(20) NOT NULL,
    resource_id UUID NOT NULL,
    severity VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    current_value DOUBLE PRECISION,
    threshold DOUBLE PRECISION,
    triggered_at TIMESTAMP NOT NULL DEFAULT NOW(),
    acknowledged_at TIMESTAMP,
    acknowledged_by UUID REFERENCES users(id),
    resolved_at TIMESTAMP,
    resolved_by UUID REFERENCES users(id),
    notes TEXT
);

CREATE INDEX idx_alert_instances_rule ON alert_instances(rule_id);
CREATE INDEX idx_alert_instances_status ON alert_instances(status);
CREATE INDEX idx_alert_instances_resource ON alert_instances(resource_type, resource_id);
CREATE INDEX idx_alert_instances_triggered ON alert_instances(triggered_at);
```

### audit_logs

Comprehensive audit trail (partitioned by time).

```sql
CREATE TABLE audit_logs (
    id BIGSERIAL,
    timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
    user_id UUID REFERENCES users(id),
    user_email VARCHAR(255),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id UUID,
    resource_name VARCHAR(255),
    result VARCHAR(20) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    session_id UUID,
    details JSONB DEFAULT '{}',
    before_state JSONB,
    after_state JSONB
) PARTITION BY RANGE (timestamp);

CREATE INDEX idx_audit_logs_user ON audit_logs(user_id, timestamp);
CREATE INDEX idx_audit_logs_action ON audit_logs(action, timestamp);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id, timestamp);
CREATE INDEX idx_audit_logs_result ON audit_logs(result, timestamp);

-- Create monthly partitions
CREATE TABLE audit_logs_2025_11 PARTITION OF audit_logs
    FOR VALUES FROM ('2025-11-01') TO ('2025-12-01');
```

**Fields**:
- `id`: Log entry identifier
- `timestamp`: When action occurred
- `user_id`, `user_email`: User who performed action
- `action`: Action performed (login, service:connect, etc.)
- `resource_type`, `resource_id`, `resource_name`: Affected resource
- `result`: Result (success, failure, denied)
- `ip_address`: Source IP address
- `user_agent`: Client user agent
- `session_id`: Session identifier
- `details`: Additional details
- `before_state`, `after_state`: State before and after change

### configurations

Stores configuration data.

```sql
CREATE TABLE configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scope VARCHAR(50) NOT NULL,
    scope_id UUID,
    key VARCHAR(255) NOT NULL,
    value JSONB NOT NULL,
    version INTEGER NOT NULL DEFAULT 1,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    UNIQUE(scope, scope_id, key)
);

CREATE INDEX idx_configurations_scope ON configurations(scope, scope_id);
```

**Fields**:
- `id`: Configuration entry identifier
- `scope`: Configuration scope (global, vm, service, user)
- `scope_id`: Scope identifier (NULL for global)
- `key`: Configuration key
- `value`: Configuration value (JSONB)
- `version`: Version number for optimistic locking
- `created_by`: User who created configuration
- `created_at`, `updated_at`: Timestamps

### configuration_history

Tracks configuration changes.

```sql
CREATE TABLE configuration_history (
    id BIGSERIAL PRIMARY KEY,
    configuration_id UUID NOT NULL REFERENCES configurations(id) ON DELETE CASCADE,
    version INTEGER NOT NULL,
    value JSONB NOT NULL,
    changed_by UUID REFERENCES users(id),
    changed_at TIMESTAMP NOT NULL DEFAULT NOW(),
    change_reason TEXT
);

CREATE INDEX idx_config_history_config ON configuration_history(configuration_id, version);
```

### webhooks

Stores webhook registrations.

```sql
CREATE TABLE webhooks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    url VARCHAR(500) NOT NULL,
    events JSONB NOT NULL DEFAULT '[]',
    secret VARCHAR(255),
    enabled BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_webhooks_enabled ON webhooks(enabled);
```

### webhook_deliveries

Tracks webhook delivery attempts.

```sql
CREATE TABLE webhook_deliveries (
    id BIGSERIAL PRIMARY KEY,
    webhook_id UUID NOT NULL REFERENCES webhooks(id) ON DELETE CASCADE,
    event VARCHAR(100) NOT NULL,
    payload JSONB NOT NULL,
    status VARCHAR(20) NOT NULL,
    response_code INTEGER,
    response_body TEXT,
    attempt INTEGER NOT NULL DEFAULT 1,
    delivered_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_webhook_deliveries_webhook ON webhook_deliveries(webhook_id, delivered_at);
CREATE INDEX idx_webhook_deliveries_status ON webhook_deliveries(status);
```

## Database Migrations

### Migration Strategy

**Alembic**: SQLAlchemy's migration tool manages schema changes.

**Version Control**: All migrations stored in version control with descriptive names.

**Rollback Support**: Each migration includes downgrade path for rollback.

**Testing**: Migrations tested on copy of production data before deployment.

**Zero-Downtime**: Migrations designed to avoid locking tables or causing downtime.

### Migration Process

1. **Generate Migration**: `alembic revision --autogenerate -m "description"`
2. **Review Migration**: Manually review generated migration code
3. **Test Migration**: Apply to test database and verify
4. **Apply to Staging**: Test on staging environment
5. **Apply to Production**: Apply during maintenance window or with zero-downtime strategy
6. **Verify**: Confirm migration successful and application functioning

### Zero-Downtime Migration Patterns

**Adding Columns**: Add column as nullable, populate data, then add NOT NULL constraint.

**Removing Columns**: Stop using column in code, deploy, then remove column in later migration.

**Renaming Columns**: Add new column, copy data, update code to use new column, remove old column.

**Changing Column Types**: Add new column with new type, copy and convert data, switch code, remove old column.

## Performance Optimization

### Indexing Strategy

**Primary Keys**: All tables have UUID primary keys with B-tree indexes.

**Foreign Keys**: All foreign key columns indexed for join performance.

**Query Patterns**: Indexes created based on common query patterns (WHERE, ORDER BY, JOIN).

**Partial Indexes**: Indexes with WHERE clauses for frequently filtered queries.

**GIN Indexes**: JSONB and array columns use GIN indexes for containment queries.

**Full-Text Search**: GIN indexes on tsvector columns for text search.

### Query Optimization

**EXPLAIN ANALYZE**: Regular analysis of slow queries.

**Query Rewriting**: Optimize complex queries for better execution plans.

**Materialized Views**: Pre-computed aggregations for dashboard queries.

**Connection Pooling**: PgBouncer reduces connection overhead.

**Prepared Statements**: Reuse query plans for frequently executed queries.

### Data Retention

**Metrics**: Raw metrics retained for 7 days, aggregated metrics for 90 days.

**Logs**: Application logs retained for 30 days, audit logs for 1 year.

**Audit Logs**: Long-term retention with archival to S3 after 1 year.

**Partitioning**: Time-series tables partitioned by month for efficient data management.

**Archival**: Old partitions archived to cold storage and dropped from database.

## Backup and Recovery

### Backup Strategy

**Continuous Archiving**: WAL archiving to S3 for point-in-time recovery.

**Daily Full Backups**: pg_dump or pg_basebackup to backup storage.

**Hourly Incremental**: WAL segments archived continuously.

**Retention**: Full backups retained for 30 days, WAL for 7 days.

**Testing**: Regular restore testing to verify backup integrity.

### Recovery Procedures

**Point-in-Time Recovery**: Restore from base backup and replay WAL to specific timestamp.

**Table Recovery**: Extract specific table from backup using pg_restore.

**Disaster Recovery**: Documented procedures for complete database restoration.

**RTO/RPO**: Recovery Time Objective < 1 hour, Recovery Point Objective < 5 minutes.

## Security

### Encryption

**At Rest**: PostgreSQL data directory encrypted with LUKS or cloud provider encryption.

**In Transit**: All connections use TLS with strong cipher suites.

**Column Encryption**: Sensitive columns (passwords, secrets) encrypted at application level.

### Access Control

**Database Users**: Separate database users for application, read-only, and admin access.

**Row-Level Security**: RLS policies for multi-tenant scenarios (future enhancement).

**Least Privilege**: Application user has minimal required permissions.

**Audit Logging**: PostgreSQL audit extension logs all database access.

### Secrets Management

**Connection Strings**: Stored in environment variables or secret vault.

**Encryption Keys**: Master encryption key stored in KMS or vault.

**Rotation**: Regular rotation of database passwords and encryption keys.
