/* ===================================
   VM Gateway Documentation Viewer
   Blue Theme with Light/Dark Mode
   =================================== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light Mode - Blue Theme */
    --primary-blue: #2563eb;
    --primary-blue-hover: #1d4ed8;
    --primary-blue-light: #3b82f6;
    --accent-blue: #60a5fa;
    --accent-blue-light: #93c5fd;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-code: #f1f5f9;
    --bg-hover: #eff6ff;
    
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #64748b;
    
    --border-color: #cbd5e1;
    --border-light: #e2e8f0;
    
    --sidebar-bg: #f8fafc;
    --sidebar-hover: #e0f2fe;
    --sidebar-active: #dbeafe;
    
    --shadow-sm: 0 1px 2px 0 rgba(37, 99, 235, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(37, 99, 235, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(37, 99, 235, 0.1);
    
    /* Spacing */
    --sidebar-width: 280px;
    --header-height: 60px;
    
    /* Typography */
    --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'Consolas', 'Monaco', 'Courier New', monospace;
}

[data-theme="dark"] {
    /* Dark Mode - Blue Theme */
    --primary-blue: #3b82f6;
    --primary-blue-hover: #60a5fa;
    --primary-blue-light: #2563eb;
    --accent-blue: #93c5fd;
    --accent-blue-light: #bfdbfe;
    
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-code: #1e293b;
    --bg-hover: #1e3a5f;
    
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    
    --border-color: #334155;
    --border-light: #475569;
    
    --sidebar-bg: #1e293b;
    --sidebar-hover: #1e3a5f;
    --sidebar-active: #1e3a8a;
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-sans);
    color: var(--text-primary);
    background-color: var(--bg-primary);
    line-height: 1.6;
    overflow-x: hidden;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* ===================================
   Theme Toggle Button
   =================================== */

.theme-toggle {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--primary-blue);
    border: none;
    color: white;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    background: var(--primary-blue-hover);
    transform: scale(1.1);
}

.theme-toggle:active {
    transform: scale(0.95);
}

/* ===================================
   Layout Structure
   =================================== */

.mobile-header {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background-color: var(--sidebar-bg);
    border-bottom: 1px solid var(--border-color);
    align-items: center;
    padding: 0 1rem;
    z-index: 1000;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.mobile-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    transition: color 0.3s ease;
}

.menu-toggle {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-right: 1rem;
    transition: transform 0.2s ease;
}

.menu-toggle:hover {
    transform: scale(1.1);
}

.hamburger {
    display: block;
    width: 24px;
    height: 2px;
    background-color: var(--text-primary);
    transition: all 0.3s ease;
}

.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    width: var(--sidebar-width);
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    z-index: 100;
    overflow: hidden;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.sidebar-header {
    padding: 1.5rem 1.25rem;
    border-bottom: 1px solid var(--border-light);
    transition: border-color 0.3s ease;
}

.sidebar-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 0.25rem;
    transition: color 0.3s ease;
}

.sidebar-subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    display: grid;
    grid-template-columns: 1fr 200px;
    grid-template-rows: auto 1fr;
    gap: 0;
}

/* ===================================
   Search Component
   =================================== */

.search-container {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--border-light);
    position: relative;
    transition: border-color 0.3s ease;
}

.search-input {
    width: 100%;
    padding: 0.625rem 0.875rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.875rem;
    font-family: var(--font-sans);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-input::placeholder {
    color: var(--text-tertiary);
}

.search-results {
    position: absolute;
    top: calc(100% - 0.5rem);
    left: 1.25rem;
    right: 1.25rem;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: var(--shadow-lg);
    max-height: 400px;
    overflow-y: auto;
    z-index: 200;
    animation: slideDown 0.2s ease;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-results.hidden {
    display: none;
}

.search-result-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-light);
    cursor: pointer;
    transition: all 0.2s ease;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item:hover {
    background-color: var(--bg-hover);
    transform: translateX(4px);
}

.search-result-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.search-result-section {
    font-size: 0.75rem;
    color: var(--primary-blue);
    margin-bottom: 0.375rem;
    transition: color 0.3s ease;
}

.search-result-snippet {
    font-size: 0.8125rem;
    color: var(--text-secondary);
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    transition: color 0.3s ease;
}

.search-no-results {
    padding: 1rem;
    text-align: center;
    color: var(--text-tertiary);
    font-size: 0.875rem;
}

/* ===================================
   Navigation Tree
   =================================== */

.nav-tree {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem 0;
}

.nav-section {
    margin-bottom: 0.5rem;
}

.nav-section-header {
    padding: 0.625rem 1.25rem;
    font-weight: 600;
    font-size: 0.8125rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.2s ease;
}

.nav-section-header:hover {
    background-color: var(--sidebar-hover);
    color: var(--primary-blue);
}

.nav-section-toggle {
    font-size: 0.75rem;
    transition: transform 0.3s ease;
}

.nav-section.collapsed .nav-section-toggle {
    transform: rotate(-90deg);
}

.nav-section-files {
    list-style: none;
    animation: expandSection 0.3s ease;
}

@keyframes expandSection {
    from {
        opacity: 0;
        max-height: 0;
    }
    to {
        opacity: 1;
        max-height: 1000px;
    }
}

.nav-section.collapsed .nav-section-files {
    display: none;
}

.nav-file-item {
    padding: 0.5rem 1.25rem 0.5rem 2rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.nav-file-item:hover {
    background-color: var(--sidebar-hover);
    color: var(--text-primary);
    transform: translateX(4px);
}

.nav-file-item.active {
    background-color: var(--sidebar-active);
    color: var(--primary-blue);
    border-left-color: var(--primary-blue);
    font-weight: 500;
}

.loading {
    padding: 2rem 1.25rem;
    text-align: center;
    color: var(--text-tertiary);
    font-size: 0.875rem;
}

/* ===================================
   Sidebar Footer
   =================================== */

.sidebar-footer {
    padding: 1rem 1.25rem;
    border-top: 1px solid var(--border-light);
    background-color: var(--bg-secondary);
    transition: all 0.3s ease;
}

.version {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    text-align: center;
    transition: color 0.3s ease;
}

/* ===================================
   Breadcrumb Navigation
   =================================== */

.breadcrumb {
    grid-column: 1 / -1;
    padding: 1.5rem 2rem 0.75rem;
    font-size: 0.875rem;
    color: var(--text-tertiary);
    border-bottom: 1px solid var(--border-light);
    background-color: var(--bg-primary);
    transition: all 0.3s ease;
}

.breadcrumb-item {
    display: inline;
}

.breadcrumb-item:not(:last-child)::after {
    content: " / ";
    margin: 0 0.5rem;
    color: var(--text-tertiary);
}

.breadcrumb-link {
    color: var(--primary-blue);
    text-decoration: none;
    transition: all 0.2s ease;
}

.breadcrumb-link:hover {
    color: var(--primary-blue-hover);
    text-decoration: underline;
}

/* ===================================
   Main Content Area
   =================================== */

.content {
    padding: 2rem;
    max-width: 900px;
    grid-column: 1;
    animation: fadeIn 0.4s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.welcome {
    padding: 3rem 0;
}

.welcome h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-blue);
    transition: color 0.3s ease;
}

.welcome p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

/* ===================================
   Typography Styles
   =================================== */

.content h1 {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--primary-blue);
    line-height: 1.2;
    transition: color 0.3s ease;
}

.content h2 {
    font-size: 1.875rem;
    font-weight: 600;
    margin-top: 2.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
    line-height: 1.3;
    transition: color 0.3s ease;
}

.content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 0.875rem;
    color: var(--text-primary);
    line-height: 1.4;
    transition: color 0.3s ease;
}

.content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
    transition: color 0.3s ease;
}

.content h5 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-top: 1.25rem;
    margin-bottom: 0.625rem;
    color: var(--text-primary);
    transition: color 0.3s ease;
}

.content h6 {
    font-size: 1rem;
    font-weight: 600;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.content p {
    margin-bottom: 1.25rem;
    line-height: 1.7;
    transition: color 0.3s ease;
}

.content a {
    color: var(--primary-blue);
    text-decoration: none;
    transition: all 0.2s ease;
    border-bottom: 1px solid transparent;
}

.content a:hover {
    color: var(--primary-blue-hover);
    border-bottom-color: var(--primary-blue-hover);
}

.content ul,
.content ol {
    margin-bottom: 1.25rem;
    padding-left: 2rem;
}

.content li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
    transition: color 0.3s ease;
}

.content blockquote {
    margin: 1.5rem 0;
    padding: 1rem 1.5rem;
    border-left: 4px solid var(--primary-blue);
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    font-style: italic;
    transition: all 0.3s ease;
}

.content hr {
    margin: 2rem 0;
    border: none;
    border-top: 1px solid var(--border-color);
    transition: border-color 0.3s ease;
}

/* ===================================
   Code Blocks and Inline Code
   =================================== */

.content code {
    font-family: var(--font-mono);
    font-size: 0.875em;
    background-color: var(--bg-code);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    border: 1px solid var(--border-light);
    color: var(--primary-blue);
    transition: all 0.3s ease;
}

.content pre {
    margin: 1.5rem 0;
    padding: 1.25rem;
    background-color: var(--bg-code);
    border: 1px solid var(--border-light);
    border-radius: 6px;
    overflow-x: auto;
    line-height: 1.5;
    transition: all 0.3s ease;
}

.content pre code {
    background: none;
    border: none;
    padding: 0;
    font-size: 0.875rem;
    color: var(--text-primary);
}

/* ===================================
   Tables
   =================================== */

.content table {
    width: 100%;
    margin: 1.5rem 0;
    border-collapse: collapse;
    font-size: 0.9375rem;
}

.content th {
    background-color: var(--bg-secondary);
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 600;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.content td {
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.content tr:nth-child(even) {
    background-color: var(--bg-secondary);
}

.content tr {
    transition: background-color 0.2s ease;
}

.content tr:hover {
    background-color: var(--bg-hover);
}

/* ===================================
   Table of Contents Sidebar
   =================================== */

.toc-sidebar {
    grid-column: 2;
    padding: 2rem 1rem 2rem 0;
    position: sticky;
    top: 2rem;
    align-self: start;
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
}

.toc-header {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--text-tertiary);
    margin-bottom: 0.75rem;
    transition: color 0.3s ease;
}

.toc {
    font-size: 0.8125rem;
}

.toc ul {
    list-style: none;
    padding-left: 0;
}

.toc li {
    margin-bottom: 0.5rem;
}

.toc a {
    color: var(--text-secondary);
    text-decoration: none;
    display: block;
    padding: 0.25rem 0;
    border-left: 2px solid transparent;
    padding-left: 0.75rem;
    transition: all 0.2s ease;
}

.toc a:hover {
    color: var(--primary-blue);
    border-left-color: var(--border-color);
    transform: translateX(4px);
}

.toc a.active {
    color: var(--primary-blue);
    border-left-color: var(--primary-blue);
    font-weight: 500;
}

.toc ul ul {
    padding-left: 1rem;
    margin-top: 0.25rem;
}

/* ===================================
   Overlay for Mobile Menu
   =================================== */

.overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 90;
    animation: fadeIn 0.3s ease;
}

.overlay.active {
    display: block;
}

/* ===================================
   Responsive Design
   =================================== */

/* Tablet */
@media (max-width: 1024px) {
    .toc-sidebar {
        display: none;
    }
    
    .main-content {
        grid-template-columns: 1fr;
    }
    
    .content {
        grid-column: 1;
    }
    
    .theme-toggle {
        bottom: 1.5rem;
        right: 1.5rem;
    }
}

/* Mobile */
@media (max-width: 768px) {
    .mobile-header {
        display: flex;
    }
    
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease, background-color 0.3s ease;
        top: var(--header-height);
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        margin-top: var(--header-height);
    }
    
    .content {
        padding: 1.5rem 1rem;
    }
    
    .breadcrumb {
        padding: 1rem;
        font-size: 0.8125rem;
    }
    
    .welcome h1 {
        font-size: 2rem;
    }
    
    .content h1 {
        font-size: 1.875rem;
    }
    
    .content h2 {
        font-size: 1.5rem;
    }
    
    .content h3 {
        font-size: 1.25rem;
    }
    
    .theme-toggle {
        bottom: 1rem;
        right: 1rem;
        width: 44px;
        height: 44px;
    }
}

/* ===================================
   Utility Classes
   =================================== */

.hidden {
    display: none !important;
}

/* ===================================
   Scrollbar Styling
   =================================== */

.nav-tree::-webkit-scrollbar,
.search-results::-webkit-scrollbar,
.toc-sidebar::-webkit-scrollbar {
    width: 6px;
}

.nav-tree::-webkit-scrollbar-track,
.search-results::-webkit-scrollbar-track,
.toc-sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.nav-tree::-webkit-scrollbar-thumb,
.search-results::-webkit-scrollbar-thumb,
.toc-sidebar::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
    transition: background 0.2s ease;
}

.nav-tree::-webkit-scrollbar-thumb:hover,
.search-results::-webkit-scrollbar-thumb:hover,
.toc-sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--primary-blue);
}
