---
title: "Real-Time Updates"
section: "Controller"
order: 5
tags: ["controller", "websocket", "real-time", "pubsub"]
last_updated: "2025-11-08"
---

# Real-Time Updates

The Controller provides real-time updates to web interface clients and desktop applications through WebSocket connections and Redis pub/sub messaging. This enables instant notification of service discoveries, status changes, alerts, and user actions without polling.

## Architecture

### WebSocket Server

**FastAPI WebSocket Support**: Built-in WebSocket support in FastAPI handles bidirectional communication with clients.

**Connection Management**: Controller maintains persistent WebSocket connections with authenticated clients, tracking connection state and user context.

**Scalability**: Redis pub/sub enables WebSocket connections across multiple Controller instances to receive the same events.

**Heartbeat**: Regular ping/pong messages keep connections alive and detect disconnections.

### Redis Pub/Sub

**Message Broker**: Redis pub/sub distributes events across Controller instances in multi-instance deployments.

**Channels**: Separate channels for different event types (service events, alert events, user events).

**Fan-Out**: Single event published to Redis reaches all subscribed Controller instances, which forward to relevant WebSocket clients.

**Persistence**: Redis persistence ensures messages aren't lost during Redis restarts.

## WebSocket Protocol

### Connection Establishment

**Endpoint**: `wss://controller.example.com/ws`

**Authentication**: Client includes JWT token in query parameter or initial message.

**Handshake**:
1. Client connects to WebSocket endpoint
2. Client sends authentication message with token
3. Controller validates token and loads user context
4. Controller sends connection acknowledgment
5. Controller subscribes to relevant Redis channels
6. Bidirectional communication begins

**Example Authentication Message**:
```json
{
  "type": "auth",
  "token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Example Acknowledgment**:
```json
{
  "type": "auth_success",
  "user_id": "usr_abc123",
  "session_id": "sess_xyz789",
  "server_time": "2025-11-08T10:30:00Z"
}
```

### Message Format

All messages follow consistent JSON format:

```json
{
  "type": "event_type",
  "timestamp": "2025-11-08T10:30:00Z",
  "data": {
    // Event-specific data
  }
}
```

### Event Types

#### Service Events

**service.discovered**: New service discovered by agent.

```json
{
  "type": "service.discovered",
  "timestamp": "2025-11-08T10:30:15Z",
  "data": {
    "service_id": "svc_new123",
    "name": "redis-cache",
    "type": "cache",
    "vm_id": "vm_abc123",
    "vm_name": "vm-prod-cache",
    "port": 6379,
    "status": "healthy"
  }
}
```

**service.removed**: Service no longer detected.

```json
{
  "type": "service.removed",
  "timestamp": "2025-11-08T10:31:00Z",
  "data": {
    "service_id": "svc_old456",
    "name": "old-service",
    "vm_id": "vm_abc123"
  }
}
```

**service.status_changed**: Service health status changed.

```json
{
  "type": "service.status_changed",
  "timestamp": "2025-11-08T10:32:00Z",
  "data": {
    "service_id": "svc_def456",
    "name": "postgres-main",
    "old_status": "healthy",
    "new_status": "warning",
    "health_score": 75,
    "reason": "High CPU usage"
  }
}
```

**service.metrics_updated**: Service metrics updated (throttled to avoid flooding).

```json
{
  "type": "service.metrics_updated",
  "timestamp": "2025-11-08T10:33:00Z",
  "data": {
    "service_id": "svc_def456",
    "metrics": {
      "cpu_usage": 85.3,
      "memory_usage": 2048,
      "connections": 47,
      "requests_per_sec": 125
    }
  }
}
```

#### VM Events

**vm.connected**: VM agent connected to Controller.

```json
{
  "type": "vm.connected",
  "timestamp": "2025-11-08T10:34:00Z",
  "data": {
    "vm_id": "vm_abc123",
    "name": "vm-prod-01",
    "agent_version": "r.1.5-454",
    "ip_address": "*********"
  }
}
```

**vm.disconnected**: VM agent disconnected.

```json
{
  "type": "vm.disconnected",
  "timestamp": "2025-11-08T10:35:00Z",
  "data": {
    "vm_id": "vm_abc123",
    "name": "vm-prod-01",
    "reason": "heartbeat_timeout"
  }
}
```

**vm.metrics_updated**: VM-level metrics updated.

```json
{
  "type": "vm.metrics_updated",
  "timestamp": "2025-11-08T10:36:00Z",
  "data": {
    "vm_id": "vm_abc123",
    "metrics": {
      "cpu_usage": 45.2,
      "memory_usage": 62.8,
      "disk_usage": 38.5,
      "network_in": 1024000,
      "network_out": 512000
    }
  }
}
```

#### Alert Events

**alert.triggered**: Alert triggered.

```json
{
  "type": "alert.triggered",
  "timestamp": "2025-11-08T10:37:00Z",
  "data": {
    "alert_id": "alert_jkl012",
    "rule_name": "High CPU Usage",
    "severity": "warning",
    "resource_type": "vm",
    "resource_id": "vm_abc123",
    "resource_name": "vm-prod-01",
    "current_value": 85.3,
    "threshold": 80,
    "message": "CPU usage exceeded 80% threshold"
  }
}
```

**alert.resolved**: Alert resolved.

```json
{
  "type": "alert.resolved",
  "timestamp": "2025-11-08T10:38:00Z",
  "data": {
    "alert_id": "alert_jkl012",
    "rule_name": "High CPU Usage",
    "resource_type": "vm",
    "resource_id": "vm_abc123",
    "resolution": "automatic",
    "current_value": 65.2
  }
}
```

#### Connection Events

**connection.established**: User connected to service.

```json
{
  "type": "connection.established",
  "timestamp": "2025-11-08T10:39:00Z",
  "data": {
    "connection_id": "conn_ghi789",
    "user_id": "usr_abc123",
    "user_name": "John Doe",
    "service_id": "svc_def456",
    "service_name": "postgres-main",
    "vm_name": "vm-prod-db"
  }
}
```

**connection.terminated**: Connection ended.

```json
{
  "type": "connection.terminated",
  "timestamp": "2025-11-08T10:40:00Z",
  "data": {
    "connection_id": "conn_ghi789",
    "reason": "user_logout",
    "duration": 3600,
    "bytes_sent": 1024000,
    "bytes_received": 2048000
  }
}
```

#### User Events

**user.login**: User logged in (sent only to that user's connections).

```json
{
  "type": "user.login",
  "timestamp": "2025-11-08T10:41:00Z",
  "data": {
    "user_id": "usr_abc123",
    "session_id": "sess_xyz789",
    "ip_address": "************",
    "device": "Chrome on Windows"
  }
}
```

**user.logout**: User logged out.

```json
{
  "type": "user.logout",
  "timestamp": "2025-11-08T10:42:00Z",
  "data": {
    "user_id": "usr_abc123",
    "session_id": "sess_xyz789"
  }
}
```

**user.permission_changed**: User permissions changed (triggers re-evaluation).

```json
{
  "type": "user.permission_changed",
  "timestamp": "2025-11-08T10:43:00Z",
  "data": {
    "user_id": "usr_abc123",
    "changed_by": "usr_admin456",
    "changes": ["role_added:operator", "permission_granted:vm:configure"]
  }
}
```

### Client Commands

Clients can send commands to Controller:

**subscribe**: Subscribe to specific events.

```json
{
  "type": "subscribe",
  "channels": ["service.discovered", "alert.triggered"],
  "filters": {
    "vm_id": "vm_abc123"
  }
}
```

**unsubscribe**: Unsubscribe from events.

```json
{
  "type": "unsubscribe",
  "channels": ["service.discovered"]
}
```

**ping**: Heartbeat ping.

```json
{
  "type": "ping"
}
```

**Response**:
```json
{
  "type": "pong",
  "timestamp": "2025-11-08T10:44:00Z"
}
```

## Event Filtering

### Permission-Based Filtering

Controller filters events based on user permissions. Users only receive events for resources they have permission to view.

**Example**: User with permission to view only development VMs receives events only for those VMs, not production VMs.

### Subscription Filtering

Clients can subscribe to specific event types and apply filters to reduce noise.

**Filter Options**:
- `vm_id`: Events for specific VM
- `service_id`: Events for specific service
- `service_type`: Events for services of specific type
- `severity`: Alerts of specific severity
- `environment`: Events for specific environment

**Example Subscription**:
```json
{
  "type": "subscribe",
  "channels": ["service.status_changed", "alert.triggered"],
  "filters": {
    "environment": "production",
    "severity": ["warning", "critical"]
  }
}
```

### Throttling

High-frequency events (metrics updates) are throttled to prevent overwhelming clients:

**Metrics Updates**: Maximum 1 update per service per 5 seconds
**Batch Updates**: Multiple metric updates batched into single message
**Sampling**: For very high-frequency data, sample subset of data points

## Connection Management

### Connection Lifecycle

1. **Connect**: Client establishes WebSocket connection
2. **Authenticate**: Client sends auth message with token
3. **Subscribe**: Client subscribes to desired event channels
4. **Receive Events**: Client receives real-time events
5. **Heartbeat**: Regular ping/pong to keep connection alive
6. **Disconnect**: Client closes connection or connection times out

### Reconnection

**Automatic Reconnection**: Clients should implement automatic reconnection with exponential backoff.

**Event Replay**: After reconnection, client can request missed events by providing last received event timestamp.

**Example Replay Request**:
```json
{
  "type": "replay_events",
  "since": "2025-11-08T10:40:00Z"
}
```

### Connection Limits

**Per-User Limit**: Maximum 10 concurrent WebSocket connections per user.

**Global Limit**: Controller instance can handle 10,000+ concurrent connections.

**Resource Limits**: Connections consuming excessive resources (bandwidth, CPU) may be throttled or terminated.

## Implementation Details

### Controller-Side Implementation

**WebSocket Handler**:
```python
from fastapi import WebSocket, WebSocketDisconnect
from typing import Dict, Set
import asyncio
import json

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        self.user_connections: Dict[str, Set[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        if user_id not in self.user_connections:
            self.user_connections[user_id] = set()
        self.user_connections[user_id].add(websocket)
    
    def disconnect(self, websocket: WebSocket, user_id: str):
        self.user_connections[user_id].discard(websocket)
    
    async def send_to_user(self, user_id: str, message: dict):
        if user_id in self.user_connections:
            for connection in self.user_connections[user_id]:
                await connection.send_json(message)
    
    async def broadcast(self, message: dict, filter_func=None):
        for user_id, connections in self.user_connections.items():
            if filter_func and not filter_func(user_id):
                continue
            for connection in connections:
                await connection.send_json(message)

manager = ConnectionManager()

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    user_id = None
    
    try:
        # Authenticate
        auth_msg = await websocket.receive_json()
        user_id = await authenticate_websocket(auth_msg["token"])
        await manager.connect(websocket, user_id)
        
        # Send acknowledgment
        await websocket.send_json({
            "type": "auth_success",
            "user_id": user_id
        })
        
        # Handle messages
        while True:
            message = await websocket.receive_json()
            await handle_client_message(websocket, user_id, message)
    
    except WebSocketDisconnect:
        if user_id:
            manager.disconnect(websocket, user_id)
```

**Redis Pub/Sub Integration**:
```python
import aioredis

async def redis_subscriber():
    redis = await aioredis.create_redis_pool('redis://localhost')
    channels = await redis.subscribe('service_events', 'alert_events')
    
    while True:
        message = await channels[0].get()
        if message:
            event = json.loads(message.decode())
            await manager.broadcast(event, filter_by_permissions)
```

### Client-Side Implementation

**JavaScript/TypeScript Client**:
```typescript
class WebSocketClient {
    private ws: WebSocket | null = null;
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 10;
    
    connect(token: string) {
        this.ws = new WebSocket(`wss://controller.example.com/ws`);
        
        this.ws.onopen = () => {
            console.log('WebSocket connected');
            this.authenticate(token);
            this.reconnectAttempts = 0;
        };
        
        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
        };
        
        this.ws.onclose = () => {
            console.log('WebSocket disconnected');
            this.reconnect(token);
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
    }
    
    authenticate(token: string) {
        this.send({ type: 'auth', token });
    }
    
    subscribe(channels: string[], filters?: any) {
        this.send({ type: 'subscribe', channels, filters });
    }
    
    send(message: any) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
    }
    
    handleMessage(message: any) {
        switch (message.type) {
            case 'service.discovered':
                // Update UI with new service
                break;
            case 'alert.triggered':
                // Show alert notification
                break;
            // Handle other event types
        }
    }
    
    reconnect(token: string) {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
            setTimeout(() => {
                this.reconnectAttempts++;
                this.connect(token);
            }, delay);
        }
    }
}
```

## Performance Considerations

**Message Batching**: Multiple events batched into single WebSocket message to reduce overhead.

**Compression**: WebSocket compression (permessage-deflate) reduces bandwidth usage.

**Selective Updates**: Only send updates for resources user has permission to view.

**Throttling**: Rate limit high-frequency events to prevent overwhelming clients.

**Connection Pooling**: Redis connection pooling for efficient pub/sub handling.

## Security

**Authentication**: All WebSocket connections require valid JWT token.

**Authorization**: Events filtered based on user permissions.

**Rate Limiting**: Limit message rate per connection to prevent abuse.

**Input Validation**: All client messages validated before processing.

**Connection Limits**: Enforce per-user and global connection limits.

## Monitoring

**Connection Metrics**: Track active connections, connection rate, disconnection rate.

**Message Metrics**: Track messages sent/received, message latency, error rate.

**Redis Metrics**: Monitor Redis pub/sub performance, channel backlog.

**Alerts**: Alert on connection failures, high latency, or unusual patterns.
