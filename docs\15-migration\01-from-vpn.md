---
title: "Migrating from Traditional VPN"
section: "Migration Guides"
order: 1
tags: ["migration", "vpn", "transition", "guide"]
last_updated: "2025-11-09"
---

# Migrating from Traditional VPN to VM Gateway

## Overview

This comprehensive guide walks you through migrating from a traditional VPN solution (OpenVPN, WireGuard, Cisco AnyConnect, etc.) to VM Gateway. The migration can be done gradually, allowing both systems to coexist during the transition period.

**Migration Benefits**:
- **Granular access control**: Service-level instead of network-level
- **Better security**: Zero-trust model, no broad network access
- **Improved user experience**: No VPN client for web services
- **Automatic service discovery**: No manual configuration
- **Comprehensive audit logs**: Know exactly who accessed what
- **Better performance**: Direct connections, no routing overhead

**Timeline**: 2-8 weeks depending on complexity

**Prerequisites**:
- VM Gateway installed and configured
- Inventory of current VPN users and their access needs
- List of services currently accessed via VPN
- Network diagram showing current architecture

---

## Table of Contents

1. [Migration Strategy](#migration-strategy)
2. [Phase 1: Assessment](#phase-1-assessment)
3. [Phase 2: Parallel Deployment](#phase-2-parallel-deployment)
4. [Phase 3: User Migration](#phase-3-user-migration)
5. [Phase 4: Service Migration](#phase-4-service-migration)
6. [Phase 5: VPN Decommission](#phase-5-vpn-decommission)
7. [Rollback Plan](#rollback-plan)
8. [Common Challenges](#common-challenges)

---

## Migration Strategy

### Recommended Approach: Gradual Migration

**Don't**: Turn off VPN and force everyone to VM Gateway immediately

**Do**: Run both systems in parallel, migrate gradually

**Migration Phases**:
1. **Assessment** (1 week): Understand current state
2. **Parallel Deployment** (1-2 weeks): Deploy VM Gateway alongside VPN
3. **User Migration** (2-4 weeks): Migrate users in waves
4. **Service Migration** (1-2 weeks): Transition services to VM Gateway
5. **VPN Decommission** (1 week): Turn off VPN

### Migration Waves

**Wave 1: Pilot Group** (Week 1-2):
- IT team members
- Early adopters
- Non-critical services
- Goal: Identify issues, gather feedback

**Wave 2: Development Teams** (Week 3-4):
- Developers
- QA engineers
- Development and staging environments
- Goal: Validate functionality, refine processes

**Wave 3: Operations Teams** (Week 5-6):
- SRE/DevOps
- Database administrators
- Production read-only access
- Goal: Ensure operational workflows work

**Wave 4: General Users** (Week 7-8):
- All remaining users
- Production write access
- Goal: Complete migration

---

## Phase 1: Assessment

### Step 1: Inventory Current VPN Users

Create spreadsheet of all VPN users:

| User | Email | Department | Role | Access Level | Services Used |
|------|-------|------------|------|--------------|---------------|
| John Doe | <EMAIL> | Engineering | Developer | Full | Web servers, databases |
| Jane Smith | <EMAIL> | Operations | SRE | Full | All production |
| ... | ... | ... | ... | ... | ... |

**Data Sources**:
- VPN server logs
- Active Directory/LDAP
- User surveys
- IT ticketing system

### Step 2: Inventory Services

List all services accessed via VPN:

| Service | Type | VM/Host | Port | Users | Criticality |
|---------|------|---------|------|-------|-------------|
| PostgreSQL Prod | Database | prod-db-01 | 5432 | DBAs, Developers | Critical |
| Jenkins | CI/CD | jenkins-01 | 8080 | Developers | High |
| Grafana | Monitoring | monitoring-01 | 3000 | Everyone | Medium |
| ... | ... | ... | ... | ... | ... |

**Data Sources**:
- Network flow logs
- Firewall rules
- Service inventory
- User interviews

### Step 3: Map Users to Services

Create access matrix:

| User | PostgreSQL Prod | Jenkins | Grafana | SSH Prod | ... |
|------|----------------|---------|---------|----------|-----|
| John Doe | Read/Write | Read/Write | Read | No | ... |
| Jane Smith | Read | Read | Read/Write | Yes | ... |
| ... | ... | ... | ... | ... | ... |

This becomes your RBAC configuration in VM Gateway.

### Step 4: Identify Dependencies

**Technical Dependencies**:
- Services that require VPN for discovery
- Legacy applications that only work with VPN
- Third-party integrations expecting VPN

**Process Dependencies**:
- Runbooks mentioning VPN
- Onboarding documentation
- Incident response procedures

**Training Dependencies**:
- Users unfamiliar with new system
- Documentation needs
- Support requirements

### Step 5: Create Migration Plan

Document:
- **Timeline**: Detailed schedule with milestones
- **Resources**: People, time, budget
- **Risks**: Potential issues and mitigation
- **Success Criteria**: How to measure success
- **Rollback Plan**: How to revert if needed

---

## Phase 2: Parallel Deployment

### Step 1: Deploy VM Gateway

Follow [Getting Started Guide](/docs/01-overview/05-getting-started.md) to deploy VM Gateway.

**Key Decisions**:
- **Deployment Model**: Single server, HA, distributed
- **Authentication**: Local, SSO, LDAP
- **Network**: Same network as VPN or separate

### Step 2: Install Agents

Install agents on all VMs currently accessed via VPN:

```bash
# Create list of VMs
cat > vms.txt <<EOF
prod-db-01
prod-web-01
prod-web-02
jenkins-01
monitoring-01
EOF

# Install agent on each VM
while read vm; do
  echo "Installing agent on $vm..."
  ssh $vm "curl -O https://vmgateway.example.com/downloads/install-agent.sh && \
           sudo bash install-agent.sh --controller https://vmgateway.example.com --token $TOKEN"
done < vms.txt
```

### Step 3: Verify Service Discovery

Check that all services are discovered:

```bash
# List all discovered services
curl -H "Authorization: Bearer $TOKEN" \
  https://vmgateway.example.com/api/v1/services | jq '.[] | {name, type, vm, port}'

# Compare with VPN service inventory
# Investigate any missing services
```

### Step 4: Configure Access Control

Create roles matching VPN access levels:

**Example: Developer Role**
```yaml
name: "Developer"
description: "Access to development and staging services"
permissions:
  - resource: "service:env:development"
    actions: ["view", "connect"]
  - resource: "service:env:staging"
    actions: ["view", "connect"]
  - resource: "service:type:database:env:development"
    actions: ["view", "connect", "query"]
```

**Example: DBA Role**
```yaml
name: "DBA"
description: "Database administrator access"
permissions:
  - resource: "service:type:database"
    actions: ["view", "connect", "query", "admin"]
  - resource: "service:type:database:env:production"
    actions: ["view", "connect", "query"]  # No admin in prod
```

### Step 5: Create Users

Import users from existing system:

**From LDAP/AD**:
```bash
# Enable LDAP sync
python -m vm_gateway.cli configure-ldap \
  --server ldap://ldap.example.com \
  --base-dn "dc=example,dc=com" \
  --bind-dn "cn=admin,dc=example,dc=com" \
  --bind-password "password"

# Sync users
python -m vm_gateway.cli sync-ldap-users
```

**From CSV**:
```bash
# Create CSV file
cat > users.csv <<EOF
email,first_name,last_name,role
<EMAIL>,John,Doe,developer
<EMAIL>,Jane,Smith,dba
EOF

# Import users
python -m vm_gateway.cli import-users --file users.csv
```

### Step 6: Test with Pilot Group

Select 5-10 pilot users:

1. **Grant access** to VM Gateway
2. **Provide training** (30-minute session)
3. **Test common workflows**:
   - Accessing web services
   - Connecting to databases
   - SSH access
4. **Gather feedback**
5. **Fix issues** before wider rollout

---

## Phase 3: User Migration

### Step 1: Prepare Users

**Communication**:
- Send email 2 weeks before migration
- Explain benefits of new system
- Provide training resources
- Set expectations

**Training Materials**:
- Quick start guide
- Video tutorials
- FAQ document
- Support contact info

**Example Email**:
```
Subject: Migrating to VM Gateway - Action Required

Hi Team,

We're upgrading our remote access system from VPN to VM Gateway, a modern platform that provides:
- Easier access to services (no VPN client for web services!)
- Better security with granular access control
- Automatic service discovery

Your migration is scheduled for [DATE].

Action Required:
1. Watch this 5-minute intro video: [LINK]
2. Read the quick start guide: [LINK]
3. Attend optional training session: [DATE/TIME]

On [DATE], you'll receive your VM Gateway credentials and can start using the new system. The VPN will remain available during the transition.

Questions? Contact [SUPPORT]

Thanks,
IT Team
```

### Step 2: Migrate Wave 1 (Pilot)

**Week 1-2: IT Team & Early Adopters**

1. **Create accounts** in VM Gateway
2. **Assign roles** based on access matrix
3. **Send credentials** and onboarding email
4. **Provide hands-on support**
5. **Monitor usage** and gather feedback
6. **Fix issues** identified

**Success Criteria**:
- All pilot users can access their services
- No critical issues reported
- Positive feedback from users

### Step 3: Migrate Wave 2 (Development)

**Week 3-4: Developers & QA**

1. **Create accounts** for all developers
2. **Assign appropriate roles**
3. **Send onboarding email** with lessons learned from pilot
4. **Host training session** (optional, recorded)
5. **Provide support** during first week
6. **Monitor metrics**:
   - Login success rate
   - Connection success rate
   - Support tickets
   - User satisfaction

**Common Issues**:
- Users forgetting to disconnect from VPN
- Confusion about which system to use
- Missing services (not discovered)
- Permission issues

### Step 4: Migrate Wave 3 (Operations)

**Week 5-6: SRE, DevOps, DBAs**

1. **Create accounts** for operations teams
2. **Assign elevated roles** as needed
3. **Test critical workflows**:
   - Incident response
   - Database maintenance
   - Deployment procedures
4. **Update runbooks** to use VM Gateway
5. **Provide dedicated support**

**Critical**: Ensure operations teams are comfortable before migrating general users.

### Step 5: Migrate Wave 4 (Everyone Else)

**Week 7-8: All Remaining Users**

1. **Create accounts** for all remaining users
2. **Send final migration notice**
3. **Provide self-service resources**
4. **Monitor support load**
5. **Celebrate completion!**

---

## Phase 4: Service Migration

### Step 1: Update Service Configurations

Some services may need configuration changes:

**Example: PostgreSQL**

Old (VPN):
```
# pg_hba.conf
host all all 10.0.0.0/8 md5  # VPN network
```

New (VM Gateway):
```
# pg_hba.conf
host all all 10.0.0.0/8 md5  # VPN network (keep during transition)
host all all **********/16 md5  # VM Gateway agent network
```

### Step 2: Update Firewall Rules

Add rules for VM Gateway while keeping VPN rules:

```bash
# Allow VM Gateway controller to agent
iptables -A INPUT -s *********** -p tcp --dport 5432 -j ACCEPT

# Keep VPN rule during transition
iptables -A INPUT -s 10.0.0.0/8 -p tcp --dport 5432 -j ACCEPT
```

### Step 3: Update Documentation

Update all documentation mentioning VPN:

**Before**:
```
To access the database:
1. Connect to VPN
2. Use connection string: postgresql://prod-db-01:5432/mydb
```

**After**:
```
To access the database:
1. Log in to VM Gateway: https://vmgateway.example.com
2. Find "PostgreSQL Production" in service catalog
3. Click "Connect" - local port forwarding established
4. Use connection string: postgresql://localhost:LOCAL_PORT/mydb

OR (for web services):
1. Log in to VM Gateway
2. Click "Open in Browser" - no client needed!
```

### Step 4: Update Automation

Update scripts and automation:

**Before**:
```bash
#!/bin/bash
# Connect to VPN first
openvpn --config vpn.conf &
sleep 10

# Run database backup
pg_dump -h prod-db-01 -U backup mydb > backup.sql
```

**After**:
```bash
#!/bin/bash
# Use VM Gateway API to establish connection
vm-gateway connect prod-db-01-postgres --local-port 5432 &
sleep 5

# Run database backup
pg_dump -h localhost -p 5432 -U backup mydb > backup.sql
```

---

## Phase 5: VPN Decommission

### Step 1: Announce VPN Sunset

**2 weeks before shutdown**:
```
Subject: VPN Shutdown - Final Notice

The VPN will be shut down on [DATE].

All users should now be using VM Gateway.

If you still need VPN access, contact IT immediately.

Final VPN shutdown: [DATE] at [TIME]
```

### Step 2: Monitor VPN Usage

Check who's still using VPN:

```bash
# Check VPN server logs
grep "Connection Initiated" /var/log/openvpn/openvpn.log | \
  awk '{print $NF}' | sort | uniq -c | sort -rn

# Contact users still using VPN
```

### Step 3: Disable VPN Access

**1 week before shutdown**:
- Disable new VPN connections
- Allow existing connections to complete
- Send final reminder

### Step 4: Shutdown VPN

**On shutdown date**:
1. **Stop VPN server**:
   ```bash
   systemctl stop openvpn
   systemctl disable openvpn
   ```

2. **Remove firewall rules**:
   ```bash
   iptables -D INPUT -s 10.0.0.0/8 -j ACCEPT
   ```

3. **Update service configurations**:
   ```
   # Remove VPN network from pg_hba.conf
   # host all all 10.0.0.0/8 md5  # REMOVED
   ```

4. **Archive VPN configuration**:
   ```bash
   tar -czf vpn-config-backup-$(date +%Y%m%d).tar.gz /etc/openvpn
   mv vpn-config-backup-*.tar.gz /backups/
   ```

5. **Send completion announcement**:
   ```
   Subject: VPN Successfully Decommissioned
   
   The VPN has been successfully shut down.
   
   All users are now using VM Gateway.
   
   Thank you for your cooperation during the migration!
   ```

---

## Rollback Plan

### When to Rollback

Rollback if:
- Critical services inaccessible via VM Gateway
- Major security issue discovered
- Widespread user complaints
- Business operations significantly impacted

### Rollback Procedure

1. **Announce rollback**:
   ```
   Subject: Temporary Rollback to VPN
   
   We're temporarily rolling back to VPN due to [REASON].
   
   Please reconnect to VPN for service access.
   
   We'll communicate next steps soon.
   ```

2. **Re-enable VPN**:
   ```bash
   systemctl start openvpn
   systemctl enable openvpn
   ```

3. **Restore firewall rules**:
   ```bash
   iptables -A INPUT -s 10.0.0.0/8 -j ACCEPT
   ```

4. **Investigate issues**:
   - Identify root cause
   - Develop fix
   - Test thoroughly

5. **Plan re-migration**:
   - Address issues
   - Communicate new timeline
   - Resume migration when ready

---

## Common Challenges

### Challenge: Users Forget to Use VM Gateway

**Symptoms**:
- Users still trying to connect to VPN
- Support tickets about VPN not working

**Solutions**:
- Send regular reminders
- Update documentation prominently
- Disable VPN for migrated users
- Provide clear error messages

### Challenge: Service Not Discovered

**Symptoms**:
- Service accessible via VPN but not in VM Gateway catalog

**Solutions**:
- Check agent is running on VM
- Verify service is listening on network interface (not just localhost)
- Check firewall rules
- Add custom classification rule if needed

### Challenge: Performance Issues

**Symptoms**:
- Slower connections via VM Gateway than VPN
- Latency complaints

**Solutions**:
- Check VM Gateway controller resources
- Verify network path (should be direct, not routed)
- Enable connection pooling
- Consider distributed deployment

### Challenge: Automation Breaks

**Symptoms**:
- Scripts fail after VPN shutdown
- CI/CD pipelines broken

**Solutions**:
- Inventory all automation using VPN
- Update scripts to use VM Gateway API
- Test thoroughly before VPN shutdown
- Provide VM Gateway CLI for scripts

---

## Related Documentation

- **[Getting Started Guide](/docs/01-overview/05-getting-started.md)**: Initial VM Gateway setup
- **[Authentication Setup](/docs/06-authentication/01-overview.md)**: Configuring authentication
- **[RBAC Configuration](/docs/06-authentication/07-rbac.md)**: Setting up roles and permissions
- **[Client Installation](/docs/05-client/01-overview.md)**: Desktop client for users
- **[API Reference](/docs/09-api-reference/01-overview.md)**: API for automation
- **[Troubleshooting](/docs/11-troubleshooting/01-common-issues.md)**: Common issues and solutions

## Summary

Migrating from VPN to VM Gateway is a significant undertaking, but the benefits are substantial. By following a gradual, phased approach, you can minimize disruption and ensure a smooth transition.

**Key Success Factors**:
- **Thorough planning**: Understand current state before starting
- **Gradual migration**: Don't rush, migrate in waves
- **Clear communication**: Keep users informed throughout
- **Adequate training**: Ensure users know how to use new system
- **Strong support**: Be available to help users during transition
- **Monitoring**: Track metrics to identify issues early
- **Flexibility**: Be prepared to adjust timeline if needed

**Timeline Summary**:
- Week 1: Assessment
- Week 2-3: Parallel deployment
- Week 4-5: Pilot migration
- Week 6-7: Development teams
- Week 8-9: Operations teams
- Week 10-11: General users
- Week 12: VPN decommission

With proper planning and execution, you'll have a more secure, user-friendly, and manageable access control system!

