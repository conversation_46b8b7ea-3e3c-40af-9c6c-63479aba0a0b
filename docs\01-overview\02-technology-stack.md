---
title: "Technology Stack"
section: "Overview"
order: 2
tags: ["technology", "stack", "architecture", "dependencies"]
last_updated: "2025-11-08"
---

# Technology Stack

## Overview

VM Gateway is built on a modern, production-ready technology stack centered around Python for backend services and React for frontend interfaces. This document provides a comprehensive breakdown of all technologies, libraries, and tools used throughout the platform, along with the rationale for each choice.

## Primary Language: Python

All core backend components are implemented in Python 3.11+ for several compelling reasons:

### Why Python?

- **Consistency**: Single language across all backend components (agent, controller, client backend) simplifies development and maintenance
- **Rich Ecosystem**: Extensive libraries for networking, system monitoring, web frameworks, and data processing
- **Rapid Development**: High productivity for implementing complex features quickly
- **Cross-Platform**: Excellent support for Linux, Windows, and macOS
- **Strong Typing**: Type hints and mypy enable static type checking for better code quality
- **Async Support**: Native async/await for high-performance concurrent operations
- **Community**: Large, active community with extensive documentation and support

### Python Version Requirements

- **Minimum**: Python 3.11
- **Recommended**: Python 3.11 or 3.12
- **Rationale**: 
  - Performance improvements in 3.11+ (10-60% faster than 3.10)
  - Enhanced error messages for better debugging
  - Exception groups for better async error handling
  - Improved type hinting features

## Backend Components

### Web Framework: FastAPI

**Purpose**: REST API server for controller and agent API endpoints

**Version**: 0.104.0+

**Key Features**:
- Async/await support for high concurrency
- Automatic OpenAPI (Swagger) documentation generation
- Built-in request validation using Pydantic
- Dependency injection system
- WebSocket support for real-time communication
- High performance (comparable to Node.js and Go)

**Why FastAPI over alternatives**:
- **vs Flask**: Native async support, automatic API documentation, better performance
- **vs Django**: Lighter weight, better for API-first applications, faster
- **vs Tornado**: More modern, better developer experience, automatic validation

**Usage in VM Gateway**:
- Controller REST API
- Agent API endpoints
- Documentation viewer backend
- WebSocket connections for real-time updates

### Database: PostgreSQL

**Purpose**: Primary data store for controller

**Version**: 14.0+

**Key Features**:
- ACID compliance for data integrity
- Advanced indexing (B-tree, GiST, GIN, BRIN)
- Full-text search capabilities
- JSON/JSONB support for flexible schemas
- Excellent performance at scale
- Robust replication and high availability options

**Why PostgreSQL**:
- **vs MySQL**: Better standards compliance, more advanced features, superior JSON support
- **vs MongoDB**: ACID guarantees, better for relational data, more mature
- **vs SQLite**: Designed for concurrent access, better for multi-user applications

**Data Stored**:
- User accounts and authentication data
- Roles and permissions
- Service catalog
- Metrics and time-series data
- Audit logs
- Configuration settings
- Secrets metadata (encrypted)

**ORM: SQLAlchemy**

**Version**: 2.0+

**Features**:
- Async support (SQLAlchemy 2.0+)
- Powerful query API
- Migration support via Alembic
- Connection pooling
- Multiple database backend support

### Agent Local Storage: SQLite

**Purpose**: Local data storage on each agent

**Version**: 3.35+

**Why SQLite for agents**:
- Embedded database (no separate server process)
- Zero configuration
- Reliable and battle-tested
- Excellent for single-writer scenarios
- Small footprint
- Works offline

**Data Stored**:
- Discovered services cache
- Historical metrics (before sync to controller)
- Agent configuration
- Pending updates queue

### Message Queue: Redis

**Purpose**: Real-time communication, caching, and task queuing

**Version**: 7.0+

**Key Features**:
- In-memory data structure store
- Pub/sub messaging
- Atomic operations
- Persistence options
- Cluster support for high availability
- Lua scripting for complex operations

**Usage in VM Gateway**:
- Session storage (fast access, automatic expiration)
- Real-time event pub/sub (service changes, alerts)
- Rate limiting counters
- Caching frequently accessed data
- Task queue backend for Celery
- WebSocket message broker

### Task Queue: Celery

**Purpose**: Background job processing

**Version**: 5.3+

**Key Features**:
- Distributed task execution
- Task scheduling (periodic tasks)
- Task prioritization
- Retry logic with exponential backoff
- Task result storage
- Monitoring and management tools

**Usage in VM Gateway**:
- Periodic service discovery scans
- Metrics aggregation and rollup
- Report generation
- Email and notification sending
- Batch operations (bulk user imports, etc.)
- Scheduled maintenance tasks

**Broker**: Redis (shared with message queue)

### Async Runtime: asyncio with uvloop

**Purpose**: High-performance async event loop

**asyncio**: Python's built-in async framework

**uvloop**: Drop-in replacement for asyncio event loop

**Version**: uvloop 0.18+

**Performance**: 2-4x faster than standard asyncio

**Usage**:
- All async operations in FastAPI
- WebSocket connections
- Concurrent HTTP requests
- Database connection pooling
- Agent-controller communication

### ASGI Server: Uvicorn

**Purpose**: Production ASGI server for FastAPI applications

**Version**: 0.24+

**Key Features**:
- High performance
- HTTP/1.1 and HTTP/2 support
- WebSocket support
- Graceful shutdown
- Worker process management
- TLS support

**Production Configuration**:
```bash
uvicorn main:app \
  --host 0.0.0.0 \
  --port 8000 \
  --workers 4 \
  --loop uvloop \
  --log-config logging.yaml
```

## Frontend Components

### Framework: React

**Purpose**: User interface for web management console

**Version**: 18.2+

**Key Features**:
- Component-based architecture
- Virtual DOM for performance
- Hooks for state management
- Large ecosystem of libraries
- Excellent developer tools
- Strong TypeScript support

**Why React**:
- **vs Vue**: Larger ecosystem, more enterprise adoption, better TypeScript support
- **vs Angular**: Lighter weight, more flexible, easier learning curve
- **vs Svelte**: More mature, larger community, more third-party libraries

### Language: TypeScript

**Purpose**: Type-safe JavaScript for frontend development

**Version**: 5.0+

**Benefits**:
- Catch errors at compile time
- Better IDE support (autocomplete, refactoring)
- Self-documenting code
- Easier refactoring
- Better collaboration in teams

### UI Components: Tailwind CSS + shadcn/ui

**Tailwind CSS**

**Version**: 3.3+

**Purpose**: Utility-first CSS framework

**Benefits**:
- Rapid UI development
- Consistent design system
- Small production bundle (unused styles purged)
- Responsive design utilities
- Dark mode support

**shadcn/ui**

**Purpose**: High-quality, accessible React components

**Benefits**:
- Built on Radix UI primitives (accessibility)
- Customizable and composable
- Copy-paste components (not a dependency)
- Consistent with Tailwind
- Beautiful default styling

**Component Examples**:
- Data tables with sorting and filtering
- Modal dialogs
- Dropdown menus
- Form inputs with validation
- Toast notifications
- Command palette

### State Management: Zustand

**Purpose**: Lightweight state management

**Version**: 4.4+

**Why Zustand**:
- **vs Redux**: Much simpler API, less boilerplate, smaller bundle
- **vs Context API**: Better performance, easier to use
- **vs Recoil**: More stable, simpler mental model

**Features**:
- Minimal boilerplate
- TypeScript support
- Devtools integration
- Middleware support (persistence, logging)
- No providers needed

**Alternative**: Redux Toolkit (for more complex state requirements)

### Real-Time Updates: WebSocket

**Implementation**: Native WebSocket API + Socket.IO (optional)

**Purpose**: Real-time bidirectional communication

**Use Cases**:
- Live service status updates
- Real-time metrics streaming
- Alert notifications
- User presence indicators
- Live log tailing

**Socket.IO** (optional enhancement):

**Version**: 4.6+

**Benefits over native WebSocket**:
- Automatic reconnection
- Fallback to HTTP long-polling
- Room/namespace support
- Acknowledgments
- Binary data support

### Build Tool: Vite

**Purpose**: Frontend build tool and dev server

**Version**: 5.0+

**Key Features**:
- Lightning-fast hot module replacement (HMR)
- Optimized production builds
- Native ES modules in development
- Plugin ecosystem
- TypeScript support out of the box

**Why Vite over alternatives**:
- **vs Webpack**: Much faster dev server, simpler configuration
- **vs Create React App**: More modern, faster, more flexible
- **vs Parcel**: More control, better plugin ecosystem

### HTTP Client: Axios

**Purpose**: HTTP requests from frontend to backend

**Version**: 1.6+

**Features**:
- Promise-based API
- Request/response interceptors
- Automatic JSON transformation
- Request cancellation
- TypeScript support
- CSRF protection

**Usage**:
- API calls to controller
- File uploads/downloads
- Authentication token management
- Error handling

## Client Application

### Framework: Electron

**Purpose**: Cross-platform desktop application

**Version**: 27.0+

**Why Electron**:
- **vs Tauri**: More mature, larger ecosystem, easier Python integration
- **vs Qt**: Web technologies more familiar to team, faster development
- **vs Native**: Single codebase for all platforms

**Components**:
- **Main Process**: Node.js backend, Python subprocess management
- **Renderer Process**: React frontend (same as web interface)
- **IPC**: Communication between main and renderer

**Alternative**: Tauri (Rust-based, smaller bundle size, but less mature)

### Python Integration

**Approach**: Embedded Python interpreter or subprocess

**Options**:

1. **Subprocess** (recommended for initial version):
   - Spawn Python process for agent functionality
   - IPC via stdin/stdout or local socket
   - Easier to develop and debug

2. **Embedded Python** (future enhancement):
   - Use node-python-bridge or similar
   - Better performance
   - More complex setup

### Frontend: React

Same React stack as web interface for consistency:
- Shared components
- Shared state management
- Shared API client code

## Infrastructure & DevOps

### Containerization: Docker

**Purpose**: Package applications with dependencies

**Version**: 24.0+

**Images**:
- Controller (FastAPI + React build)
- Agent (Python + dependencies)
- PostgreSQL (official image)
- Redis (official image)

**Docker Compose**: For local development and simple deployments

### Orchestration: Kubernetes (Optional)

**Purpose**: Container orchestration for production

**Version**: 1.28+

**Components**:
- Deployments for controller, agents
- StatefulSets for databases
- Services for networking
- Ingress for external access
- ConfigMaps and Secrets for configuration

**Helm Charts**: For easy deployment and upgrades

### Reverse Proxy: Nginx

**Purpose**: TLS termination, load balancing, static file serving

**Version**: 1.24+

**Configuration**:
- TLS 1.3 with strong ciphers
- HTTP/2 support
- WebSocket proxying
- Rate limiting
- Static file caching
- Gzip compression

**Alternative**: Traefik (for dynamic configuration, Kubernetes integration)

### Certificate Management: Certbot / Let's Encrypt

**Purpose**: Automatic TLS certificate provisioning and renewal

**Certbot Version**: 2.7+

**Features**:
- Automatic certificate issuance
- Automatic renewal (90-day certs)
- DNS challenge support for wildcard certs
- Multiple domain support

**Alternative**: cert-manager (for Kubernetes deployments)

### Monitoring: Prometheus + Grafana

**Prometheus**

**Version**: 2.47+

**Purpose**: Metrics collection and storage

**Features**:
- Time-series database
- Powerful query language (PromQL)
- Service discovery
- Alerting rules
- Federation for multi-cluster

**Metrics Exported**:
- Application metrics (request rate, latency, errors)
- System metrics (CPU, memory, disk, network)
- Business metrics (active users, service count)
- Custom metrics

**Grafana**

**Version**: 10.0+

**Purpose**: Metrics visualization and dashboards

**Features**:
- Beautiful, customizable dashboards
- Multiple data source support
- Alerting
- User management
- Dashboard sharing

**Pre-built Dashboards**:
- System overview
- Service health
- User activity
- Resource utilization
- Alert history

## Security Libraries

### TLS: OpenSSL / certifi

**OpenSSL**: System TLS library

**certifi**: Python package for CA certificates

**Version**: certifi 2023.7+

**Usage**:
- HTTPS connections
- mTLS for agent-controller communication
- Certificate validation

### Password Hashing: Argon2

**Library**: argon2-cffi

**Version**: 23.1+

**Why Argon2**:
- Winner of Password Hashing Competition
- Resistant to GPU cracking
- Memory-hard algorithm
- Configurable time and memory cost

**Alternative**: bcrypt (more widely supported, still secure)

**Configuration**:
```python
from argon2 import PasswordHasher

ph = PasswordHasher(
    time_cost=2,        # Number of iterations
    memory_cost=65536,  # Memory usage in KiB
    parallelism=4,      # Number of parallel threads
    hash_len=32,        # Length of hash in bytes
    salt_len=16         # Length of salt in bytes
)
```

### JWT: PyJWT

**Purpose**: JSON Web Tokens for API authentication

**Version**: 2.8+

**Features**:
- Token generation and validation
- Multiple algorithms (RS256, HS256)
- Expiration handling
- Custom claims

**Usage**:
- API authentication tokens
- Session tokens
- Service-to-service authentication

### Encryption: cryptography

**Purpose**: Cryptographic operations

**Version**: 41.0+

**Features**:
- Symmetric encryption (AES-256-GCM)
- Asymmetric encryption (RSA, ECDSA)
- Key derivation (PBKDF2, Scrypt)
- X.509 certificate handling

**Usage**:
- Encrypt sensitive data at rest
- Encrypt secrets in database
- Generate and validate certificates
- Secure token generation

### Secrets Management SDKs

**HashiCorp Vault SDK**

**Library**: hvac

**Version**: 2.0+

**Purpose**: Integration with Vault for secrets storage

**AWS SDK**

**Library**: boto3

**Version**: 1.28+

**Purpose**: Integration with AWS Secrets Manager and Parameter Store

**Azure SDK**

**Library**: azure-identity, azure-keyvault-secrets

**Version**: 1.14+

**Purpose**: Integration with Azure Key Vault

## System Monitoring & Discovery

### Process Monitoring: psutil

**Purpose**: Cross-platform system and process utilities

**Version**: 5.9+

**Features**:
- List running processes
- Process details (PID, user, command, etc.)
- CPU and memory usage per process
- Network connections
- Disk I/O
- System-wide metrics

**Usage in Agent**:
- Discover listening ports
- Map ports to processes
- Collect resource metrics
- Monitor service health

### Network Scanning: socket + scapy (optional)

**socket**: Python built-in for basic port scanning

**scapy** (optional): Advanced packet manipulation

**Version**: scapy 2.5+

**Usage**:
- Port scanning
- Protocol detection
- Banner grabbing
- Network diagnostics

### HTTP Client: httpx

**Purpose**: Modern HTTP client for Python

**Version**: 0.25+

**Features**:
- Async support
- HTTP/2 support
- Connection pooling
- Timeout handling
- Retry logic

**Usage**:
- Service health checks
- HTTP service classification
- API calls between components

## Testing & Quality

### Testing Framework: pytest

**Purpose**: Unit and integration testing

**Version**: 7.4+

**Plugins**:
- pytest-asyncio: Async test support
- pytest-cov: Code coverage
- pytest-mock: Mocking utilities
- pytest-xdist: Parallel test execution

### Code Formatting: Black

**Purpose**: Opinionated code formatter

**Version**: 23.11+

**Configuration**:
```toml
[tool.black]
line-length = 100
target-version = ['py311']
```

### Linting: Ruff

**Purpose**: Fast Python linter

**Version**: 0.1.6+

**Why Ruff**:
- 10-100x faster than Flake8
- Replaces multiple tools (Flake8, isort, pyupgrade)
- Written in Rust
- Compatible with Black

### Type Checking: mypy

**Purpose**: Static type checking

**Version**: 1.7+

**Configuration**:
```toml
[tool.mypy]
python_version = "3.11"
strict = true
warn_return_any = true
warn_unused_configs = true
```

## Development Tools

### Package Management: pip + pip-tools

**pip**: Standard Python package installer

**pip-tools**: Dependency management

**Features**:
- requirements.in for high-level dependencies
- requirements.txt for pinned versions
- Reproducible builds

**Alternative**: Poetry (more features, but more complex)

### Environment Management: venv

**Purpose**: Isolated Python environments

**Built-in**: Python 3.3+

**Usage**:
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

### Version Control: Git

**Purpose**: Source code management

**Version**: 2.40+

**Workflow**: Git Flow or GitHub Flow

**Hooks**: Pre-commit hooks for formatting and linting

### CI/CD: GitHub Actions (or GitLab CI)

**Purpose**: Automated testing and deployment

**Workflows**:
- Run tests on every push
- Build Docker images
- Deploy to staging/production
- Generate documentation
- Security scanning

## Documentation

### API Documentation: OpenAPI / Swagger

**Generated by**: FastAPI (automatic)

**Features**:
- Interactive API explorer
- Request/response schemas
- Authentication testing
- Code generation for clients

### Code Documentation: Sphinx

**Purpose**: Generate documentation from docstrings

**Version**: 7.2+

**Extensions**:
- autodoc: Extract docstrings
- napoleon: Google/NumPy style docstrings
- sphinx-rtd-theme: Read the Docs theme

### Markdown Rendering: Python-Markdown

**Purpose**: Render markdown documentation

**Version**: 3.5+

**Extensions**:
- pymdown-extensions: Enhanced markdown features
- codehilite: Syntax highlighting
- toc: Table of contents generation

## Logging & Observability

### Logging: Python logging + structlog

**Python logging**: Built-in logging framework

**structlog**: Structured logging

**Version**: structlog 23.2+

**Features**:
- JSON-formatted logs
- Contextual information
- Log correlation
- Performance

**Log Aggregation**: Compatible with ELK, Splunk, Datadog, etc.

### Tracing: OpenTelemetry (optional)

**Purpose**: Distributed tracing

**Version**: 1.20+

**Features**:
- Trace requests across services
- Performance profiling
- Dependency mapping

**Backends**: Jaeger, Zipkin, Datadog, New Relic

## Performance Considerations

### Caching Strategy

- **Redis**: Session data, frequently accessed data
- **Application-level**: LRU caches for expensive computations
- **HTTP caching**: ETags, Cache-Control headers
- **Database**: Query result caching, materialized views

### Database Optimization

- **Indexing**: Proper indexes on frequently queried columns
- **Connection pooling**: Reuse database connections
- **Query optimization**: Use EXPLAIN to analyze queries
- **Partitioning**: For large tables (metrics, logs)

### Async Operations

- **Non-blocking I/O**: Use async for all I/O operations
- **Concurrent requests**: Handle multiple requests simultaneously
- **Background tasks**: Offload heavy work to Celery

## Scalability Considerations

### Horizontal Scaling

- **Stateless services**: Controller can scale horizontally
- **Load balancing**: Nginx or cloud load balancer
- **Session storage**: Redis for shared sessions
- **Database**: Read replicas for read-heavy workloads

### Vertical Scaling

- **Resource limits**: Set appropriate CPU/memory limits
- **Worker processes**: Multiple Uvicorn workers
- **Database tuning**: Optimize PostgreSQL configuration

## Technology Selection Principles

Throughout the platform, technology choices follow these principles:

1. **Maturity**: Prefer battle-tested, production-ready technologies
2. **Community**: Strong community support and active development
3. **Performance**: High performance for real-time operations
4. **Developer Experience**: Good documentation, tooling, and ergonomics
5. **Compatibility**: Cross-platform support where needed
6. **Security**: Security-first design and regular updates
7. **Maintainability**: Clear, readable code with good abstractions
8. **Flexibility**: Extensible and customizable for different use cases

## Dependency Management

### Python Dependencies

**requirements.txt Structure**:

```txt
# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
sqlalchemy[asyncio]==2.0.23
asyncpg==0.29.0
alembic==1.13.0
psycopg2-binary==2.9.9

# Redis & Caching
redis[hiredis]==5.0.1
aioredis==2.0.1

# Task Queue
celery[redis]==5.3.4

# Authentication & Security
pyjwt[crypto]==2.8.0
argon2-cffi==23.1.0
cryptography==41.0.7
python-multipart==0.0.6

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# System Monitoring
psutil==5.9.6

# Secrets Management
hvac==2.0.0  # Vault
boto3==1.29.7  # AWS
azure-identity==1.15.0  # Azure
azure-keyvault-secrets==4.7.0

# Logging & Monitoring
structlog==23.2.0
python-json-logger==2.0.7

# Utilities
python-dotenv==1.0.0
pyyaml==6.0.1
click==8.1.7
rich==13.7.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
httpx-mock==0.7.0

# Code Quality
black==23.12.0
ruff==0.1.8
mypy==1.7.1
```

**Development Dependencies**:

```txt
# Development Tools
ipython==8.18.1
ipdb==0.13.13
watchdog==3.0.0

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==2.0.0
sphinx-autodoc-typehints==1.25.2

# Profiling
py-spy==0.3.14
memory-profiler==0.61.0
```

### Frontend Dependencies

**package.json**:

```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.20.0",
    "zustand": "^4.4.7",
    "axios": "^1.6.2",
    "socket.io-client": "^4.6.0",
    "@radix-ui/react-dialog": "^1.0.5",
    "@radix-ui/react-dropdown-menu": "^2.0.6",
    "@radix-ui/react-select": "^2.0.0",
    "tailwindcss": "^3.3.6",
    "clsx": "^2.0.0",
    "date-fns": "^2.30.0",
    "recharts": "^2.10.3",
    "react-hook-form": "^7.49.2",
    "zod": "^3.22.4"
  },
  "devDependencies": {
    "@types/react": "^18.2.43",
    "@types/react-dom": "^18.2.17",
    "@typescript-eslint/eslint-plugin": "^6.13.2",
    "@typescript-eslint/parser": "^6.13.2",
    "@vitejs/plugin-react": "^4.2.1",
    "typescript": "^5.3.3",
    "vite": "^5.0.7",
    "eslint": "^8.55.0",
    "prettier": "^3.1.1"
  }
}
```

## Configuration Examples

### Controller Configuration

**config.yaml**:

```yaml
# Application Settings
app:
  name: "VM Gateway"
  environment: "production"  # development, staging, production
  debug: false
  log_level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# Server Settings
server:
  host: "0.0.0.0"
  port: 8000
  workers: 4
  reload: false  # Enable for development
  access_log: true

# Database Configuration
database:
  url: "postgresql+asyncpg://vmgateway:password@localhost:5432/vmgateway"
  pool_size: 20
  max_overflow: 10
  pool_timeout: 30
  pool_recycle: 3600
  echo: false  # Log SQL queries (development only)

# Redis Configuration
redis:
  url: "redis://localhost:6379/0"
  max_connections: 50
  socket_timeout: 5
  socket_connect_timeout: 5
  decode_responses: true

# Authentication
auth:
  jwt_secret: "your-secret-key-here"
  jwt_algorithm: "HS256"
  access_token_expire_minutes: 60
  refresh_token_expire_days: 30
  password_min_length: 12
  password_require_uppercase: true
  password_require_lowercase: true
  password_require_digit: true
  password_require_special: true
  max_login_attempts: 5
  lockout_duration_minutes: 30

# MFA Settings
mfa:
  enabled: true
  issuer: "VM Gateway"
  totp_window: 1  # Allow 1 time step before/after
  backup_codes_count: 10

# Session Management
session:
  timeout_minutes: 480  # 8 hours
  max_concurrent_sessions: 3
  secure_cookies: true
  same_site: "lax"

# CORS Settings
cors:
  enabled: true
  origins:
    - "https://your-domain.com"
    - "http://localhost:3000"  # Development
  allow_credentials: true
  allow_methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
  allow_headers: ["*"]

# Rate Limiting
rate_limit:
  enabled: true
  requests_per_minute: 60
  burst: 10

# Metrics & Monitoring
metrics:
  enabled: true
  retention_days: 90
  aggregation_interval_seconds: 60
  prometheus_enabled: true
  prometheus_port: 9090

# Alerting
alerts:
  enabled: true
  email:
    smtp_host: "smtp.example.com"
    smtp_port: 587
    smtp_user: "<EMAIL>"
    smtp_password: "password"
    from_address: "VM Gateway <<EMAIL>>"
    use_tls: true
  slack:
    enabled: false
    webhook_url: "https://hooks.slack.com/services/YOUR/WEBHOOK/URL"
  pagerduty:
    enabled: false
    integration_key: "your-integration-key"

# Service Discovery
discovery:
  scan_interval_seconds: 60
  classification_confidence_threshold: 0.7
  ml_classification_enabled: true

# Proxy Settings
proxy:
  timeout_seconds: 300
  max_body_size_mb: 100
  buffer_size_kb: 64

# Secrets Management
secrets:
  backend: "vault"  # vault, aws, azure, local
  vault:
    url: "https://vault.example.com"
    token: "your-vault-token"
    mount_point: "secret"
  aws:
    region: "us-east-1"
    secret_name_prefix: "vmgateway/"
  azure:
    vault_url: "https://your-vault.vault.azure.net/"

# Backup & Recovery
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention_days: 30
  destination: "s3://your-bucket/backups"

# Feature Flags
features:
  approval_workflows: true
  ml_classification: true
  advanced_monitoring: true
  multi_tenancy: false
```

### Agent Configuration

**agent-config.yaml**:

```yaml
# Controller Connection
controller:
  url: "https://controller.example.com"
  token: "your-registration-token"
  verify_ssl: true
  ca_cert: null  # Path to custom CA cert
  reconnect_interval_seconds: 30
  max_reconnect_attempts: 0  # 0 = infinite
  heartbeat_interval_seconds: 30

# Agent Identity
agent:
  name: "Production Web Server 01"
  tags:
    - "production"
    - "web"
    - "frontend"
  environment: "production"
  datacenter: "us-east-1"

# Service Discovery
discovery:
  enabled: true
  scan_interval_seconds: 60
  port_range: "1-65535"
  scan_udp: false
  include_localhost: true
  exclude_ports: []
  
  # Classification
  classification:
    enabled: true
    confidence_threshold: 0.7
    ml_enabled: false
    custom_rules:
      - port: 8080
        process_pattern: "java.*tomcat"
        service_type: "web"
        name: "Tomcat"
      - port: 3306
        service_type: "database"
        name: "MySQL"

# Metrics Collection
metrics:
  enabled: true
  collection_interval_seconds: 30
  buffer_size: 1000
  
  # System Metrics
  system:
    cpu: true
    memory: true
    disk: true
    network: true
    processes: true
  
  # Service Metrics
  services:
    enabled: true
    collect_connections: true
    collect_traffic: true

# Local Storage
storage:
  database_path: "/var/lib/vm-gateway-agent/agent.db"
  max_size_mb: 100
  vacuum_interval_hours: 24

# Logging
logging:
  level: "INFO"
  file: "/var/log/vm-gateway-agent/agent.log"
  max_size_mb: 10
  backup_count: 5
  format: "json"  # json or text

# Security
security:
  run_as_user: "vmagent"
  enable_firewall_rules: false
  encrypt_local_data: true

# Performance
performance:
  max_concurrent_scans: 5
  scan_timeout_seconds: 30
  max_memory_mb: 200
```

## Performance Benchmarks

### Controller Performance

**Hardware**: 4 CPU cores, 8GB RAM, SSD

| Metric | Value |
|--------|-------|
| API Requests/sec | 1,000+ |
| WebSocket Connections | 5,000+ |
| Database Queries/sec | 500+ |
| Memory Usage (idle) | 200MB |
| Memory Usage (loaded) | 1-2GB |
| CPU Usage (idle) | <5% |
| CPU Usage (loaded) | 30-50% |

**Latency** (p95):
- API Response Time: <100ms
- WebSocket Message: <50ms
- Database Query: <10ms
- Service Discovery Update: <200ms

### Agent Performance

**Hardware**: 1 CPU core, 512MB RAM

| Metric | Value |
|--------|-------|
| Memory Usage | 30-50MB |
| CPU Usage (idle) | <1% |
| CPU Usage (scanning) | 2-5% |
| Port Scan Duration (1000 ports) | 2-5 seconds |
| Services Monitored | 100+ per agent |

### Database Performance

**PostgreSQL Configuration** (8GB RAM):

```ini
# postgresql.conf
shared_buffers = 2GB
effective_cache_size = 6GB
maintenance_work_mem = 512MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1  # For SSD
effective_io_concurrency = 200
work_mem = 10MB
min_wal_size = 1GB
max_wal_size = 4GB
max_worker_processes = 4
max_parallel_workers_per_gather = 2
max_parallel_workers = 4
```

## Upgrade Paths

### Python Version Upgrades

**From 3.10 to 3.11**:
- Performance improvements (10-60% faster)
- Better error messages
- No breaking changes for VM Gateway

**From 3.11 to 3.12**:
- Further performance improvements
- Enhanced type hinting
- Test thoroughly before upgrading

### Database Migrations

**Alembic Migration Example**:

```python
"""Add service_health table

Revision ID: abc123
Revises: xyz789
Create Date: 2025-11-09
"""
from alembic import op
import sqlalchemy as sa

def upgrade():
    op.create_table(
        'service_health',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('service_id', sa.Integer(), nullable=False),
        sa.Column('status', sa.String(20), nullable=False),
        sa.Column('last_check', sa.DateTime(), nullable=False),
        sa.Column('response_time_ms', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['service_id'], ['services.id']),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_service_health_service_id', 'service_health', ['service_id'])

def downgrade():
    op.drop_index('idx_service_health_service_id')
    op.drop_table('service_health')
```

**Running Migrations**:

```bash
# Generate migration
alembic revision --autogenerate -m "Add service_health table"

# Review generated migration
cat alembic/versions/abc123_add_service_health_table.py

# Apply migration
alembic upgrade head

# Rollback if needed
alembic downgrade -1
```

## Alternative Technologies Considered

### Backend Alternatives

**Django vs FastAPI**:
- ✅ FastAPI: Better for API-first, async support, automatic docs
- ❌ Django: More batteries-included, but heavier, less async support

**Flask vs FastAPI**:
- ✅ FastAPI: Native async, automatic validation, better performance
- ❌ Flask: More mature, larger ecosystem, but synchronous

**Go vs Python**:
- ✅ Python: Faster development, better for system monitoring, rich ecosystem
- ❌ Go: Better performance, but slower development, less suitable for rapid prototyping

### Database Alternatives

**MongoDB vs PostgreSQL**:
- ✅ PostgreSQL: ACID guarantees, better for relational data, mature
- ❌ MongoDB: Flexible schema, but eventual consistency, less suitable for transactional data

**TimescaleDB vs PostgreSQL**:
- ✅ TimescaleDB: Better for time-series data (metrics)
- ✅ PostgreSQL: Simpler setup, good enough for moderate scale
- Decision: Start with PostgreSQL, migrate to TimescaleDB if needed

### Frontend Alternatives

**Vue vs React**:
- ✅ React: Larger ecosystem, more enterprise adoption, better TypeScript support
- ❌ Vue: Simpler learning curve, but smaller ecosystem

**Svelte vs React**:
- ✅ React: More mature, larger community, more libraries
- ❌ Svelte: Better performance, smaller bundle, but less mature

## Related Documentation

- **[System Architecture](/docs/02-architecture/01-system-overview.md)**: How these technologies fit together
- **[Agent Implementation](/docs/03-agent/01-overview.md)**: Agent-specific technology usage
- **[Controller Implementation](/docs/04-controller/01-overview.md)**: Controller-specific technology usage
- **[Client Implementation](/docs/05-client/01-overview.md)**: Desktop client technology stack
- **[Development Setup](/docs/10-development/01-setup.md)**: Setting up development environment
- **[Performance Tuning](/docs/10-development/06-performance.md)**: Optimizing performance
- **[Security Best Practices](/docs/10-development/07-security.md)**: Security considerations
- **[Deployment Guide](/docs/08-deployment/01-overview.md)**: Deploying VM Gateway

## Summary

VM Gateway's technology stack is carefully chosen to provide a robust, scalable, and maintainable platform. Python provides consistency across backend components, while React delivers a modern, responsive user interface. The supporting technologies (PostgreSQL, Redis, FastAPI, etc.) are all production-proven and widely adopted, ensuring long-term viability and community support.

Key technology decisions:
- **Python 3.11+** for backend consistency and rich ecosystem
- **FastAPI** for high-performance async API server
- **PostgreSQL** for reliable, ACID-compliant data storage
- **Redis** for caching, sessions, and real-time messaging
- **React + TypeScript** for type-safe, modern frontend
- **Electron** for cross-platform desktop client

These choices balance performance, developer productivity, maintainability, and long-term viability. The stack is proven in production environments and supported by active communities.

For specific implementation details of each component, refer to the relevant sections in the [architecture documentation](/docs/02-architecture/01-system-overview.md) and component-specific guides.
