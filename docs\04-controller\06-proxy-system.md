---
title: "Proxy System"
section: "Controller"
order: 6
tags: ["controller", "proxy", "http", "https", "websocket"]
last_updated: "2025-11-08"
---

# Proxy System

The Controller includes a built-in HTTP/HTTPS proxy that allows users to access web-based services directly through their browser without installing client software. The proxy handles authentication, authorization, URL rewriting, WebSocket support, and comprehensive audit logging.

## Architecture

### Proxy Flow

```
User Browser
    ↓ HTTPS
Controller Proxy (validates permissions)
    ↓ Encrypted tunnel
Agent on VM
    ↓ Local connection
Service (HTTP/HTTPS)
```

### Components

**Reverse Proxy**: FastAPI-based reverse proxy intercepts requests and forwards to appropriate agent.

**URL Rewriting**: Rewrites URLs and handles relative paths to ensure correct routing.

**Session Management**: Maintains proxy sessions tied to user authentication sessions.

**Audit Logger**: Logs all proxied requests and responses for compliance.

**WebSocket Handler**: Proxies WebSocket connections for real-time web applications.

## Proxy URL Structure

### URL Format

Services accessed through proxy use special URL structure:

```
https://controller.example.com/proxy/{service_id}/
https://controller.example.com/proxy/{service_id}/path/to/resource
```

**Example**:
```
https://controller.example.com/proxy/svc_def456/
https://controller.example.com/proxy/svc_def456/admin/dashboard
https://controller.example.com/proxy/svc_def456/api/v1/users
```

### Alternative: Subdomain Routing

For cleaner URLs, services can be accessed via subdomains:

```
https://{service_name}.proxy.controller.example.com/
```

**Example**:
```
https://grafana.proxy.controller.example.com/
https://jenkins.proxy.controller.example.com/
```

**DNS Configuration**: Wildcard DNS record `*.proxy.controller.example.com` points to Controller.

**TLS Certificate**: Wildcard certificate for `*.proxy.controller.example.com`.

## Request Flow

### 1. User Initiates Request

User clicks "Open in Browser" button in web interface for HTTP/HTTPS service.

### 2. Controller Validates Permissions

**Authentication Check**: Verify user has valid session.

**Authorization Check**: Verify user has permission to access this service.

**Service Availability**: Check that service is currently healthy and agent is connected.

### 3. Proxy Session Creation

Controller creates temporary proxy session:
- Generate unique session ID
- Store session in Redis with expiration (default: 1 hour)
- Associate session with user, service, and permissions
- Return proxy URL to user

### 4. Browser Requests Proxied URL

User's browser navigates to proxy URL.

### 5. Controller Proxies Request

**Parse Request**: Extract service ID from URL path or subdomain.

**Validate Session**: Check proxy session is valid and not expired.

**Rewrite URL**: Convert proxy URL to target service URL.

**Add Headers**: Inject custom headers (user identity, audit info).

**Forward Request**: Send request to agent via established connection.

### 6. Agent Forwards to Service

Agent receives proxied request and forwards to local service.

### 7. Response Flow

Service responds → Agent → Controller → User's Browser

**Response Rewriting**: Controller rewrites response headers and content as needed.

**Audit Logging**: Log request and response details.

## URL Rewriting

### Request URL Rewriting

**Strip Proxy Prefix**: Remove `/proxy/{service_id}` from URL path.

**Preserve Query Parameters**: Maintain all query parameters.

**Handle Relative URLs**: Convert relative URLs in HTML/CSS/JS to proxy URLs.

**Example**:
```
Proxy URL:  https://controller.example.com/proxy/svc_def456/admin/users?page=2
Target URL: http://*********:8080/admin/users?page=2
```

### Response Content Rewriting

**HTML Rewriting**: Rewrite links, forms, and resource URLs in HTML.

**CSS Rewriting**: Rewrite `url()` references in CSS.

**JavaScript**: Inject JavaScript to intercept and rewrite dynamic URLs.

**Example HTML Rewriting**:
```html
<!-- Original -->
<a href="/admin/settings">Settings</a>
<img src="/static/logo.png">

<!-- Rewritten -->
<a href="/proxy/svc_def456/admin/settings">Settings</a>
<img src="/proxy/svc_def456/static/logo.png">
```

### Base Tag Injection

Inject `<base>` tag in HTML to handle relative URLs:

```html
<head>
  <base href="/proxy/svc_def456/">
  <!-- Rest of head -->
</head>
```

## Header Management

### Request Headers

**Forwarded Headers**: Standard proxy headers added to requests.

```http
X-Forwarded-For: ************
X-Forwarded-Proto: https
X-Forwarded-Host: controller.example.com
X-Real-IP: ************
```

**User Identity Headers**: Custom headers with user information.

```http
X-VMGateway-User-ID: usr_abc123
X-VMGateway-User-Email: <EMAIL>
X-VMGateway-User-Name: John Doe
X-VMGateway-Session-ID: sess_xyz789
```

**Audit Headers**: Headers for audit trail.

```http
X-VMGateway-Request-ID: req_abc123xyz
X-VMGateway-Timestamp: 2025-11-08T10:30:00Z
```

**Authentication Headers**: If service requires authentication, inject credentials from vault.

```http
Authorization: Basic dXNlcjpwYXNzd29yZA==
X-API-Key: service_api_key_from_vault
```

### Response Headers

**Security Headers**: Add security headers to responses.

```http
X-Frame-Options: SAMEORIGIN
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Content-Security-Policy: default-src 'self' 'unsafe-inline' 'unsafe-eval'
Strict-Transport-Security: max-age=31536000; includeSubDomains
```

**Cache Control**: Control caching behavior.

```http
Cache-Control: private, no-cache, no-store, must-revalidate
Pragma: no-cache
Expires: 0
```

**Custom Headers**: Remove or modify headers that could cause issues.

```http
# Remove headers that might break proxy
Remove: Content-Security-Policy (if too restrictive)
Remove: X-Frame-Options (if prevents embedding)
```

## WebSocket Support

### WebSocket Proxying

Proxy supports WebSocket connections for real-time web applications.

**Upgrade Detection**: Detect WebSocket upgrade requests.

```http
GET /proxy/svc_def456/ws HTTP/1.1
Upgrade: websocket
Connection: Upgrade
Sec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==
Sec-WebSocket-Version: 13
```

**Bidirectional Proxy**: Establish WebSocket connection to service and proxy messages in both directions.

**Connection Lifecycle**: Track WebSocket connections and close when user session ends.

### Server-Sent Events (SSE)

Proxy supports SSE for server-push updates.

**Content-Type Detection**: Detect `text/event-stream` content type.

**Streaming**: Stream SSE events from service to browser without buffering.

**Keep-Alive**: Maintain connection and handle reconnection.

## Cookie Management

### Cookie Handling

**Cookie Rewriting**: Rewrite cookie paths and domains to work through proxy.

**Example**:
```http
# Original Set-Cookie from service
Set-Cookie: session=abc123; Path=/; Domain=service.local

# Rewritten by proxy
Set-Cookie: session=abc123; Path=/proxy/svc_def456/; Domain=controller.example.com; Secure; HttpOnly
```

**Cookie Isolation**: Cookies scoped to proxy path prevent conflicts between services.

**Session Cookies**: Proxy maintains its own session cookie separate from service cookies.

### Cookie Security

**Secure Flag**: All cookies marked as Secure (HTTPS only).

**HttpOnly Flag**: Session cookies marked HttpOnly to prevent JavaScript access.

**SameSite**: SameSite=Lax or Strict to prevent CSRF attacks.

## Authentication Integration

### Service Authentication

**Credential Injection**: If service requires authentication, inject credentials from secrets vault.

**SSO Integration**: For services supporting SSO, proxy can act as SSO intermediary.

**Basic Auth**: Inject Basic Authentication header with credentials from vault.

**API Keys**: Inject API key headers for API services.

**OAuth**: Handle OAuth flows on behalf of user.

### Transparent Authentication

User authenticates once to Controller, proxy handles service authentication automatically.

**Example Flow**:
1. User logs into Controller
2. User clicks "Open in Browser" for service requiring authentication
3. Controller retrieves service credentials from vault
4. Controller injects credentials in proxied requests
5. User accesses service without additional login

## Audit Logging

### Request Logging

Every proxied request logged with details:

```json
{
  "timestamp": "2025-11-08T10:30:15Z",
  "request_id": "req_abc123xyz",
  "user_id": "usr_abc123",
  "user_email": "<EMAIL>",
  "service_id": "svc_def456",
  "service_name": "grafana",
  "vm_id": "vm_abc123",
  "method": "GET",
  "path": "/api/dashboards",
  "query_params": "?org=1",
  "source_ip": "************",
  "user_agent": "Mozilla/5.0...",
  "status_code": 200,
  "response_size": 4096,
  "duration_ms": 45
}
```

### Response Logging

Optionally log response bodies for sensitive services (configurable per service).

**Sensitive Data Masking**: Mask passwords, tokens, and other sensitive data in logs.

**Retention**: Proxy logs retained according to compliance requirements.

## Performance Optimization

### Caching

**Static Assets**: Cache static assets (images, CSS, JS) to reduce load on services.

**Cache Headers**: Respect cache headers from services.

**Shared Cache**: Redis-based shared cache across Controller instances.

### Compression

**Gzip/Brotli**: Compress responses if client supports and service doesn't already compress.

**Streaming**: Stream large responses without buffering entire response in memory.

### Connection Pooling

**Persistent Connections**: Maintain persistent connections to agents to reduce latency.

**Connection Reuse**: Reuse connections for multiple requests to same service.

## Security Considerations

### Access Control

**Permission Validation**: Every request validates user has current permission to access service.

**Session Expiration**: Proxy sessions expire after inactivity or absolute timeout.

**Revocation**: If user permissions change, active proxy sessions terminated.

### Content Security

**XSS Protection**: Inject Content-Security-Policy headers to mitigate XSS.

**CSRF Protection**: SameSite cookies and CSRF tokens prevent CSRF attacks.

**Clickjacking Protection**: X-Frame-Options prevents clickjacking.

### Request Filtering

**Malicious Requests**: Filter potentially malicious requests (SQL injection, XSS attempts).

**Rate Limiting**: Rate limit requests per user per service.

**Size Limits**: Enforce maximum request and response sizes.

### TLS/SSL

**End-to-End Encryption**: User → Controller uses HTTPS, Controller → Agent uses encrypted tunnel.

**Certificate Validation**: Validate service certificates if service uses HTTPS.

**TLS Version**: Enforce TLS 1.2+ for all connections.

## Error Handling

### Service Unavailable

If service is down or agent disconnected:

```html
<html>
<head><title>Service Unavailable</title></head>
<body>
  <h1>Service Unavailable</h1>
  <p>The service "grafana" is currently unavailable.</p>
  <p>Please try again later or contact your administrator.</p>
  <a href="/dashboard">Return to Dashboard</a>
</body>
</html>
```

### Permission Denied

If user loses permission during session:

```html
<html>
<head><title>Access Denied</title></head>
<body>
  <h1>Access Denied</h1>
  <p>You no longer have permission to access this service.</p>
  <p>If you believe this is an error, please contact your administrator.</p>
  <a href="/dashboard">Return to Dashboard</a>
</body>
</html>
```

### Timeout

If service doesn't respond within timeout (default: 30 seconds):

```html
<html>
<head><title>Gateway Timeout</title></head>
<body>
  <h1>Gateway Timeout</h1>
  <p>The service took too long to respond.</p>
  <p>Please try again or contact your administrator if the problem persists.</p>
  <a href="/dashboard">Return to Dashboard</a>
</body>
</html>
```

## Configuration

### Global Proxy Settings

```yaml
proxy:
  enabled: true
  session_timeout: 3600  # 1 hour
  request_timeout: 30  # 30 seconds
  max_request_size: 104857600  # 100 MB
  max_response_size: 104857600  # 100 MB
  enable_caching: true
  cache_ttl: 300  # 5 minutes
  enable_compression: true
  log_responses: false  # Log response bodies
  rate_limit: 1000  # requests per hour per user
```

### Per-Service Proxy Settings

```yaml
service_proxy_config:
  service_id: svc_def456
  custom_headers:
    X-Custom-Header: "value"
  remove_headers:
    - X-Frame-Options
  inject_auth: true
  auth_type: basic  # basic, bearer, api_key
  auth_secret_path: "vault/services/grafana/admin"
  url_rewriting: true
  log_responses: true  # Override global setting
  session_timeout: 7200  # 2 hours
```

## Limitations

**JavaScript-Heavy Apps**: Apps with complex JavaScript may have issues with URL rewriting.

**WebSocket Limits**: Very high-frequency WebSocket messages may experience latency.

**Binary Protocols**: Proxy designed for HTTP/HTTPS, not binary protocols.

**Client Certificates**: Services requiring client certificates not fully supported.

## Best Practices

**Use for Web Services**: Proxy best suited for web-based services (dashboards, admin panels, APIs).

**Desktop Client for Others**: Use desktop client for databases, SSH, and other non-HTTP services.

**Monitor Performance**: Monitor proxy latency and throughput.

**Limit Session Duration**: Set reasonable session timeouts for security.

**Enable Audit Logging**: Enable comprehensive audit logging for compliance.

**Test Thoroughly**: Test each service through proxy to ensure compatibility.
