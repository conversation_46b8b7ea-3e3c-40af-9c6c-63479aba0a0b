# Docker Deployment

Deploy the VM Gateway platform using Docker containers for easy setup and portability.

## Docker Images

### Controller Image

```dockerfile
# Dockerfile.controller
FROM python:3.11-slim

WORKDIR /app

# Install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY src/ src/
COPY config/ config/

# Expose ports
EXPOSE 80 443 8080

# Run controller
CMD ["python", "-m", "uvicorn", "src.controller.main:app", "--host", "0.0.0.0", "--port", "80"]
```

### Agent Image

```dockerfile
# Dockerfile.agent
FROM python:3.11-slim

WORKDIR /app

# Install dependencies
RUN apt-get update && apt-get install -y \
    nmap \
    netcat-openbsd \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY src/ src/

# Run agent
CMD ["python", "-m", "src.agent.main"]
```

## Docker Compose

### Basic Setup

```yaml
# docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: vm_gateway
      POSTGRES_USER: vm_gateway
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U vm_gateway"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  controller:
    build:
      context: .
      dockerfile: Dockerfile.controller
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    environment:
      DATABASE_URL: postgresql://vm_gateway:${DB_PASSWORD}@postgres:5432/vm_gateway
      REDIS_URL: redis://redis:6379
      SECRET_KEY: ${SECRET_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs

  agent:
    build:
      context: .
      dockerfile: Dockerfile.agent
    environment:
      CONTROLLER_URL: http://controller:80
      AGENT_TOKEN: ${AGENT_TOKEN}
    depends_on:
      - controller
    network_mode: host

volumes:
  postgres_data:
  redis_data:
```

## Running with Docker

### Build Images

```bash
# Build controller
docker build -t vm-gateway-controller:latest -f Dockerfile.controller .

# Build agent
docker build -t vm-gateway-agent:latest -f Dockerfile.agent .
```

### Start Services

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Check status
docker-compose ps
```

### Stop Services

```bash
# Stop all services
docker-compose down

# Stop and remove volumes
docker-compose down -v
```

## Production Configuration

### Environment Variables

```bash
# .env
DB_PASSWORD=secure_password_here
SECRET_KEY=your_secret_key_here
AGENT_TOKEN=agent_authentication_token
TLS_CERT_PATH=/etc/ssl/certs/vm-gateway.crt
TLS_KEY_PATH=/etc/ssl/private/vm-gateway.key
```

### Volume Mounts

```yaml
volumes:
  - ./config:/app/config:ro
  - ./logs:/app/logs
  - ./data:/app/data
  - /etc/ssl/certs:/etc/ssl/certs:ro
  - /etc/ssl/private:/etc/ssl/private:ro
```

### Resource Limits

```yaml
services:
  controller:
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 8G
        reservations:
          cpus: '2'
          memory: 4G
```

## Best Practices

1. **Use specific image tags**: Avoid `latest` in production
2. **Health checks**: Define health checks for all services
3. **Resource limits**: Set CPU and memory limits
4. **Secrets management**: Use Docker secrets or external vault
5. **Logging**: Configure log drivers for centralized logging
6. **Networking**: Use custom networks for isolation
7. **Backups**: Regular backups of volumes
8. **Updates**: Automated image updates with testing

## Next Steps

- [Kubernetes](03-kubernetes.md) - Kubernetes deployment
- [Multi-VM](04-multi-vm.md) - Multi-VM deployment
- [TLS Certificates](06-tls-certificates.md) - Certificate management
