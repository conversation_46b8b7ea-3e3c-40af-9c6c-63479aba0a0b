---
title: "Web Interface"
section: "Controller"
order: 2
tags: ["controller", "web-ui", "dashboard", "interface"]
last_updated: "2025-11-08"
---

# Web Interface

The Controller's web interface provides a comprehensive, modern, and intuitive dashboard for managing the entire VM Network Gateway platform. Built with React and TypeScript, the interface offers real-time updates, responsive design, and a rich set of features for monitoring, managing, and accessing services across all monitored VMs.

## Architecture and Technology

### Frontend Stack

**Framework**: React 18+ with TypeScript provides type safety and component-based architecture. The use of TypeScript catches errors at compile time and improves code maintainability.

**UI Components**: Tailwind CSS with shadcn/ui component library delivers a consistent, modern design system. Tailwind's utility-first approach enables rapid UI development while maintaining design consistency.

**State Management**: Zustand or Redux Toolkit manages application state including user session, service catalog, active connections, and UI preferences. The state management layer ensures consistent data across components and handles complex state updates.

**Real-time Updates**: WebSocket connections provide live updates for service discoveries, metric changes, alert notifications, and connection status. The interface automatically reflects changes without requiring page refreshes.

**Build Tool**: Vite offers fast development server with hot module replacement and optimized production builds with code splitting and tree shaking.

### Design Principles

**Responsive Design**: The interface adapts seamlessly to different screen sizes from mobile phones (320px) to large desktop monitors (4K). Layout components reflow, navigation collapses to hamburger menus, and data tables become scrollable on smaller screens.

**Accessibility**: WCAG 2.1 AA compliance ensures the interface is usable by people with disabilities. This includes proper heading hierarchy, ARIA labels, keyboard navigation, sufficient color contrast, and screen reader support.

**Performance**: Lazy loading of components, virtualized lists for large datasets, debounced search inputs, and optimized re-renders ensure smooth performance even with thousands of services.

**Dark Mode**: Full dark mode support with automatic detection of system preferences and manual toggle. All components, charts, and visualizations adapt to the selected theme.



## Main Dashboard

The main dashboard serves as the landing page after login, providing an at-a-glance view of the entire infrastructure.

### Overview Section

**Status Cards**: Four prominent cards display key metrics:

1. **VMs Status Card**: Shows total VMs with breakdown by status (online/offline/degraded). Color-coded indicators provide immediate visual feedback. Clicking the card navigates to the VM management page.

2. **Services Card**: Displays total discovered services across all VMs. Shows count of healthy, warning, and critical services. Quick link to service catalog.

3. **Active Connections Card**: Shows number of users currently connected to services. Displays connection count and bandwidth usage. Links to connection management page.

4. **Alerts Card**: Highlights active alerts from the last 24 hours. Color-coded by severity (info/warning/critical). Clicking opens alert management interface.

Each card includes a trend indicator (up/down arrow with percentage) showing change from previous period.

### VM Grid View

A comprehensive table displaying all monitored VMs with sortable and filterable columns:

**Columns**:
- **Name**: VM hostname with custom display name if configured
- **Status**: Visual indicator (green/yellow/red/gray) with tooltip explaining status
- **IP Address**: Primary IP address, hover shows all network interfaces
- **Agent Version**: Currently running agent version, warning icon if outdated
- **Services**: Count of discovered services, click to filter service catalog by VM
- **CPU**: Current CPU usage percentage with sparkline graph
- **RAM**: Current memory usage with used/total display
- **Last Seen**: Time since last heartbeat, updates in real-time

**Quick Actions**: Each row includes action buttons:
- **View Services**: Opens service catalog filtered to this VM
- **Manage**: Opens VM detail page with full configuration
- **Restart Agent**: Sends restart command to agent (requires permission)
- **View Logs**: Opens log viewer filtered to this VM

**Filtering and Sorting**: Users can filter by status, environment tag, or custom tags. Multi-column sorting supported. Search box filters by name or IP address.

**Bulk Operations**: Select multiple VMs to perform bulk actions like applying tags, updating configurations, or triggering rescans.

### Service Heatmap

Visual grid representation of service health across all VMs. Each cell represents a service, color intensity indicates health score (green=healthy, yellow=warning, red=critical, gray=unknown).

**Interactive Features**:
- Hover over cell shows service name, VM, and current status
- Click cell to open service detail view
- Filter by service type (web, database, cache, etc.)
- Time-based view shows health over last hour, day, or week
- Zoom and pan for large deployments

### Activity Feed

Real-time stream of events across the platform displayed in reverse chronological order:

**Event Types**:
- Service discovered or removed
- User connected or disconnected from service
- Configuration changed
- Alert triggered or resolved
- Agent connected or disconnected
- Error or warning logged

**Event Display**: Each event shows timestamp, event type icon, description, affected resource, and user (if applicable). Click event to view full details.

**Filtering**: Filter by event type, user, VM, or time range. Search across event descriptions.

**Pagination**: Infinite scroll loads older events as user scrolls down. Export events to CSV or JSON.

### Quick Access Panel

Personalized panel showing frequently accessed services and shortcuts:

**Pinned Services**: Users can pin favorite services for one-click access. Shows service name, VM, status indicator, and quick connect button.

**Recently Accessed**: Automatically populated list of services accessed in last 7 days, sorted by frequency and recency.

**Saved Connection Profiles**: Quick access to saved connection configurations with custom local ports and settings.

**Personal Shortcuts**: Customizable links to frequently used pages or external tools.



## Service Catalog

The service catalog is the primary interface for discovering and accessing services across all monitored VMs.

### List View

**Search and Filter Bar**: Prominent search box with real-time filtering as user types. Advanced filters accessible via dropdown:
- Service type (Web, Database, Cache, Message Queue, Custom)
- VM or VM group
- Status (Healthy, Warning, Critical, Unknown)
- Environment (Production, Staging, Development)
- Tags (user-defined labels)
- Port range
- Last accessed time
- Owner/team

**Filter Presets**: Save commonly used filter combinations for quick access. Share presets with team members.

**Service Cards**: Each service displayed as a card with:
- Service name and icon (auto-detected based on type)
- VM hostname and IP
- Port number and protocol
- Service type and version
- Health status indicator with tooltip
- Tags displayed as colored badges
- Quick action buttons (Open/Connect, Monitor, Configure)

**View Options**: Toggle between card view (visual, shows more detail) and table view (compact, shows more services per screen).

**Sorting**: Sort by name, status, VM, last accessed, or custom fields. Ascending/descending toggle.

**Bulk Operations**: Select multiple services to:
- Apply tags in bulk
- Update access permissions
- Change alert thresholds
- Export service data to CSV/JSON
- Create service groups

### Service Detail View

Clicking a service opens a comprehensive detail panel with multiple tabs:

#### Overview Tab

**Basic Information**:
- Service name (editable) and description
- Service type with icon
- VM hostname, IP address, and environment
- Port number and protocol (TCP/UDP)
- Bind address (localhost, specific interface, 0.0.0.0)
- Service version (auto-detected or manual)
- Process information: PID, user, executable path, command line
- Uptime and last restart time

**Health Status**: Large status indicator with detailed health information:
- Current status (Healthy/Warning/Critical/Unknown)
- Last health check time and result
- Health check configuration (interval, timeout, expected response)
- Consecutive failure count
- Uptime percentage (SLA tracking)

**Tags and Metadata**: Display and edit custom tags and metadata fields. Tags are color-coded and clickable to filter catalog.

**Quick Actions**: Prominent buttons for common operations:
- **Open in Browser** (for HTTP/HTTPS services): Launches web proxy
- **Connect via Client**: Downloads connection profile for desktop client
- **Copy Connection String**: Copies connection URL to clipboard
- **Add to Favorites**: Pins service to quick access panel

#### Connection Tab

**For HTTP/HTTPS Services**:
- **Open in Browser** button launches the web proxy in new tab
- Direct URL option (if service is publicly accessible)
- Authentication requirements display (if service requires credentials)
- Custom headers configuration (for API services)

**For Other Services**:
- **Connect via Client** button downloads .vmgw connection profile
- Manual connection instructions with step-by-step guide
- Connection string/URL formatted for common tools (psql, redis-cli, mongo, etc.)
- Required credentials section with vault integration (click to reveal)
- SSH tunnel command (alternative connection method)

**Connection History**: Table showing recent connections:
- User who connected
- Connection start and end time
- Duration
- Data transferred
- Connection status (active, completed, failed)

**Active Connections**: Real-time list of users currently connected to this service with ability to view details or terminate connections (requires permission).

#### Metrics Tab

**Real-time Graphs**: Interactive time-series charts powered by Chart.js or Plotly:

**Performance Metrics**:
- Request rate (requests per second)
- Response time distribution with percentiles (p50, p90, p95, p99)
- Error rate (percentage and absolute count)
- Active connections count
- Throughput (bytes per second)

**Resource Metrics**:
- CPU usage (percentage and time)
- Memory usage (RSS, VSZ, shared memory)
- Network I/O (bytes in/out, packets in/out)
- Disk I/O (reads/writes per second, throughput)
- File descriptor usage

**Time Range Selector**: Quick buttons for 1h, 6h, 24h, 7d, 30d, or custom range picker.

**Graph Interactions**:
- Zoom: Click and drag to zoom into time range
- Pan: Shift+drag to pan across time
- Hover: Tooltip shows exact values at point in time
- Legend: Click to show/hide specific metrics
- Export: Download graph as PNG or data as CSV

**Alert Configuration**: Set up custom alerts directly from metrics view. Click "Create Alert" button to define threshold-based alerts on any metric.

#### Health Tab

**Current Health Status**: Large visual indicator with detailed status message.

**Health Check Configuration**:
- Check type (TCP connect, HTTP GET, custom script)
- Check interval (how often to check)
- Timeout (how long to wait for response)
- Expected response (for HTTP checks)
- Failure threshold (consecutive failures before marking unhealthy)

**Health Check History**: Timeline view of last 100 health checks showing:
- Timestamp
- Result (success/failure)
- Response time
- Error message (if failed)
- Visual timeline with color-coded dots

**Uptime Tracking**:
- Current uptime streak
- Uptime percentage (last 24h, 7d, 30d, 90d)
- SLA compliance indicator
- Downtime incidents list

**Incident History**: List of past incidents with:
- Start and end time
- Duration
- Root cause (if determined)
- Impact assessment
- Resolution notes

#### Access Control Tab

**Users with Access**: Table listing users who have permission to access this service:
- User name and email
- Granted via (direct permission, role, group)
- Permission level (view, connect, configure)
- Granted by (admin who granted access)
- Granted date
- Expiration date (if temporary)
- Actions (revoke access, modify permissions)

**Roles with Access**: List of roles that grant access to this service with member count for each role.

**Active Sessions**: Real-time list of users currently connected:
- User name
- Connection start time
- Duration
- Data transferred
- Source IP address
- Actions (view details, terminate session)

**Access Request Form**: Users without access can request access by:
- Selecting desired permission level
- Specifying duration (if temporary access)
- Providing justification
- Submitting for approval

**Grant Access**: Administrators can grant access by:
- Selecting user or role
- Choosing permission level
- Setting expiration (optional)
- Adding notes
- Submitting grant

**Access History**: Audit log of all access grants, revocations, and modifications.



#### Configuration Tab

**Service Settings**:
- Custom name and description
- Service type override (if auto-detection incorrect)
- Version override
- Environment assignment (production, staging, development)
- Owner/team assignment

**Classification Rules**: Custom rules for service identification:
- Process name patterns
- Port number mappings
- Protocol detection overrides

**Health Check Settings**:
- Enable/disable health checks
- Check type and parameters
- Interval and timeout
- Failure threshold
- Success threshold (for recovery)

**Alert Thresholds**: Configure when alerts should trigger:
- CPU usage threshold
- Memory usage threshold
- Response time threshold
- Error rate threshold
- Connection count threshold
- Custom metric thresholds

**Tags and Labels**: Add, edit, or remove tags. Tags support autocomplete from existing tags.

**Custom Metadata**: Key-value pairs for storing additional information:
- Cost center
- Application ID
- Support contact
- Documentation URL
- Runbook URL

#### Logs Tab

**Service Logs**: If agent has access to service logs, display them here:
- Real-time log streaming
- Syntax highlighting for common log formats
- Log level filtering (ERROR, WARN, INFO, DEBUG)
- Search across log content
- Time range selection
- Export logs to file

**Agent Logs**: Logs from agent related to this service:
- Discovery events
- Health check results
- Connection attempts
- Errors and warnings

**Access Logs**: Audit trail of who accessed this service:
- User name
- Action performed
- Timestamp
- Source IP
- Result (success/failure)

**Log Features**:
- Full-text search with highlighting
- Filter by log level, source, or time range
- Tail mode (auto-scroll to latest logs)
- Pause/resume streaming
- Download logs as text file
- Share log permalink (specific time range)

## System Monitoring

### VM-Level Monitoring

Detailed monitoring interface for each VM accessible from VM detail page.

**Real-Time Metrics Dashboard**:

**CPU Metrics**:
- Overall CPU usage percentage with historical graph
- Per-core breakdown (bar chart or heatmap)
- Top processes by CPU usage (table with process name, PID, user, CPU%)
- Load average (1m, 5m, 15m) with trend indicators
- Context switches per second
- CPU wait time (I/O wait)

**Memory Metrics**:
- Used vs available RAM (gauge and graph)
- Memory breakdown (used, cached, buffers, free)
- Swap usage (if swap enabled)
- Top processes by memory usage
- Memory pressure indicators
- Page faults per second

**Disk Metrics**:
- Usage per mount point (pie chart or bar chart)
- I/O operations per second (read/write)
- Throughput (MB/s read/write)
- Queue depth and wait time
- Top processes by disk I/O
- Inode usage per filesystem

**Network Metrics**:
- Bytes in/out per second (graph)
- Packets in/out per second
- Errors and drops (in/out)
- Per-interface breakdown
- Connection count by state (established, time_wait, etc.)
- Top connections by bandwidth

**System Load**:
- Load average with historical trend
- Process count (running, sleeping, stopped, zombie)
- Thread count
- File descriptor usage (used/max)
- Uptime

**Visualization Options**:
- Time range selector (last 1h, 6h, 24h, 7d, 30d, custom)
- Graph type toggle (line, area, bar)
- Multi-metric overlay (compare multiple metrics on same graph)
- Zoom and pan controls
- Export graph as image or data as CSV

**Anomaly Detection**: Machine learning-based anomaly detection highlights unusual patterns:
- Unexpected CPU spikes
- Memory leaks (gradual memory increase)
- Disk space filling rapidly
- Network traffic anomalies
- Visual indicators on graphs with explanation

**Predictive Trends**: Optional ML-based predictions:
- Disk space exhaustion forecast
- Memory usage trend
- CPU capacity planning
- Network bandwidth growth

### Agent Health Monitoring

Dedicated section for monitoring agent health and performance:

**Agent Status**:
- Current status (connected, disconnected, error)
- Agent version and build number
- Agent uptime
- Last successful heartbeat timestamp
- Time since last heartbeat (updates in real-time)

**Communication Metrics**:
- WebSocket connection status
- Message latency (round-trip time)
- Messages sent/received count
- Connection errors and reconnection attempts
- Bandwidth usage (agent ↔ controller)

**Discovery Performance**:
- Last scan time and duration
- Scan frequency (configured vs actual)
- Services discovered in last scan
- Changes detected (new, modified, removed services)
- Scan errors or warnings

**Resource Usage**:
- Agent CPU usage
- Agent memory usage
- Agent disk usage (local database size)
- Agent network usage

**Error Log**: Recent errors from agent with:
- Timestamp
- Error type
- Error message
- Stack trace (if available)
- Affected service or operation

### Service-Level Monitoring

Aggregated view of metrics across all services or filtered by type, environment, or tag.

**Performance Overview**:
- Average response time across all services
- Total request rate (requests per second)
- Overall error rate
- Service availability percentage
- Active connections count

**Service Health Distribution**: Pie chart or bar chart showing count of services by health status.

**Top Services by Metric**: Sortable lists showing:
- Highest CPU usage
- Highest memory usage
- Highest request rate
- Highest error rate
- Slowest response time
- Most active connections

**Service Comparison**: Select multiple services to compare metrics side-by-side on same graph.

### Alerting Interface

Comprehensive alert management interface accessible from main navigation.

**Active Alerts Dashboard**:
- Count of active alerts by severity (info, warning, critical)
- List of active alerts with:
  - Alert name
  - Severity indicator
  - Affected resource (VM, service)
  - Triggered time
  - Current value vs threshold
  - Actions (acknowledge, snooze, resolve, view details)

**Alert Detail View**: Click alert to see:
- Full alert description
- Trigger condition and current value
- Historical graph showing metric leading up to alert
- Related alerts (other alerts on same resource)
- Runbook or remediation steps (if configured)
- Alert history (previous occurrences)
- Comments and notes
- Actions (acknowledge, snooze, resolve, escalate)

**Alert Actions**:
- **Acknowledge**: Mark alert as seen, stops repeat notifications
- **Snooze**: Pause notifications for specified duration (15m, 1h, 4h, 24h, custom)
- **Resolve**: Manually resolve alert (for alerts that don't auto-resolve)
- **Escalate**: Escalate to higher tier support or management
- **Add Note**: Add comment or investigation notes

**Alert History**: View past alerts with filters:
- Time range
- Severity
- Resource (VM, service)
- Status (active, acknowledged, resolved)
- User who acknowledged/resolved

**Alert Rules Management**: Configure alert rules:
- Create new alert rule
- Edit existing rules
- Enable/disable rules
- Test rule (preview which resources would trigger)
- Clone rule (create similar rule)
- Delete rule



## Access Management Interface

### User Management

**User List View**:
- Searchable and filterable table of all users
- Columns: Name, Email, Roles, Status, Last Login, Created Date
- Status indicators (active, disabled, locked, pending invitation)
- Bulk operations (enable, disable, assign role, export)
- Export user list to CSV

**User Detail/Edit Page**:

**Profile Section**:
- Profile photo (upload or gravatar)
- Full name (editable)
- Email address (editable, triggers verification)
- Username (if different from email)
- Phone number (for SMS MFA)
- Timezone preference
- Language preference

**Account Status**:
- Current status with visual indicator
- Status change buttons (activate, deactivate, lock, unlock)
- Account creation date
- Last login date and IP address
- Last password change date
- Failed login attempts count (resets on successful login)

**Password Management**:
- Force password reset (user must change on next login)
- Reset password (admin sets temporary password)
- View password policy compliance
- Password history (last 10 passwords, hashed)

**MFA Management**:
- MFA status (enabled/disabled)
- MFA methods enrolled (TOTP, WebAuthn, SMS)
- Reset MFA (if user loses device)
- Backup codes status (used/remaining)
- Force MFA enrollment

**Role Assignment**:
- Current roles list with remove button
- Add role dropdown with search
- Role expiration dates (for temporary roles)
- Effective permissions preview (shows all permissions from all roles)

**Group Membership**:
- List of groups user belongs to
- Add to group / remove from group
- Group-inherited roles display

**Explicit Permissions**:
- Permissions granted directly to user (not via role)
- Add permission button opens permission builder
- Remove permission button
- Permission conditions display (time windows, IP restrictions)

**Active Sessions**:
- Table of all active sessions for this user
- Columns: Device, IP Address, Location, Started, Last Activity
- Session details (browser, OS, device type)
- Kill session button (terminates specific session)
- Kill all sessions button (forces re-authentication)

**Access History**:
- Timeline of user's actions
- Filter by action type, resource, date range
- Search across history
- Export history to CSV
- Visual timeline view with zoom controls

**User Creation**:
- Manual creation form with fields: name, email, initial password, roles
- Send invitation email (user sets own password)
- Bulk import from CSV file with column mapping
- LDAP/AD sync (import users from directory)

### Group Management

**Group List**:
- Hierarchical tree view of groups
- Expand/collapse nested groups
- Group member count
- Create, edit, delete group buttons

**Group Detail Page**:

**Group Information**:
- Group name and description
- Parent group (if nested)
- Group type (department, team, project, custom)
- Created date and creator

**Members**:
- List of users in group
- Add member (search and select users)
- Remove member
- Bulk add from CSV
- Sync from LDAP/AD (if configured)

**Roles**:
- Roles assigned to this group
- All group members inherit these roles
- Add/remove roles
- Role expiration dates

**Permissions**:
- Group-level permissions (all members inherit)
- Add/remove permissions
- Permission conditions

**Nested Groups**:
- Child groups (groups within this group)
- Create child group
- Move group to different parent
- Flatten hierarchy (move members to parent)

### Role Management

**Role List**:
- Table of all roles (built-in and custom)
- Columns: Name, Type (built-in/custom), Member Count, Created Date
- Built-in roles marked with badge
- Create custom role button
- Clone role button (create similar role)

**Role Editor**:

**Basic Information**:
- Role name and description
- Role type (built-in/custom)
- Parent role (for inheritance)
- Created date and creator

**Permission Builder**:
- Visual interface for building permissions
- Resource type selector (VM, Service, User, Role, Settings, Secret)
- Resource selector (specific resources or wildcard)
- Action checkboxes (view, connect, configure, delete, grant, manage)
- Condition builder:
  - IP whitelist/blacklist (CIDR notation)
  - Time windows (days of week, hours, timezone)
  - MFA required toggle
  - Approval required toggle
  - Max session duration
  - Rate limits

**Permission List**:
- Table of all permissions in this role
- Edit permission button
- Remove permission button
- Test permission (preview what resources this grants access to)

**Members**:
- Users with this role
- Groups with this role
- Add/remove members

**Role Templates**:
- Save role as template
- Load from template
- Share template (export as JSON/YAML)
- Import template

**Permission Matrix**:
- Visual grid showing all roles vs all permission types
- Quick overview of permission distribution
- Identify permission conflicts or gaps
- Export matrix to CSV

### Audit Log Viewer

**Log List View**:
- Table of audit log entries in reverse chronological order
- Columns: Timestamp, User, Action, Resource, Result, IP Address
- Color-coded by result (success=green, failure=red, denied=yellow)

**Filtering**:
- Date range picker (last 24h, 7d, 30d, custom)
- User filter (dropdown with search)
- Action type filter (authentication, authorization, resource access, configuration change, security event)
- Resource filter (specific VM, service, user, etc.)
- Result filter (success, failure, denied)
- IP address filter
- Full-text search across all fields

**Log Detail View**:
- Full log entry with all fields
- Before/after values for configuration changes
- Stack trace or error details (if applicable)
- Related logs (other logs from same session or user)
- Export single log entry

**Bulk Operations**:
- Export filtered logs to CSV, JSON, or PDF
- Generate compliance report from logs
- Archive logs to external storage

**Anomaly Detection**:
- ML-based detection of unusual patterns
- Highlight suspicious activity:
  - Multiple failed login attempts
  - Access from unusual location
  - Privilege escalation attempts
  - Bulk data exports
  - Configuration changes outside business hours
- Alert on detected anomalies

**Compliance Reports**:
- Pre-built report templates for common compliance standards
- HIPAA access report
- SOC 2 change management report
- PCI-DSS access control report
- GDPR data access report
- Custom report builder

### Session Manager

**Active Sessions List**:
- Real-time list of all active sessions across platform
- Columns: User, Device, IP Address, Location, Started, Last Activity, Status
- Filter by user, device type, status
- Sort by any column
- Search by user name, IP, or device

**Session Detail**:
- Full device information (browser, OS, version)
- Geographic location (from IP geolocation)
- Connection quality (latency, packet loss)
- Resources accessed during session (list of services, pages viewed)
- Session duration and idle time
- Authentication method used
- MFA status

**Session Actions**:
- Terminate session (force logout)
- Send message to user (display notification in their browser)
- Block device (prevent future logins from this device)
- Flag session for security review
- Extend session (override timeout)

**Session Policies**:
- Maximum concurrent sessions per user
- Session timeout (idle and absolute)
- Trusted device management
- Geographic restrictions
- Device type restrictions

**Session Analytics**:
- Average session duration
- Peak concurrent sessions
- Sessions by device type (desktop, mobile, tablet)
- Sessions by browser
- Sessions by geographic location
- Session termination reasons (logout, timeout, admin action)

## Additional Features

### Configuration Management

**Global Settings Page**:
- Platform name and branding (logo, colors)
- Default timezone
- Session timeout defaults
- MFA policy (required, optional, disabled)
- Password policy (length, complexity, expiration)
- API rate limits
- Email server settings (SMTP configuration)
- Notification preferences
- Feature flags (enable/disable features)

**Per-VM Configuration**:
- Agent scan intervals
- Metric collection frequency
- Log level (ERROR, WARN, INFO, DEBUG)
- Resource limits (CPU, memory)
- Custom classification rules
- Tags and labels
- Environment assignment

**Per-Service Configuration**:
- Custom name and description
- Health check settings
- Alert thresholds
- Access policies
- Tags and metadata
- Proxy settings (for HTTP services)

**Configuration Templates**:
- Create reusable configuration templates
- Apply template to multiple resources
- Template variables (filled in per resource)
- Share templates across teams

**Configuration Version Control**:
- All configuration changes tracked
- View configuration history
- Diff between versions (side-by-side comparison)
- Rollback to previous configuration
- Audit trail (who changed what, when, why)
- Export configuration as code (YAML/JSON)

### API Documentation

Built-in interactive API documentation accessible from main navigation.

**OpenAPI/Swagger UI**:
- Complete API specification
- Interactive API explorer (try API calls from browser)
- Authentication examples (how to obtain and use tokens)
- Request/response examples for each endpoint
- Error code reference
- Rate limit information

**Code Examples**:
- Python (requests library)
- JavaScript (fetch and axios)
- cURL commands
- Go
- Ruby
- Copy to clipboard button

**Endpoint Categories**:
- Authentication (login, logout, token refresh, MFA)
- User Management (CRUD operations)
- VM Management (list, get, register, unregister)
- Service Catalog (list, get, search, filter)
- Connection Management (establish, list, terminate)
- Metrics (query, aggregate)
- Alerts (list, acknowledge, resolve)
- Logs (query, export)
- Configuration (get, update)
- Webhooks (register, list, delete, test)

**Webhook Documentation**:
- Webhook event types
- Payload schemas
- Retry behavior
- Security (signature verification)
- Testing webhooks (send test payload)

### Custom Dashboards

**Dashboard Builder**:
- Drag-and-drop interface for creating custom dashboards
- Widget library with various visualization types
- Layout grid with resize and rearrange
- Responsive layout (adapts to screen size)

**Available Widgets**:
- **Metric Graph**: Line, area, bar, or gauge chart for any metric
- **Service Status Card**: Shows status of specific service or group
- **VM Status Card**: Shows status of specific VM or group
- **Alert List**: Displays active alerts with filters
- **Log Viewer**: Embedded log viewer with search
- **Custom iframe**: Embed external tools or dashboards
- **Markdown Text**: Rich text blocks for documentation or notes
- **Image**: Display images or diagrams
- **Link List**: Quick links to frequently used pages

**Widget Configuration**:
- Data source selection (which metrics, services, VMs)
- Time range (last 1h, 24h, 7d, custom)
- Refresh interval (real-time, 30s, 1m, 5m, manual)
- Visualization options (colors, thresholds, labels)
- Size and position

**Dashboard Management**:
- Save dashboard with name and description
- Set as default dashboard (loads on login)
- Share dashboard with users or teams
- Public dashboards (read-only, no authentication)
- Export dashboard (JSON)
- Import dashboard (JSON)
- Clone dashboard (create copy)
- Dashboard templates (pre-built dashboards for common use cases)

### Reports

**Report Types**:
- Service inventory (all discovered services with details)
- Usage reports (most accessed services, by user, by team)
- User activity (who accessed what, when)
- Security audit (authentication events, permission changes)
- Compliance reports (access logs, change logs for specific standards)
- Performance reports (latency, uptime, errors)
- Capacity planning (resource trends, growth predictions)
- Cost allocation (usage by team/project for chargeback)

**Custom Report Builder**:
- Select data sources (services, VMs, users, metrics, logs)
- Choose metrics and dimensions
- Apply filters (time range, resource type, user, etc.)
- Set aggregation (sum, average, count, min, max)
- Choose visualization (table, chart, both)
- Add calculated fields
- Save report template

**Report Scheduling**:
- Schedule reports to run automatically (daily, weekly, monthly)
- Email delivery to recipients
- Upload to S3 or file share
- Webhook delivery (POST report data)
- Retention policy (how long to keep generated reports)

**Report Formats**:
- PDF (formatted, printable, includes charts)
- Excel/CSV (raw data for analysis)
- JSON (programmatic access)
- Interactive dashboard (live data, drill-down capability)

**Report Sharing**:
- Share report link (requires authentication)
- Public report link (no authentication, read-only)
- Embed report in external application (iframe)
- Export and download

## Performance Optimizations

**Lazy Loading**: Components and routes loaded on-demand to reduce initial bundle size.

**Virtual Scrolling**: Large lists (thousands of services) use virtual scrolling to render only visible items.

**Debouncing**: Search inputs debounced to reduce API calls while typing.

**Caching**: Frequently accessed data (service catalog, user permissions) cached in browser with cache invalidation on updates.

**Optimistic Updates**: UI updates immediately on user action, then syncs with server. Rollback if server rejects.

**Code Splitting**: Separate bundles for different routes, loaded only when needed.

**Image Optimization**: Icons and images optimized and served in modern formats (WebP, AVIF).

**Service Worker**: Optional service worker for offline capability and faster load times.

## Accessibility Features

**Keyboard Navigation**: All features accessible via keyboard (Tab, Enter, Escape, Arrow keys).

**Screen Reader Support**: ARIA labels, roles, and live regions for screen reader users.

**Focus Management**: Clear focus indicators, logical focus order, focus trapping in modals.

**Color Contrast**: WCAG AA compliant color contrast ratios (4.5:1 for text, 3:1 for UI components).

**Text Scaling**: Interface remains usable at 200% text zoom.

**Reduced Motion**: Respects prefers-reduced-motion for users sensitive to animations.

**Alt Text**: All images and icons have descriptive alt text.

**Form Labels**: All form inputs have associated labels.

**Error Messages**: Clear, descriptive error messages with suggestions for resolution.
