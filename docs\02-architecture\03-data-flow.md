---
title: "Data Flow"
section: "Architecture"
order: 3
tags: ["architecture", "data-flow", "diagrams", "sequences"]
last_updated: "2025-11-08"
---

# Data Flow

## Introduction

Understanding how data flows through the VM Network Gateway platform is essential for troubleshooting, optimization, and security analysis. This document provides detailed data flow diagrams and explanations for all major operations in the system, from service discovery to connection establishment to metrics collection.

## Service Discovery Data Flow

### Initial Discovery Process

When an agent first starts or performs a full scan, it goes through a comprehensive discovery process:

```mermaid
sequenceDiagram
    participant OS as Operating System
    participant Agent as Agent Process
    participant LocalDB as SQLite Database
    participant Classifier as Classification Engine
    participant Controller as Controller
    participant CentralDB as PostgreSQL

    Agent->>OS: Query listening ports
    OS-->>Agent: Port list (TCP/UDP)
    
    loop For each port
        Agent->>OS: Get process info (PID, user, command)
        OS-->>Agent: Process details
        
        Agent->>Classifier: Classify service
        Classifier->>Classifier: Check process name
        Classifier->>Classifier: Check port convention
        Classifier->>OS: Probe service (HTTP, TLS, etc.)
        OS-->>Classifier: Service respo