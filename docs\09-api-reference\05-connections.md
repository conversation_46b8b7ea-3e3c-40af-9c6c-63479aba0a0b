---
title: "Connections API"
section: "API Reference"
order: 5
tags: ["api", "connections", "tunnels", "endpoints"]
last_updated: "2025-11-08"
---

# Connections API

The Connections API provides endpoints for establishing, managing, and monitoring secure connections to services through the platform. This includes both web-based proxy connections and client-based tunnel connections.

## Base URL

```
https://your-gateway.example.com/api/v1/connections
```

## Connection Types

The platform supports two primary connection types:

1. **Web Proxy Connections**: HTTP/HTTPS services accessed directly through the web interface
2. **Tunnel Connections**: TCP/UDP services accessed through the desktop client application

## Endpoints

### POST /connections

Request a new connection to a service.

**Request:**

```http
POST /api/v1/connections HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "service_id": "svc_abc123def456",
  "connection_type": "tunnel",
  "local_port": 5432,
  "reason": "Database maintenance and optimization",
  "duration": 3600
}
```

**Request Body Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `service_id` | string | Yes | ID of the service to connect to |
| `connection_type` | string | Yes | Type: `tunnel` or `proxy` |
| `local_port` | integer | No | Desired local port (tunnel only) |
| `reason` | string | No | Reason for connection (required for approval workflows) |
| `duration` | integer | No | Requested duration in seconds (default: 28800) |
```

**Response (Immediate Grant):**

```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,
  "connection": {
    "id": "conn_xyz789abc123",
    "service": {
      "id": "svc_abc123def456",
      "name": "postgres-main",
      "type": "database",
      "vm": {
        "id": "vm_abc123",
        "name": "prod-db-01"
      }
    },
    "connection_type": "tunnel",
    "status": "active",
    "local_endpoint": {
      "host": "localhost",
      "port": 5432
    },
    "remote_endpoint": {
      "host": "*********",
      "port": 5432
    },
    "tunnel": {
      "protocol": "wireguard",
      "encryption": "ChaCha20-Poly1305",
      "public_key": "wg_public_key_abc123..."
    },
    "user": {
      "id": "usr_1234567890",
      "email": "<EMAIL>",
      "name": "John Doe"
    },
    "created_at": "2025-11-08T15:20:00Z",
    "expires_at": "2025-11-09T15:20:00Z",
    "last_activity": "2025-11-08T15:20:00Z"
  },
  "client_config": {
    "connection_string": "postgresql://localhost:5432/maindb",
    "tunnel_config": {
      "server": "gateway.example.com:51820",
      "public_key": "server_public_key...",
      "allowed_ips": ["*********/32"],
      "persistent_keepalive": 25
    }
  },
  "message": "Connection established successfully"
}
```

**Response (Approval Required):**

```http
HTTP/1.1 202 Accepted
Content-Type: application/json

{
  "success": true,
  "approval_required": true,
  "approval_request": {
    "id": "approval_abc123",
    "status": "pending",
    "workflow": "Production Database Access",
    "approvers": [
      {
        "level": 1,
        "required_count": 1,
        "eligible_users": ["<EMAIL>", "<EMAIL>"],
        "timeout": "2025-11-08T16:20:00Z"
      }
    ],
    "created_at": "2025-11-08T15:20:00Z"
  },
  "message": "Connection request submitted for approval"
}
```

**Response (Access Denied):**

```http
HTTP/1.1 403 Forbidden
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "ACCESS_DENIED",
    "message": "You do not have permission to connect to this service",
    "required_permissions": ["service:connect"],
    "missing_conditions": ["mfa_required", "time_window"]
  }
}
```

---

### GET /connections

List all active and recent connections.

**Request:**

```http
GET /api/v1/connections?status=active&user_id=usr_1234567890&page=1&limit=50 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `status` | string | Filter by status: `active`, `expired`, `terminated`, `pending` | all |
| `user_id` | string | Filter by user ID (admin only) | current user |
| `service_id` | string | Filter by service ID | all |
| `vm_id` | string | Filter by VM ID | all |
| `connection_type` | string | Filter by type: `tunnel`, `proxy` | all |
| `from` | string | Start time (ISO 8601) | 24 hours ago |
| `to` | string | End time (ISO 8601) | now |
| `page` | integer | Page number (1-indexed) | 1 |
| `limit` | integer | Results per page (max 100) | 50 |

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "connections": [
    {
      "id": "conn_xyz789abc123",
      "service": {
        "id": "svc_abc123def456",
        "name": "postgres-main",
        "type": "database",
        "port": 5432,
        "vm": {
          "id": "vm_abc123",
          "name": "prod-db-01",
          "hostname": "prod-db-01.internal"
        }
      },
      "connection_type": "tunnel",
      "status": "active",
      "user": {
        "id": "usr_1234567890",
        "email": "<EMAIL>",
        "name": "John Doe"
      },
      "local_endpoint": {
        "host": "localhost",
        "port": 5432
      },
      "remote_endpoint": {
        "host": "*********",
        "port": 5432
      },
      "statistics": {
        "bytes_sent": 1048576,
        "bytes_received": 2097152,
        "packets_sent": 1024,
        "packets_received": 2048,
        "duration_seconds": 1800
      },
      "created_at": "2025-11-08T15:20:00Z",
      "expires_at": "2025-11-09T15:20:00Z",
      "last_activity": "2025-11-08T15:50:00Z"
    },
    {
      "id": "conn_def456ghi789",
      "service": {
        "id": "svc_def456ghi789",
        "name": "webapp-api",
        "type": "web",
        "port": 8080,
        "vm": {
          "id": "vm_def456",
          "name": "prod-web-01",
          "hostname": "prod-web-01.internal"
        }
      },
      "connection_type": "proxy",
      "status": "active",
      "user": {
        "id": "usr_1234567890",
        "email": "<EMAIL>",
        "name": "John Doe"
      },
      "proxy_url": "https://gateway.example.com/proxy/conn_def456ghi789",
      "statistics": {
        "requests": 145,
        "bytes_sent": 524288,
        "bytes_received": 1048576,
        "duration_seconds": 600
      },
      "created_at": "2025-11-08T15:40:00Z",
      "expires_at": "2025-11-08T23:40:00Z",
      "last_activity": "2025-11-08T15:49:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 2,
    "total_pages": 1
  },
  "summary": {
    "total_active": 2,
    "total_bytes_transferred": 4718592,
    "total_duration_seconds": 2400
  }
}
```

---

### GET /connections/{connection_id}

Get detailed information about a specific connection.

**Request:**

```http
GET /api/v1/connections/conn_xyz789abc123 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "connection": {
    "id": "conn_xyz789abc123",
    "service": {
      "id": "svc_abc123def456",
      "name": "postgres-main",
      "description": "Primary PostgreSQL database for production",
      "type": "database",
      "subtype": "postgresql",
      "version": "14.5",
      "port": 5432,
      "vm": {
        "id": "vm_abc123",
        "name": "prod-db-01",
        "hostname": "prod-db-01.internal",
        "ip_addresses": ["*********"]
      }
    },
    "connection_type": "tunnel",
    "status": "active",
    "user": {
      "id": "usr_1234567890",
      "email": "<EMAIL>",
      "name": "John Doe",
      "roles": ["developer", "dba"]
    },
    "local_endpoint": {
      "host": "localhost",
      "port": 5432
    },
    "remote_endpoint": {
      "host": "*********",
      "port": 5432
    },
    "tunnel": {
      "protocol": "wireguard",
      "encryption": "ChaCha20-Poly1305",
      "server_endpoint": "gateway.example.com:51820",
      "client_ip": "**********",
      "server_ip": "**********",
      "mtu": 1420,
      "persistent_keepalive": 25
    },
    "statistics": {
      "bytes_sent": 1048576,
      "bytes_received": 2097152,
      "packets_sent": 1024,
      "packets_received": 2048,
      "errors": 0,
      "retransmissions": 2,
      "duration_seconds": 1800,
      "average_latency_ms": 15,
      "peak_bandwidth_mbps": 2.5
    },
    "access_control": {
      "granted_by": "role:dba",
      "approval_required": false,
      "mfa_verified": true,
      "ip_address": "************",
      "device": {
        "id": "dev_abc123",
        "name": "Work Laptop",
        "type": "desktop",
        "os": "Windows 11"
      }
    },
    "audit": {
      "reason": "Database maintenance and optimization",
      "requested_duration": 3600,
      "auto_terminate": true
    },
    "created_at": "2025-11-08T15:20:00Z",
    "expires_at": "2025-11-09T15:20:00Z",
    "last_activity": "2025-11-08T15:50:00Z",
    "updated_at": "2025-11-08T15:50:00Z"
  }
}
```

---

### DELETE /connections/{connection_id}

Terminate an active connection.

**Request:**

```http
DELETE /api/v1/connections/conn_xyz789abc123 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "reason": "Maintenance completed"
}
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "connection": {
    "id": "conn_xyz789abc123",
    "status": "terminated",
    "terminated_at": "2025-11-08T15:55:00Z",
    "terminated_by": "usr_1234567890",
    "termination_reason": "Maintenance completed",
    "final_statistics": {
      "bytes_sent": 1048576,
      "bytes_received": 2097152,
      "duration_seconds": 2100
    }
  },
  "message": "Connection terminated successfully"
}
```

---

### POST /connections/{connection_id}/extend

Extend the duration of an active connection.

**Request:**

```http
POST /api/v1/connections/conn_xyz789abc123/extend HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "additional_duration": 3600,
  "reason": "Additional maintenance required"
}
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "connection": {
    "id": "conn_xyz789abc123",
    "status": "active",
    "expires_at": "2025-11-09T16:20:00Z",
    "extension_granted": true,
    "extension_reason": "Additional maintenance required"
  },
  "message": "Connection extended successfully"
}
```

**Response (Extension Denied):**

```http
HTTP/1.1 403 Forbidden
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "EXTENSION_DENIED",
    "message": "Connection extension requires approval",
    "max_duration_exceeded": true,
    "approval_required": true
  }
}
```

---

### GET /connections/{connection_id}/statistics

Get real-time statistics for an active connection.

**Request:**

```http
GET /api/v1/connections/conn_xyz789abc123/statistics HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "statistics": {
    "connection_id": "conn_xyz789abc123",
    "status": "active",
    "uptime_seconds": 2100,
    "traffic": {
      "bytes_sent": 1048576,
      "bytes_received": 2097152,
      "packets_sent": 1024,
      "packets_received": 2048,
      "bytes_sent_rate": 512,
      "bytes_received_rate": 1024
    },
    "performance": {
      "latency_ms": {
        "current": 15,
        "average": 14,
        "min": 10,
        "max": 25,
        "p50": 14,
        "p95": 20,
        "p99": 23
      },
      "bandwidth_mbps": {
        "current": 1.2,
        "average": 0.8,
        "peak": 2.5
      },
      "packet_loss_percent": 0.01,
      "jitter_ms": 2
    },
    "reliability": {
      "errors": 0,
      "retransmissions": 2,
      "timeouts": 0,
      "connection_resets": 0
    },
    "last_updated": "2025-11-08T15:55:00Z"
  }
}
```

---

### GET /connections/{connection_id}/logs

Get connection audit logs.

**Request:**

```http
GET /api/v1/connections/conn_xyz789abc123/logs?limit=100 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `level` | string | Log level: `DEBUG`, `INFO`, `WARNING`, `ERROR` | all |
| `from` | string | Start time (ISO 8601) | connection start |
| `to` | string | End time (ISO 8601) | now |
| `limit` | integer | Maximum number of logs (max 1000) | 100 |

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "logs": [
    {
      "timestamp": "2025-11-08T15:20:00Z",
      "level": "INFO",
      "event": "CONNECTION_ESTABLISHED",
      "message": "Connection established successfully",
      "details": {
        "user": "<EMAIL>",
        "service": "postgres-main",
        "vm": "prod-db-01",
        "local_port": 5432
      }
    },
    {
      "timestamp": "2025-11-08T15:20:15Z",
      "level": "INFO",
      "event": "TUNNEL_HANDSHAKE_COMPLETE",
      "message": "WireGuard tunnel handshake completed",
      "details": {
        "protocol": "wireguard",
        "encryption": "ChaCha20-Poly1305",
        "latency_ms": 12
      }
    },
    {
      "timestamp": "2025-11-08T15:25:00Z",
      "level": "INFO",
      "event": "DATA_TRANSFER",
      "message": "Data transfer statistics",
      "details": {
        "bytes_sent": 524288,
        "bytes_received": 1048576,
        "duration_seconds": 300
      }
    },
    {
      "timestamp": "2025-11-08T15:55:00Z",
      "level": "INFO",
      "event": "CONNECTION_TERMINATED",
      "message": "Connection terminated by user",
      "details": {
        "reason": "Maintenance completed",
        "total_duration_seconds": 2100,
        "total_bytes_transferred": 3145728
      }
    }
  ],
  "total": 4
}
```

---

### POST /connections/bulk-terminate

Terminate multiple connections at once (admin only).

**Request:**

```http
POST /api/v1/connections/bulk-terminate HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "connection_ids": ["conn_xyz789abc123", "conn_def456ghi789"],
  "reason": "Emergency maintenance window"
}
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "results": [
    {
      "connection_id": "conn_xyz789abc123",
      "status": "terminated",
      "success": true
    },
    {
      "connection_id": "conn_def456ghi789",
      "status": "terminated",
      "success": true
    }
  ],
  "summary": {
    "total": 2,
    "succeeded": 2,
    "failed": 0
  },
  "message": "2 connections terminated successfully"
}
```

---

## WebSocket API

For real-time connection monitoring, the platform provides a WebSocket endpoint.

### WS /connections/stream

Subscribe to real-time connection events.

**Connection:**

```javascript
const ws = new WebSocket('wss://your-gateway.example.com/api/v1/connections/stream');
ws.onopen = () => {
  ws.send(JSON.stringify({
    type: 'authenticate',
    token: 'YOUR_ACCESS_TOKEN'
  }));
  
  ws.send(JSON.stringify({
    type: 'subscribe',
    filters: {
      user_id: 'usr_1234567890',
      events: ['connection_established', 'connection_terminated', 'statistics_update']
    }
  }));
};

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('Connection event:', data);
};
```

**Event Types:**

```json
{
  "type": "connection_established",
  "timestamp": "2025-11-08T15:20:00Z",
  "connection": {
    "id": "conn_xyz789abc123",
    "service_name": "postgres-main",
    "user_email": "<EMAIL>"
  }
}

{
  "type": "connection_terminated",
  "timestamp": "2025-11-08T15:55:00Z",
  "connection": {
    "id": "conn_xyz789abc123",
    "reason": "User terminated",
    "duration_seconds": 2100
  }
}

{
  "type": "statistics_update",
  "timestamp": "2025-11-08T15:50:00Z",
  "connection": {
    "id": "conn_xyz789abc123",
    "bytes_sent": 1048576,
    "bytes_received": 2097152,
    "latency_ms": 15
  }
}
```

---

## Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `CONNECTION_NOT_FOUND` | 404 | Connection with specified ID does not exist |
| `SERVICE_UNAVAILABLE` | 503 | Target service is not available |
| `ACCESS_DENIED` | 403 | User lacks permission to connect |
| `APPROVAL_REQUIRED` | 202 | Connection requires approval |
| `MAX_CONNECTIONS_EXCEEDED` | 429 | User has reached maximum concurrent connections |
| `PORT_IN_USE` | 409 | Requested local port is already in use |
| `TUNNEL_ESTABLISHMENT_FAILED` | 500 | Failed to establish tunnel |
| `CONNECTION_EXPIRED` | 410 | Connection has expired |
| `EXTENSION_DENIED` | 403 | Connection extension not allowed |

## Code Examples

### Python

```python
import requests
import time

headers = {"Authorization": "Bearer YOUR_ACCESS_TOKEN"}

# Request a connection
response = requests.post(
    "https://your-gateway.example.com/api/v1/connections",
    headers=headers,
    json={
        "service_id": "svc_abc123def456",
        "connection_type": "tunnel",
        "local_port": 5432,
        "reason": "Database maintenance",
        "duration": 3600
    }
)

connection = response.json()["connection"]
connection_id = connection["id"]
print(f"Connection established: {connection_id}")

# Monitor connection statistics
while True:
    response = requests.get(
        f"https://your-gateway.example.com/api/v1/connections/{connection_id}/statistics",
        headers=headers
    )
    stats = response.json()["statistics"]
    print(f"Bytes sent: {stats['traffic']['bytes_sent']}, "
          f"Bytes received: {stats['traffic']['bytes_received']}, "
          f"Latency: {stats['performance']['latency_ms']['current']}ms")
    time.sleep(10)

# Terminate connection when done
requests.delete(
    f"https://your-gateway.example.com/api/v1/connections/{connection_id}",
    headers=headers,
    json={"reason": "Work completed"}
)
```

### JavaScript

```javascript
const headers = {
  'Authorization': 'Bearer YOUR_ACCESS_TOKEN',
  'Content-Type': 'application/json'
};

// Request a connection
const response = await fetch('https://your-gateway.example.com/api/v1/connections', {
  method: 'POST',
  headers,
  body: JSON.stringify({
    service_id: 'svc_abc123def456',
    connection_type: 'proxy',
    reason: 'Web application testing',
    duration: 7200
  })
});

const data = await response.json();
const connectionId = data.connection.id;
const proxyUrl = data.connection.proxy_url;

console.log(`Connection established: ${connectionId}`);
console.log(`Access service at: ${proxyUrl}`);

// List all active connections
const listResponse = await fetch(
  'https://your-gateway.example.com/api/v1/connections?status=active',
  { headers }
);

const connections = await listResponse.json();
console.log(`Active connections: ${connections.connections.length}`);
```

### cURL

```bash
# Request a connection
curl -X POST https://your-gateway.example.com/api/v1/connections \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "service_id": "svc_abc123def456",
    "connection_type": "tunnel",
    "local_port": 5432,
    "reason": "Database maintenance",
    "duration": 3600
  }'

# Get connection details
curl -X GET https://your-gateway.example.com/api/v1/connections/conn_xyz789abc123 \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Terminate connection
curl -X DELETE https://your-gateway.example.com/api/v1/connections/conn_xyz789abc123 \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"reason": "Work completed"}'
```

## Best Practices

### Connection Management

1. **Always provide a reason** for connections, especially to production services
2. **Request appropriate durations** - don't request longer than needed
3. **Terminate connections** when finished to free up resources
4. **Monitor connection statistics** to detect issues early
5. **Use approval workflows** for sensitive services

### Security

1. **Verify MFA** before establishing connections to critical services
2. **Use time-limited connections** with automatic expiration
3. **Review active connections** regularly
4. **Audit connection logs** for suspicious activity
5. **Implement IP whitelisting** for production access

### Performance

1. **Choose appropriate connection types** (proxy for web, tunnel for databases)
2. **Monitor latency and bandwidth** usage
3. **Use connection pooling** when possible
4. **Close idle connections** to conserve resources
5. **Implement retry logic** with exponential backoff

## Related Documentation

- [API Overview](./01-overview.md) - API design principles
- [Authentication API](./02-authentication.md) - Authentication methods
- [Service Catalog API](./04-service-catalog.md) - Service discovery
- [Client Application](../05-client/01-overview.md) - Desktop client usage
