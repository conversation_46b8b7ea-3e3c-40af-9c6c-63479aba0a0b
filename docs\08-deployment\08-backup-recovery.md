# Backup and Recovery

Implement comprehensive backup and disaster recovery procedures for the VM Gateway platform.

## Backup Strategy

### 3-2-1 Rule

- **3** copies of data
- **2** different media types
- **1** offsite copy

### Backup Types

**Full Backup**: Complete copy of all data
**Incremental Backup**: Only changed data since last backup
**Differential Backup**: Changed data since last full backup

## Database Backups

### PostgreSQL Backups

**Full Backup**:
```bash
#!/bin/bash
# /usr/local/bin/backup-database.sh

BACKUP_DIR=/backups/postgres
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME=vm_gateway

# Create backup
pg_dump -h localhost -U vm_gateway $DB_NAME | \
  gzip > $BACKUP_DIR/${DB_NAME}_${DATE}.sql.gz

# Upload to S3
aws s3 cp $BACKUP_DIR/${DB_NAME}_${DATE}.sql.gz \
  s3://vm-gateway-backups/postgres/

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
```

**Continuous Archiving (WAL)**:
```bash
# postgresql.conf
wal_level = replica
archive_mode = on
archive_command = 'aws s3 cp %p s3://vm-gateway-backups/wal/%f'
```

**Point-in-Time Recovery**:
```bash
# Restore base backup
pg_restore -d vm_gateway backup.dump

# Restore WAL files
restore_command = 'aws s3 cp s3://vm-gateway-backups/wal/%f %p'
recovery_target_time = '2025-11-08 10:30:00'
```

### Automated Backups

```yaml
# backup-config.yml
backups:
  database:
    schedule: "0 2 * * *"  # Daily at 2 AM
    retention_days: 30
    compression: gzip
    encryption: true
    destinations:
      - type: local
        path: /backups/postgres
      - type: s3
        bucket: vm-gateway-backups
        prefix: postgres/
  
  config:
    schedule: "0 3 * * *"  # Daily at 3 AM
    retention_days: 90
    paths:
      - /etc/vm-gateway/
      - /etc/nginx/
      - /etc/haproxy/
  
  logs:
    schedule: "0 4 * * *"  # Daily at 4 AM
    retention_days: 365
    paths:
      - /var/log/vm-gateway/
```

## Configuration Backups

```bash
#!/bin/bash
# /usr/local/bin/backup-config.sh

BACKUP_DIR=/backups/config
DATE=$(date +%Y%m%d)

# Backup configuration files
tar czf $BACKUP_DIR/config_${DATE}.tar.gz \
  /etc/vm-gateway/ \
  /etc/nginx/ \
  /etc/haproxy/ \
  /etc/postgresql/ \
  /etc/redis/

# Upload to S3
aws s3 cp $BACKUP_DIR/config_${DATE}.tar.gz \
  s3://vm-gateway-backups/config/

# Keep 90 days
find $BACKUP_DIR -name "config_*.tar.gz" -mtime +90 -delete
```

## Secrets Backups

```bash
#!/bin/bash
# /usr/local/bin/backup-secrets.sh

BACKUP_DIR=/backups/secrets
DATE=$(date +%Y%m%d)

# Export secrets (encrypted)
python3 -m vm_gateway.secrets export --encrypted > \
  $BACKUP_DIR/secrets_${DATE}.enc

# Upload to S3 with encryption
aws s3 cp $BACKUP_DIR/secrets_${DATE}.enc \
  s3://vm-gateway-backups/secrets/ \
  --sse AES256

# Keep 365 days
find $BACKUP_DIR -name "secrets_*.enc" -mtime +365 -delete
```

## Recovery Procedures

### Database Recovery

**Full Recovery**:
```bash
# Stop application
systemctl stop vm-gateway-controller

# Drop existing database
sudo -u postgres psql -c "DROP DATABASE vm_gateway;"
sudo -u postgres psql -c "CREATE DATABASE vm_gateway;"

# Restore from backup
gunzip < backup.sql.gz | sudo -u postgres psql vm_gateway

# Start application
systemctl start vm-gateway-controller

# Verify
curl http://localhost/health
```

**Point-in-Time Recovery**:
```bash
# Stop PostgreSQL
systemctl stop postgresql

# Remove data directory
rm -rf /var/lib/postgresql/15/main/*

# Restore base backup
tar xzf base_backup.tar.gz -C /var/lib/postgresql/15/main/

# Create recovery.conf
cat > /var/lib/postgresql/15/main/recovery.conf <<EOF
restore_command = 'aws s3 cp s3://vm-gateway-backups/wal/%f %p'
recovery_target_time = '2025-11-08 10:30:00'
EOF

# Start PostgreSQL
systemctl start postgresql
```

### Configuration Recovery

```bash
# Extract backup
tar xzf config_backup.tar.gz -C /

# Restart services
systemctl restart vm-gateway-controller
systemctl restart nginx
systemctl restart haproxy
```

### Secrets Recovery

```bash
# Import secrets
python3 -m vm_gateway.secrets import --encrypted secrets_backup.enc

# Verify
python3 -m vm_gateway.secrets list
```

## Disaster Recovery

### DR Site Setup

```
Primary Site (US-East)          DR Site (US-West)
┌─────────────────┐            ┌─────────────────┐
│  Controller     │            │  Controller     │
│  Database       │  ────────> │  Database       │
│  (Active)       │  Replication│  (Standby)      │
└─────────────────┘            └─────────────────┘
```

### Failover Procedure

1. **Detect Failure**:
```bash
# Monitor primary site
while true; do
  if ! curl -f https://primary.vm-gateway.example.com/health; then
    alert "Primary site down, initiating failover"
    break
  fi
  sleep 30
done
```

2. **Promote DR Site**:
```bash
# Promote standby database
sudo -u postgres pg_ctl promote -D /var/lib/postgresql/15/main

# Update DNS
aws route53 change-resource-record-sets \
  --hosted-zone-id Z123456 \
  --change-batch file://failover-dns.json

# Start controller
systemctl start vm-gateway-controller
```

3. **Verify DR Site**:
```bash
# Test connectivity
curl https://vm-gateway.example.com/health

# Verify database
psql -h localhost -U vm_gateway -c "SELECT 1;"

# Check logs
tail -f /var/log/vm-gateway/controller.log
```

## Backup Verification

### Automated Testing

```python
# test_backups.py
def test_database_backup():
    """Test database backup restoration"""
    # Create test database
    create_test_database()
    
    # Restore backup
    restore_backup("latest_backup.sql.gz", "test_db")
    
    # Verify data
    assert verify_database_integrity("test_db")
    
    # Cleanup
    drop_test_database()

def test_config_backup():
    """Test configuration backup"""
    # Extract backup
    extract_backup("config_backup.tar.gz", "/tmp/test_restore")
    
    # Verify files
    assert os.path.exists("/tmp/test_restore/etc/vm-gateway/config.yml")
    
    # Cleanup
    shutil.rmtree("/tmp/test_restore")
```

### Monthly DR Drills

```bash
#!/bin/bash
# /usr/local/bin/dr-drill.sh

echo "Starting DR drill..."

# 1. Restore database to DR site
restore_database_to_dr

# 2. Verify data integrity
verify_data_integrity

# 3. Test application startup
test_application_startup

# 4. Run smoke tests
run_smoke_tests

# 5. Generate report
generate_dr_drill_report

echo "DR drill complete"
```

## Monitoring Backups

### Backup Monitoring

```python
# monitor_backups.py
def check_backup_freshness():
    """Alert if backups are stale"""
    latest_backup = get_latest_backup()
    age_hours = (datetime.now() - latest_backup.timestamp).total_seconds() / 3600
    
    if age_hours > 25:  # Should run daily
        alert(f"Backup is {age_hours:.1f} hours old")

def check_backup_size():
    """Alert if backup size changes significantly"""
    latest_size = get_latest_backup().size
    avg_size = get_average_backup_size(days=7)
    
    if latest_size < avg_size * 0.5:
        alert(f"Backup size dropped significantly: {latest_size} vs {avg_size}")
```

## Best Practices

1. **Automate backups**: Never rely on manual backups
2. **Test restores**: Regular restore testing
3. **Encrypt backups**: Encrypt sensitive data
4. **Offsite storage**: Store backups in different location
5. **Monitor backups**: Alert on backup failures
6. **Document procedures**: Maintain recovery runbooks
7. **Version control**: Track configuration changes
8. **Retention policy**: Define and enforce retention

## Recovery Time Objectives

- **RTO** (Recovery Time Objective): < 1 hour
- **RPO** (Recovery Point Objective): < 15 minutes
- **Database**: < 30 minutes
- **Configuration**: < 15 minutes
- **Full System**: < 1 hour

## Next Steps

- [High Availability](07-high-availability.md) - HA configuration
- [Multi-VM](04-multi-vm.md) - Multi-VM deployment
- [Kubernetes](03-kubernetes.md) - Kubernetes deployment
