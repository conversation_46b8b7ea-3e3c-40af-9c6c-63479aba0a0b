# Secrets Management Overview

The VM Gateway platform includes a comprehensive secrets management system designed to securely store, manage, and distribute sensitive credentials across the platform. This system ensures that secrets like passwords, API keys, certificates, and tokens are never stored in plain text and are accessible only to authorized components and users.

## Purpose

The secrets management system serves several critical functions:

- **Secure Storage**: Encrypt and store sensitive credentials using industry-standard encryption
- **Access Control**: Enforce fine-grained permissions on who can access which secrets
- **Audit Trail**: Track all secret access and modifications for compliance
- **Lifecycle Management**: Automate secret rotation and expiration
- **Integration**: Seamlessly integrate with external vault systems
- **Distribution**: Securely distribute secrets to agents and services

## Architecture Overview

```
┌─────────────────────────────────────────────────────────┐
│                    Controller                           │
│  ┌───────────────────────────────────────────────────┐  │
│  │         Secrets Management API                    │  │
│  │  - Create/Read/Update/Delete secrets              │  │
│  │  - Access control enforcement                     │  │
│  │  - Audit logging                                  │  │
│  └──────────────┬────────────────────────────────────┘  │
│                 │                                        │
│  ┌──────────────▼────────────────────────────────────┐  │
│  │         Secrets Engine                            │  │
│  │  - Encryption/Decryption                          │  │
│  │  - Key management                                 │  │
│  │  - Secret versioning                              │  │
│  └──────────────┬────────────────────────────────────┘  │
│                 │                                        │
│  ┌──────────────▼────────────────────────────────────┐  │
│  │         Storage Backend                           │  │
│  │  - PostgreSQL (encrypted secrets)                 │  │
│  │  - External vault integration                     │  │
│  └───────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────┘
```

## Key Features

### 1. Encryption at Rest

All secrets are encrypted before being stored in the database using AES-256-GCM encryption. The encryption keys are managed separately and never stored alongside the encrypted data.

### 2. Encryption in Transit

Secrets are transmitted over TLS-encrypted connections only. The platform enforces TLS 1.3 for all secret-related communications.

### 3. Access Control

Fine-grained access control determines who can:
- View secret metadata (name, tags, creation date)
- Read secret values
- Create new secrets
- Update existing secrets
- Delete secrets
- Manage secret permissions

### 4. Secret Versioning

The system maintains a complete history of secret values, allowing:
- Rollback to previous versions
- Audit trail of changes
- Comparison between versions
- Retention policies for old versions

### 5. Automatic Rotation

Secrets can be configured for automatic rotation based on:
- Time-based policies (e.g., every 90 days)
- Event-based triggers (e.g., after access by terminated user)
- Manual rotation requests
- Integration with external rotation systems

### 6. External Vault Integration

The platform can integrate with external secret management systems:
- HashiCorp Vault
- AWS Secrets Manager
- Azure Key Vault
- Google Cloud Secret Manager

## Secret Types

The platform supports various types of secrets:

### Database Credentials
- Username/password pairs
- Connection strings
- Certificate-based authentication

### API Keys
- REST API keys
- Service account tokens
- OAuth client secrets

### Certificates
- TLS/SSL certificates
- Private keys
- Certificate chains
- CA certificates

### SSH Keys
- Private SSH keys
- Public keys
- Known hosts

### Custom Secrets
- Generic key-value pairs
- JSON-formatted secrets
- Binary data (base64 encoded)

## Secret Metadata

Each secret includes comprehensive metadata:

```json
{
  "id": "secret_abc123",
  "name": "database-prod-password",
  "type": "database_credential",
  "description": "Production database password",
  "tags": ["production", "database", "critical"],
  "created_at": "2025-11-08T10:00:00Z",
  "created_by": "user_xyz789",
  "updated_at": "2025-11-08T15:30:00Z",
  "updated_by": "user_xyz789",
  "version": 3,
  "rotation_policy": {
    "enabled": true,
    "interval_days": 90,
    "next_rotation": "2026-02-06T10:00:00Z"
  },
  "access_control": {
    "owner": "user_xyz789",
    "readers": ["group_admins", "service_backup"],
    "writers": ["group_admins"]
  }
}
```

## Security Model

### Encryption Keys

The platform uses a hierarchical key management system:

1. **Master Key**: Root encryption key, stored in secure hardware or KMS
2. **Data Encryption Keys (DEK)**: Per-secret encryption keys
3. **Key Encryption Keys (KEK)**: Encrypt the DEKs

This approach allows for:
- Key rotation without re-encrypting all secrets
- Separation of key management from secret storage
- Integration with hardware security modules (HSM)

### Access Patterns

Secret access follows the principle of least privilege:

1. **Authentication**: User/service must authenticate first
2. **Authorization**: Check if requester has permission
3. **Audit**: Log the access attempt
4. **Decryption**: Decrypt and return secret (if authorized)
5. **Tracking**: Track secret usage for rotation decisions

### Audit Logging

All secret operations are logged:
- Secret creation, updates, deletions
- Access attempts (successful and failed)
- Permission changes
- Rotation events
- Export operations

## Use Cases

### Service Discovery Credentials

Agents need credentials to authenticate with discovered services. These credentials are:
- Stored centrally in the secrets system
- Distributed to agents on-demand
- Rotated automatically
- Revoked when agents are decommissioned

### Database Connections

The controller needs database credentials that are:
- Encrypted at rest
- Rotated regularly
- Accessible only to the controller process
- Backed up securely

### API Authentication

External integrations require API keys that are:
- Generated securely
- Stored encrypted
- Revocable instantly
- Audited for usage

### Certificate Management

TLS certificates for secure communications are:
- Stored with private keys encrypted
- Renewed automatically before expiration
- Distributed to relevant services
- Tracked for compliance

## Integration Points

### Controller Integration

The controller uses the secrets system for:
- Database credentials
- External API keys
- TLS certificates
- Service account tokens

### Agent Integration

Agents retrieve secrets for:
- Service authentication
- Metric collection credentials
- Proxy authentication
- Tunnel encryption keys

### Client Integration

Clients may access secrets for:
- Service connection credentials
- SSH keys for VM access
- API tokens for automation

### Web Interface

The web UI provides:
- Secret management interface
- Access control configuration
- Audit log viewing
- Rotation policy management

## Best Practices

### Secret Naming

Use descriptive, hierarchical names:
- `prod/database/primary/password`
- `staging/api/external-service/key`
- `dev/ssh/deployment/private-key`

### Tagging Strategy

Apply consistent tags for organization:
- Environment: `production`, `staging`, `development`
- Component: `database`, `api`, `ssh`
- Criticality: `critical`, `important`, `standard`
- Compliance: `pci`, `hipaa`, `gdpr`

### Rotation Policies

Implement appropriate rotation schedules:
- Critical production secrets: 30-90 days
- Standard secrets: 90-180 days
- Development secrets: 180-365 days
- Emergency rotation: Immediate

### Access Control

Follow least privilege principles:
- Grant read-only access by default
- Limit write access to administrators
- Use groups instead of individual users
- Review permissions regularly

## Performance Considerations

### Caching

The secrets system implements intelligent caching:
- Frequently accessed secrets cached in memory
- Cache invalidation on secret updates
- TTL-based cache expiration
- Encrypted cache storage

### Scalability

The system scales through:
- Read replicas for secret metadata
- Distributed caching layer
- Asynchronous rotation processing
- Batch secret retrieval APIs

## Compliance

The secrets management system supports compliance with:

- **SOC 2**: Audit logging, access controls, encryption
- **PCI DSS**: Key management, access restrictions, audit trails
- **HIPAA**: Encryption, access controls, audit logging
- **GDPR**: Data protection, access controls, right to deletion

## Next Steps

Explore the detailed documentation for each aspect of the secrets management system:

- [Storage](02-storage.md) - Encryption and storage mechanisms
- [Vault Integration](03-vault-integration.md) - External vault integrations
- [Lifecycle](04-lifecycle.md) - Secret lifecycle management
- [Access Control](05-access-control.md) - Permission system
- [Rotation](06-rotation.md) - Automatic rotation
- [Audit](07-audit.md) - Audit logging and compliance
