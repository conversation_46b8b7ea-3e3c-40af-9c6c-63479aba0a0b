---
title: "Slack Integration"
section: "Integrations"
order: 1
tags: ["integration", "slack", "notifications", "alerts"]
last_updated: "2025-11-09"
---

# Slack Integration

## Overview

Integrate VM Gateway with Slack to receive real-time notifications about service changes, alerts, approval requests, and system events directly in your Slack workspace. This integration enables teams to stay informed and respond quickly to important events.

**Features**:
- Real-time service discovery notifications
- Alert notifications with severity levels
- Approval workflow notifications
- User activity notifications
- System health notifications
- Interactive buttons for approvals
- Custom notification channels per event type

**Prerequisites**:
- VM Gateway Professional or Enterprise Edition
- Slack workspace with admin access
- Ability to create Slack apps

---

## Table of Contents

1. [Creating a Slack App](#creating-a-slack-app)
2. [Configuring Webhooks](#configuring-webhooks)
3. [Configuring VM Gateway](#configuring-vm-gateway)
4. [Notification Types](#notification-types)
5. [Custom Notifications](#custom-notifications)
6. [Interactive Approvals](#interactive-approvals)
7. [Troubleshooting](#troubleshooting)

---

## Creating a Slack App

### Step 1: Create Slack App

1. **Go to Slack API**:
   - Navigate to https://api.slack.com/apps
   - Click **Create New App**

2. **Choose Creation Method**:
   - Select **From scratch**
   - App Name: `VM Gateway`
   - Workspace: Select your workspace
   - Click **Create App**

### Step 2: Enable Incoming Webhooks

1. **Navigate to Incoming Webhooks**:
   - In left sidebar, click **Incoming Webhooks**
   - Toggle **Activate Incoming Webhooks** to **On**

2. **Add New Webhook**:
   - Scroll down and click **Add New Webhook to Workspace**
   - Select channel for notifications (e.g., `#vm-gateway-alerts`)
   - Click **Allow**

3. **Copy Webhook URL**:
   - Copy the webhook URL (looks like `https://hooks.slack.com/services/T00000000/B00000000/XXXXXXXXXXXXXXXXXXXX`)
   - Save this for VM Gateway configuration

### Step 3: Configure Bot (Optional, for Interactive Features)

For interactive approvals and advanced features:

1. **OAuth & Permissions**:
   - Click **OAuth & Permissions** in sidebar
   - Add these Bot Token Scopes:
     - `chat:write` - Send messages
     - `chat:write.public` - Send messages to public channels
     - `channels:read` - View channels
     - `users:read` - View users
     - `reactions:write` - Add reactions to messages

2. **Install App**:
   - Click **Install to Workspace**
   - Click **Allow**
   - Copy **Bot User OAuth Token** (starts with `xoxb-`)

### Step 4: Enable Interactivity (Optional)

For approval buttons:

1. **Interactivity & Shortcuts**:
   - Click **Interactivity & Shortcuts** in sidebar
   - Toggle **Interactivity** to **On**
   - Request URL: `https://your-vmgateway-domain.com/api/v1/integrations/slack/interactions`
   - Click **Save Changes**

---

## Configuring Webhooks

### Multiple Webhooks for Different Channels

Create separate webhooks for different notification types:

1. **Alerts Channel** (`#vm-gateway-alerts`):
   - For critical alerts and system issues
   - High-priority notifications

2. **Activity Channel** (`#vm-gateway-activity`):
   - For service discoveries, user actions
   - General activity notifications

3. **Approvals Channel** (`#vm-gateway-approvals`):
   - For approval requests
   - Requires user interaction

**Create webhooks for each channel**:
- Repeat "Add New Webhook to Workspace" for each channel
- Save each webhook URL

---

## Configuring VM Gateway

### Basic Configuration

Edit `/opt/vm-gateway/.env`:

```bash
# Slack Integration
SLACK_ENABLED=true
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/T00000000/B00000000/XXXXXXXXXXXXXXXXXXXX

# Optional: Bot token for advanced features
SLACK_BOT_TOKEN=xoxb-your-bot-token-here

# Default channel for notifications
SLACK_DEFAULT_CHANNEL=#vm-gateway-alerts

# Notification settings
SLACK_NOTIFY_SERVICE_DISCOVERY=true
SLACK_NOTIFY_ALERTS=true
SLACK_NOTIFY_APPROVALS=true
SLACK_NOTIFY_USER_ACTIONS=false  # Can be noisy

# Alert severity threshold (only notify for these and above)
SLACK_ALERT_MIN_SEVERITY=warning  # debug, info, warning, error, critical
```

### Advanced Configuration

For multiple webhooks and custom routing, create `config/slack-config.yaml`:

```yaml
# Slack Integration Configuration
enabled: true

# Webhooks for different channels
webhooks:
  alerts:
    url: "https://hooks.slack.com/services/T00/B00/XXX"
    channel: "#vm-gateway-alerts"
    username: "VM Gateway Alerts"
    icon_emoji: ":rotating_light:"
  
  activity:
    url: "https://hooks.slack.com/services/T00/B01/YYY"
    channel: "#vm-gateway-activity"
    username: "VM Gateway"
    icon_emoji: ":robot_face:"
  
  approvals:
    url: "https://hooks.slack.com/services/T00/B02/ZZZ"
    channel: "#vm-gateway-approvals"
    username: "VM Gateway Approvals"
    icon_emoji: ":white_check_mark:"

# Bot configuration (for interactive features)
bot:
  token: "xoxb-your-bot-token-here"
  enabled: true

# Notification routing
routing:
  # Service discovery notifications
  service_discovered:
    webhook: "activity"
    enabled: true
    throttle_minutes: 5  # Don't spam if many services discovered
  
  service_removed:
    webhook: "activity"
    enabled: true
  
  # Alert notifications
  alert_critical:
    webhook: "alerts"
    enabled: true
    mention_users: ["@oncall", "@admin"]
  
  alert_error:
    webhook: "alerts"
    enabled: true
  
  alert_warning:
    webhook: "alerts"
    enabled: true
  
  alert_info:
    webhook: "activity"
    enabled: false
  
  # Approval notifications
  approval_requested:
    webhook: "approvals"
    enabled: true
    mention_users: ["@approvers"]
  
  approval_approved:
    webhook: "approvals"
    enabled: true
  
  approval_denied:
    webhook: "approvals"
    enabled: true
  
  # User activity
  user_login:
    webhook: "activity"
    enabled: false  # Usually too noisy
  
  user_created:
    webhook: "activity"
    enabled: true
  
  connection_established:
    webhook: "activity"
    enabled: false  # Can be noisy
  
  # System events
  agent_connected:
    webhook: "activity"
    enabled: true
  
  agent_disconnected:
    webhook: "alerts"
    enabled: true
  
  controller_started:
    webhook: "activity"
    enabled: true

# Message formatting
formatting:
  use_markdown: true
  include_links: true
  include_timestamps: true
  include_user_info: true
  
  # Color coding for alerts
  colors:
    critical: "#FF0000"  # Red
    error: "#FF6600"     # Orange
    warning: "#FFCC00"   # Yellow
    info: "#0099FF"      # Blue
    success: "#00CC00"   # Green

# Rate limiting
rate_limit:
  enabled: true
  max_messages_per_minute: 10
  burst: 5

# Retry configuration
retry:
  enabled: true
  max_attempts: 3
  backoff_seconds: 5
```

### Restart VM Gateway

```bash
sudo systemctl restart vm-gateway-controller
```

Check logs:
```bash
sudo journalctl -u vm-gateway-controller | grep -i slack
```

Look for:
```
INFO: Slack integration enabled
INFO: Slack webhook configured for channel #vm-gateway-alerts
INFO: Slack bot token configured
```

---

## Notification Types

### Service Discovery Notifications

**When**: New service discovered or removed

**Example Message**:
```
🔍 New Service Discovered

Service: PostgreSQL Database
Type: Database (PostgreSQL)
VM: prod-db-01
Port: 5432
Status: Healthy

View Service: https://your-vmgateway.com/services/svc_123
```

**Configuration**:
```yaml
routing:
  service_discovered:
    webhook: "activity"
    enabled: true
    include_fields:
      - service_name
      - service_type
      - vm_name
      - port
      - status
```

### Alert Notifications

**When**: Alert triggered based on metrics or health checks

**Example Message**:
```
🚨 CRITICAL ALERT

Alert: High CPU Usage
Service: web-server-01
Metric: cpu.usage.percent
Current Value: 95%
Threshold: 80%
Duration: 5 minutes

Details: https://your-vmgateway.com/alerts/alert_456
Acknowledge: https://your-vmgateway.com/alerts/alert_456/ack
```

**Configuration**:
```yaml
routing:
  alert_critical:
    webhook: "alerts"
    enabled: true
    mention_users: ["@oncall"]
    include_fields:
      - alert_name
      - service_name
      - metric_name
      - current_value
      - threshold
      - duration
```

### Approval Request Notifications

**When**: User requests access requiring approval

**Example Message**:
```
✋ Approval Required

User: <EMAIL>
Requesting Access To: Production Database (prod-db-01)
Reason: Debug production issue #1234
Duration: 2 hours
Expires: 2025-11-09 18:00 UTC

[Approve] [Deny] [View Details]
```

**Configuration**:
```yaml
routing:
  approval_requested:
    webhook: "approvals"
    enabled: true
    mention_users: ["@approvers"]
    interactive: true  # Enable buttons
```

### User Activity Notifications

**When**: Significant user actions occur

**Example Message**:
```
👤 User Activity

Action: Connection Established
User: <EMAIL>
Service: PostgreSQL (prod-db-01)
Time: 2025-11-09 15:30 UTC
Duration: 2 hours (auto-disconnect)

View Connection: https://your-vmgateway.com/connections/conn_789
```

### System Event Notifications

**When**: System-level events occur

**Example Message**:
```
⚙️ System Event

Event: Agent Disconnected
Agent: prod-web-01
Last Seen: 2025-11-09 15:25 UTC
Services Affected: 12

View Agent: https://your-vmgateway.com/agents/agent_321
```

---

## Custom Notifications

### Sending Custom Notifications via API

```python
import requests

def send_slack_notification(message, channel="#vm-gateway-alerts", severity="info"):
    """Send custom notification to Slack."""
    
    # Color based on severity
    colors = {
        "critical": "#FF0000",
        "error": "#FF6600",
        "warning": "#FFCC00",
        "info": "#0099FF",
        "success": "#00CC00"
    }
    
    payload = {
        "channel": channel,
        "username": "VM Gateway",
        "icon_emoji": ":robot_face:",
        "attachments": [{
            "color": colors.get(severity, "#0099FF"),
            "title": message["title"],
            "text": message["text"],
            "fields": message.get("fields", []),
            "footer": "VM Gateway",
            "ts": int(time.time())
        }]
    }
    
    response = requests.post(
        "https://hooks.slack.com/services/YOUR/WEBHOOK/URL",
        json=payload
    )
    
    return response.status_code == 200

# Example usage
send_slack_notification({
    "title": "Custom Alert",
    "text": "Something important happened!",
    "fields": [
        {"title": "Detail 1", "value": "Value 1", "short": True},
        {"title": "Detail 2", "value": "Value 2", "short": True}
    ]
}, severity="warning")
```

### Sending via VM Gateway API

```bash
curl -X POST https://your-vmgateway.com/api/v1/integrations/slack/notify \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "channel": "#vm-gateway-alerts",
    "message": {
      "title": "Custom Notification",
      "text": "This is a custom notification",
      "severity": "info"
    }
  }'
```

---

## Interactive Approvals

### Enabling Interactive Buttons

Interactive buttons allow approvers to approve/deny requests directly from Slack.

**Configuration**:

1. **Enable interactivity in Slack app** (see [Step 4](#step-4-enable-interactivity-optional))

2. **Configure VM Gateway**:
   ```yaml
   # In slack-config.yaml
   bot:
     enabled: true
     token: "xoxb-your-bot-token"
   
   routing:
     approval_requested:
       interactive: true
   ```

3. **Restart VM Gateway**

### Approval Message with Buttons

When approval is requested, message includes interactive buttons:

```
✋ Approval Required

User: <EMAIL>
Requesting Access To: Production Database
Reason: Debug production issue
Duration: 2 hours

[Approve] [Deny] [View Details]
```

**Clicking "Approve"**:
1. Button updates to show "Approved by @approver"
2. User is granted access
3. Notification sent to requester
4. Audit log created

**Clicking "Deny"**:
1. Modal appears asking for denial reason
2. Button updates to show "Denied by @approver"
3. User is notified of denial
4. Audit log created

### Handling Button Clicks

VM Gateway handles button clicks via webhook:

```python
@app.post("/api/v1/integrations/slack/interactions")
async def handle_slack_interaction(request: Request):
    """Handle Slack interactive button clicks."""
    
    # Parse Slack payload
    form_data = await request.form()
    payload = json.loads(form_data["payload"])
    
    action = payload["actions"][0]
    action_id = action["action_id"]
    user = payload["user"]["id"]
    
    if action_id == "approve_access":
        # Approve the request
        approval_id = action["value"]
        approve_access_request(approval_id, user)
        
        # Update message
        return {
            "replace_original": True,
            "text": f"✅ Approved by <@{user}>"
        }
    
    elif action_id == "deny_access":
        # Show modal for denial reason
        return {
            "response_action": "push",
            "view": {
                "type": "modal",
                "title": {"type": "plain_text", "text": "Deny Access"},
                "submit": {"type": "plain_text", "text": "Submit"},
                "blocks": [
                    {
                        "type": "input",
                        "block_id": "reason",
                        "label": {"type": "plain_text", "text": "Reason for denial"},
                        "element": {
                            "type": "plain_text_input",
                            "action_id": "denial_reason",
                            "multiline": True
                        }
                    }
                ]
            }
        }
```

---

## Troubleshooting

### Issue: Notifications Not Appearing

**Symptoms**:
- No messages in Slack channel
- No errors in logs

**Solutions**:

1. **Verify webhook URL**:
   ```bash
   # Test webhook manually
   curl -X POST https://hooks.slack.com/services/YOUR/WEBHOOK/URL \
     -H "Content-Type: application/json" \
     -d '{"text":"Test message from VM Gateway"}'
   ```

2. **Check Slack integration is enabled**:
   ```bash
   grep SLACK_ENABLED /opt/vm-gateway/.env
   # Should be: SLACK_ENABLED=true
   ```

3. **Check logs for errors**:
   ```bash
   sudo journalctl -u vm-gateway-controller | grep -i slack
   ```

4. **Verify webhook is not revoked**:
   - Go to https://api.slack.com/apps
   - Check if webhook is still active

### Issue: Messages Appearing in Wrong Channel

**Symptoms**:
- Messages appear in unexpected channel
- Channel override not working

**Solutions**:

1. **Check webhook configuration**:
   ```yaml
   # Each webhook is tied to specific channel
   # Verify webhook URL matches intended channel
   ```

2. **Use correct webhook**:
   ```yaml
   # In slack-config.yaml
   routing:
     alert_critical:
       webhook: "alerts"  # Must match webhook name
   ```

### Issue: Interactive Buttons Not Working

**Symptoms**:
- Buttons appear but don't respond
- Error when clicking buttons

**Solutions**:

1. **Verify interactivity is enabled**:
   - Check Slack app settings
   - Ensure Request URL is correct

2. **Check bot token**:
   ```bash
   grep SLACK_BOT_TOKEN /opt/vm-gateway/.env
   ```

3. **Verify endpoint is accessible**:
   ```bash
   curl https://your-vmgateway.com/api/v1/integrations/slack/interactions
   # Should return 405 Method Not Allowed (POST required)
   ```

4. **Check logs for interaction errors**:
   ```bash
   sudo journalctl -u vm-gateway-controller | grep "slack/interactions"
   ```

### Issue: Too Many Notifications

**Symptoms**:
- Slack channel flooded with messages
- Notification fatigue

**Solutions**:

1. **Enable rate limiting**:
   ```yaml
   rate_limit:
     enabled: true
     max_messages_per_minute: 10
   ```

2. **Disable noisy notifications**:
   ```yaml
   routing:
     user_login:
       enabled: false
     connection_established:
       enabled: false
   ```

3. **Increase throttle time**:
   ```yaml
   routing:
     service_discovered:
       throttle_minutes: 15  # Group discoveries
   ```

4. **Use severity filtering**:
   ```bash
   # Only notify for warnings and above
   SLACK_ALERT_MIN_SEVERITY=warning
   ```

---

## Related Documentation

- **[Webhook Integration](/docs/09-api-reference/08-webhooks.md)**: General webhook configuration
- **[Alert Configuration](/docs/04-controller/07-monitoring.md#alerting)**: Setting up alerts
- **[Approval Workflows](/docs/06-authentication/08-approval-workflows.md)**: Configuring approvals
- **[API Reference](/docs/09-api-reference/01-overview.md)**: API endpoints for integrations
- **[PagerDuty Integration](/docs/13-integrations/02-pagerduty.md)**: Similar integration guide
- **[Email Notifications](/docs/13-integrations/03-email.md)**: Email notification setup

## Summary

Slack integration keeps your team informed about important VM Gateway events in real-time. With customizable routing, interactive approvals, and rich message formatting, you can create a notification system that fits your team's workflow.

**Key Features**:
- Multiple webhooks for different channels
- Customizable notification routing
- Interactive approval buttons
- Rich message formatting with colors and links
- Rate limiting to prevent spam
- Flexible configuration

**Best Practices**:
- Use separate channels for different notification types
- Enable rate limiting to prevent flooding
- Disable noisy notifications (user logins, etc.)
- Use interactive buttons for approvals
- Test webhooks before deploying to production
- Monitor Slack API rate limits

