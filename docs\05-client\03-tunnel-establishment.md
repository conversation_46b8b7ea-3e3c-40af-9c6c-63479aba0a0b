---
title: "Tunnel Establishment"
section: "Client"
order: 3
tags: ["client", "tunnel", "wireguard", "tls", "websocket"]
last_updated: "2025-11-08"
---

# Tunnel Establishment

## Overview

Tunnel establishment is the core networking functionality of the VM Gateway Client. When a user requests access to a remote service, the client creates an encrypted tunnel between the local machine and the remote agent, enabling secure traffic forwarding without exposing services to the public internet or requiring complex firewall configurations.

The client supports multiple tunnel protocols to accommodate different network environments and security requirements, automatically selecting the most appropriate protocol based on network conditions and policy constraints.

## Tunnel Protocols

### WireGuard (Preferred)

WireGuard is the preferred tunnel protocol due to its modern design, excellent performance, and strong security guarantees.

**Advantages:**
- Minimal overhead (~4% bandwidth overhead)
- Low latency (~1-2ms additional latency)
- Strong cryptography (Curve25519, ChaCha20, Poly1305)
- Built-in roaming support (handles IP address changes)
- Simple implementation (fewer lines of code = fewer bugs)
- NAT traversal capabilities
- Efficient battery usage on mobile devices

**Technical Details:**
- Uses UDP for transport (typically port 51820)
- Stateless protocol (no connection handshake)
- Cryptokey routing (routes based on public keys)
- Perfect forward secrecy
- Resistance to denial-of-service attacks

**Implementation:**
```python
import wireguard

def establish_wireguard_tunnel(tunnel_config: dict):
    # Generate client keypair
    private_key = wireguard.generate_private_key()
    public_key = wireguard.derive_public_key(private_key)
    
    # Request tunnel from controller
    response = requests.post(
        f"{controller_url}/api/tunnels/wireguard",
        json={
            "service_id": tunnel_config["service_id"],
            "client_public_key": public_key
        },
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    tunnel_info = response.json()
    # {
    #   "server_public_key": "...",
    #   "server_endpoint": "agent.example.com:51820",
    #   "client_ip": "**********/32",
    #   "allowed_ips": "**********/32"
    # }
    
    # Configure WireGuard interface
    wg_config = f"""
    [Interface]
    PrivateKey = {private_key}
    Address = {tunnel_info['client_ip']}
    
    [Peer]
    PublicKey = {tunnel_info['server_public_key']}
    Endpoint = {tunnel_info['server_endpoint']}
    AllowedIPs = {tunnel_info['allowed_ips']}
    PersistentKeepalive = 25
    """
    
    # Create interface
    interface = wireguard.create_interface("wg-vmgw", wg_config)
    interface.up()
    
    return interface
```


### TLS Tunnel (Fallback)

When WireGuard is blocked or unavailable, the client falls back to a custom TLS-based tunnel protocol.

**Advantages:**
- Works in restrictive networks (uses TCP port 443)
- Looks like standard HTTPS traffic
- Certificate pinning prevents MITM attacks
- Connection multiplexing (multiple services over one tunnel)
- Widely supported (no special kernel modules required)

**Disadvantages:**
- Higher overhead (~10-15% bandwidth overhead)
- Slightly higher latency (~2-5ms additional latency)
- TCP-over-TCP performance issues (when tunneling TCP services)
- More complex implementation

**Technical Details:**
- Uses TLS 1.3 for encryption
- TCP transport (typically port 443)
- Custom framing protocol for multiplexing
- Certificate pinning for security
- Heartbeat mechanism for keep-alive

**Implementation:**
```python
import ssl
import socket

def establish_tls_tunnel(tunnel_config: dict):
    # Request tunnel from controller
    response = requests.post(
        f"{controller_url}/api/tunnels/tls",
        json={"service_id": tunnel_config["service_id"]},
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    tunnel_info = response.json()
    # {
    #   "tunnel_id": "tun-abc123",
    #   "endpoint": "agent.example.com:443",
    #   "certificate_fingerprint": "sha256:...",
    #   "session_key": "..."
    # }
    
    # Create TLS context
    context = ssl.create_default_context()
    context.check_hostname = False
    context.verify_mode = ssl.CERT_REQUIRED
    
    # Load pinned certificate
    context.load_verify_locations(cadata=tunnel_info["certificate"])
    
    # Connect to agent
    sock = socket.create_connection(
        (tunnel_info["endpoint"].split(":")[0], 
         int(tunnel_info["endpoint"].split(":")[1]))
    )
    
    tls_sock = context.wrap_socket(
        sock,
        server_hostname=tunnel_info["endpoint"].split(":")[0]
    )
    
    # Send tunnel initialization
    init_message = {
        "type": "INIT",
        "tunnel_id": tunnel_info["tunnel_id"],
        "session_key": tunnel_info["session_key"]
    }
    
    send_frame(tls_sock, json.dumps(init_message).encode())
    
    # Wait for acknowledgment
    response = receive_frame(tls_sock)
    if json.loads(response)["type"] != "ACK":
        raise TunnelError("Tunnel initialization failed")
    
    return TLSTunnel(tls_sock, tunnel_info["tunnel_id"])
```

### WebSocket Tunnel (Maximum Compatibility)

For highly restrictive networks that block non-HTTP protocols, the client can tunnel traffic over WebSocket connections.

**Advantages:**
- Works through HTTP proxies
- Bypasses most firewall restrictions
- Standard protocol (RFC 6455)
- Browser-compatible (can be used in web-based clients)

**Disadvantages:**
- Highest overhead (~15-20% bandwidth overhead)
- Highest latency (~5-10ms additional latency)
- Requires HTTP/HTTPS infrastructure
- More complex framing and buffering

**Technical Details:**
- Uses WebSocket protocol over HTTPS
- Binary frames for data transfer
- Ping/pong frames for keep-alive
- Automatic reconnection on connection loss

**Implementation:**
```python
import websocket

def establish_websocket_tunnel(tunnel_config: dict):
    # Request tunnel from controller
    response = requests.post(
        f"{controller_url}/api/tunnels/websocket",
        json={"service_id": tunnel_config["service_id"]},
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    tunnel_info = response.json()
    # {
    #   "tunnel_id": "tun-xyz789",
    #   "websocket_url": "wss://agent.example.com/tunnel/tun-xyz789",
    #   "auth_token": "..."
    # }
    
    # Create WebSocket connection
    ws = websocket.WebSocketApp(
        tunnel_info["websocket_url"],
        header={
            "Authorization": f"Bearer {tunnel_info['auth_token']}"
        },
        on_message=on_tunnel_message,
        on_error=on_tunnel_error,
        on_close=on_tunnel_close,
        on_open=on_tunnel_open
    )
    
    # Start WebSocket in background thread
    ws_thread = threading.Thread(target=ws.run_forever)
    ws_thread.daemon = True
    ws_thread.start()
    
    return WebSocketTunnel(ws, tunnel_info["tunnel_id"])
```

## Connection Process

### Step-by-Step Flow

**1. User Initiates Connection**

User selects a service from the client UI and clicks "Connect":

```python
def connect_to_service(service_id: str):
    # Retrieve service details
    service = get_service_details(service_id)
    
    # Check if already connected
    if connection_manager.is_connected(service_id):
        show_notification(f"Already connected to {service['name']}")
        return
    
    # Start connection process
    show_notification(f"Connecting to {service['name']}...")
    connection_manager.connect(service)
```

**2. Authorization Check**

Client requests connection authorization from controller:

```python
def request_connection_authorization(service_id: str):
    response = requests.post(
        f"{controller_url}/api/connections/authorize",
        json={
            "service_id": service_id,
            "requested_duration": 3600  # 1 hour
        },
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    if response.status_code == 403:
        error = response.json()
        if error["reason"] == "approval_required":
            show_notification("Access requires approval. Request sent to manager.")
            return None
        elif error["reason"] == "mfa_required":
            prompt_mfa()
            return request_connection_authorization(service_id)
        else:
            show_error(f"Access denied: {error['message']}")
            return None
    
    return response.json()
```

**3. Service Availability Check**

Controller verifies the service is available:

```python
# Controller-side logic
def check_service_availability(service_id: str):
    service = db.query(Service).filter_by(id=service_id).first()
    
    if not service:
        raise ServiceNotFoundError()
    
    # Check if agent is online
    agent = service.vm.agent
    if not agent.is_online():
        raise AgentOfflineError()
    
    # Check if service is healthy
    if service.health_status != "healthy":
        raise ServiceUnhealthyError()
    
    # Ping agent to confirm service is still available
    agent_response = agent.check_service(service.port)
    if not agent_response.available:
        raise ServiceUnavailableError()
    
    return True
```

**4. Tunnel Credential Generation**

Controller generates ephemeral credentials for the tunnel:

```python
def generate_tunnel_credentials(user_id: str, service_id: str):
    # Generate short-lived session key
    session_key = secrets.token_urlsafe(32)
    
    # Store in Redis with expiration
    redis.setex(
        f"tunnel:session:{session_key}",
        300,  # 5 minutes to establish tunnel
        json.dumps({
            "user_id": user_id,
            "service_id": service_id,
            "created_at": datetime.now().isoformat()
        })
    )
    
    return session_key
```

**5. Protocol Selection**

Client selects appropriate tunnel protocol:

```python
def select_tunnel_protocol(service: dict, user_preferences: dict):
    # Check user preference
    preferred = user_preferences.get("tunnel_protocol", "auto")
    
    if preferred != "auto":
        return preferred
    
    # Auto-select based on network conditions
    if is_wireguard_available() and not is_udp_blocked():
        return "wireguard"
    elif is_port_443_available():
        return "tls"
    else:
        return "websocket"

def is_wireguard_available():
    # Check if WireGuard kernel module is loaded
    try:
        subprocess.run(["wg", "version"], check=True, capture_output=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def is_udp_blocked():
    # Test UDP connectivity to controller
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(2)
        sock.sendto(b"test", (controller_host, 51820))
        sock.recvfrom(1024)
        return False
    except socket.timeout:
        return True
    finally:
        sock.close()
```

**6. Tunnel Establishment**

Client establishes the tunnel using selected protocol:

```python
def establish_tunnel(service: dict, protocol: str, credentials: dict):
    if protocol == "wireguard":
        tunnel = establish_wireguard_tunnel({
            "service_id": service["id"],
            "credentials": credentials
        })
    elif protocol == "tls":
        tunnel = establish_tls_tunnel({
            "service_id": service["id"],
            "credentials": credentials
        })
    elif protocol == "websocket":
        tunnel = establish_websocket_tunnel({
            "service_id": service["id"],
            "credentials": credentials
        })
    else:
        raise ValueError(f"Unknown protocol: {protocol}")
    
    # Wait for tunnel to be ready
    if not tunnel.wait_ready(timeout=10):
        raise TunnelError("Tunnel establishment timed out")
    
    return tunnel
```

**7. Local Port Binding**

Client binds a local port for forwarding:

```python
def bind_local_port(service: dict, tunnel: Tunnel):
    # Determine local port
    local_port = determine_local_port(service)
    
    # Create port forwarder
    forwarder = PortForwarder(
        local_port=local_port,
        tunnel=tunnel,
        remote_host=service["bind_address"],
        remote_port=service["port"]
    )
    
    # Start forwarding
    forwarder.start()
    
    return forwarder

def determine_local_port(service: dict):
    # Check user preference
    if service.get("preferred_local_port"):
        port = service["preferred_local_port"]
        if is_port_available(port):
            return port
    
    # Try to use same port as remote
    if is_port_available(service["port"]):
        return service["port"]
    
    # Find any available port
    return find_available_port()
```

**8. Connection Established**

Connection is now active and ready for use:

```python
def finalize_connection(service: dict, tunnel: Tunnel, forwarder: PortForwarder):
    connection = Connection(
        service_id=service["id"],
        service_name=service["name"],
        tunnel=tunnel,
        forwarder=forwarder,
        established_at=datetime.now()
    )
    
    # Register connection
    connection_manager.register(connection)
    
    # Update UI
    ui.add_active_connection(connection)
    
    # Show notification
    show_notification(
        f"Connected to {service['name']}",
        f"Local port: {forwarder.local_port}"
    )
    
    # Start monitoring
    connection.start_monitoring()
    
    return connection
```

## Tunnel Maintenance

### Keep-Alive Mechanism

Tunnels implement keep-alive to detect connection failures:

**WireGuard:**
```python
# WireGuard has built-in keep-alive
wg_config = f"""
[Peer]
PersistentKeepalive = 25  # Send keep-alive every 25 seconds
"""
```

**TLS Tunnel:**
```python
def tls_keep_alive_loop(tunnel: TLSTunnel):
    while tunnel.is_active():
        try:
            # Send heartbeat
            tunnel.send_frame({
                "type": "HEARTBEAT",
                "timestamp": time.time()
            })
            
            # Wait for response
            response = tunnel.receive_frame(timeout=5)
            if response["type"] != "HEARTBEAT_ACK":
                raise TunnelError("Invalid heartbeat response")
            
            # Update last seen
            tunnel.last_heartbeat = time.time()
            
        except Exception as e:
            logger.error(f"Heartbeat failed: {e}")
            tunnel.mark_unhealthy()
            break
        
        time.sleep(30)  # Heartbeat every 30 seconds
```

**WebSocket Tunnel:**
```python
def websocket_keep_alive(ws: WebSocketApp):
    # WebSocket has built-in ping/pong
    ws.run_forever(ping_interval=30, ping_timeout=10)
```

### Network Change Detection

Client detects network changes and reconnects tunnels:

```python
import netifaces

def monitor_network_changes():
    previous_interfaces = get_active_interfaces()
    
    while True:
        time.sleep(5)
        
        current_interfaces = get_active_interfaces()
        
        if current_interfaces != previous_interfaces:
            logger.info("Network change detected")
            handle_network_change(previous_interfaces, current_interfaces)
            previous_interfaces = current_interfaces

def handle_network_change(old: set, new: set):
    # Interfaces removed
    removed = old - new
    if removed:
        logger.warning(f"Interfaces removed: {removed}")
        # WireGuard handles this automatically
        # TLS/WebSocket tunnels need reconnection
        reconnect_non_wireguard_tunnels()
    
    # Interfaces added
    added = new - old
    if added:
        logger.info(f"Interfaces added: {added}")
        # May have better connectivity now
        optimize_tunnel_routes()
```

### Automatic Reconnection

When a tunnel fails, the client attempts automatic reconnection:

```python
def reconnect_tunnel(connection: Connection, max_attempts: int = 5):
    attempt = 0
    backoff = 1
    
    while attempt < max_attempts:
        attempt += 1
        logger.info(f"Reconnection attempt {attempt}/{max_attempts}")
        
        try:
            # Close old tunnel
            connection.tunnel.close()
            
            # Request new tunnel credentials
            credentials = request_connection_authorization(connection.service_id)
            if not credentials:
                raise AuthorizationError("Failed to get new credentials")
            
            # Establish new tunnel
            new_tunnel = establish_tunnel(
                connection.service,
                connection.tunnel.protocol,
                credentials
            )
            
            # Update connection
            connection.tunnel = new_tunnel
            connection.reconnect_count += 1
            connection.last_reconnect = datetime.now()
            
            # Resume port forwarding
            connection.forwarder.update_tunnel(new_tunnel)
            
            show_notification(f"Reconnected to {connection.service_name}")
            return True
            
        except Exception as e:
            logger.error(f"Reconnection attempt {attempt} failed: {e}")
            
            if attempt < max_attempts:
                time.sleep(backoff)
                backoff = min(backoff * 2, 60)  # Exponential backoff, max 60s
            else:
                show_error(f"Failed to reconnect to {connection.service_name}")
                connection_manager.remove(connection)
                return False
```

## Performance Optimization

### Connection Multiplexing

Multiple local connections can share a single tunnel:

```python
class PortForwarder:
    def __init__(self, local_port: int, tunnel: Tunnel, remote_host: str, remote_port: int):
        self.local_port = local_port
        self.tunnel = tunnel
        self.remote_host = remote_host
        self.remote_port = remote_port
        self.active_streams = {}
    
    def handle_local_connection(self, local_sock: socket.socket):
        # Assign stream ID
        stream_id = self.next_stream_id()
        self.active_streams[stream_id] = local_sock
        
        # Send stream open message
        self.tunnel.send_frame({
            "type": "STREAM_OPEN",
            "stream_id": stream_id,
            "remote_host": self.remote_host,
            "remote_port": self.remote_port
        })
        
        # Forward data bidirectionally
        threading.Thread(
            target=self.forward_stream,
            args=(stream_id, local_sock)
        ).start()
```

### Bandwidth Management

Client can limit bandwidth usage per connection:

```python
class BandwidthLimiter:
    def __init__(self, max_bytes_per_second: int):
        self.max_bytes_per_second = max_bytes_per_second
        self.tokens = max_bytes_per_second
        self.last_update = time.time()
        self.lock = threading.Lock()
    
    def consume(self, bytes_count: int):
        with self.lock:
            # Refill tokens based on time elapsed
            now = time.time()
            elapsed = now - self.last_update
            self.tokens = min(
                self.max_bytes_per_second,
                self.tokens + elapsed * self.max_bytes_per_second
            )
            self.last_update = now
            
            # Wait if not enough tokens
            if bytes_count > self.tokens:
                wait_time = (bytes_count - self.tokens) / self.max_bytes_per_second
                time.sleep(wait_time)
                self.tokens = 0
            else:
                self.tokens -= bytes_count
```

## Security Considerations

### Ephemeral Credentials

Tunnel credentials are short-lived and single-use:

- Generated on-demand for each connection
- Expire after 5 minutes if not used
- Cannot be reused after tunnel establishment
- Revoked immediately when connection closes

### Certificate Pinning

TLS tunnels use certificate pinning to prevent MITM attacks:

```python
def verify_certificate_pinning(cert: bytes, expected_fingerprint: str):
    # Calculate certificate fingerprint
    fingerprint = hashlib.sha256(cert).hexdigest()
    
    # Compare with expected
    if fingerprint != expected_fingerprint:
        raise SecurityError("Certificate fingerprint mismatch - possible MITM attack")
```

### Traffic Encryption

All tunnel protocols provide strong encryption:

- **WireGuard**: ChaCha20-Poly1305 authenticated encryption
- **TLS**: AES-256-GCM or ChaCha20-Poly1305 (TLS 1.3)
- **WebSocket**: TLS encryption (same as TLS tunnel)

## Summary

Tunnel establishment is a sophisticated process that balances security, performance, and compatibility. By supporting multiple tunnel protocols and implementing intelligent protocol selection, the client ensures reliable connectivity across diverse network environments. Automatic reconnection, keep-alive mechanisms, and network change detection provide a seamless user experience even in challenging network conditions.

The use of ephemeral credentials, certificate pinning, and strong encryption ensures that all traffic remains secure, implementing the zero-trust security model at the network layer.
