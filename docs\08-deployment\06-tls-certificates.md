# TLS Certificate Management

Secure the VM Gateway platform with TLS/SSL certificates for encrypted communications.

## Certificate Types

### Let's Encrypt (Recommended)

Free, automated certificates:

```bash
# Install certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d vm-gateway.example.com -d api.vm-gateway.example.com

# Auto-renewal
sudo certbot renew --dry-run
```

### Commercial CA

Purchase from trusted CA:
- DigiCert
- GlobalSign
- Sectigo

### Self-Signed (Development Only)

```bash
# Generate self-signed certificate
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout /etc/ssl/private/vm-gateway.key \
  -out /etc/ssl/certs/vm-gateway.crt \
  -subj "/C=US/ST=State/L=City/O=Organization/CN=vm-gateway.local"
```

## Certificate Installation

### Nginx

```nginx
server {
    listen 443 ssl http2;
    server_name vm-gateway.example.com;
    
    ssl_certificate /etc/letsencrypt/live/vm-gateway.example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/vm-gateway.example.com/privkey.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    
    add_header Strict-Transport-Security "max-age=31536000" always;
}
```

### HAProxy

```
frontend https_front
    bind *:443 ssl crt /etc/ssl/certs/vm-gateway.pem
    
    http-request set-header X-Forwarded-Proto https
    default_backend http_back
```

## Automated Renewal

### Certbot Renewal

```bash
# Add cron job
echo "0 0,12 * * * root certbot renew --quiet" | sudo tee -a /etc/crontab
```

### Renewal Hook

```bash
# /etc/letsencrypt/renewal-hooks/deploy/reload-services.sh
#!/bin/bash
systemctl reload nginx
systemctl reload haproxy
```

## Certificate Monitoring

```python
# check_cert_expiry.py
import ssl
import socket
from datetime import datetime

def check_certificate_expiry(hostname, port=443):
    """Check certificate expiration"""
    context = ssl.create_default_context()
    with socket.create_connection((hostname, port)) as sock:
        with context.wrap_socket(sock, server_hostname=hostname) as ssock:
            cert = ssock.getpeercert()
            expires = datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
            days_left = (expires - datetime.now()).days
            
            if days_left < 30:
                alert(f"Certificate expires in {days_left} days")
            
            return days_left
```

## Best Practices

1. **Use TLS 1.3**: Disable older protocols
2. **Strong ciphers**: Use modern cipher suites
3. **HSTS**: Enable HTTP Strict Transport Security
4. **Certificate pinning**: For mobile apps
5. **Monitor expiry**: Alert 30 days before expiration
6. **Automated renewal**: Use certbot or ACME
7. **Wildcard certificates**: For multiple subdomains

## Next Steps

- [High Availability](07-high-availability.md) - HA configuration
- [Backup Recovery](08-backup-recovery.md) - Backup strategies
