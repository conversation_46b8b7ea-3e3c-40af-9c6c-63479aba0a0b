# DNS Routing

Configure domain-based routing for the VM Gateway platform to provide clean URLs and support multi-tenant deployments.

## DNS Configuration

### Basic Setup

```
# A Records
vm-gateway.example.com        A    ************
api.vm-gateway.example.com    A    ************
ws.vm-gateway.example.com     A    ************

# CNAME Records
www.vm-gateway.example.com    CNAME    vm-gateway.example.com
```

### Multi-Region Setup

```
# Primary Region (US East)
us-east.vm-gateway.example.com    A    ************

# Secondary Region (EU West)
eu-west.vm-gateway.example.com    A    198.51.100.10

# GeoDNS
vm-gateway.example.com    CNAME    us-east.vm-gateway.example.com  # US traffic
vm-gateway.example.com    CNAME    eu-west.vm-gateway.example.com  # EU traffic
```

## Subdomain Routing

### Service-Based Routing

```nginx
# /etc/nginx/sites-available/vm-gateway
server {
    listen 80;
    server_name vm-gateway.example.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name vm-gateway.example.com;
    
    ssl_certificate /etc/ssl/certs/vm-gateway.crt;
    ssl_certificate_key /etc/ssl/private/vm-gateway.key;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}

server {
    listen 443 ssl http2;
    server_name api.vm-gateway.example.com;
    
    ssl_certificate /etc/ssl/certs/vm-gateway.crt;
    ssl_certificate_key /etc/ssl/private/vm-gateway.key;
    
    location / {
        proxy_pass http://localhost:8001;
        proxy_set_header Host $host;
    }
}

server {
    listen 443 ssl http2;
    server_name ws.vm-gateway.example.com;
    
    ssl_certificate /etc/ssl/certs/vm-gateway.crt;
    ssl_certificate_key /etc/ssl/private/vm-gateway.key;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## Load Balancing

### DNS Round Robin

```
vm-gateway.example.com    A    ************
vm-gateway.example.com    A    ************
vm-gateway.example.com    A    ************
```

### Weighted DNS

```
# Primary (70% traffic)
vm-gateway.example.com    A    ************    weight=70

# Secondary (30% traffic)
vm-gateway.example.com    A    ************    weight=30
```

## Health Checks

### DNS Health Monitoring

```python
# health_check.py
import dns.resolver
import requests

def check_dns_health(domain):
    """Check if DNS resolves and endpoints are healthy"""
    try:
        answers = dns.resolver.resolve(domain, 'A')
        for rdata in answers:
            ip = str(rdata)
            response = requests.get(f"http://{ip}/health", timeout=5)
            if response.status_code != 200:
                alert(f"Health check failed for {ip}")
    except Exception as e:
        alert(f"DNS resolution failed for {domain}: {e}")
```

## Best Practices

1. **Use short TTLs**: For faster failover (300-600 seconds)
2. **DNSSEC**: Enable for security
3. **CDN integration**: Use CloudFlare or similar
4. **Monitoring**: Monitor DNS resolution times
5. **Backup DNS**: Use multiple DNS providers
6. **GeoDNS**: Route based on user location

## Next Steps

- [TLS Certificates](06-tls-certificates.md) - Certificate management
- [High Availability](07-high-availability.md) - HA configuration
