/**
 * Theme Management
 * Handles light/dark mode switching with localStorage persistence
 */

(function() {
    'use strict';

    const THEME_KEY = 'vm-gateway-theme';
    const THEME_LIGHT = 'light';
    const THEME_DARK = 'dark';

    // Get saved theme or default to light
    function getSavedTheme() {
        return localStorage.getItem(THEME_KEY) || THEME_LIGHT;
    }

    // Save theme preference
    function saveTheme(theme) {
        localStorage.setItem(THEME_KEY, theme);
    }

    // Apply theme to document
    function applyTheme(theme) {
        const html = document.documentElement;
        const themeIcon = document.getElementById('themeIcon');
        const highlightLight = document.getElementById('highlight-light');
        const highlightDark = document.getElementById('highlight-dark');

        if (theme === THEME_DARK) {
            html.setAttribute('data-theme', 'dark');
            if (themeIcon) themeIcon.textContent = '☀️';
            if (highlightLight) highlightLight.disabled = true;
            if (highlightDark) highlightDark.disabled = false;
        } else {
            html.removeAttribute('data-theme');
            if (themeIcon) themeIcon.textContent = '🌙';
            if (highlightLight) highlightLight.disabled = false;
            if (highlightDark) highlightDark.disabled = true;
        }
    }

    // Toggle between themes
    function toggleTheme() {
        const currentTheme = getSavedTheme();
        const newTheme = currentTheme === THEME_LIGHT ? THEME_DARK : THEME_LIGHT;
        saveTheme(newTheme);
        applyTheme(newTheme);
    }

    // Initialize theme on page load
    function initTheme() {
        const savedTheme = getSavedTheme();
        applyTheme(savedTheme);

        // Add click handler to theme toggle button
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', toggleTheme);
        }
    }

    // Apply theme immediately (before DOM ready) to prevent flash
    applyTheme(getSavedTheme());

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTheme);
    } else {
        initTheme();
    }

    // Expose theme functions globally if needed
    window.vmGatewayTheme = {
        toggle: toggleTheme,
        get: getSavedTheme,
        set: function(theme) {
            saveTheme(theme);
            applyTheme(theme);
        }
    };
})();
