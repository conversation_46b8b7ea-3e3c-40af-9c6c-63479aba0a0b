---
title: "Connection Lifecycle Management"
section: "Client"
order: 7
tags: ["client", "connection", "lifecycle", "management"]
last_updated: "2025-11-08"
---

# Connection Lifecycle Management

## Overview

Connection lifecycle management encompasses the complete journey of a service connection from initial request through active use to graceful termination. The VM Gateway Client implements sophisticated connection management to ensure reliable, secure, and efficient access to remote services while handling network changes, failures, and policy enforcement.

This document details the connection lifecycle, state management, monitoring, and cleanup processes that ensure a robust and user-friendly experience.

## Connection States

### State Diagram

```
                    ┌──────────────┐
                    │  REQUESTED   │
                    └──────┬───────┘
                           │
                    ┌──────▼───────┐
                    │ AUTHORIZING  │
                    └──────┬───────┘
                           │
                    ┌──────▼───────┐
                    │ ESTABLISHING │
                    └──────┬───────┘
                           │
                    ┌──────▼───────┐
                    │  CONNECTED   │◄──────┐
                    └──────┬───────┘       │
                           │               │
                    ┌──────▼───────┐       │
                    │ RECONNECTING │───────┘
                    └──────┬───────┘
                           │
                    ┌──────▼───────┐
                    │ DISCONNECTING│
                    └──────┬───────┘
                           │
                    ┌──────▼───────┐
                    │ DISCONNECTED │
                    └──────────────┘
```

### State Descriptions

**REQUESTED:**
- User has initiated connection request
- Connection object created
- UI shows "Connecting..." status
- No network activity yet

**AUTHORIZING:**
- Requesting authorization from controller
- Checking user permissions
- May require MFA or approval
- Waiting for authorization response

**ESTABLISHING:**
- Authorization granted
- Creating encrypted tunnel
- Binding local port
- Establishing connection to agent

**CONNECTED:**
- Tunnel established and healthy
- Local port bound and forwarding
- Service accessible
- Monitoring active

**RECONNECTING:**
- Connection lost or degraded
- Attempting to re-establish tunnel
- Local port remains bound
- Automatic retry with backoff

**DISCONNECTING:**
- User requested disconnect or policy enforcement
- Closing tunnel gracefully
- Unbinding local port
- Cleaning up resources

**DISCONNECTED:**
- Connection fully closed
- All resources released
- Final state


## Connection Object

### Data Structure

```python
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Optional

class ConnectionState(Enum):
    REQUESTED = "requested"
    AUTHORIZING = "authorizing"
    ESTABLISHING = "establishing"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    DISCONNECTING = "disconnecting"
    DISCONNECTED = "disconnected"

@dataclass
class Connection:
    # Identification
    connection_id: str
    service_id: str
    service_name: str
    service_type: str
    
    # Network details
    remote_host: str
    remote_port: int
    local_port: int
    bind_address: str = "127.0.0.1"
    
    # State
    state: ConnectionState = ConnectionState.REQUESTED
    state_changed_at: datetime = None
    
    # Components
    tunnel: Optional[Tunnel] = None
    forwarder: Optional[PortForwarder] = None
    
    # Lifecycle timestamps
    requested_at: datetime = None
    established_at: Optional[datetime] = None
    disconnected_at: Optional[datetime] = None
    
    # Statistics
    bytes_sent: int = 0
    bytes_received: int = 0
    reconnect_count: int = 0
    error_count: int = 0
    
    # Monitoring
    last_health_check: Optional[datetime] = None
    average_latency: float = 0.0
    
    # Policy
    max_duration: Optional[int] = None  # seconds
    expires_at: Optional[datetime] = None
    
    # User preferences
    auto_reconnect: bool = True
    favorite: bool = False
    
    def __post_init__(self):
        if self.requested_at is None:
            self.requested_at = datetime.now()
        if self.state_changed_at is None:
            self.state_changed_at = datetime.now()
    
    def set_state(self, new_state: ConnectionState):
        """Update connection state"""
        self.state = new_state
        self.state_changed_at = datetime.now()
        logger.info(f"Connection {self.connection_id} state: {new_state.value}")
    
    def is_active(self) -> bool:
        """Check if connection is active"""
        return self.state in [
            ConnectionState.CONNECTED,
            ConnectionState.RECONNECTING
        ]
    
    def duration(self) -> Optional[float]:
        """Get connection duration in seconds"""
        if not self.established_at:
            return None
        
        end_time = self.disconnected_at or datetime.now()
        return (end_time - self.established_at).total_seconds()
    
    def time_remaining(self) -> Optional[float]:
        """Get remaining time before expiration"""
        if not self.expires_at:
            return None
        
        remaining = (self.expires_at - datetime.now()).total_seconds()
        return max(0, remaining)
```

## Connection Manager

### Manager Implementation

```python
class ConnectionManager:
    def __init__(self):
        self.connections: Dict[str, Connection] = {}
        self.lock = threading.Lock()
        self.monitor_thread: Optional[threading.Thread] = None
        self.running = False
    
    def start(self):
        """Start connection manager"""
        self.running = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            daemon=True
        )
        self.monitor_thread.start()
        logger.info("Connection manager started")
    
    def stop(self):
        """Stop connection manager"""
        self.running = False
        
        # Disconnect all connections
        for connection in list(self.connections.values()):
            self.disconnect(connection.connection_id)
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        logger.info("Connection manager stopped")
    
    def connect(self, service: dict, preferences: dict = None) -> Connection:
        """Initiate new connection"""
        # Create connection object
        connection = Connection(
            connection_id=generate_connection_id(),
            service_id=service["id"],
            service_name=service["name"],
            service_type=service["type"],
            remote_host=service["host"],
            remote_port=service["port"],
            local_port=0,  # Will be assigned
            state=ConnectionState.REQUESTED
        )
        
        # Apply preferences
        if preferences:
            connection.auto_reconnect = preferences.get("auto_reconnect", True)
            connection.favorite = preferences.get("favorite", False)
        
        # Register connection
        with self.lock:
            self.connections[connection.connection_id] = connection
        
        # Start connection process in background
        threading.Thread(
            target=self._establish_connection,
            args=(connection,),
            daemon=True
        ).start()
        
        return connection
    
    def _establish_connection(self, connection: Connection):
        """Establish connection (runs in background thread)"""
        try:
            # Update state
            connection.set_state(ConnectionState.AUTHORIZING)
            
            # Request authorization
            auth_response = self._request_authorization(connection)
            if not auth_response:
                raise ConnectionError("Authorization denied")
            
            # Apply policy
            if auth_response.get("max_duration"):
                connection.max_duration = auth_response["max_duration"]
                connection.expires_at = datetime.now() + timedelta(
                    seconds=auth_response["max_duration"]
                )
            
            # Update state
            connection.set_state(ConnectionState.ESTABLISHING)
            
            # Select tunnel protocol
            protocol = select_tunnel_protocol(connection.service_type)
            
            # Establish tunnel
            tunnel = establish_tunnel(
                service_id=connection.service_id,
                protocol=protocol,
                credentials=auth_response["credentials"]
            )
            connection.tunnel = tunnel
            
            # Assign local port
            local_port = assign_local_port(
                connection.service_id,
                connection.remote_port
            )
            connection.local_port = local_port
            
            # Create port forwarder
            forwarder = PortForwarder(
                local_port=local_port,
                tunnel=tunnel,
                remote_host=connection.remote_host,
                remote_port=connection.remote_port
            )
            forwarder.start()
            connection.forwarder = forwarder
            
            # Update state
            connection.set_state(ConnectionState.CONNECTED)
            connection.established_at = datetime.now()
            
            # Notify user
            show_notification(
                "Connected",
                f"Connected to {connection.service_name} on localhost:{local_port}"
            )
            
            logger.info(f"Connection established: {connection.connection_id}")
            
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            connection.set_state(ConnectionState.DISCONNECTED)
            connection.error_count += 1
            
            show_notification(
                "Connection Failed",
                f"Failed to connect to {connection.service_name}: {str(e)}"
            )
    
    def disconnect(self, connection_id: str):
        """Disconnect connection"""
        with self.lock:
            connection = self.connections.get(connection_id)
            if not connection:
                logger.warning(f"Connection not found: {connection_id}")
                return
        
        if connection.state == ConnectionState.DISCONNECTED:
            return
        
        # Update state
        connection.set_state(ConnectionState.DISCONNECTING)
        
        # Stop port forwarder
        if connection.forwarder:
            connection.forwarder.stop()
        
        # Close tunnel
        if connection.tunnel:
            connection.tunnel.close()
        
        # Update state
        connection.set_state(ConnectionState.DISCONNECTED)
        connection.disconnected_at = datetime.now()
        
        # Notify user
        show_notification(
            "Disconnected",
            f"Disconnected from {connection.service_name}"
        )
        
        logger.info(f"Connection disconnected: {connection_id}")
    
    def reconnect(self, connection_id: str):
        """Manually reconnect connection"""
        with self.lock:
            connection = self.connections.get(connection_id)
            if not connection:
                return
        
        if connection.state != ConnectionState.CONNECTED:
            return
        
        # Trigger reconnection
        connection.set_state(ConnectionState.RECONNECTING)
        self._attempt_reconnection(connection)
    
    def _attempt_reconnection(self, connection: Connection):
        """Attempt to reconnect failed connection"""
        max_attempts = 5
        attempt = 0
        backoff = 1
        
        while attempt < max_attempts and connection.auto_reconnect:
            attempt += 1
            logger.info(
                f"Reconnection attempt {attempt}/{max_attempts} "
                f"for {connection.connection_id}"
            )
            
            try:
                # Close old tunnel
                if connection.tunnel:
                    connection.tunnel.close()
                
                # Request new authorization
                auth_response = self._request_authorization(connection)
                if not auth_response:
                    raise ConnectionError("Authorization denied")
                
                # Establish new tunnel
                protocol = select_tunnel_protocol(connection.service_type)
                tunnel = establish_tunnel(
                    service_id=connection.service_id,
                    protocol=protocol,
                    credentials=auth_response["credentials"]
                )
                
                # Update connection
                connection.tunnel = tunnel
                connection.forwarder.update_tunnel(tunnel)
                connection.set_state(ConnectionState.CONNECTED)
                connection.reconnect_count += 1
                
                show_notification(
                    "Reconnected",
                    f"Reconnected to {connection.service_name}"
                )
                
                logger.info(f"Reconnection successful: {connection.connection_id}")
                return
                
            except Exception as e:
                logger.error(f"Reconnection attempt {attempt} failed: {e}")
                connection.error_count += 1
                
                if attempt < max_attempts:
                    time.sleep(backoff)
                    backoff = min(backoff * 2, 60)
        
        # All attempts failed
        logger.error(f"Reconnection failed after {max_attempts} attempts")
        connection.set_state(ConnectionState.DISCONNECTED)
        
        show_notification(
            "Reconnection Failed",
            f"Failed to reconnect to {connection.service_name}"
        )
    
    def _monitor_loop(self):
        """Monitor all connections"""
        while self.running:
            try:
                with self.lock:
                    connections = list(self.connections.values())
                
                for connection in connections:
                    if connection.is_active():
                        self._check_connection_health(connection)
                        self._check_connection_expiration(connection)
                
            except Exception as e:
                logger.error(f"Monitor loop error: {e}")
            
            time.sleep(10)  # Check every 10 seconds
    
    def _check_connection_health(self, connection: Connection):
        """Check connection health"""
        try:
            # Measure latency
            start = time.time()
            connection.tunnel.ping()
            latency = (time.time() - start) * 1000  # ms
            
            # Update average latency
            if connection.average_latency == 0:
                connection.average_latency = latency
            else:
                connection.average_latency = (
                    connection.average_latency * 0.9 + latency * 0.1
                )
            
            connection.last_health_check = datetime.now()
            
            # Check for high latency
            if latency > 200:  # 200ms threshold
                logger.warning(
                    f"High latency detected: {latency:.1f}ms "
                    f"on {connection.connection_id}"
                )
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            connection.error_count += 1
            
            # Trigger reconnection if too many errors
            if connection.error_count >= 3:
                connection.set_state(ConnectionState.RECONNECTING)
                self._attempt_reconnection(connection)
    
    def _check_connection_expiration(self, connection: Connection):
        """Check if connection has expired"""
        if not connection.expires_at:
            return
        
        time_remaining = connection.time_remaining()
        
        # Warn user when approaching expiration
        if time_remaining and time_remaining <= 300:  # 5 minutes
            show_notification(
                "Connection Expiring Soon",
                f"{connection.service_name}: {int(time_remaining/60)} minutes remaining"
            )
        
        # Disconnect when expired
        if time_remaining == 0:
            logger.info(f"Connection expired: {connection.connection_id}")
            self.disconnect(connection.connection_id)
    
    def _request_authorization(self, connection: Connection) -> dict:
        """Request connection authorization from controller"""
        response = requests.post(
            f"{controller_url}/api/connections/authorize",
            json={
                "service_id": connection.service_id,
                "requested_duration": connection.max_duration
            },
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        if response.status_code == 200:
            return response.json()
        elif response.status_code == 403:
            error = response.json()
            if error["reason"] == "approval_required":
                show_notification(
                    "Approval Required",
                    f"Access to {connection.service_name} requires approval"
                )
            return None
        else:
            raise ConnectionError(f"Authorization failed: {response.status_code}")
    
    def get_connection(self, connection_id: str) -> Optional[Connection]:
        """Get connection by ID"""
        with self.lock:
            return self.connections.get(connection_id)
    
    def get_all_connections(self) -> List[Connection]:
        """Get all connections"""
        with self.lock:
            return list(self.connections.values())
    
    def get_active_connections(self) -> List[Connection]:
        """Get all active connections"""
        with self.lock:
            return [c for c in self.connections.values() if c.is_active()]
```

## Connection Persistence

### Saving Connection State

```python
class ConnectionPersistence:
    def __init__(self, db_path: str):
        self.db = sqlite3.connect(db_path)
        self._create_tables()
    
    def _create_tables(self):
        self.db.execute("""
            CREATE TABLE IF NOT EXISTS connections (
                connection_id TEXT PRIMARY KEY,
                service_id TEXT NOT NULL,
                service_name TEXT NOT NULL,
                local_port INTEGER,
                established_at TIMESTAMP,
                disconnected_at TIMESTAMP,
                bytes_sent INTEGER,
                bytes_received INTEGER,
                reconnect_count INTEGER,
                average_latency REAL
            )
        """)
        
        self.db.execute("""
            CREATE TABLE IF NOT EXISTS connection_profiles (
                profile_id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                services TEXT NOT NULL,  -- JSON array
                auto_connect BOOLEAN,
                created_at TIMESTAMP,
                last_used TIMESTAMP
            )
        """)
    
    def save_connection(self, connection: Connection):
        """Save connection to database"""
        self.db.execute("""
            INSERT OR REPLACE INTO connections
            (connection_id, service_id, service_name, local_port,
             established_at, disconnected_at, bytes_sent, bytes_received,
             reconnect_count, average_latency)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            connection.connection_id,
            connection.service_id,
            connection.service_name,
            connection.local_port,
            connection.established_at,
            connection.disconnected_at,
            connection.bytes_sent,
            connection.bytes_received,
            connection.reconnect_count,
            connection.average_latency
        ))
        self.db.commit()
    
    def get_connection_history(
        self,
        service_id: str = None,
        limit: int = 100
    ) -> List[dict]:
        """Get connection history"""
        query = "SELECT * FROM connections"
        params = []
        
        if service_id:
            query += " WHERE service_id = ?"
            params.append(service_id)
        
        query += " ORDER BY established_at DESC LIMIT ?"
        params.append(limit)
        
        cursor = self.db.execute(query, params)
        
        return [dict(row) for row in cursor.fetchall()]
    
    def save_profile(self, profile: dict):
        """Save connection profile"""
        self.db.execute("""
            INSERT OR REPLACE INTO connection_profiles
            (profile_id, name, services, auto_connect, created_at, last_used)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            profile["id"],
            profile["name"],
            json.dumps(profile["services"]),
            profile.get("auto_connect", False),
            profile.get("created_at", datetime.now()),
            profile.get("last_used")
        ))
        self.db.commit()
    
    def get_profiles(self) -> List[dict]:
        """Get all connection profiles"""
        cursor = self.db.execute("""
            SELECT * FROM connection_profiles
            ORDER BY last_used DESC
        """)
        
        profiles = []
        for row in cursor.fetchall():
            profile = dict(row)
            profile["services"] = json.loads(profile["services"])
            profiles.append(profile)
        
        return profiles
```

## Auto-Reconnect on Startup

### Restore Previous Connections

```python
def restore_connections_on_startup():
    """Restore connections from previous session"""
    # Load saved preferences
    preferences = load_preferences()
    
    if not preferences.get("reconnect_on_startup", False):
        return
    
    # Get connection history
    persistence = ConnectionPersistence(db_path)
    recent_connections = persistence.get_connection_history(limit=10)
    
    # Filter to connections that were active when app closed
    active_connections = [
        c for c in recent_connections
        if c["disconnected_at"] is None
    ]
    
    if not active_connections:
        return
    
    # Ask user if they want to reconnect
    response = prompt_user(
        f"Reconnect to {len(active_connections)} previous connections?",
        options=["Yes", "No", "Choose"]
    )
    
    if response == "No":
        return
    
    if response == "Choose":
        # Show selection dialog
        selected = show_connection_selector(active_connections)
        active_connections = selected
    
    # Reconnect to selected connections
    for conn_data in active_connections:
        try:
            service = get_service_details(conn_data["service_id"])
            connection_manager.connect(service)
        except Exception as e:
            logger.error(f"Failed to restore connection: {e}")
```

## Connection Cleanup

### Resource Cleanup

```python
def cleanup_connection(connection: Connection):
    """Clean up connection resources"""
    # Stop port forwarder
    if connection.forwarder:
        try:
            connection.forwarder.stop()
        except Exception as e:
            logger.error(f"Error stopping forwarder: {e}")
    
    # Close tunnel
    if connection.tunnel:
        try:
            connection.tunnel.close()
        except Exception as e:
            logger.error(f"Error closing tunnel: {e}")
    
    # Remove firewall rules (if any)
    try:
        remove_firewall_rule(connection.local_port)
    except Exception as e:
        logger.warning(f"Error removing firewall rule: {e}")
    
    # Save final state
    try:
        persistence.save_connection(connection)
    except Exception as e:
        logger.error(f"Error saving connection state: {e}")
    
    logger.info(f"Connection cleanup complete: {connection.connection_id}")
```

### Graceful Shutdown

```python
def shutdown_client():
    """Gracefully shutdown client"""
    logger.info("Shutting down client...")
    
    # Get all active connections
    active_connections = connection_manager.get_active_connections()
    
    if active_connections:
        # Notify user
        show_notification(
            "Shutting Down",
            f"Disconnecting {len(active_connections)} active connections..."
        )
        
        # Disconnect all connections
        for connection in active_connections:
            connection_manager.disconnect(connection.connection_id)
    
    # Stop connection manager
    connection_manager.stop()
    
    # Save preferences
    save_preferences()
    
    # Clean up temporary files
    cleanup_temp_files()
    
    logger.info("Client shutdown complete")
```

## Summary

Connection lifecycle management is a critical component of the VM Gateway Client, ensuring reliable and secure access to remote services. The connection manager handles the complete lifecycle from initial request through active monitoring to graceful termination, implementing automatic reconnection, health monitoring, and policy enforcement.

By maintaining detailed connection state, persisting connection history, and providing automatic reconnection capabilities, the client delivers a robust and user-friendly experience even in challenging network conditions. The connection manager's monitoring capabilities ensure that issues are detected early and handled gracefully, minimizing disruption to user workflows.
