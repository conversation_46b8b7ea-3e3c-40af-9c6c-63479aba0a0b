---
title: "System Overview"
section: "Architecture"
order: 1
tags: ["architecture", "overview", "components", "design"]
last_updated: "2025-11-08"
---

# System Overview

## Introduction

The VM Network Gateway & Access Control Platform is a comprehensive, self-hosted solution for managing network access to services across multiple virtual machines. The platform implements a zero-trust security model with automatic service discovery, granular role-based access control (RBAC), and secure remote port forwarding capabilities. This document provides a high-level architectural overview of the system's components, their interactions, and the design principles that guide the platform.

## Architectural Philosophy

### Zero-Trust Security Model

The platform is built on zero-trust principles, meaning no implicit trust is granted based on network location. Every access request is authenticated, authorized, and encrypted regardless of where it originates. Unlike traditional VPN solutions that provide broad network access, this platform grants access only to specific services on a per-request basis.

Key zero-trust principles implemented:

- **Verify Explicitly**: Every access request requires authentication and authorization
- **Least Privilege Access**: Users receive only the minimum permissions needed
- **Assume Breach**: All communications are encrypted, all actions are logged
- **Continuous Validation**: Tokens expire, sessions timeout, permissions are re-evaluated
- **Microsegmentation**: Access is granted per-service, not per-network

### Design Principles

The platform architecture follows several core design principles:

**Separation of Concerns**: Each component has a well-defined responsibility. The Agent handles discovery and metrics, the Controller manages access control and orchestration, and the Client provides user-facing connectivity.

**Scalability**: The architecture supports horizontal scaling of Controllers and can manage thousands of VMs and services. Components communicate asynchronously where possible to avoid blocking operations.

**Resilience**: Components continue operating during network partitions or component failures. Agents cache data locally, Controllers can run in high-availability configurations, and Clients handle reconnection automatically.

**Security by Default**: All network communication is encrypted, credentials are never stored in plaintext, and audit logging is comprehensive and immutable.

**Extensibility**: The platform provides well-defined APIs and plugin interfaces for custom integrations, authentication providers, and service classifiers.

**Observability**: Every component emits detailed metrics, logs, and traces. The platform provides comprehensive visibility into system health, performance, and user activity.

## High-Level Architecture

### Component Overview

The platform consists of three primary components that work together to provide secure service access:

```
┌─────────────────────────────────────────────────────────────────────┐
│                           Users & Clients                           │
│                                                                     │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────────────────┐ │
│  │   Browser    │  │   Browser    │  │   Desktop Client         │ │
│  │   (Admin)    │  │   (User)     │  │   (Windows/Mac/Linux)    │ │
│  └──────┬───────┘  └──────┬───────┘  └──────────┬───────────────┘ │
└─────────┼──────────────────┼──────────────────────┼─────────────────┘
          │                  │                      │
          │ HTTPS            │ HTTPS                │ WSS/HTTPS
          │ (Web UI)         │ (Proxy)              │ (Tunnels)
          │                  │                      │
┌─────────▼──────────────────▼──────────────────────▼─────────────────┐
│                    Controller & Web Interface                       │
│                                                                     │
│  ┌────────────────────────────────────────────────────────────┐   │
│  │  Web Interface (React)                                     │   │
│  │  - Dashboard & Monitoring                                  │   │
│  │  - Service Catalog Browser                                 │   │
│  │  - User & Access Management                                │   │
│  │  - Configuration & Settings                                │   │
│  └────────────────────────────────────────────────────────────┘   │
│                                                                     │
│  ┌────────────────────────────────────────────────────────────┐   │
│  │  API Server (FastAPI)                                      │   │
│  │  - REST API Endpoints                                      │   │
│  │  - WebSocket Server (Real-time updates)                    │   │
│  │  - Authentication & Authorization                          │   │
│  │  - Service Catalog Management                              │   │
│  │  - Connection Management                                   │   │
│  │  - Metrics Aggregation                                     │   │
│  │  - Alert Management                                        │   │
│  └────────────────────────────────────────────────────────────┘   │
│                                                                     │
│  ┌────────────────────────────────────────────────────────────┐   │
│  │  HTTP/HTTPS Proxy                                          │   │
│  │  - Reverse proxy for web services                          │   │
│  │  - URL rewriting & session management                      │   │
│  │  - WebSocket proxying                                      │   │
│  └────────────────────────────────────────────────────────────┘   │
│                                                                     │
│  ┌────────────────────────────────────────────────────────────┐   │
│  │  Tunnel Server                                             │   │
│  │  - WireGuard/OpenVPN server                                │   │
│  │  - Connection orchestration                                │   │
│  │  - Traffic routing                                         │   │
│  └────────────────────────────────────────────────────────────┘   │
│                                                                     │
│  ┌────────────────────────────────────────────────────────────┐   │
│  │  Data Layer                                                │   │
│  │  - PostgreSQL (persistent data)                            │   │
│  │  - Redis (cache, sessions, pub/sub)                        │   │
│  │  - Secrets Vault (credentials, keys)                       │   │
│  └────────────────────────────────────────────────────────────┘   │
└─────────────────────────┬───────────────────────────────────────────┘
                          │
                          │ WebSocket + REST (mTLS)
                          │ Bidirectional communication
                          │
┌─────────────────────────┴───────────────────────────────────────────┐
│                    VM Fleet (Monitored Infrastructure)              │
│                                                                     │
│  ┌──────────────────────┐  ┌──────────────────────┐  ┌──────────┐ │
│  │  VM 1 (Web Server)   │  │  VM 2 (Database)     │  │  VM N    │ │
│  │  ┌────────────────┐  │  │  ┌────────────────┐  │  │  ┌────┐  │ │
│  │  │  Agent         │  │  │  │  Agent         │  │  │  │Agnt│  │ │
│  │  │  - Discovery   │  │  │  │  - Discovery   │  │  │  │    │  │ │
│  │  │  - Metrics     │  │  │  │  - Metrics     │  │  │  │    │  │ │
│  │  │  - Tunnel      │  │  │  │  - Tunnel      │  │  │  │    │  │ │
│  │  └────────────────┘  │  │  └────────────────┘  │  │  └────┘  │ │
│  │  ┌────────────────┐  │  │  ┌────────────────┐  │  │  ┌────┐  │ │
│  │  │  Services      │  │  │  │  Services      │  │  │  │Svcs│  │ │
│  │  │  - Nginx:80    │  │  │  │  - Postgres:   │  │  │  │... │  │ │
│  │  │  - Node:3000   │  │  │  │    5432        │  │  │  │    │  │ │
│  │  │  - Redis:6379  │  │  │  │  - Redis:6379  │  │  │  │    │  │ │
│  │  └────────────────┘  │  │  └────────────────┘  │  │  └────┘  │ │
│  └──────────────────────┘  └──────────────────────┘  └──────────┘ │
└─────────────────────────────────────────────────────────────────────┘
```

### Component Responsibilities

**Agent (Deployed on each VM)**:
- Automatically discovers services running on the VM by scanning listening ports
- Classifies services using intelligent pattern matching and optional ML
- Collects system and service metrics (CPU, memory, network, custom metrics)
- Maintains persistent connection to Controller for real-time updates
- Acts as tunnel endpoint for secure port forwarding
- Operates autonomously when disconnected from Controller

**Controller (Centralized management plane)**:
- Provides web-based management interface for administrators and users
- Maintains centralized service catalog aggregated from all Agents
- Enforces authentication and authorization policies
- Manages user accounts, roles, and permissions
- Orchestrates secure connections between Clients and services
- Aggregates metrics and generates alerts
- Provides REST API for programmatic access
- Acts as HTTP/HTTPS proxy for web-based services
- Stores audit logs for compliance and forensics

**Client (Desktop application)**:
- Provides user-friendly interface for discovering and connecting to services
- Establishes secure tunnels to services through Controller and Agent
- Manages local port forwarding (e.g., localhost:5432 → remote PostgreSQL)
- Handles authentication and token management
- Displays connection status and statistics
- Supports connection profiles for frequently accessed services
- Available for Windows, macOS, and Linux

## Data Flow Patterns

### Service Discovery Flow

The service discovery process runs continuously on each Agent:

```
1. Agent scans listening ports on VM (every 60 seconds by default)
   ↓
2. Agent maps ports to processes using OS APIs
   ↓
3. Agent classifies services based on:
   - Process name and executable path
   - Port number conventions
   - Protocol detection
   - Optional ML-based classification
   ↓
4. Agent detects changes by comparing with previous scan
   ↓
5. Agent sends updates to Controller via WebSocket
   ↓
6. Controller updates service catalog in database
   ↓
7. Controller broadcasts changes to connected web clients
   ↓
8. Web interface updates in real-time
```

**Key Characteristics**:
- **Automatic**: No manual service registration required
- **Real-time**: Changes detected within scan interval (typically 60 seconds)
- **Resilient**: Agent caches locally if Controller unavailable
- **Efficient**: Only changes are transmitted, not full catalog

### Authentication Flow

User authentication follows a multi-step process with optional MFA:

```
1. User enters credentials in web interface or client
   ↓
2. Controller validates credentials against auth backend
   (Local database, LDAP, Active Directory, or SSO)
   ↓
3. If MFA enabled, Controller requests second factor
   (TOTP, WebAuthn, SMS, or Email)
   ↓
4. User provides second factor
   ↓
5. Controller validates second factor
   ↓
6. Controller generates JWT access token and refresh token
   ↓
7. Controller creates session in Redis
   ↓
8. Tokens returned to user
   ↓
9. User includes access token in subsequent requests
   ↓
10. Controller validates token on each request
    ↓
11. If token expired, user can refresh using refresh token
```

**Security Features**:
- **Short-lived tokens**: Access tokens expire after 1 hour (configurable)
- **Refresh tokens**: Long-lived tokens (30 days) for obtaining new access tokens
- **Token revocation**: Tokens can be revoked immediately
- **Device tracking**: Sessions tied to specific devices
- **IP validation**: Optional IP address validation
- **Concurrent session limits**: Configurable per user

### Connection Establishment Flow

When a user requests access to a service, the following flow occurs:

```
1. User selects service in web interface or client
   ↓
2. Client/Browser sends connection request to Controller API
   ↓
3. Controller validates user authentication
   ↓
4. Controller checks user permissions for this service
   (RBAC, time-based restrictions, approval workflows)
   ↓
5. If approval required, Controller creates approval request
   and notifies approvers
   ↓
6. Once approved (or if no approval needed), Controller:
   - Allocates tunnel resources
   - Generates connection credentials
   - Notifies Agent to prepare for connection
   ↓
7. Controller returns connection details to Client
   ↓
8. Client establishes tunnel to Controller
   ↓
9. Controller routes tunnel traffic to Agent
   ↓
10. Agent forwards traffic to local service
    ↓
11. Service responds, traffic flows back through tunnel
    ↓
12. Connection remains active until:
    - User terminates connection
    - Connection expires (time-based)
    - Permissions revoked
    - Service becomes unavailable
```

**Connection Types**:
- **Tunnel**: Encrypted tunnel for TCP/UDP services (databases, SSH, etc.)
- **Proxy**: HTTP/HTTPS proxy for web services (accessed via browser)

### Metrics Collection Flow

Metrics flow from Agents to Controller for aggregation and visualization:

```
1. Agent collects metrics from VM and services
   - System metrics: CPU, memory, disk, network
   - Service metrics: connections, requests, latency
   - Custom metrics: Application-specific metrics
   ↓
2. Agent buffers metrics locally (SQLite)
   ↓
3. Agent sends metrics to Controller periodically (every 60 seconds)
   ↓
4. Controller receives and validates metrics
   ↓
5. Controller stores metrics in PostgreSQL (time-series optimized)
   ↓
6. Controller evaluates alert rules against metrics
   ↓
7. If alert triggered, Controller:
   - Creates alert record
   - Sends notifications (email, Slack, webhook)
   - Updates web interface
   ↓
8. Web interface queries metrics for dashboards
   ↓
9. Grafana (optional) queries metrics via API
```

**Metrics Retention**:
- **Raw metrics**: 7 days (configurable)
- **1-minute aggregates**: 30 days
- **5-minute aggregates**: 90 days
- **1-hour aggregates**: 1 year
- **1-day aggregates**: Forever

## Communication Protocols

### Agent ↔ Controller Communication

**Primary Protocol**: WebSocket over TLS (WSS)

**Why WebSocket**:
- Bidirectional communication (Controller can push updates to Agent)
- Persistent connection reduces latency
- Efficient for real-time updates
- Supports both text (JSON) and binary data

**Connection Establishment**:
1. Agent initiates WebSocket connection to Controller
2. Agent authenticates using pre-shared key or certificate
3. Controller validates Agent identity
4. Connection established, heartbeat begins

**Message Types**:
- **Service Update**: Agent reports discovered services
- **Metrics**: Agent sends collected metrics
- **Command**: Controller sends commands to Agent (rescan, restart, etc.)
- **Heartbeat**: Keep-alive messages every 30 seconds
- **Acknowledgment**: Confirm message receipt

**Fallback**: If WebSocket unavailable, Agent falls back to HTTP polling

**Security**:
- **mTLS**: Mutual TLS authentication (both Agent and Controller verify each other)
- **Encryption**: All data encrypted with TLS 1.3
- **Certificate pinning**: Agent pins Controller certificate to prevent MITM

### Client ↔ Controller Communication

**REST API**: HTTPS for API requests

**WebSocket**: WSS for real-time updates (service changes, alerts, etc.)

**Tunnel Protocol**: WireGuard or OpenVPN for secure port forwarding

**Authentication**: JWT bearer tokens in Authorization header

### Controller ↔ Database Communication

**PostgreSQL**: Standard PostgreSQL protocol over TLS

**Redis**: Redis protocol over TLS (optional)

**Connection Pooling**: SQLAlchemy connection pool for PostgreSQL

## Security Architecture

### Defense in Depth

The platform implements multiple layers of security:

**Layer 1: Network Security**
- All communication encrypted with TLS 1.3
- mTLS for Agent-Controller communication
- Firewall rules restrict access to Controller
- DDoS protection at load balancer

**Layer 2: Authentication**
- Multi-factor authentication (TOTP, WebAuthn, SMS)
- Strong password policies with breach detection
- Account lockout after failed attempts
- Session management with device tracking

**Layer 3: Authorization**
- Granular RBAC with resource-level permissions
- Time-based access restrictions
- IP whitelisting
- Approval workflows for sensitive resources
- Conditional access policies

**Layer 4: Data Protection**
- Secrets encrypted at rest (AES-256-GCM)
- Database encryption (PostgreSQL TDE)
- Secure credential storage (HashiCorp Vault integration)
- PII masking in logs

**Layer 5: Audit & Monitoring**
- Comprehensive audit logging
- Immutable log storage
- Real-time security alerts
- Anomaly detection
- Compliance reporting

### Threat Model

The platform is designed to protect against:

**External Threats**:
- Unauthorized access attempts
- Credential stuffing attacks
- DDoS attacks
- Man-in-the-middle attacks
- SQL injection and XSS attacks

**Internal Threats**:
- Privilege escalation
- Lateral movement
- Data exfiltration
- Insider threats
- Compromised credentials

**Operational Threats**:
- Configuration errors
- Unpatched vulnerabilities
- Weak passwords
- Excessive permissions
- Lack of monitoring

## Scalability Considerations

### Horizontal Scaling

**Controller Scaling**:
- Multiple Controller instances behind load balancer
- Stateless design (state in Redis/PostgreSQL)
- Session affinity not required
- WebSocket connections distributed across instances

**Agent Scaling**:
- Each VM runs independent Agent
- No coordination required between Agents
- Agents can handle thousands of services per VM

**Database Scaling**:
- PostgreSQL read replicas for read-heavy workloads
- Connection pooling to limit database connections
- Query optimization and indexing
- Partitioning for large tables (metrics, logs)

**Redis Scaling**:
- Redis Sentinel for high availability
- Redis Cluster for horizontal scaling
- Separate Redis instances for different use cases (cache, sessions, pub/sub)

### Vertical Scaling

**Controller Resources**:
- CPU: 4-8 cores for typical deployments
- Memory: 8-16 GB for typical deployments
- Disk: SSD for database and logs

**Agent Resources**:
- CPU: < 1% during normal operation
- Memory: 50-100 MB typical
- Disk: Minimal (local cache only)

### Performance Targets

**Service Discovery**:
- Scan 100 services in < 5 seconds
- Detect changes within 60 seconds
- Support 10,000+ services per Controller

**Connection Establishment**:
- Establish tunnel in < 2 seconds
- Support 1,000+ concurrent connections per Controller
- Latency overhead < 10ms

**API Performance**:
- API response time < 100ms (p95)
- Support 1,000+ requests per second per Controller
- WebSocket message latency < 50ms

**Metrics Collection**:
- Collect metrics from 1,000+ VMs
- Process 100,000+ metrics per minute
- Query response time < 1 second for dashboards

## High Availability

### Controller HA

**Active-Active Configuration**:
- Multiple Controller instances running simultaneously
- Load balancer distributes traffic
- Shared state in PostgreSQL and Redis
- No single point of failure

**Failover**:
- Load balancer health checks detect failed instances
- Traffic automatically routed to healthy instances
- WebSocket connections reconnect automatically
- No data loss during failover

### Database HA

**PostgreSQL**:
- Primary-replica replication (streaming replication)
- Automatic failover with Patroni or similar
- Point-in-time recovery (PITR)
- Regular backups to object storage

**Redis**:
- Redis Sentinel for automatic failover
- Master-replica replication
- Persistence enabled (RDB + AOF)

### Agent Resilience

**Autonomous Operation**:
- Agent continues discovery and metrics collection when disconnected
- Local cache in SQLite
- Automatic reconnection with exponential backoff
- Queued updates sent when connection restored

## Disaster Recovery

### Backup Strategy

**Database Backups**:
- Full backup daily
- Incremental backups every 6 hours
- Transaction log archiving for PITR
- Backups stored in multiple locations
- Retention: 30 days

**Configuration Backups**:
- Version-controlled configuration files
- Automated backup of Controller configuration
- Secrets backed up to secure location

**Recovery Time Objective (RTO)**: < 1 hour

**Recovery Point Objective (RPO)**: < 6 hours

### Disaster Scenarios

**Controller Failure**:
- Restore from backup
- Reconfigure load balancer
- Agents reconnect automatically

**Database Corruption**:
- Restore from most recent backup
- Replay transaction logs to minimize data loss
- Verify data integrity

**Complete Site Loss**:
- Restore in alternate datacenter
- Update DNS to point to new location
- Agents reconnect to new Controller

## Monitoring & Observability

### Metrics

**System Metrics**:
- CPU, memory, disk, network usage
- Process counts and resource usage
- Database connection pool stats
- Redis memory usage and hit rate

**Application Metrics**:
- API request rate, latency, error rate
- WebSocket connection count
- Active tunnel count
- Service discovery scan duration
- Authentication success/failure rate

**Business Metrics**:
- Active users
- Services discovered
- Connections established
- Data transferred

### Logging

**Structured Logging**:
- JSON-formatted logs
- Correlation IDs for request tracing
- Log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL
- Contextual information (user, service, VM)

**Log Aggregation**:
- Centralized logging (ELK, Splunk, or similar)
- Log retention: 90 days
- Full-text search
- Real-time log tailing

### Tracing

**Distributed Tracing** (Optional):
- OpenTelemetry instrumentation
- Trace requests across components
- Identify performance bottlenecks
- Visualize request flow

### Alerting

**Alert Types**:
- Service health alerts
- System resource alerts
- Security alerts (failed logins, permission changes)
- Performance alerts (high latency, error rate)

**Alert Channels**:
- Email
- Slack/Teams
- PagerDuty
- Webhooks

## Deployment Models

### Single-Server Deployment

**Use Case**: Small deployments (< 50 VMs)

**Architecture**:
- Controller, PostgreSQL, and Redis on single VM
- Agents on monitored VMs
- Simple to deploy and manage

**Limitations**:
- Single point of failure
- Limited scalability
- Not suitable for production

### Multi-Server Deployment

**Use Case**: Medium deployments (50-500 VMs)

**Architecture**:
- Controller on dedicated VM (or multiple for HA)
- PostgreSQL on dedicated VM with replication
- Redis on dedicated VM with Sentinel
- Agents on monitored VMs

**Benefits**:
- Better performance
- High availability
- Scalable

### Distributed Deployment

**Use Case**: Large deployments (500+ VMs, multiple regions)

**Architecture**:
- Multiple Controller instances across regions
- Regional PostgreSQL clusters with cross-region replication
- Redis clusters per region
- Domain-based routing to nearest Controller

**Benefits**:
- Geographic distribution
- Reduced latency
- Maximum scalability
- Disaster recovery

## Technology Stack Summary

**Backend**:
- Python 3.11+ (FastAPI, SQLAlchemy, Celery)
- PostgreSQL 14+ (primary database)
- Redis 7+ (cache, sessions, pub/sub)

**Frontend**:
- React 18+ (web interface)
- TypeScript (type safety)
- Tailwind CSS + shadcn/ui (UI components)
- Zustand (state management)

**Client**:
- Electron (cross-platform desktop app)
- React (UI)
- Python (backend logic)

**Infrastructure**:
- Docker (containerization)
- Kubernetes (orchestration, optional)
- Nginx (reverse proxy, load balancer)
- WireGuard/OpenVPN (tunneling)

**Monitoring**:
- Prometheus (metrics)
- Grafana (visualization)
- ELK Stack (logging, optional)

## Next Steps

For deeper dives into specific aspects of the architecture:

- **[Component Interaction](02-component-interaction.md)**: Detailed component communication patterns
- **[Data Flow](03-data-flow.md)**: Data flow diagrams and sequences
- **[Security Model](04-security-model.md)**: Comprehensive security architecture
- **[Deployment Models](05-deployment-models.md)**: Deployment options and configurations

For component-specific documentation:

- **[Agent Architecture](../03-agent/01-overview.md)**: Agent design and implementation
- **[Controller Architecture](../04-controller/01-overview.md)**: Controller design and implementation
- **[Client Architecture](../05-client/01-overview.md)**: Client design and implementation