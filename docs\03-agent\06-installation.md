---
title: "Agent Installation and Configuration"
section: "Agent"
order: 6
tags: ["agent", "installation", "configuration", "deployment"]
last_updated: "2025-11-08"
---

# Agent Installation and Configuration

## Introduction

This guide provides comprehensive instructions for installing, configuring, and managing the VM Gateway Agent across different operating systems and deployment scenarios. The agent is designed for easy deployment with sensible defaults while offering extensive configuration options for advanced use cases.

## Prerequisites

### System Requirements

**Minimum Requirements:**
- CPU: 1 core
- RAM: 512 MB
- Disk: 1 GB free space
- Network: Outbound connectivity to controller

**Recommended Requirements:**
- CPU: 2 cores
- RAM: 1 GB
- Disk: 5 GB free space (for metrics storage)
- Network: Low-latency connection to controller

### Supported Operating Systems

**Linux:**
- Ubuntu 20.04 LTS or later
- Debian 10 or later
- RHEL/CentOS 8 or later
- Fedora 35 or later
- Amazon Linux 2
- SUSE Linux Enterprise 15 or later

**Windows:**
- Windows Server 2016 or later
- Windows 10 or later (for testing)

**macOS:**
- macOS 10.15 (Catalina) or later (primarily for development)

### Required Permissions

The agent requires:
- Read access to `/proc` filesystem (Linux) for process information
- Network socket access for port scanning
- Ability to bind to local ports for API server
- Write access to log directory
- Write access to data directory

## Installation Methods

### Method 1: Package Manager (Recommended)

**Ubuntu/Debian:**
```bash
# Add VM Gateway repository
curl -fsSL https://packages.vmgateway.io/gpg | sudo gpg --dearmor -o /usr/share/keyrings/vmgateway.gpg
echo "deb [signed-by=/usr/share/keyrings/vmgateway.gpg] https://packages.vmgateway.io/apt stable main" | sudo tee /etc/apt/sources.list.d/vmgateway.list

# Update package list
sudo apt update

# Install agent
sudo apt install vm-gateway-agent

# Start agent service
sudo systemctl start vm-gateway-agent
sudo systemctl enable vm-gateway-agent
```

**RHEL/CentOS/Fedora:**
```bash
# Add VM Gateway repository
sudo tee /etc/yum.repos.d/vmgateway.repo <<EOF
[vmgateway]
name=VM Gateway Repository
baseurl=https://packages.vmgateway.io/rpm/stable
enabled=1
gpgcheck=1
gpgkey=https://packages.vmgateway.io/gpg
EOF

# Install agent
sudo dnf install vm-gateway-agent

# Start agent service
sudo systemctl start vm-gateway-agent
sudo systemctl enable vm-gateway-agent
```

**Windows:**
```powershell
# Download installer
Invoke-WebRequest -Uri "https://packages.vmgateway.io/windows/vm-gateway-agent-latest.msi" -OutFile "vm-gateway-agent.msi"

# Install (silent mode)
msiexec /i vm-gateway-agent.msi /quiet /qn /norestart

# Start service
Start-Service VMGatewayAgent
Set-Service VMGatewayAgent -StartupType Automatic
```

### Method 2: One-Line Install Script

```bash
# Linux/macOS
curl -fsSL https://install.vmgateway.io/agent.sh | sudo bash

# With custom controller URL
curl -fsSL https://install.vmgateway.io/agent.sh | sudo bash -s -- --controller-url wss://controller.example.com:8443
```

### Method 3: Docker Container

```bash
# Pull agent image
docker pull vmgateway/agent:latest

# Run agent container
docker run -d \
  --name vm-gateway-agent \
  --restart unless-stopped \
  --network host \
  --pid host \
  --privileged \
  -v /proc:/host/proc:ro \
  -v /sys:/host/sys:ro \
  -v /var/run/docker.sock:/var/run/docker.sock:ro \
  -v /etc/vm-gateway:/etc/vm-gateway \
  -e CONTROLLER_URL=wss://controller.example.com:8443 \
  -e AGENT_ID=agent-$(hostname) \
  vmgateway/agent:latest
```

### Method 4: Kubernetes DaemonSet

```yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vm-gateway-agent
  namespace: vm-gateway
spec:
  selector:
    matchLabels:
      app: vm-gateway-agent
  template:
    metadata:
      labels:
        app: vm-gateway-agent
    spec:
      hostNetwork: true
      hostPID: true
      containers:
      - name: agent
        image: vmgateway/agent:latest
        securityContext:
          privileged: true
        env:
        - name: CONTROLLER_URL
          value: "wss://controller.vm-gateway.svc.cluster.local:8443"
        - name: AGENT_ID
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        volumeMounts:
        - name: proc
          mountPath: /host/proc
          readOnly: true
        - name: sys
          mountPath: /host/sys
          readOnly: true
        - name: docker-sock
          mountPath: /var/run/docker.sock
          readOnly: true
        - name: config
          mountPath: /etc/vm-gateway
      volumes:
      - name: proc
        hostPath:
          path: /proc
      - name: sys
        hostPath:
          path: /sys
      - name: docker-sock
        hostPath:
          path: /var/run/docker.sock
      - name: config
        configMap:
          name: vm-gateway-agent-config
```

### Method 5: Manual Installation

```bash
# Download agent binary
wget https://releases.vmgateway.io/agent/v1.0.0/vm-gateway-agent-linux-amd64.tar.gz

# Extract
tar -xzf vm-gateway-agent-linux-amd64.tar.gz

# Move to system location
sudo mv vm-gateway-agent /usr/local/bin/
sudo chmod +x /usr/local/bin/vm-gateway-agent

# Create system user
sudo useradd -r -s /bin/false vm-gateway

# Create directories
sudo mkdir -p /etc/vm-gateway
sudo mkdir -p /var/lib/vm-gateway
sudo mkdir -p /var/log/vm-gateway

# Set permissions
sudo chown -R vm-gateway:vm-gateway /var/lib/vm-gateway
sudo chown -R vm-gateway:vm-gateway /var/log/vm-gateway

# Create systemd service
sudo tee /etc/systemd/system/vm-gateway-agent.service <<EOF
[Unit]
Description=VM Gateway Agent
After=network.target

[Service]
Type=simple
User=vm-gateway
Group=vm-gateway
ExecStart=/usr/local/bin/vm-gateway-agent --config /etc/vm-gateway/agent.yaml
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd and start service
sudo systemctl daemon-reload
sudo systemctl start vm-gateway-agent
sudo systemctl enable vm-gateway-agent
```

## Configuration

### Configuration File

The agent uses a YAML configuration file located at `/etc/vm-gateway/agent.yaml`:

```yaml
# Agent identification
agent:
  id: "agent-web-01"  # Auto-generated if not specified
  hostname: "web-server-01"
  tags:
    - "production"
    - "web-tier"
    - "us-east-1"

# Controller connection
controller:
  url: "wss://controller.example.com:8443/agent/ws"
  connection_timeout: 10s
  reconnect_max_retries: null  # Infinite retries
  reconnect_backoff_max: 60s

# mTLS certificates
security:
  cert_file: "/etc/vm-gateway/certs/agent-cert.pem"
  key_file: "/etc/vm-gateway/certs/agent-key.pem"
  ca_file: "/etc/vm-gateway/certs/ca-cert.pem"
  verify_hostname: true

# Service discovery
discovery:
  scan_interval: 60s
  scan_timeout: 30s
  enable_ipv6: true
  collect_environment_vars: false
  collect_command_line: true
  detect_docker: true
  detect_kubernetes: true
  exclude_ports: [22]  # Don't report SSH
  exclude_processes: ["sshd"]

# Classification
classification:
  enable_process_matching: true
  enable_port_matching: true
  enable_protocol_detection: true
  enable_deep_packet_inspection: false
  protocol_detection_timeout: 2s
  cache_ttl: 3600s
  rules_file: "/etc/vm-gateway/classification-rules.yaml"

# Metrics collection
metrics:
  system_metrics_interval: 30s
  service_metrics_interval: 60s
  health_check_interval: 60s
  local_storage_path: "/var/lib/vm-gateway/metrics.db"
  retention_days: 7
  batch_size: 1000
  upload_interval: 300s
  enable_prometheus_endpoint: true
  prometheus_port: 9100

# Logging
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  file: "/var/log/vm-gateway/agent.log"
  max_size_mb: 100
  max_backups: 5
  format: "json"  # json or text

# API server
api:
  enabled: true
  bind_address: "127.0.0.1"
  port: 9090
  enable_tls: false

# Performance
performance:
  max_cpu_percent: 5
  max_memory_mb: 200
  worker_threads: 4
```

### Environment Variables

Configuration can be overridden with environment variables:

```bash
# Controller connection
export VM_GATEWAY_CONTROLLER_URL="wss://controller.example.com:8443"
export VM_GATEWAY_AGENT_ID="agent-custom-id"

# Security
export VM_GATEWAY_CERT_FILE="/path/to/cert.pem"
export VM_GATEWAY_KEY_FILE="/path/to/key.pem"
export VM_GATEWAY_CA_FILE="/path/to/ca.pem"

# Discovery
export VM_GATEWAY_SCAN_INTERVAL="30s"
export VM_GATEWAY_ENABLE_DPI="false"

# Logging
export VM_GATEWAY_LOG_LEVEL="DEBUG"
export VM_GATEWAY_LOG_FILE="/var/log/vm-gateway/agent.log"
```

### Certificate Setup

Generate and configure mTLS certificates:

```bash
# On controller, generate agent certificate
vm-gateway-ctl agent cert create --agent-id agent-web-01 --output /tmp/agent-web-01-certs

# Copy certificates to agent
scp /tmp/agent-web-01-certs/* user@web-01:/etc/vm-gateway/certs/

# Set permissions
sudo chown vm-gateway:vm-gateway /etc/vm-gateway/certs/*
sudo chmod 600 /etc/vm-gateway/certs/agent-key.pem
sudo chmod 644 /etc/vm-gateway/certs/agent-cert.pem
sudo chmod 644 /etc/vm-gateway/certs/ca-cert.pem
```

### Custom Classification Rules

Create `/etc/vm-gateway/classification-rules.yaml`:

```yaml
classification_rules:
  - name: "Internal API Server"
    match:
      port: 9999
      process: "custom-app"
    classification:
      type: "api"
      name: "Internal API Server"
      category: "internal"
      tags: ["internal", "critical"]
    confidence: 1.0

  - name: "Development Servers"
    match:
      port_range: [3000, 3999]
      username: "developer"
    classification:
      type: "web_server"
      name: "Development Server"
      category: "development"
      tags: ["dev", "non-production"]
    confidence: 1.0
```

## Verification

### Check Agent Status

```bash
# Linux (systemd)
sudo systemctl status vm-gateway-agent

# Check logs
sudo journalctl -u vm-gateway-agent -f

# Or check log file
sudo tail -f /var/log/vm-gateway/agent.log

# Windows
Get-Service VMGatewayAgent
Get-EventLog -LogName Application -Source "VM Gateway Agent" -Newest 50
```

### Test Agent API

```bash
# Health check
curl http://localhost:9090/health

# Metrics endpoint
curl http://localhost:9090/metrics

# Service catalog
curl http://localhost:9090/api/services
```

### Verify Controller Connection

```bash
# Check agent logs for connection status
sudo journalctl -u vm-gateway-agent | grep "Connected to controller"

# Check from controller
vm-gateway-ctl agent list
vm-gateway-ctl agent status agent-web-01
```

## Troubleshooting

### Agent Won't Start

**Check logs:**
```bash
sudo journalctl -u vm-gateway-agent -n 100
```

**Common issues:**
- Missing or invalid certificates
- Controller URL unreachable
- Permission issues (can't access /proc)
- Port conflicts (API port already in use)

**Solutions:**
```bash
# Test controller connectivity
curl -k https://controller.example.com:8443/health

# Check certificate validity
openssl x509 -in /etc/vm-gateway/certs/agent-cert.pem -text -noout

# Verify permissions
sudo -u vm-gateway ls /proc
```

### Agent Can't Connect to Controller

**Check network connectivity:**
```bash
# Test WebSocket connection
wscat -c wss://controller.example.com:8443/agent/ws --ca /etc/vm-gateway/certs/ca-cert.pem --cert /etc/vm-gateway/certs/agent-cert.pem --key /etc/vm-gateway/certs/agent-key.pem
```

**Check firewall:**
```bash
# Linux
sudo iptables -L -n | grep 8443

# Windows
Get-NetFirewallRule | Where-Object {$_.DisplayName -like "*VM Gateway*"}
```

### High Resource Usage

**Check current usage:**
```bash
# CPU and memory
ps aux | grep vm-gateway-agent

# Detailed metrics
curl http://localhost:9090/metrics | grep process_
```

**Reduce resource usage:**
```yaml
# In agent.yaml
discovery:
  scan_interval: 120s  # Increase interval
classification:
  enable_deep_packet_inspection: false  # Disable DPI
metrics:
  system_metrics_interval: 60s  # Reduce frequency
performance:
  max_cpu_percent: 2  # Lower limit
  max_memory_mb: 100  # Lower limit
```

### Services Not Discovered

**Check discovery logs:**
```bash
sudo journalctl -u vm-gateway-agent | grep "discovery"
```

**Manual scan:**
```bash
# Trigger immediate scan
curl -X POST http://localhost:9090/api/scan

# Check discovered services
curl http://localhost:9090/api/services
```

**Common issues:**
- Services binding to localhost only
- Permission issues (can't access process info)
- Services in excluded list

## Upgrading

### Package Manager Upgrade

```bash
# Ubuntu/Debian
sudo apt update
sudo apt upgrade vm-gateway-agent

# RHEL/CentOS/Fedora
sudo dnf upgrade vm-gateway-agent

# Restart service
sudo systemctl restart vm-gateway-agent
```

### Docker Upgrade

```bash
# Pull latest image
docker pull vmgateway/agent:latest

# Stop and remove old container
docker stop vm-gateway-agent
docker rm vm-gateway-agent

# Start new container (same command as installation)
docker run -d ...
```

### Manual Upgrade

```bash
# Download new version
wget https://releases.vmgateway.io/agent/v1.1.0/vm-gateway-agent-linux-amd64.tar.gz

# Stop service
sudo systemctl stop vm-gateway-agent

# Backup old binary
sudo cp /usr/local/bin/vm-gateway-agent /usr/local/bin/vm-gateway-agent.backup

# Extract and install new binary
tar -xzf vm-gateway-agent-linux-amd64.tar.gz
sudo mv vm-gateway-agent /usr/local/bin/
sudo chmod +x /usr/local/bin/vm-gateway-agent

# Start service
sudo systemctl start vm-gateway-agent

# Verify
sudo systemctl status vm-gateway-agent
```

## Uninstallation

### Package Manager

```bash
# Ubuntu/Debian
sudo systemctl stop vm-gateway-agent
sudo apt remove vm-gateway-agent
sudo apt purge vm-gateway-agent  # Remove config files

# RHEL/CentOS/Fedora
sudo systemctl stop vm-gateway-agent
sudo dnf remove vm-gateway-agent
```

### Docker

```bash
docker stop vm-gateway-agent
docker rm vm-gateway-agent
docker rmi vmgateway/agent
```

### Manual

```bash
# Stop service
sudo systemctl stop vm-gateway-agent
sudo systemctl disable vm-gateway-agent

# Remove service file
sudo rm /etc/systemd/system/vm-gateway-agent.service
sudo systemctl daemon-reload

# Remove binary
sudo rm /usr/local/bin/vm-gateway-agent

# Remove data (optional)
sudo rm -rf /etc/vm-gateway
sudo rm -rf /var/lib/vm-gateway
sudo rm -rf /var/log/vm-gateway

# Remove user
sudo userdel vm-gateway
```

## Best Practices

### Security

- Use unique certificates per agent
- Rotate certificates regularly (every 90 days)
- Run agent as dedicated user (not root)
- Enable firewall rules to restrict API access
- Use strong file permissions on certificates (600)
- Enable audit logging for sensitive operations

### Performance

- Adjust scan intervals based on environment stability
- Disable deep packet inspection unless needed
- Use local metrics storage with appropriate retention
- Monitor agent resource usage
- Batch metric uploads during off-peak hours

### Reliability

- Enable automatic restart on failure
- Monitor agent health from controller
- Set up alerts for agent disconnections
- Keep agents updated to latest stable version
- Test configuration changes in non-production first

### Monitoring

- Export metrics to Prometheus
- Set up alerts for high resource usage
- Monitor discovery scan duration
- Track failed health checks
- Review logs regularly for errors

## Summary

The VM Gateway Agent is designed for easy installation and minimal configuration while offering extensive customization options. Whether deploying via package managers, containers, or manual installation, the agent integrates seamlessly into existing infrastructure.

Key takeaways:

- Multiple installation methods for different environments
- Sensible defaults with extensive configuration options
- Comprehensive troubleshooting guidance
- Security best practices built-in
- Easy upgrade and maintenance procedures

## Next Steps

- **[API Reference](07-api-reference.md)**: Explore the complete agent API
- **[Overview](01-overview.md)**: Review agent architecture and capabilities
- **[Communication Protocol](05-communication-protocol.md)**: Understand agent-controller communication
