/**
 * Navigation module for VM Gateway documentation viewer.
 * Handles loading and rendering the navigation tree, section toggling,
 * and navigation state management.
 */

const Nav = (function() {
    'use strict';
    
    let docIndex = null;
    let currentPath = null;
    
    /**
     * Initialize the navigation system
     */
    async function init() {
        try {
            // Load documentation index
            const response = await fetch('/api/docs/list');
            if (!response.ok) {
                throw new Error('Failed to load documentation index');
            }
            
            docIndex = await response.json();
            renderNavTree();
            setupEventListeners();
            
            // Load and display version
            await loadVersion();
            
            // Load initial document from URL hash or first document
            const hash = window.location.hash.slice(1);
            if (hash) {
                loadDocumentByPath(hash);
            }
            
        } catch (error) {
            console.error('Navigation initialization failed:', error);
            showError('Failed to load documentation navigation');
        }
    }
    
    /**
     * Load and display version information
     */
    async function loadVersion() {
        try {
            const response = await fetch('/api/version');
            if (response.ok) {
                const versionInfo = await response.json();
                const versionEl = document.querySelector('.sidebar-footer .version');
                if (versionEl) {
                    versionEl.textContent = `Version ${versionInfo.version}`;
                    versionEl.title = `${versionInfo.phase_name} - Build ${versionInfo.build}`;
                }
            }
        } catch (error) {
            console.error('Failed to load version:', error);
        }
    }
    
    /**
     * Render the navigation tree in the sidebar
     */
    function renderNavTree() {
        const navTree = document.getElementById('nav-tree');
        if (!navTree || !docIndex) return;
        
        navTree.innerHTML = '';
        
        docIndex.sections.forEach(section => {
            const sectionEl = createSectionElement(section);
            navTree.appendChild(sectionEl);
        });
    }
    
    /**
     * Create a section element with its files
     */
    function createSectionElement(section) {
        const sectionDiv = document.createElement('div');
        sectionDiv.className = 'nav-section';
        sectionDiv.dataset.section = section.directory;
        
        // Section header
        const header = document.createElement('div');
        header.className = 'nav-section-header';
        header.innerHTML = `
            <span>${section.name}</span>
            <span class="nav-section-toggle">▼</span>
        `;
        
        // Toggle section on click
        header.addEventListener('click', () => {
            sectionDiv.classList.toggle('collapsed');
        });
        
        sectionDiv.appendChild(header);
        
        // Files list
        const filesList = document.createElement('ul');
        filesList.className = 'nav-section-files';
        
        section.files.forEach(file => {
            const fileItem = createFileElement(file);
            filesList.appendChild(fileItem);
        });
        
        sectionDiv.appendChild(filesList);
        
        return sectionDiv;
    }
    
    /**
     * Create a file navigation item
     */
    function createFileElement(file) {
        const li = document.createElement('li');
        li.className = 'nav-file-item';
        li.textContent = file.title;
        li.dataset.path = file.path;
        
        li.addEventListener('click', () => {
            loadDocumentByPath(file.path);
            closeMobileMenu();
        });
        
        return li;
    }
    
    /**
     * Setup event listeners for navigation
     */
    function setupEventListeners() {
        // Mobile menu toggle
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        
        if (menuToggle) {
            menuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('active');
                overlay.classList.toggle('active');
            });
        }
        
        if (overlay) {
            overlay.addEventListener('click', closeMobileMenu);
        }
        
        // Handle browser back/forward
        window.addEventListener('hashchange', () => {
            const hash = window.location.hash.slice(1);
            if (hash) {
                loadDocumentByPath(hash, false);
            }
        });
    }
    
    /**
     * Close mobile menu
     */
    function closeMobileMenu() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        
        if (sidebar) sidebar.classList.remove('active');
        if (overlay) overlay.classList.remove('active');
    }
    
    /**
     * Load a document by its path
     */
    async function loadDocumentByPath(path, updateHash = true) {
        if (currentPath === path) return;
        
        try {
            // Parse path (e.g., "01-overview/01-introduction.md")
            const parts = path.split('/');
            if (parts.length !== 2) {
                throw new Error('Invalid document path');
            }
            
            const [section, file] = parts;
            
            // Fetch document content
            const response = await fetch(`/api/docs/${section}/${file}`);
            if (!response.ok) {
                throw new Error('Document not found');
            }
            
            const doc = await response.json();
            
            // Update current path
            currentPath = path;
            
            // Update URL hash
            if (updateHash) {
                window.location.hash = path;
            }
            
            // Update active state in navigation
            updateActiveNavItem(path);
            
            // Render document content
            renderDocument(doc);
            
            // Update breadcrumb
            updateBreadcrumb(doc);
            
            // Scroll to top
            window.scrollTo(0, 0);
            
        } catch (error) {
            console.error('Failed to load document:', error);
            showError('Failed to load document. Please try again.');
        }
    }
    
    /**
     * Update active state in navigation
     */
    function updateActiveNavItem(path) {
        // Remove active class from all items
        document.querySelectorAll('.nav-file-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to current item
        const activeItem = document.querySelector(`.nav-file-item[data-path="${path}"]`);
        if (activeItem) {
            activeItem.classList.add('active');
            
            // Ensure parent section is expanded
            const section = activeItem.closest('.nav-section');
            if (section) {
                section.classList.remove('collapsed');
            }
        }
    }
    
    /**
     * Render document content
     */
    function renderDocument(doc) {
        const contentEl = document.getElementById('content');
        if (!contentEl) return;
        
        contentEl.innerHTML = doc.html;
        
        // Apply syntax highlighting to code blocks
        contentEl.querySelectorAll('pre code').forEach(block => {
            hljs.highlightElement(block);
        });
        
        // Generate table of contents
        generateTableOfContents(contentEl);
        
        // Setup heading anchor links
        setupHeadingAnchors(contentEl);
    }
    
    /**
     * Generate table of contents from headings
     */
    function generateTableOfContents(contentEl) {
        const tocEl = document.getElementById('toc');
        if (!tocEl) return;
        
        // Get all h2 and h3 headings
        const headings = contentEl.querySelectorAll('h2, h3');
        
        if (headings.length === 0) {
            tocEl.innerHTML = '<p style="color: var(--text-tertiary); font-size: 0.8125rem;">No headings found</p>';
            return;
        }
        
        const tocList = document.createElement('ul');
        let currentH2List = null;
        
        headings.forEach(heading => {
            const li = document.createElement('li');
            const link = document.createElement('a');
            
            // Create ID if not exists
            if (!heading.id) {
                heading.id = heading.textContent
                    .toLowerCase()
                    .replace(/[^a-z0-9]+/g, '-')
                    .replace(/^-|-$/g, '');
            }
            
            link.href = `#${heading.id}`;
            link.textContent = heading.textContent;
            link.addEventListener('click', (e) => {
                e.preventDefault();
                heading.scrollIntoView({ behavior: 'smooth', block: 'start' });
                window.location.hash = `${currentPath}#${heading.id}`;
            });
            
            li.appendChild(link);
            
            if (heading.tagName === 'H2') {
                tocList.appendChild(li);
                currentH2List = document.createElement('ul');
                li.appendChild(currentH2List);
            } else if (heading.tagName === 'H3' && currentH2List) {
                currentH2List.appendChild(li);
            }
        });
        
        tocEl.innerHTML = '';
        tocEl.appendChild(tocList);
        
        // Setup scroll spy for TOC
        setupTocScrollSpy(headings);
    }
    
    /**
     * Setup scroll spy for table of contents
     */
    function setupTocScrollSpy(headings) {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    const id = entry.target.id;
                    const tocLink = document.querySelector(`.toc a[href="#${id}"]`);
                    
                    if (entry.isIntersecting) {
                        // Remove active from all
                        document.querySelectorAll('.toc a').forEach(link => {
                            link.classList.remove('active');
                        });
                        // Add active to current
                        if (tocLink) {
                            tocLink.classList.add('active');
                        }
                    }
                });
            },
            {
                rootMargin: '-100px 0px -66%',
                threshold: 0
            }
        );
        
        headings.forEach(heading => {
            if (heading.id) {
                observer.observe(heading);
            }
        });
    }
    
    /**
     * Setup heading anchor links
     */
    function setupHeadingAnchors(contentEl) {
        const headings = contentEl.querySelectorAll('h1, h2, h3, h4, h5, h6');
        
        headings.forEach(heading => {
            if (!heading.id) {
                heading.id = heading.textContent
                    .toLowerCase()
                    .replace(/[^a-z0-9]+/g, '-')
                    .replace(/^-|-$/g, '');
            }
        });
    }
    
    /**
     * Update breadcrumb navigation
     */
    function updateBreadcrumb(doc) {
        const breadcrumbEl = document.getElementById('breadcrumb');
        if (!breadcrumbEl) return;
        
        breadcrumbEl.innerHTML = `
            <span class="breadcrumb-item">
                <a href="#" class="breadcrumb-link" id="home-link">Home</a>
            </span>
            <span class="breadcrumb-item">
                <span>${doc.section_name}</span>
            </span>
            <span class="breadcrumb-item">
                <span>${doc.title}</span>
            </span>
        `;
        
        // Home link handler
        const homeLink = document.getElementById('home-link');
        if (homeLink) {
            homeLink.addEventListener('click', (e) => {
                e.preventDefault();
                window.location.hash = '';
                currentPath = null;
                showWelcome();
                closeMobileMenu();
            });
        }
    }
    
    /**
     * Show welcome screen
     */
    function showWelcome() {
        const contentEl = document.getElementById('content');
        const breadcrumbEl = document.getElementById('breadcrumb');
        const tocEl = document.getElementById('toc');
        
        if (contentEl) {
            contentEl.innerHTML = `
                <div class="welcome">
                    <h1>Welcome to VM Gateway Documentation</h1>
                    <p>Select a topic from the navigation menu to get started.</p>
                </div>
            `;
        }
        
        if (breadcrumbEl) {
            breadcrumbEl.innerHTML = '';
        }
        
        if (tocEl) {
            tocEl.innerHTML = '';
        }
        
        // Remove active state from all nav items
        document.querySelectorAll('.nav-file-item').forEach(item => {
            item.classList.remove('active');
        });
    }
    
    /**
     * Show error message
     */
    function showError(message) {
        const contentEl = document.getElementById('content');
        if (contentEl) {
            contentEl.innerHTML = `
                <div style="padding: 2rem; text-align: center;">
                    <h2 style="color: #dc2626;">Error</h2>
                    <p style="color: var(--text-secondary);">${message}</p>
                </div>
            `;
        }
    }
    
    /**
     * Get current document path
     */
    function getCurrentPath() {
        return currentPath;
    }
    
    /**
     * Get documentation index
     */
    function getDocIndex() {
        return docIndex;
    }
    
    // Public API
    return {
        init,
        loadDocumentByPath,
        getCurrentPath,
        getDocIndex,
        closeMobileMenu
    };
})();
