---
title: "Authentication Flow"
section: "Client"
order: 2
tags: ["client", "authentication", "oauth", "security", "tokens"]
last_updated: "2025-11-08"
---

# Client Authentication Flow

The VM Gateway Client implements a secure, user-friendly authentication system based on OAuth 2.0 with PKCE (Proof Key for Code Exchange), providing enterprise-grade security while maintaining a seamless user experience

The VM Gateway Client implements a secure, user-friendly authentication system based on OAuth 2.0 with browser-based login. This approach provides several advantages: users authenticate through the familiar web interface, multi-factor authentication is seamlessly integrated, and the client never handles user passwords directly.

The authentication flow is designed to balance security with user experience, supporting single sign-on (SSO), multi-factor authentication (MFA), and secure token storage while minimizing friction for legitimate users.

## Authentication Architecture

### Components

**Client Application:**
- Initiates authentication flow
- Handles OAuth redirect callbacks
- Stores and manages access tokens
- Refreshes tokens before expiration
- Revokes tokens on logout

**System Browser:**
- Renders controller login page
- Handles user credential input
- Processes MFA challenges
- Redirects back to client with authorization code

**Controller:**
- Validates user credentials
- Enforces MFA requirements
- Issues access and refresh tokens
- Validates token requests
- Tracks active sessions

**OS Keyring:**
- Securely stores tokens
- Encrypts sensitive data at rest
- Provides access control
- Integrates with platform security features

## Initial Authentication Flow

### Step-by-Step Process

**1. Client Launch**

When the user launches the client application:

```
Client starts → Check for stored token → Token exists and valid?
                                              ↓ No
                                         Initiate OAuth flow
```

The client first checks its secure storage (OS keyring) for an existing access token. If a valid token exists, the client proceeds directly to the main interface. If no token exists or the token is expired, the authentication flow begins.

**2. OAuth Authorization Request**

The client constructs an OAuth 2.0 authorization request:

```
https://controller.example.com/oauth/authorize?
  response_type=code
  &client_id=vm-gateway-client
  &redirect_uri=vmgateway://auth/callback
  &scope=services:read services:connect user:profile
  &state=random_state_value
  &code_challenge=base64url(sha256(code_verifier))
  &code_challenge_method=S256
```


**Key Parameters:**
- `response_type=code`: Authorization code flow (most secure)
- `client_id`: Identifies the client application
- `redirect_uri`: Custom URL scheme for callback
- `scope`: Requested permissions
- `state`: CSRF protection token
- `code_challenge`: PKCE (Proof Key for Code Exchange) for additional security

**3. Browser Launch**

The client opens the user's default system browser with the authorization URL:

```python
import webbrowser

auth_url = build_authorization_url(
    controller_url=config.controller_url,
    client_id=config.client_id,
    redirect_uri="vmgateway://auth/callback",
    scopes=["services:read", "services:connect", "user:profile"],
    state=generate_random_state(),
    code_verifier=generate_code_verifier()
)

webbrowser.open(auth_url)
```

The browser displays the controller's login page, where the user enters their credentials.

**4. User Authentication**

The user authenticates through the web interface:

- **Username/Password**: User enters credentials
- **SSO**: User redirects to identity provider (Okta, Azure AD, etc.)
- **MFA Challenge**: If required, user completes second factor (TOTP, WebAuthn, SMS)

The controller validates credentials and enforces all authentication policies (password complexity, account lockout, MFA requirements, etc.).

**5. Authorization Grant**

After successful authentication, the controller:

1. Generates an authorization code
2. Associates the code with the user and requested scopes
3. Redirects the browser to the client's redirect URI

```
vmgateway://auth/callback?
  code=authorization_code_here
  &state=random_state_value
```

**6. Custom URL Scheme Handling**

The client registers a custom URL scheme handler (`vmgateway://`) with the operating system. When the browser redirects to this URL, the OS launches the client application (if not already running) and passes the URL to it.

The client extracts the authorization code and state parameter from the URL:

```python
def handle_oauth_callback(url: str):
    parsed = urllib.parse.urlparse(url)
    params = urllib.parse.parse_qs(parsed.query)
    
    code = params.get('code', [None])[0]
    state = params.get('state', [None])[0]
    
    # Verify state matches what we sent (CSRF protection)
    if state != stored_state:
        raise SecurityError("State mismatch - possible CSRF attack")
    
    return code
```

**7. Token Exchange**

The client exchanges the authorization code for access and refresh tokens:

```python
token_response = requests.post(
    f"{controller_url}/oauth/token",
    data={
        "grant_type": "authorization_code",
        "code": authorization_code,
        "redirect_uri": "vmgateway://auth/callback",
        "client_id": client_id,
        "code_verifier": code_verifier  # PKCE verification
    }
)

tokens = token_response.json()
# {
#   "access_token": "eyJhbGc...",
#   "refresh_token": "eyJhbGc...",
#   "token_type": "Bearer",
#   "expires_in": 3600,
#   "scope": "services:read services:connect user:profile"
# }
```

**8. Secure Token Storage**

The client stores tokens in the OS keyring for secure persistence:

```python
import keyring

# Store access token
keyring.set_password(
    "vm-gateway-client",
    "access_token",
    tokens["access_token"]
)

# Store refresh token
keyring.set_password(
    "vm-gateway-client",
    "refresh_token",
    tokens["refresh_token"]
)

# Store token metadata in local database
db.execute("""
    INSERT INTO tokens (expires_at, scopes, issued_at)
    VALUES (?, ?, ?)
""", (
    datetime.now() + timedelta(seconds=tokens["expires_in"]),
    tokens["scope"],
    datetime.now()
))
```

**9. Authentication Complete**

The client now has a valid access token and can make authenticated API requests to the controller. The main application interface is displayed, and the user can browse and connect to services.

## Token Management

### Access Token Usage

Every API request to the controller includes the access token in the Authorization header:

```python
headers = {
    "Authorization": f"Bearer {access_token}",
    "Content-Type": "application/json"
}

response = requests.get(
    f"{controller_url}/api/services",
    headers=headers
)
```

The controller validates the token on each request, checking:
- Token signature is valid
- Token has not expired
- Token has not been revoked
- User account is still active
- User still has required permissions

### Token Refresh

Access tokens have limited lifetimes (typically 1 hour) for security. Before the access token expires, the client automatically refreshes it using the refresh token:

```python
def refresh_access_token():
    refresh_token = keyring.get_password("vm-gateway-client", "refresh_token")
    
    response = requests.post(
        f"{controller_url}/oauth/token",
        data={
            "grant_type": "refresh_token",
            "refresh_token": refresh_token,
            "client_id": client_id
        }
    )
    
    new_tokens = response.json()
    
    # Store new access token
    keyring.set_password(
        "vm-gateway-client",
        "access_token",
        new_tokens["access_token"]
    )
    
    # Update expiration time
    db.execute("""
        UPDATE tokens 
        SET expires_at = ?, 
            issued_at = ?
        WHERE id = 1
    """, (
        datetime.now() + timedelta(seconds=new_tokens["expires_in"]),
        datetime.now()
    ))
    
    return new_tokens["access_token"]
```

The client implements proactive token refresh:

```python
def ensure_valid_token():
    token_info = db.execute("SELECT expires_at FROM tokens WHERE id = 1").fetchone()
    expires_at = token_info["expires_at"]
    
    # Refresh if token expires in less than 5 minutes
    if expires_at - datetime.now() < timedelta(minutes=5):
        return refresh_access_token()
    
    return keyring.get_password("vm-gateway-client", "access_token")
```

### Token Revocation

When the user logs out, the client revokes both tokens:

```python
def logout():
    access_token = keyring.get_password("vm-gateway-client", "access_token")
    refresh_token = keyring.get_password("vm-gateway-client", "refresh_token")
    
    # Revoke tokens on server
    requests.post(
        f"{controller_url}/oauth/revoke",
        data={"token": access_token},
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    requests.post(
        f"{controller_url}/oauth/revoke",
        data={"token": refresh_token},
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    # Delete tokens from local storage
    keyring.delete_password("vm-gateway-client", "access_token")
    keyring.delete_password("vm-gateway-client", "refresh_token")
    db.execute("DELETE FROM tokens")
    
    # Close all active connections
    connection_manager.disconnect_all()
```

## Multi-Factor Authentication

### MFA Challenge Flow

When MFA is required, the authentication flow includes additional steps:

**1. Initial Authentication**

User enters username and password in the browser.

**2. MFA Challenge**

Controller determines MFA is required and presents challenge:

- **TOTP**: User enters 6-digit code from authenticator app
- **WebAuthn**: User interacts with hardware security key or biometric
- **SMS**: User enters code sent via text message
- **Email**: User enters code sent via email

**3. MFA Verification**

Controller validates the MFA response:

```python
# TOTP validation
def verify_totp(user_id: str, code: str) -> bool:
    user_secret = get_user_totp_secret(user_id)
    totp = pyotp.TOTP(user_secret)
    return totp.verify(code, valid_window=1)

# WebAuthn validation
def verify_webauthn(user_id: str, credential: dict) -> bool:
    stored_credential = get_user_webauthn_credential(user_id)
    return webauthn.verify_authentication_response(
        credential,
        stored_credential,
        challenge=session.get('webauthn_challenge')
    )
```

**4. Authorization Grant**

Only after successful MFA verification does the controller issue the authorization code.

### Remember Device

Users can opt to "remember this device" to skip MFA for a period:

```python
# Controller sets a device token cookie
response.set_cookie(
    "device_token",
    value=generate_device_token(user_id, device_fingerprint),
    max_age=30 * 24 * 60 * 60,  # 30 days
    secure=True,
    httponly=True,
    samesite="Strict"
)
```

On subsequent logins from the same device, MFA is skipped if the device token is valid.

## Single Sign-On Integration

### SAML Flow

When SSO is configured, the authentication flow redirects to the identity provider:

**1. Client Initiates OAuth**

Client opens browser to controller's OAuth endpoint.

**2. Controller Redirects to IdP**

Controller detects user's domain requires SSO and redirects to SAML IdP:

```
https://idp.example.com/saml/login?
  SAMLRequest=base64_encoded_request
  &RelayState=original_state
```

**3. User Authenticates with IdP**

User logs in through their organization's identity provider (Okta, Azure AD, etc.).

**4. IdP Redirects Back**

IdP redirects back to controller with SAML assertion:

```
POST https://controller.example.com/saml/acs
SAMLResponse=base64_encoded_assertion
RelayState=original_state
```

**5. Controller Validates Assertion**

Controller validates the SAML assertion and creates a session.

**6. Controller Redirects to Client**

Controller redirects to the client's callback URL with authorization code.

**7. Client Exchanges Code for Tokens**

Client completes the OAuth flow as normal.

### OAuth/OIDC Flow

For OAuth-based SSO (Google, GitHub, etc.):

**1. Controller Redirects to OAuth Provider**

```
https://accounts.google.com/o/oauth2/v2/auth?
  client_id=controller_client_id
  &redirect_uri=https://controller.example.com/oauth/callback/google
  &response_type=code
  &scope=openid email profile
```

**2. User Authorizes**

User logs in and authorizes the controller application.

**3. Provider Redirects Back**

OAuth provider redirects to controller with authorization code.

**4. Controller Exchanges Code**

Controller exchanges code for user information.

**5. Controller Issues Client Token**

Controller creates a session and redirects to client callback.

## Device Registration

### Device Identification

The client generates a unique device identifier on first launch:

```python
import uuid
import platform

def generate_device_id():
    # Combine machine-specific information
    machine_id = str(uuid.getnode())  # MAC address
    hostname = platform.node()
    system = platform.system()
    
    # Generate stable device ID
    device_string = f"{machine_id}:{hostname}:{system}"
    device_id = hashlib.sha256(device_string.encode()).hexdigest()
    
    return device_id
```

### Device Registration Flow

On first authentication, the client registers the device:

```python
def register_device(access_token: str):
    device_info = {
        "device_id": get_device_id(),
        "device_name": platform.node(),
        "os": platform.system(),
        "os_version": platform.version(),
        "client_version": CLIENT_VERSION
    }
    
    response = requests.post(
        f"{controller_url}/api/devices/register",
        json=device_info,
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    device_token = response.json()["device_token"]
    keyring.set_password("vm-gateway-client", "device_token", device_token)
```

### Device Management

Users can view and manage their registered devices through the web interface:

- View list of registered devices
- See last used date for each device
- Revoke access for lost or stolen devices
- Rename devices for easier identification

When a device is revoked, all tokens issued to that device are invalidated.

## Security Considerations

### PKCE (Proof Key for Code Exchange)

The client implements PKCE to prevent authorization code interception attacks:

```python
import secrets
import hashlib
import base64

def generate_code_verifier():
    # Generate random 43-128 character string
    return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')

def generate_code_challenge(verifier: str):
    # SHA256 hash of verifier
    digest = hashlib.sha256(verifier.encode('utf-8')).digest()
    return base64.urlsafe_b64encode(digest).decode('utf-8').rstrip('=')
```

The code verifier is stored locally and sent during token exchange, while the code challenge is sent during authorization. This prevents attackers from using intercepted authorization codes.

### State Parameter

The state parameter prevents CSRF attacks:

```python
def generate_state():
    return secrets.token_urlsafe(32)

# Store state before opening browser
stored_state = generate_state()
session_storage.set("oauth_state", stored_state)

# Verify state in callback
if callback_state != stored_state:
    raise SecurityError("State mismatch")
```

### Secure Token Storage

Tokens are stored in OS-specific secure storage:

- **Windows**: Windows Credential Manager (encrypted with DPAPI)
- **macOS**: Keychain (encrypted with user's login password)
- **Linux**: Secret Service API (GNOME Keyring, KWallet)

This ensures tokens are encrypted at rest and protected by OS-level access controls.

### Token Scope Limitation

The client requests only the minimum required scopes:

```python
REQUIRED_SCOPES = [
    "services:read",      # View available services
    "services:connect",   # Establish connections
    "user:profile"        # Read user profile information
]
```

This follows the principle of least privilege, limiting potential damage if a token is compromised.

## Error Handling

### Authentication Failures

The client handles various authentication error scenarios:

**Invalid Credentials:**
```python
if response.status_code == 401:
    show_error("Invalid username or password. Please try again.")
    retry_authentication()
```

**MFA Required:**
```python
if response.json().get("error") == "mfa_required":
    # Browser will show MFA challenge
    # Client waits for callback
    pass
```

**Account Locked:**
```python
if response.json().get("error") == "account_locked":
    show_error(
        "Your account has been locked due to too many failed login attempts. "
        "Please contact your administrator or try again later."
    )
```

**Token Expired:**
```python
if response.status_code == 401 and "token_expired" in response.json().get("error"):
    # Attempt token refresh
    try:
        new_token = refresh_access_token()
        # Retry original request with new token
        return retry_request_with_token(new_token)
    except RefreshTokenExpired:
        # Refresh token also expired, need to re-authenticate
        initiate_authentication_flow()
```

### Network Errors

The client handles network connectivity issues gracefully:

```python
def make_authenticated_request(url: str, **kwargs):
    max_retries = 3
    retry_delay = 1
    
    for attempt in range(max_retries):
        try:
            token = ensure_valid_token()
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get(url, headers=headers, timeout=10, **kwargs)
            return response
        except requests.exceptions.ConnectionError:
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
            else:
                show_error("Unable to connect to controller. Please check your network connection.")
                raise
        except requests.exceptions.Timeout:
            show_error("Request timed out. Please try again.")
            raise
```

## Summary

The VM Gateway Client authentication flow implements industry-standard OAuth 2.0 with PKCE for maximum security while maintaining a seamless user experience. By leveraging browser-based authentication, the client supports all authentication methods configured on the controller (local auth, SSO, MFA) without requiring client-side implementation of each method.

Secure token storage in OS keyrings, automatic token refresh, and proper error handling ensure that users remain authenticated while maintaining strong security postures. The device registration system provides additional security by allowing users to track and manage their authenticated devices.
