"""
Centralized version management for VM Gateway platform.

This module provides a single source of truth for the platform version,
ensuring consistency across all components (agent, controller, client, doc_viewer).
"""

# Single source of truth for platform version
__version__ = "a.0.0-1"

# Version components
VERSION_PHASE = "a"  # a=alpha, b=beta, c=release candidate, r=release
VERSION_MAJOR = 0
VERSION_MINOR = 0
VERSION_BUILD = 1

# Full version string
VERSION_STRING = __version__


def get_version() -> str:
    """
    Get the current platform version string.
    
    Returns:
        Version string in format: [PHASE].[MAJOR].[MINOR]-[BUILD]
    """
    return __version__


def get_version_info() -> dict:
    """
    Get detailed version information.
    
    Returns:
        Dictionary containing version components and metadata
    """
    phase_names = {
        "a": "Alpha",
        "b": "Beta",
        "c": "Release Candidate",
        "r": "Release"
    }
    
    return {
        "version": __version__,
        "phase": VERSION_PHASE,
        "phase_name": phase_names.get(VERSION_PHASE, "Unknown"),
        "major": VERSION_MAJOR,
        "minor": VERSION_MINOR,
        "build": VERSION_BUILD,
        "is_stable": VERSION_PHASE == "r"
    }
