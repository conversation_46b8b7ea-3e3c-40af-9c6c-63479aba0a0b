---
title: "Session Management"
section: "Authentication & Authorization"
order: 6
tags: ["sessions", "security", "cookies", "redis"]
last_updated: "2025-11-08"
---

# Session Management

Session management handles the lifecycle of user sessions from creation through validation to termination. The platform implements secure session handling with features like device tracking, concurrent session limits, and anomaly detection.

## Overview

Secure session management is critical for maintaining platform security and user experience:

**Key Features:**
- Secure HTTP-only cookies
- Configurable session timeouts
- Sliding expiration windows
- Device fingerprinting and tracking
- Geographic location tracking
- Concurrent session management
- Remote session termination
- Session activity logging
- Anomaly detection

**Session Storage:**
- Primary: Redis (fast, distributed)
- Fallback: PostgreSQL (persistent)
- Session data encrypted at rest

## Session Lifecycle

### Session Creation

```python
from datetime import datetime, timedelta
import secrets
import hashlib

class SessionManager:
    """Manage user sessions"""
    
    def __init__(self, redis_client, db_pool):
        self.redis = redis_client
        self.db = db_pool
        self.default_timeout = 28800  # 8 hours
        self.absolute_timeout = 86400  # 24 hours
        self.max_concurrent = 5
    
    async def create_session(
        self,
        user: User,
        ip_address: str,
        user_agent: str,
        duration: Optional[int] = None,
        remember_me: bool = False
    ) -> dict:
        """Create a new user session"""
        
        # Generate session token
        session_token = secrets.token_urlsafe(32)
        session_id = hashlib.sha256(session_token.encode()).hexdigest()
        
        # Calculate expiration
        timeout = duration or self.default_timeout
        if remember_me:
            timeout = 2592000  # 30 days
        
        expires_at = datetime.utcnow() + timedelta(seconds=timeout)
        absolute_expires_at = datetime.utcnow() + timedelta(seconds=self.absolute_timeout)
        
        # Extract device information
        device_info = await self.extract_device_info(user_agent, ip_address)
        
        # Check concurrent session limit
        await self.enforce_concurrent_limit(user.id)
        
        # Store session in Redis
        session_data = {
            "user_id": user.id,
            "username": user.username,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "device_type": device_info["device_type"],
            "browser": device_info["browser"],
            "os": device_info["os"],
            "location": device_info["location"],
            "created_at": datetime.utcnow().isoformat(),
            "last_activity": datetime.utcnow().isoformat(),
            "expires_at": expires_at.isoformat(),
            "absolute_expires_at": absolute_expires_at.isoformat()
        }
        
        await self.redis.setex(
            f"session:{session_id}",
            timeout,
            json.dumps(session_data)
        )
        
        # Store in database for persistence
        await self.db.execute(
            """
            INSERT INTO sessions (
                session_id, user_id, ip_address, user_agent,
                device_info, created_at, expires_at, last_activity
            )
            VALUES ($1, $2, $3, $4, $5, NOW(), $6, NOW())
            """,
            session_id, user.id, ip_address, user_agent,
            json.dumps(device_info), expires_at
        )
        
        # Log session creation
        await log_audit_event(
            actor=user.username,
            action="session_created",
            details={
                "ip_address": ip_address,
                "device": device_info["device_type"],
                "location": device_info["location"]
            }
        )
        
        return {
            "token": session_token,
            "expires_at": expires_at,
            "session_id": session_id
        }
```



    async def extract_device_info(self, user_agent: str, ip_address: str) -> dict:
        """Extract device information from user agent and IP"""
        from user_agents import parse
        import geoip2.database
        
        # Parse user agent
        ua = parse(user_agent)
        
        device_info = {
            "device_type": "mobile" if ua.is_mobile else "tablet" if ua.is_tablet else "desktop",
            "browser": f"{ua.browser.family} {ua.browser.version_string}",
            "os": f"{ua.os.family} {ua.os.version_string}",
            "device_family": ua.device.family
        }
        
        # Get geographic location from IP
        try:
            reader = geoip2.database.Reader('/path/to/GeoLite2-City.mmdb')
            response = reader.city(ip_address)
            device_info["location"] = {
                "city": response.city.name,
                "country": response.country.name,
                "latitude": response.location.latitude,
                "longitude": response.location.longitude
            }
        except:
            device_info["location"] = {"city": "Unknown", "country": "Unknown"}
        
        return device_info
    
    async def enforce_concurrent_limit(self, user_id: str) -> None:
        """Enforce maximum concurrent sessions"""
        
        # Get active sessions
        sessions = await db.fetch(
            """
            SELECT session_id, created_at FROM sessions
            WHERE user_id = $1 AND expires_at > NOW()
            ORDER BY created_at DESC
            """,
            user_id
        )
        
        # If at limit, terminate oldest session
        if len(sessions) >= self.max_concurrent:
            oldest_session = sessions[-1]
            await self.terminate_session(oldest_session['session_id'])
            
            await log_audit_event(
                actor=user_id,
                action="session_terminated",
                details={"reason": "concurrent_limit", "session_id": oldest_session['session_id']}
            )
```

### Session Validation

```python
    async def validate_session(self, session_token: str) -> Optional[User]:
        """Validate session and return user"""
        
        session_id = hashlib.sha256(session_token.encode()).hexdigest()
        
        # Try Redis first (fast path)
        session_data = await self.redis.get(f"session:{session_id}")
        
        if session_data:
            session = json.loads(session_data)
            
            # Check expiration
            expires_at = datetime.fromisoformat(session['expires_at'])
            if datetime.utcnow() > expires_at:
                await self.terminate_session(session_id)
                return None
            
            # Check absolute expiration
            absolute_expires_at = datetime.fromisoformat(session['absolute_expires_at'])
            if datetime.utcnow() > absolute_expires_at:
                await self.terminate_session(session_id)
                return None
            
            # Update last activity (sliding expiration)
            session['last_activity'] = datetime.utcnow().isoformat()
            await self.redis.setex(
                f"session:{session_id}",
                self.default_timeout,
                json.dumps(session)
            )
            
            # Get user
            user = await get_user_by_id(session['user_id'])
            return user
        
        # Fallback to database
        session_record = await db.fetchrow(
            """
            SELECT s.*, u.* FROM sessions s
            JOIN users u ON s.user_id = u.id
            WHERE s.session_id = $1 AND s.expires_at > NOW()
            """,
            session_id
        )
        
        if not session_record:
            return None
        
        # Restore to Redis
        session_data = {
            "user_id": session_record['user_id'],
            "username": session_record['username'],
            "ip_address": session_record['ip_address'],
            "user_agent": session_record['user_agent'],
            "created_at": session_record['created_at'].isoformat(),
            "last_activity": datetime.utcnow().isoformat(),
            "expires_at": session_record['expires_at'].isoformat()
        }
        
        await self.redis.setex(
            f"session:{session_id}",
            self.default_timeout,
            json.dumps(session_data)
        )
        
        return User(**dict(session_record))
```

## Device Fingerprinting

### Overview

Device fingerprinting creates a unique identifier for each device to detect suspicious activity and enable trusted device features.

```python
class DeviceFingerprintManager:
    """Generate and validate device fingerprints"""
    
    def generate_fingerprint(
        self,
        user_agent: str,
        accept_language: str,
        screen_resolution: str,
        timezone: str,
        canvas_hash: str
    ) -> str:
        """Generate device fingerprint from browser characteristics"""
        
        fingerprint_data = {
            "user_agent": user_agent,
            "accept_language": accept_language,
            "screen_resolution": screen_resolution,
            "timezone": timezone,
            "canvas_hash": canvas_hash
        }
        
        # Create hash of all characteristics
        fingerprint_str = json.dumps(fingerprint_data, sort_keys=True)
        fingerprint = hashlib.sha256(fingerprint_str.encode()).hexdigest()
        
        return fingerprint
    
    async def validate_fingerprint(
        self,
        session_id: str,
        current_fingerprint: str
    ) -> bool:
        """Validate device fingerprint matches session"""
        
        session = await db.fetchrow(
            """
            SELECT device_fingerprint FROM sessions
            WHERE session_id = $1
            """,
            session_id
        )
        
        if not session:
            return False
        
        stored_fingerprint = session['device_fingerprint']
        
        # Allow some tolerance for minor changes
        similarity = self._calculate_similarity(stored_fingerprint, current_fingerprint)
        
        if similarity < 0.8:  # 80% similarity threshold
            await log_security_event(
                event_type="device_fingerprint_mismatch",
                session_id=session_id,
                similarity=similarity
            )
            return False
        
        return True
    
    def _calculate_similarity(self, fp1: str, fp2: str) -> float:
        """Calculate similarity between two fingerprints"""
        # Simple Hamming distance for demonstration
        if fp1 == fp2:
            return 1.0
        
        matches = sum(c1 == c2 for c1, c2 in zip(fp1, fp2))
        return matches / len(fp1)
```

## Session Security

### IP Binding

```python
async def validate_ip_binding(
    session_id: str,
    current_ip: str,
    strict: bool = False
) -> bool:
    """Validate session IP address"""
    
    session = await db.fetchrow(
        """
        SELECT ip_address FROM sessions
        WHERE session_id = $1
        """,
        session_id
    )
    
    if not session:
        return False
    
    stored_ip = session['ip_address']
    
    if strict:
        # Exact match required
        if current_ip != stored_ip:
            await log_security_event(
                event_type="session_ip_mismatch",
                session_id=session_id,
                stored_ip=stored_ip,
                current_ip=current_ip
            )
            return False
    else:
        # Allow same subnet (for mobile users)
        import ipaddress
        stored_network = ipaddress.ip_network(f"{stored_ip}/24", strict=False)
        current_ip_obj = ipaddress.ip_address(current_ip)
        
        if current_ip_obj not in stored_network:
            await log_security_event(
                event_type="session_ip_subnet_mismatch",
                session_id=session_id,
                stored_ip=stored_ip,
                current_ip=current_ip
            )
            return False
    
    return True
```

### CSRF Protection

```python
class CSRFProtection:
    """CSRF token management"""
    
    async def generate_token(self, session_id: str) -> str:
        """Generate CSRF token for session"""
        
        csrf_token = secrets.token_urlsafe(32)
        
        # Store token in Redis
        await redis.setex(
            f"csrf:{session_id}",
            3600,  # 1 hour
            csrf_token
        )
        
        return csrf_token
    
    async def validate_token(self, session_id: str, csrf_token: str) -> bool:
        """Validate CSRF token"""
        
        stored_token = await redis.get(f"csrf:{session_id}")
        
        if not stored_token:
            return False
        
        return secrets.compare_digest(csrf_token, stored_token)

# FastAPI dependency
async def verify_csrf(
    csrf_token: str = Header(None, alias="X-CSRF-Token"),
    session: dict = Depends(get_current_session)
):
    """Verify CSRF token for state-changing operations"""
    
    if not csrf_token:
        raise HTTPException(status_code=403, detail="CSRF token missing")
    
    csrf_protection = CSRFProtection()
    if not await csrf_protection.validate_token(session['id'], csrf_token):
        raise HTTPException(status_code=403, detail="Invalid CSRF token")
```

## Session Termination

### Manual Logout

```python
async def logout(session_token: str) -> None:
    """Terminate session (logout)"""
    
    session_id = hashlib.sha256(session_token.encode()).hexdigest()
    
    # Get session info for logging
    session = await db.fetchrow(
        """
        SELECT user_id, username FROM sessions s
        JOIN users u ON s.user_id = u.id
        WHERE s.session_id = $1
        """,
        session_id
    )
    
    if session:
        # Remove from Redis
        await redis.delete(f"session:{session_id}")
        
        # Mark as terminated in database
        await db.execute(
            """
            UPDATE sessions
            SET terminated_at = NOW()
            WHERE session_id = $1
            """,
            session_id
        )
        
        # Log logout
        await log_audit_event(
            actor=session['username'],
            action="logout",
            details={"session_id": session_id}
        )
```

### Remote Session Termination

```python
async def terminate_session(session_id: str, terminated_by: User) -> bool:
    """Terminate another user's session (admin action)"""
    
    # Get session info
    session = await db.fetchrow(
        """
        SELECT s.*, u.username FROM sessions s
        JOIN users u ON s.user_id = u.id
        WHERE s.session_id = $1
        """,
        session_id
    )
    
    if not session:
        return False
    
    # Check permission
    if not await has_permission(terminated_by, "sessions.terminate"):
        raise PermissionDenied("Cannot terminate sessions")
    
    # Remove from Redis
    await redis.delete(f"session:{session_id}")
    
    # Mark as terminated
    await db.execute(
        """
        UPDATE sessions
        SET terminated_at = NOW(), terminated_by = $1
        WHERE session_id = $2
        """,
        terminated_by.id,
        session_id
    )
    
    # Log termination
    await log_audit_event(
        actor=terminated_by.username,
        action="session_terminated",
        target=session['username'],
        details={"session_id": session_id, "reason": "admin_action"}
    )
    
    # Notify user
    await send_email(
        to=session['email'],
        subject="Session Terminated",
        template="session_terminated",
        context={
            "user": session['username'],
            "terminated_by": terminated_by.username
        }
    )
    
    return True
```

### Bulk Session Termination

```python
async def terminate_all_sessions(user_id: str, except_session: Optional[str] = None) -> int:
    """Terminate all sessions for a user"""
    
    # Get all active sessions
    sessions = await db.fetch(
        """
        SELECT session_id FROM sessions
        WHERE user_id = $1 AND expires_at > NOW()
        """,
        user_id
    )
    
    count = 0
    for session in sessions:
        session_id = session['session_id']
        
        # Skip current session if specified
        if except_session and session_id == except_session:
            continue
        
        # Remove from Redis
        await redis.delete(f"session:{session_id}")
        count += 1
    
    # Mark all as terminated in database
    await db.execute(
        """
        UPDATE sessions
        SET terminated_at = NOW()
        WHERE user_id = $1 AND expires_at > NOW()
        AND ($2::text IS NULL OR session_id != $2)
        """,
        user_id,
        except_session
    )
    
    return count
```

## Session Monitoring

### Active Sessions View

```python
@router.get("/api/sessions/active")
async def get_active_sessions(current_user: User = Depends(get_current_user)):
    """Get user's active sessions"""
    
    sessions = await db.fetch(
        """
        SELECT
            session_id,
            ip_address,
            user_agent,
            device_info,
            created_at,
            last_activity,
            expires_at
        FROM sessions
        WHERE user_id = $1 AND expires_at > NOW()
        ORDER BY last_activity DESC
        """,
        current_user.id
    )
    
    return [
        {
            "id": s['session_id'],
            "ip_address": s['ip_address'],
            "device": json.loads(s['device_info']),
            "created_at": s['created_at'],
            "last_activity": s['last_activity'],
            "is_current": s['session_id'] == current_session_id
        }
        for s in sessions
    ]
```

### Anomaly Detection

```python
class SessionAnomalyDetector:
    """Detect anomalous session behavior"""
    
    async def check_anomalies(self, session_id: str, current_request: dict) -> List[str]:
        """Check for anomalous behavior"""
        
        anomalies = []
        
        # Get session history
        session = await db.fetchrow(
            """
            SELECT * FROM sessions WHERE session_id = $1
            """,
            session_id
        )
        
        # Check for impossible travel
        if await self._detect_impossible_travel(session, current_request):
            anomalies.append("impossible_travel")
        
        # Check for unusual access patterns
        if await self._detect_unusual_access_pattern(session['user_id']):
            anomalies.append("unusual_access_pattern")
        
        # Check for suspicious user agent changes
        if session['user_agent'] != current_request['user_agent']:
            anomalies.append("user_agent_change")
        
        # Log anomalies
        if anomalies:
            await log_security_event(
                event_type="session_anomaly_detected",
                session_id=session_id,
                anomalies=anomalies
            )
        
        return anomalies
    
    async def _detect_impossible_travel(self, session: dict, current_request: dict) -> bool:
        """Detect impossible travel between locations"""
        
        # Get previous location
        prev_location = json.loads(session['device_info']).get('location', {})
        curr_location = current_request.get('location', {})
        
        if not prev_location or not curr_location:
            return False
        
        # Calculate distance and time
        distance_km = self._calculate_distance(
            prev_location['latitude'],
            prev_location['longitude'],
            curr_location['latitude'],
            curr_location['longitude']
        )
        
        time_diff_hours = (datetime.utcnow() - session['last_activity']).total_seconds() / 3600
        
        # Check if travel speed is impossible (> 1000 km/h)
        if time_diff_hours > 0:
            speed = distance_km / time_diff_hours
            if speed > 1000:
                return True
        
        return False
    
    def _calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate distance between two coordinates (Haversine formula)"""
        from math import radians, sin, cos, sqrt, atan2
        
        R = 6371  # Earth radius in km
        
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * atan2(sqrt(a), sqrt(1-a))
        
        return R * c
```

## Related Documentation

- [Authentication Overview](./01-overview.md) - System architecture
- [Local Authentication](./02-local-auth.md) - Password authentication
- [Multi-Factor Authentication](./03-mfa.md) - MFA implementation
- [API Authentication](./05-api-auth.md) - API access methods
