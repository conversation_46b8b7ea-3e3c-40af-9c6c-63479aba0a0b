---
title: "Monitoring and Alerting"
section: "Controller"
order: 7
tags: ["controller", "monitoring", "alerting", "metrics", "observability"]
last_updated: "2025-11-08"
---

# Monitoring and Alerting

The Controller provides comprehensive monitoring and alerting capabilities for the entire platform, including VM health, service status, user activity, and system performance. The monitoring system collects metrics from agents, evaluates alert rules, and dispatches notifications through multiple channels.

## Monitoring Architecture

### Components

**Metrics Collection**: Agents continuously collect metrics and send to Controller via WebSocket or REST API.

**Metrics Storage**: PostgreSQL stores raw and aggregated metrics with time-series partitioning.

**Metrics Aggregation**: Celery workers aggregate raw metrics into 5-minute, 1-hour, and 1-day rollups.

**Alert Evaluation**: Celery workers evaluate alert rules against current metrics every minute.

**Notification Dispatch**: Alert notifications sent through configured channels (email, Slack, PagerDuty, webhooks).

**Metrics API**: REST API provides access to metrics for dashboards and external systems.

### Data Flow

```
Agent → WebSocket/REST → Controller → PostgreSQL (raw metrics)
                                    ↓
                            Celery Workers (aggregation)
                                    ↓
                            PostgreSQL (aggregated metrics)
                                    ↓
                            Celery Workers (alert evaluation)
                                    ↓
                            Notification Services
```

## Metrics Collection

### VM-Level Metrics

Collected every 60 seconds (configurable):

**CPU Metrics**:
- `vm.cpu.usage_percent`: Overall CPU usage percentage
- `vm.cpu.usage_per_core`: Per-core CPU usage array
- `vm.cpu.load_1m`, `vm.cpu.load_5m`, `vm.cpu.load_15m`: Load averages
- `vm.cpu.context_switches`: Context switches per second
- `vm.cpu.interrupts`: Interrupts per second

**Memory Metrics**:
- `vm.memory.used_bytes`: Used memory in bytes
- `vm.memory.available_bytes`: Available memory in bytes
- `vm.memory.usage_percent`: Memory usage percentage
- `vm.memory.swap_used_bytes`: Swap usage in bytes
- `vm.memory.cached_bytes`: Cached memory in bytes
- `vm.memory.buffers_bytes`: Buffer memory in bytes

**Disk Metrics** (per mount point):
- `vm.disk.used_bytes`: Used disk space
- `vm.disk.available_bytes`: Available disk space
- `vm.disk.usage_percent`: Disk usage percentage
- `vm.disk.read_bytes_per_sec`: Read throughput
- `vm.disk.write_bytes_per_sec`: Write throughput
- `vm.disk.read_ops_per_sec`: Read operations per second
- `vm.disk.write_ops_per_sec`: Write operations per second
- `vm.disk.inode_usage_percent`: Inode usage percentage

**Network Metrics** (per interface):
- `vm.network.bytes_sent_per_sec`: Outbound bytes per second
- `vm.network.bytes_recv_per_sec`: Inbound bytes per second
- `vm.network.packets_sent_per_sec`: Outbound packets per second
- `vm.network.packets_recv_per_sec`: Inbound packets per second
- `vm.network.errors_in`: Inbound errors
- `vm.network.errors_out`: Outbound errors
- `vm.network.drops_in`: Inbound drops
- `vm.network.drops_out`: Outbound drops

**System Metrics**:
- `vm.system.uptime_seconds`: System uptime
- `vm.system.process_count`: Total process count
- `vm.system.thread_count`: Total thread count
- `vm.system.file_descriptors_used`: Open file descriptors
- `vm.system.file_descriptors_max`: Maximum file descriptors

### Service-Level Metrics

Collected every 30 seconds (configurable):

**Performance Metrics**:
- `service.requests_per_sec`: Request rate
- `service.response_time_ms`: Average response time
- `service.response_time_p50`: 50th percentile response time
- `service.response_time_p95`: 95th percentile response time
- `service.response_time_p99`: 99th percentile response time
- `service.error_rate_percent`: Error rate percentage
- `service.error_count`: Total error count
- `service.active_connections`: Current active connections
- `service.throughput_bytes_per_sec`: Data throughput

**Resource Metrics**:
- `service.cpu.usage_percent`: Service CPU usage
- `service.memory.used_bytes`: Service memory usage
- `service.memory.rss_bytes`: Resident set size
- `service.memory.vms_bytes`: Virtual memory size
- `service.network.bytes_sent`: Bytes sent by service
- `service.network.bytes_recv`: Bytes received by service
- `service.disk.read_bytes`: Disk bytes read
- `service.disk.write_bytes`: Disk bytes written
- `service.file_descriptors`: Open file descriptors

**Health Metrics**:
- `service.health_score`: Health score (0-100)
- `service.health_check_success`: Health check success (0 or 1)
- `service.health_check_duration_ms`: Health check duration
- `service.uptime_seconds`: Service uptime

### Platform Metrics

Controller self-monitoring metrics:

**Request Metrics**:
- `controller.requests_total`: Total API requests
- `controller.requests_per_sec`: Request rate
- `controller.request_duration_ms`: Request duration
- `controller.request_errors`: Request errors

**WebSocket Metrics**:
- `controller.websocket_connections`: Active WebSocket connections
- `controller.websocket_messages_sent`: Messages sent
- `controller.websocket_messages_received`: Messages received

**Database Metrics**:
- `controller.db.connections_active`: Active database connections
- `controller.db.connections_idle`: Idle database connections
- `controller.db.query_duration_ms`: Query duration
- `controller.db.slow_queries`: Slow query count

**Cache Metrics**:
- `controller.cache.hits`: Cache hits
- `controller.cache.misses`: Cache misses
- `controller.cache.hit_rate_percent`: Cache hit rate

**Agent Metrics**:
- `controller.agents_connected`: Connected agents
- `controller.agents_disconnected`: Disconnected agents
- `controller.agent_heartbeat_latency_ms`: Heartbeat latency

## Alert Rules

### Alert Rule Structure

```yaml
alert_rule:
  name: "High CPU Usage"
  description: "Alert when CPU exceeds 80% for 5 minutes"
  enabled: true
  
  # What to monitor
  metric: "vm.cpu.usage_percent"
  resource_type: "vm"
  resource_filter:
    environment: "production"
    tags: ["critical"]
  
  # Trigger condition
  condition:
    operator: "greater_than"
    threshold: 80
    duration: "5m"
    evaluation_interval: "1m"
  
  # Alert properties
  severity: "warning"
  priority: "high"
  
  # Actions
  actions:
    - type: "email"
      recipients: ["<EMAIL>"]
      template: "high_cpu_alert"
    
    - type: "slack"
      channel: "#production-alerts"
      mention: "@oncall"
    
    - type: "pagerduty"
      service_key: "xxxxx"
      escalation_policy: "default"
  
  # Notification settings
  notification:
    grouping_window: "15m"
    repeat_interval: "4h"
    auto_resolve: true
    suppress_during: ["maintenance_window"]
```

### Condition Operators

**Comparison Operators**:
- `greater_than`: Value > threshold
- `greater_than_or_equal`: Value >= threshold
- `less_than`: Value < threshold
- `less_than_or_equal`: Value <= threshold
- `equal`: Value == threshold
- `not_equal`: Value != threshold

**Range Operators**:
- `between`: threshold_min < Value < threshold_max
- `outside`: Value < threshold_min OR Value > threshold_max

**Change Operators**:
- `increased_by`: Value increased by X% or X units
- `decreased_by`: Value decreased by X% or X units
- `changed_by`: Value changed by X% or X units (either direction)

**Statistical Operators**:
- `anomaly`: ML-detected anomaly (requires ML module)
- `spike`: Sudden increase beyond normal variance
- `drop`: Sudden decrease beyond normal variance

### Alert Severity Levels

**Info**: Informational alerts, no immediate action required.
- Example: New service discovered, configuration changed

**Warning**: Potential issues that should be investigated.
- Example: CPU usage above 80%, disk space above 85%

**Critical**: Serious issues requiring immediate attention.
- Example: Service down, disk space above 95%, agent disconnected

### Alert Evaluation

**Evaluation Interval**: How often to check condition (default: 1 minute).

**Duration**: How long condition must be true before triggering (prevents flapping).

**Example**: Alert triggers if CPU > 80% for 5 consecutive minutes.

**Evaluation Logic**:
1. Fetch current metric value
2. Compare against threshold using operator
3. If condition true, increment duration counter
4. If condition false, reset duration counter
5. If duration counter >= configured duration, trigger alert
6. If alert already active and condition false, resolve alert (if auto_resolve enabled)

## Alert Notifications

### Notification Channels

#### Email

**SMTP Configuration**:
```yaml
email:
  smtp_host: "smtp.example.com"
  smtp_port: 587
  smtp_user: "<EMAIL>"
  smtp_password: "password"
  from_address: "VM Gateway Alerts <<EMAIL>>"
  use_tls: true
```

**Email Template**:
```html
Subject: [WARNING] High CPU Usage on vm-prod-01

VM Gateway Alert

Alert: High CPU Usage
Severity: WARNING
Resource: vm-prod-01 (*********)
Triggered: 2025-11-08 10:30:00 UTC

Current Value: 85.3%
Threshold: 80%
Duration: 5 minutes

View in Dashboard: https://controller.example.com/vms/vm_abc123

---
This is an automated alert from VM Gateway.
```

#### Slack

**Webhook Integration**:
```yaml
slack:
  webhook_url: "https://hooks.slack.com/services/T00000000/B00000000/XXXXXXXXXXXXXXXXXXXX"
  channel: "#production-alerts"
  username: "VM Gateway"
  icon_emoji: ":warning:"
```

**Slack Message Format**:
```json
{
  "channel": "#production-alerts",
  "username": "VM Gateway",
  "icon_emoji": ":warning:",
  "attachments": [
    {
      "color": "warning",
      "title": "High CPU Usage",
      "title_link": "https://controller.example.com/vms/vm_abc123",
      "fields": [
        {"title": "Resource", "value": "vm-prod-01", "short": true},
        {"title": "Severity", "value": "WARNING", "short": true},
        {"title": "Current Value", "value": "85.3%", "short": true},
        {"title": "Threshold", "value": "80%", "short": true}
      ],
      "footer": "VM Gateway",
      "ts": **********
    }
  ]
}
```

#### PagerDuty

**Integration**:
```yaml
pagerduty:
  integration_key: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  api_url: "https://events.pagerduty.com/v2/enqueue"
```

**Event Payload**:
```json
{
  "routing_key": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "event_action": "trigger",
  "payload": {
    "summary": "High CPU Usage on vm-prod-01",
    "severity": "warning",
    "source": "vm-prod-01",
    "custom_details": {
      "current_value": "85.3%",
      "threshold": "80%",
      "duration": "5 minutes"
    }
  },
  "links": [
    {
      "href": "https://controller.example.com/vms/vm_abc123",
      "text": "View in Dashboard"
    }
  ]
}
```

#### Webhooks

**Generic Webhook**:
```yaml
webhook:
  url: "https://api.example.com/alerts"
  method: "POST"
  headers:
    Authorization: "Bearer token"
    Content-Type: "application/json"
```

**Webhook Payload**:
```json
{
  "alert_id": "alert_jkl012",
  "rule_name": "High CPU Usage",
  "severity": "warning",
  "status": "triggered",
  "resource_type": "vm",
  "resource_id": "vm_abc123",
  "resource_name": "vm-prod-01",
  "metric": "vm.cpu.usage_percent",
  "current_value": 85.3,
  "threshold": 80,
  "triggered_at": "2025-11-08T10:30:00Z",
  "dashboard_url": "https://controller.example.com/vms/vm_abc123"
}
```

### Notification Features

**Grouping**: Multiple alerts for same resource grouped into single notification.

**Throttling**: Repeat notifications sent at configured interval (e.g., every 4 hours) until resolved.

**Auto-Resolve**: Automatically resolve alert and send resolution notification when condition clears.

**Escalation**: Escalate to higher tier if alert not acknowledged within timeout.

**Maintenance Windows**: Suppress notifications during scheduled maintenance.

**Quiet Hours**: Suppress non-critical notifications during configured hours.

## Alert Management

### Alert Lifecycle

1. **Triggered**: Condition met, alert created, notifications sent
2. **Acknowledged**: User acknowledges alert, stops repeat notifications
3. **Resolved**: Condition cleared or manually resolved, resolution notification sent
4. **Expired**: Alert auto-expires after maximum duration (prevents stale alerts)

### Alert Actions

**Acknowledge**: Mark alert as seen, stops repeat notifications.

```http
POST /api/v1/alerts/{alert_id}/acknowledge
{
  "note": "Investigating high CPU usage"
}
```

**Snooze**: Pause notifications for specified duration.

```http
POST /api/v1/alerts/{alert_id}/snooze
{
  "duration": 3600,
  "note": "Snoozing during maintenance"
}
```

**Resolve**: Manually resolve alert.

```http
POST /api/v1/alerts/{alert_id}/resolve
{
  "note": "CPU usage returned to normal after optimization"
}
```

**Escalate**: Escalate to higher tier support.

```http
POST /api/v1/alerts/{alert_id}/escalate
{
  "tier": "senior_engineer",
  "note": "Unable to resolve, escalating"
}
```

### Alert Dashboard

Web interface provides alert dashboard with:

**Active Alerts**: List of currently active alerts with severity, resource, and duration.

**Alert History**: Historical alerts with resolution time and notes.

**Alert Statistics**: Alert frequency, mean time to acknowledge, mean time to resolve.

**Alert Trends**: Graphs showing alert trends over time.

## Metrics API

### Query Metrics

**Endpoint**: `GET /api/v1/metrics`

**Query Parameters**:
- `resource_type`: vm, service, controller
- `resource_id`: Specific resource ID
- `metric`: Metric name
- `start`: Start timestamp (ISO 8601)
- `end`: End timestamp (ISO 8601)
- `resolution`: Data resolution (1m, 5m, 1h, 1d)
- `aggregation`: Aggregation function (avg, min, max, sum, count)

**Example Request**:
```http
GET /api/v1/metrics?resource_type=vm&resource_id=vm_abc123&metric=cpu.usage_percent&start=2025-11-08T09:00:00Z&end=2025-11-08T10:00:00Z&resolution=1m
```

**Response**:
```json
{
  "resource_type": "vm",
  "resource_id": "vm_abc123",
  "metric": "cpu.usage_percent",
  "start": "2025-11-08T09:00:00Z",
  "end": "2025-11-08T10:00:00Z",
  "resolution": "1m",
  "data_points": [
    {"timestamp": "2025-11-08T09:00:00Z", "value": 45.2},
    {"timestamp": "2025-11-08T09:01:00Z", "value": 46.1},
    {"timestamp": "2025-11-08T09:02:00Z", "value": 44.8}
  ],
  "statistics": {
    "min": 42.1,
    "max": 52.3,
    "avg": 46.5,
    "p50": 46.2,
    "p95": 50.8,
    "p99": 51.9
  }
}
```

### Aggregate Metrics

**Endpoint**: `GET /api/v1/metrics/aggregate`

Aggregate metrics across multiple resources.

**Example**: Average CPU usage across all production VMs.

```http
GET /api/v1/metrics/aggregate?resource_type=vm&metric=cpu.usage_percent&filter=environment:production&aggregation=avg&start=2025-11-08T09:00:00Z&end=2025-11-08T10:00:00Z&resolution=5m
```

## External Integrations

### Prometheus

**Metrics Endpoint**: `/metrics` exposes Prometheus-format metrics.

**Scrape Configuration**:
```yaml
scrape_configs:
  - job_name: 'vmgateway'
    static_configs:
      - targets: ['controller.example.com:443']
    scheme: https
    metrics_path: /metrics
    bearer_token: 'prometheus_token'
```

**Exported Metrics**: All platform metrics exported in Prometheus format.

### Grafana

**Data Source**: Prometheus or direct PostgreSQL connection.

**Pre-built Dashboards**: Official Grafana dashboards for VM Gateway.

**Dashboard Features**:
- VM overview dashboard
- Service health dashboard
- Alert dashboard
- User activity dashboard
- Platform performance dashboard

### Datadog

**Agent Integration**: Datadog agent can scrape metrics endpoint.

**Custom Metrics**: Send metrics directly to Datadog API.

**Log Forwarding**: Forward logs to Datadog for centralized logging.

### ELK Stack

**Log Forwarding**: Forward logs to Logstash or directly to Elasticsearch.

**Kibana Dashboards**: Pre-built Kibana dashboards for log analysis.

**Index Templates**: Elasticsearch index templates for optimal log storage.

## Performance Considerations

**Metrics Batching**: Agents batch metrics before sending to reduce network overhead.

**Aggregation**: Pre-aggregate metrics to reduce storage and query load.

**Partitioning**: Time-series tables partitioned by month for efficient queries.

**Indexing**: Strategic indexes on timestamp and resource ID for fast queries.

**Retention**: Automatic data retention policies to manage storage growth.

**Caching**: Frequently accessed metrics cached in Redis.

## Monitoring Best Practices

**Set Appropriate Thresholds**: Avoid alert fatigue by setting realistic thresholds.

**Use Duration**: Require condition to persist for duration to avoid flapping.

**Group Alerts**: Group related alerts to reduce notification volume.

**Prioritize Alerts**: Use severity levels to prioritize response.

**Document Runbooks**: Link alerts to runbooks with resolution steps.

**Test Alerts**: Regularly test alert rules and notification channels.

**Review Metrics**: Regularly review metrics to identify trends and capacity needs.

**Tune Over Time**: Adjust thresholds and rules based on operational experience.
