# Secret Storage

The VM Gateway platform implements a robust secret storage system that ensures sensitive data is protected at rest through multiple layers of encryption and security controls.

## Storage Architecture

```
┌─────────────────────────────────────────────────────────┐
│                  Application Layer                      │
│  ┌───────────────────────────────────────────────────┐  │
│  │         Secrets API                               │  │
│  │  - Validate requests                              │  │
│  │  - Enforce access control                         │  │
│  └──────────────┬────────────────────────────────────┘  │
└─────────────────┼───────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────┐
│              Encryption Layer                           │
│  ┌───────────────────────────────────────────────────┐  │
│  │         Encryption Engine                         │  │
│  │  - AES-256-GCM encryption                         │  │
│  │  - Key derivation (PBKDF2/Argon2)                │  │
│  │  - Envelope encryption                            │  │
│  └──────────────┬────────────────────────────────────┘  │
└─────────────────┼───────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────┐
│              Storage Layer                              │
│  ┌───────────────────────────────────────────────────┐  │
│  │         PostgreSQL Database                       │  │
│  │  - Encrypted secret values                        │  │
│  │  - Secret metadata                                │  │
│  │  - Version history                                │  │
│  └───────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────┘
```

## Encryption Methods

### Envelope Encryption

The platform uses envelope encryption for optimal security and performance:

1. **Data Encryption Key (DEK)**: Unique key for each secret
2. **Key Encryption Key (KEK)**: Encrypts the DEKs
3. **Master Key**: Encrypts the KEKs (stored in KMS or HSM)

**Benefits**:
- Fast encryption/decryption (symmetric keys)
- Easy key rotation (only re-encrypt KEKs)
- Separation of concerns
- Integration with external KMS

### AES-256-GCM

All secrets are encrypted using AES-256 in Galois/Counter Mode (GCM):

**Features**:
- 256-bit key length (maximum security)
- Authenticated encryption (prevents tampering)
- Initialization vector (IV) for each encryption
- Authentication tag verification

**Implementation**:
```python
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
import os

def encrypt_secret(plaintext: bytes, key: bytes) -> dict:
    """Encrypt secret using AES-256-GCM"""
    aesgcm = AESGCM(key)
    nonce = os.urandom(12)  # 96-bit nonce
    ciphertext = aesgcm.encrypt(nonce, plaintext, None)
    
    return {
        "ciphertext": ciphertext,
        "nonce": nonce,
        "algorithm": "AES-256-GCM"
    }

def decrypt_secret(encrypted_data: dict, key: bytes) -> bytes:
    """Decrypt secret using AES-256-GCM"""
    aesgcm = AESGCM(key)
    plaintext = aesgcm.decrypt(
        encrypted_data["nonce"],
        encrypted_data["ciphertext"],
        None
    )
    return plaintext
```


### Key Derivation

For password-based encryption, the platform uses strong key derivation functions:

**Argon2id** (Recommended):
- Memory-hard algorithm
- Resistant to GPU/ASIC attacks
- Configurable memory, iterations, parallelism

**PBKDF2-HMAC-SHA256** (Fallback):
- NIST approved
- Widely supported
- Configurable iterations (minimum 100,000)

## Database Schema

### Secrets Table

```sql
CREATE TABLE secrets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    type VARCHAR(50) NOT NULL,
    description TEXT,
    encrypted_value BYTEA NOT NULL,
    encryption_metadata JSONB NOT NULL,
    version INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES users(id),
    deleted_at TIMESTAMP WITH TIME ZONE,
    tags TEXT[],
    metadata JSONB DEFAULT '{}'::jsonb,
    
    CONSTRAINT secrets_name_check CHECK (name ~ '^[a-zA-Z0-9/_-]+$')
);

CREATE INDEX idx_secrets_name ON secrets(name) WHERE deleted_at IS NULL;
CREATE INDEX idx_secrets_type ON secrets(type) WHERE deleted_at IS NULL;
CREATE INDEX idx_secrets_tags ON secrets USING GIN(tags);
CREATE INDEX idx_secrets_created_by ON secrets(created_by);
```

### Secret Versions Table

```sql
CREATE TABLE secret_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    secret_id UUID NOT NULL REFERENCES secrets(id) ON DELETE CASCADE,
    version INTEGER NOT NULL,
    encrypted_value BYTEA NOT NULL,
    encryption_metadata JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    
    UNIQUE(secret_id, version)
);

CREATE INDEX idx_secret_versions_secret_id ON secret_versions(secret_id);
CREATE INDEX idx_secret_versions_created_at ON secret_versions(created_at);
```

### Encryption Keys Table

```sql
CREATE TABLE encryption_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key_id VARCHAR(255) NOT NULL UNIQUE,
    encrypted_key BYTEA NOT NULL,
    algorithm VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    rotated_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    metadata JSONB DEFAULT '{}'::jsonb,
    
    CONSTRAINT encryption_keys_status_check 
        CHECK (status IN ('active', 'rotating', 'retired'))
);

CREATE INDEX idx_encryption_keys_status ON encryption_keys(status);
CREATE INDEX idx_encryption_keys_key_id ON encryption_keys(key_id);
```

## Storage Backends

### PostgreSQL (Primary)

The default storage backend uses PostgreSQL with:

**Advantages**:
- ACID compliance
- Strong consistency
- Built-in replication
- Mature ecosystem
- SQL query capabilities

**Configuration**:
```python
# config/secrets.yaml
storage:
  backend: postgresql
  connection:
    host: localhost
    port: 5432
    database: vm_gateway
    user: secrets_service
    password: ${DB_PASSWORD}
    ssl_mode: require
  encryption:
    algorithm: AES-256-GCM
    key_derivation: argon2id
  performance:
    connection_pool_size: 20
    max_overflow: 10
    pool_timeout: 30
```

### External Vault (Optional)

Integration with external vault systems:

**HashiCorp Vault**:
```python
# config/secrets.yaml
storage:
  backend: vault
  connection:
    address: https://vault.example.com:8200
    token: ${VAULT_TOKEN}
    namespace: vm-gateway
    mount_path: secret
  options:
    tls_verify: true
    timeout: 30
```

**AWS Secrets Manager**:
```python
# config/secrets.yaml
storage:
  backend: aws_secrets_manager
  connection:
    region: us-east-1
    access_key_id: ${AWS_ACCESS_KEY_ID}
    secret_access_key: ${AWS_SECRET_ACCESS_KEY}
  options:
    kms_key_id: arn:aws:kms:us-east-1:123456789:key/abc-123
```

## Key Management

### Master Key Storage

The master encryption key can be stored in multiple ways:

**1. Environment Variable** (Development):
```bash
export VM_GATEWAY_MASTER_KEY="base64_encoded_key"
```

**2. File System** (Simple deployments):
```bash
# /etc/vm-gateway/master.key (chmod 400)
echo "base64_encoded_key" > /etc/vm-gateway/master.key
chmod 400 /etc/vm-gateway/master.key
chown vm-gateway:vm-gateway /etc/vm-gateway/master.key
```

**3. Hardware Security Module** (Production):
```python
# config/secrets.yaml
key_management:
  provider: hsm
  connection:
    type: pkcs11
    library_path: /usr/lib/libsofthsm2.so
    slot: 0
    pin: ${HSM_PIN}
```

**4. Cloud KMS** (Cloud deployments):
```python
# config/secrets.yaml
key_management:
  provider: aws_kms
  key_id: arn:aws:kms:us-east-1:123456789:key/abc-123
  region: us-east-1
```

### Key Rotation

Encryption keys are rotated periodically:

**Rotation Process**:
1. Generate new KEK
2. Encrypt new KEK with master key
3. Re-encrypt all DEKs with new KEK
4. Update encryption metadata
5. Mark old KEK as retired
6. Audit rotation event

**Rotation Schedule**:
- Master key: Annually or on compromise
- KEKs: Quarterly
- DEKs: On secret update or annually

## Data Protection

### Encryption at Rest

All sensitive data is encrypted before storage:

**Encrypted Fields**:
- Secret values
- Encryption keys (envelope encrypted)
- Backup files
- Log files containing secrets

**Unencrypted Fields** (Metadata only):
- Secret names
- Types
- Tags
- Timestamps
- Access control lists

### Secure Deletion

When secrets are deleted:

1. **Soft Delete**: Mark as deleted, retain for recovery period
2. **Cryptographic Erasure**: Delete encryption keys
3. **Hard Delete**: Overwrite data after retention period
4. **Backup Cleanup**: Remove from backups after retention

```python
def delete_secret(secret_id: str, hard_delete: bool = False):
    """Delete a secret securely"""
    if hard_delete:
        # Cryptographic erasure
        delete_encryption_keys(secret_id)
        # Overwrite data
        overwrite_secret_data(secret_id)
        # Remove from database
        db.execute("DELETE FROM secrets WHERE id = ?", secret_id)
    else:
        # Soft delete
        db.execute(
            "UPDATE secrets SET deleted_at = NOW() WHERE id = ?",
            secret_id
        )
```

### Backup Encryption

Backups are encrypted separately:

```bash
# Backup with encryption
pg_dump vm_gateway | \
  gpg --encrypt --recipient <EMAIL> | \
  aws s3 cp - s3://backups/vm-gateway-$(date +%Y%m%d).sql.gpg
```

## Performance Optimization

### Caching Strategy

Decrypted secrets are cached in memory:

**Cache Configuration**:
```python
cache:
  enabled: true
  backend: redis
  ttl: 300  # 5 minutes
  max_size: 1000
  encryption: true  # Encrypt cached values
```

**Cache Invalidation**:
- On secret update
- On permission change
- On TTL expiration
- On manual flush

### Connection Pooling

Database connections are pooled for performance:

```python
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

engine = create_engine(
    "postgresql://user:pass@localhost/vm_gateway",
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=10,
    pool_timeout=30,
    pool_recycle=3600
)
```

### Batch Operations

Retrieve multiple secrets efficiently:

```python
def get_secrets_batch(secret_ids: list[str]) -> dict:
    """Retrieve multiple secrets in one query"""
    secrets = db.execute(
        "SELECT id, name, encrypted_value, encryption_metadata "
        "FROM secrets WHERE id = ANY(?)",
        secret_ids
    )
    
    return {
        secret.id: decrypt_secret(secret)
        for secret in secrets
    }
```

## Security Considerations

### Access Patterns

Monitor and alert on suspicious access:

- Multiple failed access attempts
- Access to many secrets in short time
- Access from unusual locations
- Access outside business hours
- Bulk secret exports

### Encryption Verification

Regularly verify encryption integrity:

```python
def verify_encryption_integrity():
    """Verify all secrets are properly encrypted"""
    secrets = db.execute("SELECT id, encrypted_value FROM secrets")
    
    for secret in secrets:
        try:
            # Attempt decryption
            decrypt_secret(secret)
        except Exception as e:
            alert(f"Encryption integrity check failed for {secret.id}: {e}")
```

### Compliance Requirements

Meet regulatory requirements:

- **FIPS 140-2**: Use FIPS-approved algorithms
- **PCI DSS**: Encrypt cardholder data, rotate keys
- **HIPAA**: Encrypt PHI, maintain audit logs
- **GDPR**: Encrypt personal data, support deletion

## Disaster Recovery

### Backup Strategy

Regular encrypted backups:

```bash
# Daily full backup
0 2 * * * /usr/local/bin/backup-secrets.sh

# Hourly incremental backup
0 * * * * /usr/local/bin/backup-secrets-incremental.sh
```

### Recovery Procedures

Restore from backup:

```bash
# Decrypt backup
gpg --decrypt backup.sql.gpg > backup.sql

# Restore to database
psql vm_gateway < backup.sql

# Verify encryption keys
python scripts/verify_keys.py

# Test secret access
python scripts/test_secret_access.py
```

## Best Practices

1. **Never log secret values**: Only log metadata
2. **Use envelope encryption**: Simplifies key rotation
3. **Rotate keys regularly**: Follow rotation schedule
4. **Monitor access patterns**: Detect anomalies
5. **Encrypt backups**: Protect data at rest
6. **Test recovery**: Regularly test backup restoration
7. **Limit key access**: Restrict master key access
8. **Use HSM in production**: Hardware-backed security

## Next Steps

- [Vault Integration](03-vault-integration.md) - External vault systems
- [Lifecycle](04-lifecycle.md) - Secret lifecycle management
- [Access Control](05-access-control.md) - Permission system
- [Rotation](06-rotation.md) - Automatic rotation policies
