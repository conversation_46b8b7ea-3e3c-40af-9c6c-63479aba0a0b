# Controller Configuration

Complete reference for configuring the VM Gateway controller.

## Configuration File Location

The controller reads configuration from a YAML file specified at startup:

**Linux:**
```bash
/etc/vm-gateway/controller.yaml
```

**Windows:**
```
C:\ProgramData\VMGateway\config\controller.yaml
```

**macOS:**
```bash
/usr/local/etc/vm-gateway/controller.yaml
```

## Configuration File Format

The configuration file uses YAML format. Here's a complete example with all available options:

```yaml
# VM Gateway Controller Configuration
# Version: a.0.0-1

# Server configuration
server:
  # Host to bind to (0.0.0.0 for all interfaces)
  host: 0.0.0.0
  
  # Web interface port
  web_port: 8443
  
  # Agent API port
  agent_port: 8444
  
  # Client tunnel port
  tunnel_port: 8445
  
  # Enable debug mode (verbose logging)
  debug: false
  
  # Worker processes (0 = auto-detect CPU cores)
  workers: 0
  
  # Request timeout in seconds
  timeout: 30

# TLS/SSL configuration
tls:
  # Enable TLS (required for production)
  enabled: true
  
  # Certificate file path
  cert_file: /path/to/cert.pem
  
  # Private key file path
  key_file: /path/to/key.pem
  
  # CA certificate for client verification (optional)
  ca_file: /path/to/ca.pem
  
  # Minimum TLS version (1.2 or 1.3)
  min_version: "1.2"
  
  # Cipher suites (leave empty for secure defaults)
  cipher_suites: []

# Database configuration
database:
  # Database host
  host: localhost
  
  # Database port
  port: 5432
  
  # Database name
  name: vm_gateway
  
  # Database user
  user: vm_gateway
  
  # Database password
  password: your_secure_password
  
  # SSL mode (disable, require, verify-ca, verify-full)
  ssl_mode: require
  
  # Connection pool settings
  pool:
    min_size: 5
    max_size: 20
    max_overflow: 10
    timeout: 30
    recycle: 3600
  
  # Query timeout in seconds
  query_timeout: 30
  
  # Enable query logging (debug only)
  log_queries: false

# Redis configuration
redis:
  # Redis host
  host: localhost
  
  # Redis port
  port: 6379
  
  # Redis password
  password: your_redis_password
  
  # Redis database number
  db: 0
  
  # Enable SSL/TLS
  ssl: false
  
  # Connection pool size
  pool_size: 10
  
  # Connection timeout in seconds
  timeout: 5
  
  # Key prefix for namespacing
  key_prefix: "vmgateway:"

# Authentication configuration
auth:
  # Secret key for JWT tokens (min 32 characters)
  secret_key: your_secret_key_here_minimum_32_characters_long
  
  # JWT algorithm
  algorithm: HS256
  
  # Access token expiration (seconds)
  access_token_expiry: 3600
  
  # Refresh token expiration (seconds)
  refresh_token_expiry: 604800
  
  # Session timeout (seconds)
  session_timeout: 3600
  
  # Absolute session timeout (seconds)
  absolute_session_timeout: 28800
  
  # Maximum concurrent sessions per user
  max_sessions_per_user: 3
  
  # Enable "remember me" functionality
  allow_remember_me: false
  
  # Remember me duration (seconds)
  remember_me_duration: 2592000

# Password policy
password_policy:
  # Minimum password length
  min_length: 12
  
  # Require uppercase letters
  require_uppercase: true
  
  # Require lowercase letters
  require_lowercase: true
  
  # Require numbers
  require_numbers: true
  
  # Require special characters
  require_symbols: true
  
  # Password history (prevent reuse)
  password_history: 5
  
  # Password expiration (days, 0 = never)
  expiration_days: 90
  
  # Maximum failed login attempts
  max_failed_attempts: 5
  
  # Account lockout duration (minutes)
  lockout_duration: 30

# Multi-factor authentication
mfa:
  # Require MFA for all users
  required: false
  
  # Grace period for MFA enrollment (days)
  enrollment_grace_period: 7
  
  # Supported MFA methods
  methods:
    - totp
    - webauthn
    - sms
  
  # TOTP settings
  totp:
    issuer: "VM Gateway"
    period: 30
    digits: 6
    algorithm: SHA1
  
  # WebAuthn settings
  webauthn:
    rp_name: "VM Gateway"
    rp_id: "your-controller-domain.com"
    timeout: 60000
  
  # SMS settings (if enabled)
  sms:
    provider: twilio
    account_sid: your_twilio_sid
    auth_token: your_twilio_token
    from_number: "+**********"

# SSO configuration
sso:
  # Enable SSO
  enabled: false
  
  # SSO provider (saml, oidc, ldap)
  provider: saml
  
  # Auto-provision users on first login
  auto_provision: true
  
  # Default roles for auto-provisioned users
  default_roles:
    - user
  
  # SAML configuration
  saml:
    idp_entity_id: "https://idp.example.com/entity"
    sso_url: "https://idp.example.com/sso"
    slo_url: "https://idp.example.com/slo"
    cert_file: /path/to/idp-cert.pem
    
    # Attribute mapping
    attributes:
      username: "urn:oid:0.9.2342.********.100.1.1"
      email: "urn:oid:0.9.2342.********.100.1.3"
      first_name: "urn:oid:2.5.4.42"
      last_name: "urn:oid:2.5.4.4"
      groups: "urn:oid:1.3.6.1.4.1.5923.1.5.1.1"
    
    # Group to role mapping
    group_mapping:
      "Engineering": ["developer"]
      "DevOps": ["operator"]
      "Admins": ["administrator"]
  
  # OIDC configuration
  oidc:
    issuer: "https://accounts.google.com"
    client_id: your_client_id
    client_secret: your_client_secret
    redirect_uri: "https://your-controller:8443/auth/oidc/callback"
    scopes:
      - openid
      - email
      - profile
  
  # LDAP configuration
  ldap:
    server: "ldap://ldap.example.com:389"
    bind_dn: "cn=admin,dc=example,dc=com"
    bind_password: your_ldap_password
    base_dn: "dc=example,dc=com"
    user_filter: "(uid={username})"
    group_filter: "(member={dn})"
    attributes:
      username: uid
      email: mail
      first_name: givenName
      last_name: sn

# Agent configuration
agent:
  # Agent API authentication
  api_key_header: "X-Agent-API-Key"
  
  # Agent heartbeat interval (seconds)
  heartbeat_interval: 30
  
  # Agent timeout (seconds)
  timeout: 90
  
  # Maximum agents per controller
  max_agents: 1000
  
  # Agent certificate auto-rotation (days)
  cert_rotation_days: 90
  
  # Metrics buffer size
  metrics_buffer_size: 10000
  
  # Metrics batch size
  metrics_batch_size: 100

# Service discovery configuration
discovery:
  # Enable automatic service discovery
  enabled: true
  
  # Service classification confidence threshold
  confidence_threshold: 0.7
  
  # Enable ML-based classification
  ml_classification: false
  
  # ML model path (if enabled)
  ml_model_path: /path/to/model.pkl
  
  # Service health check interval (seconds)
  health_check_interval: 60
  
  # Service timeout (seconds)
  service_timeout: 300

# Monitoring configuration
monitoring:
  # Enable Prometheus metrics endpoint
  prometheus_enabled: true
  
  # Prometheus metrics port
  prometheus_port: 9090
  
  # Metrics retention (days)
  metrics_retention_days: 30
  
  # Enable health checks
  health_checks_enabled: true
  
  # Health check interval (seconds)
  health_check_interval: 30

# Alerting configuration
alerting:
  # Enable alerting
  enabled: true
  
  # Alert evaluation interval (seconds)
  evaluation_interval: 60
  
  # Alert channels
  channels:
    # Email alerts
    email:
      enabled: true
      smtp_host: smtp.example.com
      smtp_port: 587
      smtp_user: <EMAIL>
      smtp_password: your_smtp_password
      from_address: <EMAIL>
      use_tls: true
    
    # Slack alerts
    slack:
      enabled: false
      webhook_url: https://hooks.slack.com/services/YOUR/WEBHOOK/URL
      channel: "#alerts"
      username: "VM Gateway"
    
    # PagerDuty alerts
    pagerduty:
      enabled: false
      integration_key: your_pagerduty_key
    
    # Webhook alerts
    webhook:
      enabled: false
      url: https://your-webhook-endpoint.com
      method: POST
      headers:
        Authorization: "Bearer your_token"

# Proxy configuration
proxy:
  # Enable HTTP/HTTPS proxy
  enabled: true
  
  # Proxy timeout (seconds)
  timeout: 300
  
  # Maximum request size (MB)
  max_request_size: 100
  
  # Enable WebSocket proxying
  websocket_enabled: true
  
  # Buffer size (KB)
  buffer_size: 64
  
  # Enable request logging
  log_requests: true

# Tunnel configuration
tunnel:
  # Enable client tunnels
  enabled: true
  
  # Maximum tunnels per user
  max_tunnels_per_user: 50
  
  # Tunnel idle timeout (seconds)
  idle_timeout: 300
  
  # Tunnel buffer size (KB)
  buffer_size: 64
  
  # Enable tunnel compression
  compression_enabled: true
  
  # Compression level (1-9)
  compression_level: 6

# Secrets management
secrets:
  # Secrets backend (local, vault, aws, azure)
  backend: local
  
  # Local secrets configuration
  local:
    encryption_key: your_encryption_key_32_chars_min
    storage_path: /var/lib/vm-gateway/secrets
  
  # HashiCorp Vault configuration
  vault:
    address: https://vault.example.com:8200
    token: your_vault_token
    mount_path: secret
    namespace: vm-gateway
  
  # AWS Secrets Manager configuration
  aws:
    region: us-east-1
    access_key_id: your_aws_access_key
    secret_access_key: your_aws_secret_key
  
  # Azure Key Vault configuration
  azure:
    vault_url: https://your-vault.vault.azure.net
    tenant_id: your_tenant_id
    client_id: your_client_id
    client_secret: your_client_secret

# Audit logging
audit:
  # Enable audit logging
  enabled: true
  
  # Audit log level (info, warning, error)
  level: info
  
  # Log authentication events
  log_auth: true
  
  # Log authorization events
  log_authz: true
  
  # Log resource access
  log_access: true
  
  # Log configuration changes
  log_config: true
  
  # Audit log retention (days)
  retention_days: 365
  
  # Archive old logs
  archive_enabled: true
  
  # Archive destination (local, s3, azure)
  archive_destination: local
  
  # Archive path
  archive_path: /var/lib/vm-gateway/audit-archive
  
  # S3 archive configuration
  s3:
    bucket: vm-gateway-audit-logs
    region: us-east-1
    access_key_id: your_aws_access_key
    secret_access_key: your_aws_secret_key
  
  # Azure Blob archive configuration
  azure:
    account_name: your_storage_account
    account_key: your_storage_key
    container: audit-logs

# Logging configuration
logging:
  # Log level (debug, info, warning, error, critical)
  level: info
  
  # Log format (text, json)
  format: text
  
  # Log file path
  file: /var/log/vm-gateway/controller.log
  
  # Maximum log file size (MB)
  max_size_mb: 100
  
  # Maximum log file backups
  max_backups: 10
  
  # Log file compression
  compress: true
  
  # Enable console logging
  console: true
  
  # Enable syslog
  syslog_enabled: false
  
  # Syslog address
  syslog_address: localhost:514
  
  # Syslog protocol (udp, tcp)
  syslog_protocol: udp

# Rate limiting
rate_limiting:
  # Enable rate limiting
  enabled: true
  
  # API rate limits
  api:
    # Requests per minute per IP
    per_ip: 100
    
    # Requests per minute per user
    per_user: 1000
    
    # Burst allowance
    burst: 20
  
  # Authentication rate limits
  auth:
    # Login attempts per minute per IP
    per_ip: 10
    
    # Login attempts per minute per user
    per_user: 5

# CORS configuration
cors:
  # Enable CORS
  enabled: true
  
  # Allowed origins
  allowed_origins:
    - https://your-frontend.com
  
  # Allowed methods
  allowed_methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  
  # Allowed headers
  allowed_headers:
    - Authorization
    - Content-Type
  
  # Allow credentials
  allow_credentials: true
  
  # Max age (seconds)
  max_age: 3600

# Backup configuration
backup:
  # Enable automatic backups
  enabled: true
  
  # Backup schedule (cron format)
  schedule: "0 2 * * *"
  
  # Backup destination
  destination: /var/backups/vm-gateway
  
  # Backup retention (days)
  retention_days: 30
  
  # Compress backups
  compress: true
  
  # Encrypt backups
  encrypt: true
  
  # Encryption key
  encryption_key: your_backup_encryption_key

# Feature flags
features:
  # Enable experimental features
  experimental: false
  
  # Enable beta features
  beta: false
  
  # Specific feature flags
  flags:
    ml_classification: false
    advanced_analytics: false
    custom_integrations: true

# Performance tuning
performance:
  # Database connection pool size
  db_pool_size: 20
  
  # Redis connection pool size
  redis_pool_size: 10
  
  # Worker threads
  worker_threads: 4
  
  # Request queue size
  request_queue_size: 1000
  
  # Enable caching
  caching_enabled: true
  
  # Cache TTL (seconds)
  cache_ttl: 300
  
  # Cache size (MB)
  cache_size_mb: 256

# Development settings (disable in production)
development:
  # Enable development mode
  enabled: false
  
  # Enable hot reload
  hot_reload: false
  
  # Enable debug endpoints
  debug_endpoints: false
  
  # Disable authentication (DANGEROUS)
  disable_auth: false
```

## Environment Variables

Configuration values can be overridden using environment variables:

```bash
# Database configuration
export VMGATEWAY_DB_HOST=localhost
export VMGATEWAY_DB_PORT=5432
export VMGATEWAY_DB_NAME=vm_gateway
export VMGATEWAY_DB_USER=vm_gateway
export VMGATEWAY_DB_PASSWORD=your_password

# Redis configuration
export VMGATEWAY_REDIS_HOST=localhost
export VMGATEWAY_REDIS_PORT=6379
export VMGATEWAY_REDIS_PASSWORD=your_redis_password

# Authentication
export VMGATEWAY_AUTH_SECRET_KEY=your_secret_key

# TLS
export VMGATEWAY_TLS_CERT_FILE=/path/to/cert.pem
export VMGATEWAY_TLS_KEY_FILE=/path/to/key.pem

# Logging
export VMGATEWAY_LOG_LEVEL=info
export VMGATEWAY_LOG_FILE=/var/log/vm-gateway/controller.log
```

Environment variables take precedence over configuration file values.

## Configuration Validation

Validate your configuration before starting the controller:

```bash
# Validate configuration file
vm-gateway-controller validate --config /etc/vm-gateway/controller.yaml

# Check for errors
vm-gateway-controller check --config /etc/vm-gateway/controller.yaml

# Show effective configuration (with env vars applied)
vm-gateway-controller show-config --config /etc/vm-gateway/controller.yaml
```

## Minimal Configuration

Minimum required configuration for development:

```yaml
server:
  host: 0.0.0.0
  web_port: 8443

database:
  host: localhost
  port: 5432
  name: vm_gateway
  user: vm_gateway
  password: your_password

redis:
  host: localhost
  port: 6379

auth:
  secret_key: your_secret_key_minimum_32_characters_long

tls:
  enabled: false  # Only for development!

logging:
  level: info
```

## Production Configuration

Recommended configuration for production:

```yaml
server:
  host: 0.0.0.0
  web_port: 8443
  agent_port: 8444
  tunnel_port: 8445
  workers: 0  # Auto-detect
  timeout: 30

tls:
  enabled: true
  cert_file: /etc/vm-gateway/tls/cert.pem
  key_file: /etc/vm-gateway/tls/key.pem
  min_version: "1.3"

database:
  host: db.internal.example.com
  port: 5432
  name: vm_gateway
  user: vm_gateway
  password: ${VMGATEWAY_DB_PASSWORD}
  ssl_mode: verify-full
  pool:
    min_size: 10
    max_size: 50

redis:
  host: redis.internal.example.com
  port: 6379
  password: ${VMGATEWAY_REDIS_PASSWORD}
  ssl: true
  pool_size: 20

auth:
  secret_key: ${VMGATEWAY_AUTH_SECRET}
  access_token_expiry: 3600
  session_timeout: 3600
  max_sessions_per_user: 3

password_policy:
  min_length: 14
  require_uppercase: true
  require_lowercase: true
  require_numbers: true
  require_symbols: true
  password_history: 10
  expiration_days: 90
  max_failed_attempts: 5
  lockout_duration: 30

mfa:
  required: true
  enrollment_grace_period: 7
  methods:
    - totp
    - webauthn

sso:
  enabled: true
  provider: saml
  auto_provision: true
  default_roles:
    - user

monitoring:
  prometheus_enabled: true
  prometheus_port: 9090
  metrics_retention_days: 90

alerting:
  enabled: true
  evaluation_interval: 60
  channels:
    email:
      enabled: true
      smtp_host: smtp.example.com
      smtp_port: 587
      smtp_user: <EMAIL>
      smtp_password: ${SMTP_PASSWORD}
      from_address: <EMAIL>
      use_tls: true

audit:
  enabled: true
  level: info
  log_auth: true
  log_authz: true
  log_access: true
  log_config: true
  retention_days: 365
  archive_enabled: true

logging:
  level: info
  format: json
  file: /var/log/vm-gateway/controller.log
  max_size_mb: 100
  max_backups: 30
  compress: true

rate_limiting:
  enabled: true
  api:
    per_ip: 100
    per_user: 1000
  auth:
    per_ip: 10
    per_user: 5

backup:
  enabled: true
  schedule: "0 2 * * *"
  destination: /var/backups/vm-gateway
  retention_days: 30
  compress: true
  encrypt: true
```

## Configuration Best Practices

### Security

1. **Never commit secrets to version control**
   - Use environment variables for sensitive values
   - Use secrets management systems (Vault, AWS Secrets Manager)

2. **Enable TLS in production**
   - Use TLS 1.3 when possible
   - Use certificates from trusted CAs
   - Enable certificate verification

3. **Strong authentication**
   - Require MFA for all users
   - Enforce strong password policies
   - Enable SSO when available

4. **Rate limiting**
   - Enable rate limiting to prevent abuse
   - Adjust limits based on your usage patterns

### Performance

1. **Database connection pooling**
   - Set pool size based on expected load
   - Monitor connection usage
   - Adjust timeouts appropriately

2. **Redis configuration**
   - Use Redis for session storage
   - Enable persistence for important data
   - Configure appropriate memory limits

3. **Worker processes**
   - Set to 0 for auto-detection
   - Or set to number of CPU cores
   - Monitor CPU usage and adjust

### Reliability

1. **Logging**
   - Use structured logging (JSON format)
   - Set appropriate log levels
   - Implement log rotation
   - Monitor log files for errors

2. **Monitoring**
   - Enable Prometheus metrics
   - Set up health checks
   - Configure alerting

3. **Backups**
   - Enable automatic backups
   - Test restore procedures regularly
   - Store backups off-site

## Related Documentation

- [Controller Overview](/docs/04-controller/01-overview.md) - Controller architecture
- [Deployment Guide](/docs/08-deployment/01-overview.md) - Deployment strategies
- [Security Model](/docs/02-architecture/04-security-model.md) - Security configuration
- [High Availability](/docs/08-deployment/07-high-availability.md) - HA configuration
