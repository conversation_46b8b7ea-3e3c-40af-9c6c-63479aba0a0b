# Adding VMs to VM Gateway

This guide walks you through adding new virtual machines to your VM Gateway deployment for monitoring and access control.

## Overview

Adding a VM to VM Gateway involves:
1. Generating an agent API key
2. Installing the agent on the target VM
3. Configuring the agent to connect to your controller
4. Verifying the connection and service discovery

## Prerequisites

Before adding a VM, ensure:
- The controller is running and accessible
- You have admin access to the controller web interface
- You have admin/root access to the target VM
- Network connectivity exists between the VM and controller
- Required ports are open (see [System Requirements](/docs/00-getting-started/02-system-requirements.md#network-requirements))

## Step 1: Generate Agent API Key

Agent API keys authenticate agents to the controller and control their permissions.

### Via Web Interface

1. Log into the controller web interface
2. Navigate to **Settings** > **Agent Management**
3. Click **Generate New Agent Key**
4. Configure the key:
   - **Name**: Descriptive name (e.g., "production-web-servers")
   - **Expiration**: Set expiration date or leave blank for no expiration
   - **IP Whitelist**: Optional, restrict to specific IP ranges
   - **Permissions**: Select what the agent can do (usually "full" for normal agents)
5. Click **Generate**
6. **Copy the API key immediately** - it won't be shown again
7. Store the key securely (password manager, secrets vault, etc.)

### Via API

```bash
curl -X POST https://your-controller:8443/api/v1/agent-keys \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "production-web-servers",
    "expiration": "2025-12-31T23:59:59Z",
    "ip_whitelist": ["10.0.0.0/8"],
    "permissions": ["discover", "report_metrics", "health_check"]
  }'
```

Response:
```json
{
  "key_id": "agk_1234567890abcdef",
  "api_key": "vmg_agent_1234567890abcdefghijklmnopqrstuvwxyz",
  "name": "production-web-servers",
  "created_at": "2025-11-09T10:30:00Z",
  "expires_at": "2025-12-31T23:59:59Z"
}
```

## Step 2: Download Agent Executable

Download the appropriate agent executable for your VM's operating system.

### Linux

```bash
# Download agent (replace with your actual download URL)
wget https://your-controller:8443/downloads/vm-gateway-agent-linux-amd64 \
  -O /tmp/vm-gateway-agent

# Or use curl
curl -L https://your-controller:8443/downloads/vm-gateway-agent-linux-amd64 \
  -o /tmp/vm-gateway-agent
```

### Windows

```powershell
# Download agent
Invoke-WebRequest -Uri "https://your-controller:8443/downloads/vm-gateway-agent-windows-amd64.exe" `
  -OutFile "C:\Temp\vm-gateway-agent.exe"
```

### macOS

```bash
# Download agent
curl -L https://your-controller:8443/downloads/vm-gateway-agent-darwin-amd64 \
  -o /tmp/vm-gateway-agent
```

## Step 3: Install the Agent

### Linux Installation

```bash
# Move agent to system location
sudo mv /tmp/vm-gateway-agent /usr/local/bin/vm-gateway-agent
sudo chmod +x /usr/local/bin/vm-gateway-agent

# Create configuration directory
sudo mkdir -p /etc/vm-gateway

# Create agent configuration file
sudo tee /etc/vm-gateway/agent.yaml > /dev/null <<EOF
# Controller connection
controller:
  url: https://your-controller:8443
  api_key: vmg_agent_YOUR_API_KEY_HERE
  verify_tls: true
  
# Agent configuration
agent:
  vm_name: $(hostname)
  vm_id: $(hostname)-$(date +%s)
  scan_interval: 60
  report_interval: 30
  
# Service discovery
discovery:
  enabled: true
  scan_type: smart
  port_range: 1-65535
  exclude_ports: []
  
# Metrics collection
metrics:
  enabled: true
  collection_interval: 30
  include_process_metrics: true
  include_network_metrics: true
  
# Logging
logging:
  level: info
  file: /var/log/vm-gateway/agent.log
  max_size_mb: 100
  max_backups: 5
EOF

# Create log directory
sudo mkdir -p /var/log/vm-gateway
sudo chmod 755 /var/log/vm-gateway

# Create systemd service
sudo tee /etc/systemd/system/vm-gateway-agent.service > /dev/null <<EOF
[Unit]
Description=VM Gateway Agent
Documentation=https://your-controller:8443/docs
After=network-online.target
Wants=network-online.target

[Service]
Type=simple
User=root
Group=root
ExecStart=/usr/local/bin/vm-gateway-agent --config /etc/vm-gateway/agent.yaml
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=vm-gateway-agent

# Security hardening
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/vm-gateway /etc/vm-gateway

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd
sudo systemctl daemon-reload

# Enable and start the agent
sudo systemctl enable vm-gateway-agent
sudo systemctl start vm-gateway-agent

# Check status
sudo systemctl status vm-gateway-agent
```

### Windows Installation

```powershell
# Create installation directory
New-Item -ItemType Directory -Path "C:\Program Files\VMGateway" -Force

# Move agent executable
Move-Item -Path "C:\Temp\vm-gateway-agent.exe" `
  -Destination "C:\Program Files\VMGateway\vm-gateway-agent.exe" -Force

# Create configuration directory
New-Item -ItemType Directory -Path "C:\ProgramData\VMGateway\config" -Force

# Create agent configuration file
$config = @"
# Controller connection
controller:
  url: https://your-controller:8443
  api_key: vmg_agent_YOUR_API_KEY_HERE
  verify_tls: true
  
# Agent configuration
agent:
  vm_name: $env:COMPUTERNAME
  vm_id: $env:COMPUTERNAME-$(Get-Date -Format 'yyyyMMddHHmmss')
  scan_interval: 60
  report_interval: 30
  
# Service discovery
discovery:
  enabled: true
  scan_type: smart
  port_range: 1-65535
  exclude_ports: []
  
# Metrics collection
metrics:
  enabled: true
  collection_interval: 30
  include_process_metrics: true
  include_network_metrics: true
  
# Logging
logging:
  level: info
  file: C:\ProgramData\VMGateway\logs\agent.log
  max_size_mb: 100
  max_backups: 5
"@

$config | Out-File -FilePath "C:\ProgramData\VMGateway\config\agent.yaml" -Encoding UTF8

# Create log directory
New-Item -ItemType Directory -Path "C:\ProgramData\VMGateway\logs" -Force

# Install as Windows service
& "C:\Program Files\VMGateway\vm-gateway-agent.exe" install `
  --config "C:\ProgramData\VMGateway\config\agent.yaml" `
  --service-name "VMGatewayAgent" `
  --display-name "VM Gateway Agent" `
  --description "VM Gateway service discovery and monitoring agent"

# Start the service
Start-Service VMGatewayAgent

# Check status
Get-Service VMGatewayAgent
```

### macOS Installation

```bash
# Move agent to system location
sudo mv /tmp/vm-gateway-agent /usr/local/bin/vm-gateway-agent
sudo chmod +x /usr/local/bin/vm-gateway-agent

# Create configuration directory
sudo mkdir -p /usr/local/etc/vm-gateway

# Create agent configuration file
sudo tee /usr/local/etc/vm-gateway/agent.yaml > /dev/null <<EOF
# Controller connection
controller:
  url: https://your-controller:8443
  api_key: vmg_agent_YOUR_API_KEY_HERE
  verify_tls: true
  
# Agent configuration
agent:
  vm_name: $(hostname)
  vm_id: $(hostname)-$(date +%s)
  scan_interval: 60
  report_interval: 30
  
# Service discovery
discovery:
  enabled: true
  scan_type: smart
  port_range: 1-65535
  exclude_ports: []
  
# Metrics collection
metrics:
  enabled: true
  collection_interval: 30
  include_process_metrics: true
  include_network_metrics: true
  
# Logging
logging:
  level: info
  file: /usr/local/var/log/vm-gateway/agent.log
  max_size_mb: 100
  max_backups: 5
EOF

# Create log directory
sudo mkdir -p /usr/local/var/log/vm-gateway

# Create launchd plist
sudo tee /Library/LaunchDaemons/com.vmgateway.agent.plist > /dev/null <<EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.vmgateway.agent</string>
    <key>ProgramArguments</key>
    <array>
        <string>/usr/local/bin/vm-gateway-agent</string>
        <string>--config</string>
        <string>/usr/local/etc/vm-gateway/agent.yaml</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardOutPath</key>
    <string>/usr/local/var/log/vm-gateway/agent.stdout.log</string>
    <key>StandardErrorPath</key>
    <string>/usr/local/var/log/vm-gateway/agent.stderr.log</string>
</dict>
</plist>
EOF

# Load and start the service
sudo launchctl load /Library/LaunchDaemons/com.vmgateway.agent.plist

# Check status
sudo launchctl list | grep vmgateway
```

## Step 4: Verify Agent Connection

### Check Agent Status

**Linux:**
```bash
# Check service status
sudo systemctl status vm-gateway-agent

# View recent logs
sudo journalctl -u vm-gateway-agent -n 50 -f

# Check agent log file
sudo tail -f /var/log/vm-gateway/agent.log
```

**Windows:**
```powershell
# Check service status
Get-Service VMGatewayAgent

# View event logs
Get-EventLog -LogName Application -Source "VMGatewayAgent" -Newest 50

# Check agent log file
Get-Content "C:\ProgramData\VMGateway\logs\agent.log" -Tail 50 -Wait
```

**macOS:**
```bash
# Check service status
sudo launchctl list | grep vmgateway

# View logs
tail -f /usr/local/var/log/vm-gateway/agent.log
```

### Verify in Web Interface

1. Log into the controller web interface
2. Navigate to **Infrastructure** > **VMs**
3. Look for your new VM in the list
4. Status should show "Connected" with a green indicator
5. Click on the VM to see details:
   - Last seen timestamp (should be recent)
   - Agent version
   - Discovered services count
   - System metrics

### Check Service Discovery

After a few minutes, the agent will complete its initial service discovery:

1. In the web interface, click on your VM
2. Navigate to the **Services** tab
3. You should see a list of discovered services
4. Each service shows:
   - Service name and type
   - Port number
   - Process name
   - Classification confidence
   - Health status

## Step 5: Configure Service Classification

The agent automatically classifies discovered services, but you can improve accuracy:

### Review Auto-Classification

1. Navigate to **Infrastructure** > **VMs** > [Your VM] > **Services**
2. Review the automatically classified services
3. Look for services marked as "Unknown" or with low confidence

### Manual Classification

For services that weren't automatically classified:

1. Click on the service
2. Click **Edit Classification**
3. Select the correct service type from the dropdown
4. Add tags if needed (e.g., "production", "database", "api")
5. Click **Save**

### Custom Classification Rules

Create custom rules for your environment:

1. Navigate to **Settings** > **Service Classification**
2. Click **Add Custom Rule**
3. Configure the rule:
   - **Port**: Port number or range
   - **Process Pattern**: Regex pattern for process name
   - **Service Type**: What to classify it as
   - **Priority**: Rule priority (higher = checked first)
4. Click **Save**

Example custom rules:
```yaml
# Custom application on port 8080
- port: 8080
  process_pattern: "myapp.*"
  service_type: "custom_web_app"
  priority: 100

# Internal API service
- port: 9000
  process_pattern: "internal-api"
  service_type: "internal_api"
  priority: 100
```

## Step 6: Configure Access Control

Set up who can access services on this VM:

### Create Service Groups

1. Navigate to **Access Control** > **Service Groups**
2. Click **Create Group**
3. Configure:
   - **Name**: "Production Databases" (example)
   - **Description**: "All production database services"
   - **Services**: Select services from your new VM
4. Click **Create**

### Assign Permissions

1. Navigate to **Access Control** > **Roles**
2. Select a role (e.g., "Database Administrators")
3. Click **Edit Permissions**
4. Add permission:
   - **Resource**: Select your service group
   - **Actions**: ["connect", "view_metrics"]
   - **Conditions**: Optional (time-based, IP-based, etc.)
5. Click **Save**

See [RBAC Configuration](/docs/06-authentication/07-rbac.md) for detailed access control setup.

## Bulk VM Addition

For adding multiple VMs at once, use automation:

### Using Ansible

```yaml
# playbook.yml
---
- name: Install VM Gateway Agent
  hosts: all
  become: yes
  vars:
    controller_url: "https://your-controller:8443"
    agent_api_key: "{{ lookup('env', 'VMGATEWAY_AGENT_KEY') }}"
    
  tasks:
    - name: Download agent
      get_url:
        url: "{{ controller_url }}/downloads/vm-gateway-agent-linux-amd64"
        dest: /usr/local/bin/vm-gateway-agent
        mode: '0755'
        
    - name: Create config directory
      file:
        path: /etc/vm-gateway
        state: directory
        mode: '0755'
        
    - name: Deploy agent configuration
      template:
        src: agent.yaml.j2
        dest: /etc/vm-gateway/agent.yaml
        mode: '0600'
        
    - name: Deploy systemd service
      template:
        src: vm-gateway-agent.service.j2
        dest: /etc/systemd/system/vm-gateway-agent.service
        mode: '0644'
        
    - name: Enable and start agent
      systemd:
        name: vm-gateway-agent
        enabled: yes
        state: started
        daemon_reload: yes
```

Run the playbook:
```bash
export VMGATEWAY_AGENT_KEY="vmg_agent_YOUR_API_KEY"
ansible-playbook -i inventory.ini playbook.yml
```

### Using Terraform

```hcl
# main.tf
resource "null_resource" "install_agent" {
  count = length(var.vm_ips)
  
  connection {
    type        = "ssh"
    host        = var.vm_ips[count.index]
    user        = "root"
    private_key = file(var.ssh_private_key)
  }
  
  provisioner "remote-exec" {
    inline = [
      "curl -L ${var.controller_url}/downloads/vm-gateway-agent-linux-amd64 -o /usr/local/bin/vm-gateway-agent",
      "chmod +x /usr/local/bin/vm-gateway-agent",
      "mkdir -p /etc/vm-gateway",
      "cat > /etc/vm-gateway/agent.yaml <<EOF",
      "controller:",
      "  url: ${var.controller_url}",
      "  api_key: ${var.agent_api_key}",
      "agent:",
      "  vm_name: $(hostname)",
      "  scan_interval: 60",
      "EOF",
      "systemctl enable vm-gateway-agent",
      "systemctl start vm-gateway-agent"
    ]
  }
}
```

## Troubleshooting

### Agent Won't Connect

**Check network connectivity:**
```bash
# Test controller reachability
curl -k https://your-controller:8443/health

# Check if port 8444 is accessible
telnet your-controller 8444
```

**Verify API key:**
- Ensure the API key is correct (no extra spaces or line breaks)
- Check if the key has expired
- Verify IP whitelist settings allow your VM's IP

**Check firewall rules:**
```bash
# Linux - check if outbound HTTPS is allowed
sudo iptables -L OUTPUT -n | grep 8444

# Windows - check firewall rules
Get-NetFirewallRule | Where-Object {$_.DisplayName -like "*VMGateway*"}
```

### Services Not Discovered

**Check agent permissions:**
- Agent must run as root/administrator to scan ports
- Verify the agent process is running with correct privileges

**Review scan configuration:**
- Check `scan_interval` in agent.yaml
- Verify `port_range` includes the ports you expect
- Check `exclude_ports` doesn't exclude needed ports

**Manual service scan:**
```bash
# Trigger immediate scan (Linux)
sudo systemctl kill -s SIGUSR1 vm-gateway-agent

# Windows
Restart-Service VMGatewayAgent
```

### High Resource Usage

**Reduce scan frequency:**
```yaml
# In agent.yaml
agent:
  scan_interval: 300  # Scan every 5 minutes instead of 1
  
discovery:
  scan_type: smart  # Use smart scan instead of full
```

**Limit port range:**
```yaml
discovery:
  port_range: 1-10000  # Only scan common ports
```

**Disable unnecessary features:**
```yaml
metrics:
  include_process_metrics: false  # Disable if not needed
  include_network_metrics: false
```

## Next Steps

Now that you've added VMs to VM Gateway:

- [Configure User Access](/docs/00-getting-started/04-user-management.md) - Set up users and permissions
- [Set Up Monitoring](/docs/04-controller/07-monitoring.md) - Configure alerts and dashboards
- [Create Port Forwards](/docs/05-client/04-port-forwarding.md) - Access services securely
- [Integrate with SSO](/docs/06-authentication/04-sso.md) - Connect to your identity provider

## Related Documentation

- [Agent Overview](/docs/03-agent/01-overview.md) - Learn more about the agent
- [Service Discovery](/docs/03-agent/02-service-discovery.md) - How service discovery works
- [Agent Configuration](/docs/03-agent/06-installation.md) - Complete configuration reference
- [Troubleshooting](/docs/11-troubleshooting/01-common-issues.md) - Common issues and solutions
