# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
insert_final_newline = true
charset = utf-8
trim_trailing_whitespace = true

# Python files
[*.py]
indent_style = space
indent_size = 4
max_line_length = 100

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# JSON files
[*.json]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = off

# JavaScript/TypeScript files
[*.{js,ts,jsx,tsx}]
indent_style = space
indent_size = 2

# HTML files
[*.html]
indent_style = space
indent_size = 2

# CSS files
[*.css]
indent_style = space
indent_size = 2

# TOML files
[*.toml]
indent_style = space
indent_size = 2

# Shell scripts
[*.sh]
indent_style = space
indent_size = 2

# Makefile
[Makefile]
indent_style = tab
