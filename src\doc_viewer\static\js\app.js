/**
 * Main application module for VM Gateway documentation viewer.
 * Initializes all components and manages application state.
 */

(function () {
    'use strict';

    /**
     * Initialize the application
     */
    async function init() {
        try {
            console.log('Initializing VM Gateway Documentation Viewer...');

            // Initialize navigation system
            await Nav.init();
            console.log('Navigation initialized');

            // Initialize search system
            Search.init();
            console.log('Search initialized');

            // Setup additional event listeners
            setupEventListeners();

            console.log('Documentation viewer ready');

        } catch (error) {
            console.error('Application initialization failed:', error);
            showFatalError('Failed to initialize documentation viewer. Please refresh the page.');
        }
    }

    /**
     * Setup additional event listeners
     */
    function setupEventListeners() {
        // Handle external links (open in new tab)
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');
            if (link && link.href && !link.href.startsWith(window.location.origin)) {
                e.preventDefault();
                window.open(link.href, '_blank', 'noopener,noreferrer');
            }
        });

        // Handle keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K to focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.getElementById('search-input');
                if (searchInput) {
                    searchInput.focus();
                    searchInput.select();
                }
            }

            // Escape to close mobile menu
            if (e.key === 'Escape') {
                Nav.closeMobileMenu();
            }
        });

        // Handle window resize
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                handleResize();
            }, 250);
        });

        // Handle print
        window.addEventListener('beforeprint', () => {
            // Expand all sections for printing
            document.querySelectorAll('.nav-section').forEach(section => {
                section.classList.remove('collapsed');
            });
        });
    }

    /**
     * Handle window resize
     */
    function handleResize() {
        const width = window.innerWidth;

        // Close mobile menu on desktop
        if (width > 768) {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('overlay');

            if (sidebar) sidebar.classList.remove('active');
            if (overlay) overlay.classList.remove('active');
        }
    }

    /**
     * Show fatal error message
     */
    function showFatalError(message) {
        const body = document.body;
        body.innerHTML = `
            <div style="
                display: flex;
                align-items: center;
                justify-content: center;
                min-height: 100vh;
                padding: 2rem;
                text-align: center;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            ">
                <div>
                    <h1 style="
                        font-size: 2rem;
                        color: #dc2626;
                        margin-bottom: 1rem;
                    ">Error</h1>
                    <p style="
                        font-size: 1.125rem;
                        color: #4b5563;
                        margin-bottom: 1.5rem;
                    ">${message}</p>
                    <button onclick="window.location.reload()" style="
                        padding: 0.75rem 1.5rem;
                        background-color: #3b82f6;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        font-size: 1rem;
                        cursor: pointer;
                        font-family: inherit;
                    ">Reload Page</button>
                </div>
            </div>
        `;
    }

    /**
     * Check if browser is supported
     */
    function checkBrowserSupport() {
        // Check for required features
        const required = [
            'fetch',
            'Promise',
            'IntersectionObserver',
            'URLSearchParams'
        ];

        const missing = required.filter(feature => !(feature in window));

        if (missing.length > 0) {
            console.warn('Missing browser features:', missing);
            return false;
        }

        return true;
    }

    /**
     * Show browser compatibility warning
     */
    function showBrowserWarning() {
        const warning = document.createElement('div');
        warning.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: #fef3c7;
            color: #92400e;
            padding: 1rem;
            text-align: center;
            z-index: 9999;
            font-size: 0.875rem;
        `;
        warning.innerHTML = `
            <strong>Browser Compatibility Warning:</strong>
            Your browser may not support all features of this documentation viewer.
            Please consider upgrading to a modern browser for the best experience.
        `;
        document.body.insertBefore(warning, document.body.firstChild);
    }

    // Check browser support
    if (!checkBrowserSupport()) {
        showBrowserWarning();
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Expose app utilities globally for debugging
    window.VMGatewayDocs = {
        version: 'a.0.0-20',
        getCurrentPath: () => Nav.getCurrentPath(),
        getDocIndex: () => Nav.getDocIndex(),
        loadDocument: (path) => Nav.loadDocumentByPath(path)
    };

})();
