---
title: "PagerDuty Integration"
section: "Integrations"
order: 2
tags: ["integration", "pagerduty", "alerts", "oncall", "incidents"]
last_updated: "2025-11-09"
---

# PagerDuty Integration

## Overview

Integrate VM Gateway with PagerDuty to automatically create incidents for critical alerts, route notifications to on-call engineers, and manage incident lifecycle. This integration ensures your team is immediately notified of critical issues requiring attention.

**Features**:
- Automatic incident creation for critical alerts
- Intelligent alert routing based on severity
- Incident acknowledgment and resolution sync
- Escalation policy support
- On-call schedule integration
- Rich incident details with context
- Bi-directional sync (VM Gateway ↔ PagerDuty)

**Prerequisites**:
- VM Gateway Professional or Enterprise Edition
- PagerDuty account with admin access
- Ability to create PagerDuty services and integrations

---

## Table of Contents

1. [Creating PagerDuty Service](#creating-pagerduty-service)
2. [Configuring Integration](#configuring-integration)
3. [Configuring VM Gateway](#configuring-vm-gateway)
4. [Alert Routing](#alert-routing)
5. [Incident Management](#incident-management)
6. [Bi-Directional Sync](#bi-directional-sync)
7. [Troubleshooting](#troubleshooting)

---

## Creating PagerDuty Service

### Step 1: Create Service

1. **Log in to PagerDuty**:
   - Navigate to https://your-subdomain.pagerduty.com
   - Use your administrator credentials

2. **Create New Service**:
   - Go to **Services** → **Service Directory**
   - Click **+ New Service**

3. **Service Configuration**:
   - **Name**: `VM Gateway Alerts`
   - **Description**: `Critical alerts from VM Gateway platform`
   - **Escalation Policy**: Select appropriate policy (e.g., "Default")
   - **Alert Grouping**: `Intelligent` (recommended)
   - **Incident Settings**:
     - Create alerts and incidents: `Create incidents`
     - Incident Urgency: `High for all incidents`

4. **Integration Settings**:
   - **Integration Type**: Select `Events API v2`
   - **Integration Name**: `VM Gateway`
   - Click **Add Integration**

5. **Copy Integration Key**:
   - Copy the **Integration Key** (looks like `R03XXXXXXXXXXXXXXXXXX`)
   - Save this for VM Gateway configuration

### Step 2: Configure Escalation Policy (Optional)

Create custom escalation policy for VM Gateway alerts:

1. **Go to Escalation Policies**:
   - Navigate to **People** → **Escalation Policies**
   - Click **+ New Escalation Policy**

2. **Policy Configuration**:
   - **Name**: `VM Gateway Escalation`
   - **Description**: `Escalation for VM Gateway critical alerts`

3. **Escalation Rules**:
   - **Level 1**: Notify on-call engineer immediately
     - Escalates after: 5 minutes
   - **Level 2**: Notify team lead
     - Escalates after: 10 minutes
   - **Level 3**: Notify engineering manager
     - Escalates after: 15 minutes

4. **Save Policy**

5. **Update Service**:
   - Go back to VM Gateway service
   - Change escalation policy to `VM Gateway Escalation`

---

## Configuring Integration

### Integration Types

PagerDuty offers multiple integration methods:

**1. Events API v2** (Recommended):
- Modern API with rich event data
- Supports change events and custom details
- Better incident grouping

**2. Events API v1** (Legacy):
- Simpler API
- Limited features
- Use only if v2 not available

**3. Webhooks** (Bi-directional):
- Receive updates from PagerDuty
- Sync incident status back to VM Gateway
- Requires publicly accessible endpoint

### Events API v2 Setup

Already configured in [Step 1](#step-1-create-service).

### Webhook Setup (Optional, for Bi-directional Sync)

1. **Create Webhook Extension**:
   - Go to **Integrations** → **Extensions**
   - Click **+ New Extension**
   - Select **Generic V3 Webhook**

2. **Webhook Configuration**:
   - **Name**: `VM Gateway Webhook`
   - **URL**: `https://your-vmgateway-domain.com/api/v1/integrations/pagerduty/webhook`
   - **Scope**: Select `VM Gateway Alerts` service
   - **Events**: Select all relevant events:
     - `incident.triggered`
     - `incident.acknowledged`
     - `incident.resolved`
     - `incident.escalated`
     - `incident.reassigned`

3. **Save Extension**

4. **Copy Webhook Secret** (if provided)

---

## Configuring VM Gateway

### Basic Configuration

Edit `/opt/vm-gateway/.env`:

```bash
# PagerDuty Integration
PAGERDUTY_ENABLED=true
PAGERDUTY_INTEGRATION_KEY=R03XXXXXXXXXXXXXXXXXX

# API Configuration
PAGERDUTY_API_VERSION=v2  # v1 or v2
PAGERDUTY_API_URL=https://events.pagerduty.com

# Alert Settings
PAGERDUTY_MIN_SEVERITY=error  # Only create incidents for error and critical
PAGERDUTY_AUTO_RESOLVE=true  # Auto-resolve when alert clears
PAGERDUTY_DEDUP_ENABLED=true  # Group similar alerts

# Webhook (for bi-directional sync)
PAGERDUTY_WEBHOOK_ENABLED=false
PAGERDUTY_WEBHOOK_SECRET=your-webhook-secret
```

### Advanced Configuration

For fine-grained control, create `config/pagerduty-config.yaml`:

```yaml
# PagerDuty Integration Configuration
enabled: true

# API Configuration
api:
  version: "v2"
  url: "https://events.pagerduty.com"
  integration_key: "R03XXXXXXXXXXXXXXXXXX"
  timeout_seconds: 30
  retry_attempts: 3
  retry_backoff_seconds: 5

# Alert Routing
routing:
  # Map alert severity to PagerDuty severity
  severity_mapping:
    critical: "critical"
    error: "error"
    warning: "warning"
    info: "info"
  
  # Minimum severity to create incident
  min_severity: "error"
  
  # Alert type routing
  alert_types:
    service_down:
      severity: "critical"
      urgency: "high"
      create_incident: true
    
    high_cpu:
      severity: "error"
      urgency: "high"
      create_incident: true
    
    high_memory:
      severity: "error"
      urgency: "high"
      create_incident: true
    
    disk_space_low:
      severity: "warning"
      urgency: "low"
      create_incident: false  # Just create alert
    
    agent_disconnected:
      severity: "error"
      urgency: "high"
      create_incident: true

# Incident Configuration
incidents:
  # Auto-resolve incidents when alert clears
  auto_resolve: true
  auto_resolve_timeout_minutes: 5
  
  # Deduplication
  deduplication:
    enabled: true
    window_minutes: 10
    key_template: "vmgateway-{alert_type}-{service_id}"
  
  # Incident details
  include_details:
    - alert_description
    - service_name
    - vm_name
    - metric_value
    - threshold
    - duration
    - dashboard_link
  
  # Custom fields
  custom_details:
    environment: "production"
    platform: "vm-gateway"
    version: "a.0.0-20"

# Webhook Configuration (bi-directional sync)
webhook:
  enabled: false
  secret: "your-webhook-secret"
  verify_signature: true
  
  # Actions to sync back to VM Gateway
  sync_actions:
    acknowledged: true
    resolved: true
    escalated: true
    reassigned: true

# Rate Limiting
rate_limit:
  enabled: true
  max_events_per_minute: 60
  burst: 10

# Retry Configuration
retry:
  enabled: true
  max_attempts: 3
  backoff_seconds: 5
  backoff_multiplier: 2

# Logging
logging:
  log_events: true
  log_responses: true
  log_errors: true
```

### Restart VM Gateway

```bash
sudo systemctl restart vm-gateway-controller
```

Check logs:
```bash
sudo journalctl -u vm-gateway-controller | grep -i pagerduty
```

Look for:
```
INFO: PagerDuty integration enabled
INFO: PagerDuty Events API v2 configured
INFO: PagerDuty integration key validated
```

---

## Alert Routing

### Severity Mapping

VM Gateway alerts are mapped to PagerDuty severities:

| VM Gateway Severity | PagerDuty Severity | Action |
|---------------------|-------------------|--------|
| Critical | critical | Create incident, page on-call |
| Error | error | Create incident, page on-call |
| Warning | warning | Create alert only |
| Info | info | Create alert only |
| Debug | - | Not sent to PagerDuty |

### Alert Type Routing

Different alert types can have different routing:

```yaml
routing:
  alert_types:
    # Critical: Service completely down
    service_down:
      severity: "critical"
      urgency: "high"
      escalation_policy: "immediate"
      create_incident: true
    
    # Error: Service degraded
    service_degraded:
      severity: "error"
      urgency: "high"
      create_incident: true
    
    # Warning: Potential issue
    high_resource_usage:
      severity: "warning"
      urgency: "low"
      create_incident: false  # Alert only
    
    # Info: FYI
    service_discovered:
      severity: "info"
      create_incident: false
```

### Deduplication

Prevent duplicate incidents for the same issue:

```yaml
deduplication:
  enabled: true
  window_minutes: 10
  key_template: "vmgateway-{alert_type}-{service_id}"
```

**How it works**:
1. Alert triggered: `high_cpu` on service `svc_123`
2. Dedup key generated: `vmgateway-high_cpu-svc_123`
3. Incident created with this dedup key
4. Another `high_cpu` alert for `svc_123` within 10 minutes
5. PagerDuty groups it with existing incident (no new page)
6. After 10 minutes, new incident would be created

### Custom Incident Details

Include rich context in incidents:

```yaml
incidents:
  include_details:
    - alert_description
    - service_name
    - vm_name
    - metric_value
    - threshold
    - duration
    - dashboard_link
    - runbook_link
```

**Example Incident**:
```
Title: High CPU Usage - web-server-01

Details:
- Service: Nginx Web Server
- VM: web-server-01
- Metric: cpu.usage.percent
- Current Value: 95%
- Threshold: 80%
- Duration: 5 minutes
- Dashboard: https://vmgateway.com/dashboards/svc_123
- Runbook: https://wiki.company.com/runbooks/high-cpu
```

---

## Incident Management

### Creating Incidents

Incidents are created automatically when alerts meet criteria:

```python
# Example: VM Gateway creates PagerDuty incident
def create_pagerduty_incident(alert):
    """Create PagerDuty incident for alert."""
    
    # Check if alert meets criteria
    if alert.severity not in ["critical", "error"]:
        return  # Don't create incident
    
    # Build event payload
    event = {
        "routing_key": PAGERDUTY_INTEGRATION_KEY,
        "event_action": "trigger",
        "dedup_key": f"vmgateway-{alert.type}-{alert.service_id}",
        "payload": {
            "summary": f"{alert.title} - {alert.service_name}",
            "severity": alert.severity,
            "source": alert.vm_name,
            "timestamp": alert.timestamp.isoformat(),
            "component": alert.service_name,
            "group": alert.vm_name,
            "class": alert.type,
            "custom_details": {
                "alert_id": alert.id,
                "service_id": alert.service_id,
                "vm_id": alert.vm_id,
                "metric": alert.metric_name,
                "current_value": alert.current_value,
                "threshold": alert.threshold,
                "duration": alert.duration_minutes,
                "dashboard_url": f"https://vmgateway.com/services/{alert.service_id}",
                "alert_url": f"https://vmgateway.com/alerts/{alert.id}"
            }
        },
        "links": [
            {
                "href": f"https://vmgateway.com/services/{alert.service_id}",
                "text": "View Service"
            },
            {
                "href": f"https://vmgateway.com/alerts/{alert.id}",
                "text": "View Alert"
            }
        ],
        "images": [
            {
                "src": f"https://vmgateway.com/api/v1/alerts/{alert.id}/graph",
                "alt": "Alert Graph"
            }
        ]
    }
    
    # Send to PagerDuty
    response = requests.post(
        "https://events.pagerduty.com/v2/enqueue",
        json=event,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 202:
        logger.info(f"PagerDuty incident created for alert {alert.id}")
    else:
        logger.error(f"Failed to create PagerDuty incident: {response.text}")
```

### Acknowledging Incidents

When on-call engineer acknowledges incident in PagerDuty:

1. **PagerDuty sends webhook** to VM Gateway
2. **VM Gateway updates alert status** to "acknowledged"
3. **Alert stops escalating** in VM Gateway
4. **Audit log created** with acknowledger info

### Resolving Incidents

**Auto-Resolution**:
- When alert clears in VM Gateway
- VM Gateway sends "resolve" event to PagerDuty
- Incident automatically resolved

**Manual Resolution**:
- Engineer resolves incident in PagerDuty
- Webhook sent to VM Gateway
- Alert marked as resolved in VM Gateway

---

## Bi-Directional Sync

### Enabling Webhook

Configure webhook to sync incident status back to VM Gateway:

```yaml
webhook:
  enabled: true
  secret: "your-webhook-secret"
  verify_signature: true
```

### Webhook Endpoint

VM Gateway provides endpoint for PagerDuty webhooks:

```
POST https://your-vmgateway-domain.com/api/v1/integrations/pagerduty/webhook
```

### Handling Webhook Events

```python
@app.post("/api/v1/integrations/pagerduty/webhook")
async def handle_pagerduty_webhook(request: Request):
    """Handle PagerDuty webhook events."""
    
    # Verify signature
    signature = request.headers.get("X-PagerDuty-Signature")
    if not verify_pagerduty_signature(signature, await request.body()):
        raise HTTPException(status_code=401, detail="Invalid signature")
    
    # Parse payload
    payload = await request.json()
    
    for message in payload.get("messages", []):
        event = message.get("event")
        incident = message.get("incident")
        
        # Extract dedup key to find corresponding alert
        dedup_key = incident.get("dedup_key")
        alert_id = extract_alert_id_from_dedup_key(dedup_key)
        
        if event == "incident.acknowledged":
            # Update alert status
            update_alert_status(alert_id, "acknowledged")
            
            # Log who acknowledged
            acknowledger = message.get("log_entries", [{}])[0].get("agent", {})
            log_alert_acknowledgment(alert_id, acknowledger.get("summary"))
        
        elif event == "incident.resolved":
            # Resolve alert
            resolve_alert(alert_id)
            
            # Log resolution
            resolver = message.get("log_entries", [{}])[0].get("agent", {})
            log_alert_resolution(alert_id, resolver.get("summary"))
        
        elif event == "incident.escalated":
            # Log escalation
            log_alert_escalation(alert_id, message.get("escalation_policy"))
    
    return {"status": "ok"}
```

### Signature Verification

Verify webhook authenticity:

```python
import hmac
import hashlib

def verify_pagerduty_signature(signature: str, body: bytes) -> bool:
    """Verify PagerDuty webhook signature."""
    
    # Extract signature from header
    # Format: v1=<signature>
    if not signature or not signature.startswith("v1="):
        return False
    
    provided_signature = signature[3:]
    
    # Calculate expected signature
    expected_signature = hmac.new(
        PAGERDUTY_WEBHOOK_SECRET.encode(),
        body,
        hashlib.sha256
    ).hexdigest()
    
    # Compare signatures (constant-time comparison)
    return hmac.compare_digest(provided_signature, expected_signature)
```

---

## Troubleshooting

### Issue: Incidents Not Being Created

**Symptoms**:
- Alerts triggered in VM Gateway
- No incidents in PagerDuty
- No errors in logs

**Solutions**:

1. **Verify integration key**:
   ```bash
   grep PAGERDUTY_INTEGRATION_KEY /opt/vm-gateway/.env
   ```

2. **Test integration key**:
   ```bash
   curl -X POST https://events.pagerduty.com/v2/enqueue \
     -H "Content-Type: application/json" \
     -d '{
       "routing_key": "YOUR_INTEGRATION_KEY",
       "event_action": "trigger",
       "payload": {
         "summary": "Test from VM Gateway",
         "severity": "error",
         "source": "test"
       }
     }'
   ```

3. **Check severity threshold**:
   ```bash
   grep PAGERDUTY_MIN_SEVERITY /opt/vm-gateway/.env
   # Ensure alert severity meets threshold
   ```

4. **Check logs for errors**:
   ```bash
   sudo journalctl -u vm-gateway-controller | grep -i pagerduty
   ```

### Issue: Duplicate Incidents

**Symptoms**:
- Multiple incidents for same issue
- On-call engineer paged multiple times

**Solutions**:

1. **Enable deduplication**:
   ```yaml
   deduplication:
     enabled: true
     window_minutes: 10
   ```

2. **Check dedup key template**:
   ```yaml
   deduplication:
     key_template: "vmgateway-{alert_type}-{service_id}"
     # Ensure template is unique per issue
   ```

3. **Verify dedup keys in logs**:
   ```bash
   sudo journalctl -u vm-gateway-controller | grep "dedup_key"
   ```

### Issue: Incidents Not Auto-Resolving

**Symptoms**:
- Alert clears in VM Gateway
- Incident remains open in PagerDuty

**Solutions**:

1. **Enable auto-resolve**:
   ```bash
   echo "PAGERDUTY_AUTO_RESOLVE=true" >> /opt/vm-gateway/.env
   sudo systemctl restart vm-gateway-controller
   ```

2. **Check resolve events in logs**:
   ```bash
   sudo journalctl -u vm-gateway-controller | grep "event_action.*resolve"
   ```

3. **Verify dedup key matches**:
   - Resolve event must use same dedup key as trigger event
   - Check logs for dedup key consistency

### Issue: Webhook Not Working

**Symptoms**:
- Incidents acknowledged in PagerDuty
- Status not updated in VM Gateway

**Solutions**:

1. **Verify webhook URL is accessible**:
   ```bash
   curl https://your-vmgateway-domain.com/api/v1/integrations/pagerduty/webhook
   # Should return 405 Method Not Allowed (POST required)
   ```

2. **Check webhook secret**:
   ```bash
   grep PAGERDUTY_WEBHOOK_SECRET /opt/vm-gateway/.env
   ```

3. **Check webhook logs**:
   ```bash
   sudo journalctl -u vm-gateway-controller | grep "pagerduty/webhook"
   ```

4. **Test webhook manually**:
   ```bash
   curl -X POST https://your-vmgateway-domain.com/api/v1/integrations/pagerduty/webhook \
     -H "Content-Type: application/json" \
     -H "X-PagerDuty-Signature: v1=test" \
     -d '{"messages":[{"event":"incident.acknowledged","incident":{"dedup_key":"test"}}]}'
   ```

---

## Related Documentation

- **[Alert Configuration](/docs/04-controller/07-monitoring.md#alerting)**: Setting up alerts
- **[Webhook Integration](/docs/09-api-reference/08-webhooks.md)**: General webhook configuration
- **[Slack Integration](/docs/13-integrations/01-slack.md)**: Similar integration guide
- **[Email Notifications](/docs/13-integrations/03-email.md)**: Email notification setup
- **[Monitoring Overview](/docs/04-controller/07-monitoring.md)**: Monitoring and alerting
- **[API Reference](/docs/09-api-reference/01-overview.md)**: API endpoints for integrations

## Summary

PagerDuty integration ensures your on-call team is immediately notified of critical issues requiring attention. With intelligent routing, deduplication, and bi-directional sync, you can create a robust incident management workflow.

**Key Features**:
- Automatic incident creation for critical alerts
- Intelligent deduplication to prevent alert fatigue
- Rich incident details with context and links
- Bi-directional sync for status updates
- Escalation policy support
- Auto-resolution when issues clear

**Best Practices**:
- Use appropriate severity thresholds (error and above)
- Enable deduplication to prevent duplicate pages
- Include rich context in incident details
- Set up escalation policies for critical services
- Enable auto-resolution to keep incidents clean
- Test integration before deploying to production
- Monitor PagerDuty API rate limits

