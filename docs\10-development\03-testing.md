---
title: "Testing Guide"
section: "Development"
order: 3
tags: ["development", "testing", "quality", "automation"]
last_updated: "2025-11-08"
---

# Testing Guide

This guide covers testing strategies, practices, and tools for the VM Network Gateway & Access Control Platform. Comprehensive testing ensures code quality, prevents regressions, and builds confidence in deployments.

## Testing Philosophy

### Testing Pyramid

We follow the testing pyramid approach:

```
        /\
       /  \
      / E2E \
     /--------\
    /          \
   / Integration \
  /--------------\
 /                \
/   Unit Tests     \
--------------------
```

- **Unit Tests (70%)**: Fast, isolated tests of individual functions/classes
- **Integration Tests (20%)**: Tests of component interactions
- **End-to-End Tests (10%)**: Full system tests from user perspective

### Test-Driven Development (TDD)

We encourage TDD for new features:

1. **Red**: Write a failing test
2. **Green**: Write minimal code to pass the test
3. **Refactor**: Improve code while keeping tests green

## Python Testing

### Test Framework

We use **pytest** for Python testing:

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_services.py

# Run specific test
pytest tests/test_services.py::test_create_service

# Run with coverage
pytest --cov=src --cov-report=html

# Run with verbose output
pytest -v

# Run only failed tests
pytest --lf

# Run tests matching pattern
pytest -k "test_connection"
```

### Test Structure

Organize tests to mirror source code structure:

```
tests/
├── unit/
│   ├── test_agent/
│   │   ├── test_scanner.py
│   │   ├── test_classifier.py
│   │   └── test_metrics.py
│   ├── test_controller/
│   │   ├── test_api.py
│   │   ├── test_auth.py
│   │   └── test_services.py
│   └── test_shared/
│       ├── test_logging.py
│       └── test_utils.py
├── integration/
│   ├── test_api_integration.py
│   ├── test_database_integration.py
│   └── test_redis_integration.py
├── e2e/
│   ├── test_user_workflows.py
│   └── test_connection_flows.py
└── conftest.py
```

### Writing Unit Tests

**Example: Testing a Service Function**

```python
# src/controller/services.py
from typing import Optional
from sqlalchemy.orm import Session
from .models import Service
from .exceptions import ServiceNotFoundError

def get_service_by_id(db: Session, service_id: str) -> Service:
    """Get service by ID."""
    service = db.query(Service).filter(Service.id == service_id).first()
    if not service:
        raise ServiceNotFoundError(f"Service {service_id} not found")
    return service

def create_service(
    db: Session,
    name: str,
    type: str,
    port: int,
    vm_id: str
) -> Service:
    """Create a new service."""
    service = Service(
        name=name,
        type=type,
        port=port,
        vm_id=vm_id
    )
    db.add(service)
    db.commit()
    db.refresh(service)
    return service
```

**Test File:**

```python
# tests/unit/test_controller/test_services.py
import pytest
from sqlalchemy.orm import Session
from src.controller.services import get_service_by_id, create_service
from src.controller.models import Service, VM
from src.controller.exceptions import ServiceNotFoundError

class TestGetServiceById:
    """Tests for get_service_by_id function."""
    
    def test_get_existing_service(self, db: Session, sample_service: Service):
        """Test retrieving an existing service."""
        # Act
        result = get_service_by_id(db, sample_service.id)
        
        # Assert
        assert result.id == sample_service.id
        assert result.name == sample_service.name
        assert result.type == sample_service.type
    
    def test_get_nonexistent_service(self, db: Session):
        """Test retrieving a non-existent service raises error."""
        # Arrange
        nonexistent_id = "svc_nonexistent"
        
        # Act & Assert
        with pytest.raises(ServiceNotFoundError) as exc_info:
            get_service_by_id(db, nonexistent_id)
        
        assert nonexistent_id in str(exc_info.value)

class TestCreateService:
    """Tests for create_service function."""
    
    def test_create_service_success(self, db: Session, sample_vm: VM):
        """Test successful service creation."""
        # Arrange
        service_data = {
            "name": "test-service",
            "type": "web",
            "port": 8080,
            "vm_id": sample_vm.id
        }
        
        # Act
        service = create_service(db, **service_data)
        
        # Assert
        assert service.id is not None
        assert service.name == service_data["name"]
        assert service.type == service_data["type"]
        assert service.port == service_data["port"]
        assert service.vm_id == service_data["vm_id"]
        
        # Verify it's in database
        db_service = db.query(Service).filter(Service.id == service.id).first()
        assert db_service is not None
        assert db_service.name == service_data["name"]
    
    def test_create_service_with_invalid_vm(self, db: Session):
        """Test creating service with invalid VM ID."""
        # Arrange
        service_data = {
            "name": "test-service",
            "type": "web",
            "port": 8080,
            "vm_id": "vm_nonexistent"
        }
        
        # Act & Assert
        with pytest.raises(Exception):  # Should raise foreign key constraint error
            create_service(db, **service_data)
```

### Fixtures

Use pytest fixtures for test setup:

```python
# tests/conftest.py
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from src.shared.database import Base
from src.controller.models import User, VM, Service

@pytest.fixture(scope="session")
def engine():
    """Create test database engine."""
    engine = create_engine("postgresql://test:test@localhost:5432/vm_gateway_test")
    Base.metadata.create_all(engine)
    yield engine
    Base.metadata.drop_all(engine)

@pytest.fixture(scope="function")
def db(engine) -> Session:
    """Create database session for test."""
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()
    
    yield session
    
    session.rollback()
    session.close()

@pytest.fixture
def sample_user(db: Session) -> User:
    """Create a sample user for testing."""
    user = User(
        email="<EMAIL>",
        name="Test User",
        password_hash="hashed_password"
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user

@pytest.fixture
def sample_vm(db: Session) -> VM:
    """Create a sample VM for testing."""
    vm = VM(
        name="test-vm",
        hostname="test-vm.local",
        ip_addresses=["*********"]
    )
    db.add(vm)
    db.commit()
    db.refresh(vm)
    return vm

@pytest.fixture
def sample_service(db: Session, sample_vm: VM) -> Service:
    """Create a sample service for testing."""
    service = Service(
        name="test-service",
        type="web",
        port=8080,
        vm_id=sample_vm.id
    )
    db.add(service)
    db.commit()
    db.refresh(service)
    return service
```

### Mocking

Use `unittest.mock` or `pytest-mock` for mocking:

```python
# tests/unit/test_agent/test_scanner.py
import pytest
from unittest.mock import Mock, patch, MagicMock
from src.agent.scanner import ServiceScanner

class TestServiceScanner:
    """Tests for ServiceScanner class."""
    
    @patch('src.agent.scanner.psutil.net_connections')
    def test_scan_ports(self, mock_net_connections):
        """Test port scanning functionality."""
        # Arrange
        mock_connection = Mock()
        mock_connection.laddr.port = 8080
        mock_connection.status = 'LISTEN'
        mock_connection.pid = 1234
        mock_net_connections.return_value = [mock_connection]
        
        scanner = ServiceScanner()
        
        # Act
        ports = scanner.scan_ports()
        
        # Assert
        assert len(ports) == 1
        assert ports[0]['port'] == 8080
        assert ports[0]['pid'] == 1234
        mock_net_connections.assert_called_once()
    
    @patch('src.agent.scanner.psutil.Process')
    def test_get_process_info(self, mock_process_class):
        """Test process information retrieval."""
        # Arrange
        mock_process = Mock()
        mock_process.name.return_value = "nginx"
        mock_process.cmdline.return_value = ["/usr/sbin/nginx", "-g", "daemon off;"]
        mock_process.username.return_value = "www-data"
        mock_process_class.return_value = mock_process
        
        scanner = ServiceScanner()
        
        # Act
        info = scanner.get_process_info(1234)
        
        # Assert
        assert info['name'] == "nginx"
        assert info['user'] == "www-data"
        assert len(info['cmdline']) == 3
        mock_process_class.assert_called_once_with(1234)
```

### Async Testing

Test async functions with pytest-asyncio:

```python
# tests/unit/test_controller/test_api.py
import pytest
from httpx import AsyncClient
from src.controller.main import app

@pytest.mark.asyncio
async def test_get_services():
    """Test GET /api/v1/services endpoint."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.get("/api/v1/services")
    
    assert response.status_code == 200
    data = response.json()
    assert "services" in data
    assert isinstance(data["services"], list)

@pytest.mark.asyncio
async def test_create_connection():
    """Test POST /api/v1/connections endpoint."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.post(
            "/api/v1/connections",
            json={
                "service_id": "svc_abc123",
                "user_id": "usr_xyz789",
                "duration": 3600
            },
            headers={"Authorization": "Bearer test_token"}
        )
    
    assert response.status_code == 201
    data = response.json()
    assert "connection" in data
    assert data["connection"]["service_id"] == "svc_abc123"
```

### Parametrized Tests

Use parametrize for testing multiple scenarios:

```python
# tests/unit/test_shared/test_validation.py
import pytest
from src.shared.validation import validate_email, validate_port

class TestValidateEmail:
    """Tests for email validation."""
    
    @pytest.mark.parametrize("email", [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ])
    def test_valid_emails(self, email):
        """Test that valid emails pass validation."""
        assert validate_email(email) is True
    
    @pytest.mark.parametrize("email", [
        "invalid",
        "@example.com",
        "user@",
        "user @example.com",
        "user@example",
    ])
    def test_invalid_emails(self, email):
        """Test that invalid emails fail validation."""
        assert validate_email(email) is False

class TestValidatePort:
    """Tests for port validation."""
    
    @pytest.mark.parametrize("port,expected", [
        (80, True),
        (443, True),
        (8080, True),
        (65535, True),
        (0, False),
        (65536, False),
        (-1, False),
    ])
    def test_port_validation(self, port, expected):
        """Test port number validation."""
        assert validate_port(port) == expected
```

## JavaScript/TypeScript Testing

### Test Framework

We use **Jest** and **React Testing Library**:

```bash
# Run all tests
npm test

# Run with coverage
npm test -- --coverage

# Run in watch mode
npm test -- --watch

# Run specific test file
npm test -- ServiceCard.test.tsx

# Update snapshots
npm test -- -u
```

### Writing Component Tests

```typescript
// src/components/ServiceCard.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ServiceCard } from './ServiceCard';
import { Service } from '../types';

describe('ServiceCard', () => {
  const mockService: Service = {
    id: 'svc_123',
    name: 'Test Service',
    type: 'web',
    port: 8080,
    status: 'healthy',
  };

  const mockOnConnect = jest.fn();
  const mockOnConfigure = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders service information', () => {
    render(<ServiceCard service={mockService} onConnect={mockOnConnect} />);

    expect(screen.getByText('Test Service')).toBeInTheDocument();
    expect(screen.getByText(/web/i)).toBeInTheDocument();
    expect(screen.getByText(/8080/i)).toBeInTheDocument();
  });

  it('calls onConnect when connect button is clicked', async () => {
    render(<ServiceCard service={mockService} onConnect={mockOnConnect} />);

    const connectButton = screen.getByRole('button', { name: /connect/i });
    fireEvent.click(connectButton);

    await waitFor(() => {
      expect(mockOnConnect).toHaveBeenCalledWith('svc_123');
    });
  });

  it('shows loading state while connecting', async () => {
    mockOnConnect.mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 100))
    );

    render(<ServiceCard service={mockService} onConnect={mockOnConnect} />);

    const connectButton = screen.getByRole('button', { name: /connect/i });
    fireEvent.click(connectButton);

    expect(screen.getByText(/connecting/i)).toBeInTheDocument();
    expect(connectButton).toBeDisabled();

    await waitFor(() => {
      expect(screen.getByText(/connect/i)).toBeInTheDocument();
      expect(connectButton).not.toBeDisabled();
    });
  });

  it('renders configure button when onConfigure is provided', () => {
    render(
      <ServiceCard
        service={mockService}
        onConnect={mockOnConnect}
        onConfigure={mockOnConfigure}
      />
    );

    const configureButton = screen.getByRole('button', { name: /configure/i });
    expect(configureButton).toBeInTheDocument();

    fireEvent.click(configureButton);
    expect(mockOnConfigure).toHaveBeenCalledWith('svc_123');
  });

  it('does not render configure button when onConfigure is not provided', () => {
    render(<ServiceCard service={mockService} onConnect={mockOnConnect} />);

    expect(screen.queryByRole('button', { name: /configure/i })).not.toBeInTheDocument();
  });
});
```

### Testing Hooks

```typescript
// src/hooks/useServices.test.ts
import { renderHook, waitFor } from '@testing-library/react';
import { useServices } from './useServices';
import * as api from '../api/services';

jest.mock('../api/services');

describe('useServices', () => {
  const mockServices = [
    { id: 'svc_1', name: 'Service 1', type: 'web' },
    { id: 'svc_2', name: 'Service 2', type: 'database' },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('fetches services on mount', async () => {
    (api.fetchServices as jest.Mock).mockResolvedValue(mockServices);

    const { result } = renderHook(() => useServices());

    expect(result.current.loading).toBe(true);
    expect(result.current.services).toEqual([]);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.services).toEqual(mockServices);
    expect(api.fetchServices).toHaveBeenCalledTimes(1);
  });

  it('handles fetch error', async () => {
    const error = new Error('Failed to fetch');
    (api.fetchServices as jest.Mock).mockRejectedValue(error);

    const { result } = renderHook(() => useServices());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe(error);
    expect(result.current.services).toEqual([]);
  });
});
```

## Integration Testing

Test component interactions and API integration:

```python
# tests/integration/test_api_integration.py
import pytest
from fastapi.testclient import TestClient
from src.controller.main import app
from src.controller.models import User, VM, Service

@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)

@pytest.fixture
def auth_headers(client, sample_user):
    """Get authentication headers."""
    response = client.post(
        "/api/v1/auth/login",
        json={
            "username": sample_user.email,
            "password": "password123"
        }
    )
    token = response.json()["tokens"]["access_token"]
    return {"Authorization": f"Bearer {token}"}

class TestServiceAPI:
    """Integration tests for service API."""
    
    def test_list_services(self, client, auth_headers, sample_service):
        """Test listing services."""
        response = client.get("/api/v1/services", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "services" in data
        assert len(data["services"]) >= 1
        
        service = data["services"][0]
        assert "id" in service
        assert "name" in service
        assert "type" in service
    
    def test_create_connection(self, client, auth_headers, sample_service):
        """Test creating a connection."""
        response = client.post(
            "/api/v1/connections",
            headers=auth_headers,
            json={
                "service_id": sample_service.id,
                "connection_type": "tunnel",
                "duration": 3600
            }
        )
        
        assert response.status_code == 201
        data = response.json()
        assert "connection" in data
        assert data["connection"]["service"]["id"] == sample_service.id
        
        # Verify connection is in database
        connection_id = data["connection"]["id"]
        get_response = client.get(
            f"/api/v1/connections/{connection_id}",
            headers=auth_headers
        )
        assert get_response.status_code == 200
```

## End-to-End Testing

Test complete user workflows:

```python
# tests/e2e/test_user_workflows.py
import pytest
from playwright.sync_api import Page, expect

@pytest.fixture(scope="session")
def browser():
    """Create browser instance."""
    from playwright.sync_api import sync_playwright
    with sync_playwright() as p:
        browser = p.chromium.launch()
        yield browser
        browser.close()

@pytest.fixture
def page(browser):
    """Create new page for each test."""
    page = browser.new_page()
    yield page
    page.close()

class TestUserLogin:
    """E2E tests for user login flow."""
    
    def test_successful_login(self, page: Page):
        """Test successful user login."""
        # Navigate to login page
        page.goto("http://localhost:3000/login")
        
        # Fill in credentials
        page.fill('input[name="email"]', "<EMAIL>")
        page.fill('input[name="password"]', "password123")
        
        # Click login button
        page.click('button[type="submit"]')
        
        # Wait for redirect to dashboard
        page.wait_for_url("http://localhost:3000/dashboard")
        
        # Verify dashboard elements
        expect(page.locator('h1')).to_contain_text("Dashboard")
        expect(page.locator('[data-testid="user-menu"]')).to_be_visible()
    
    def test_failed_login(self, page: Page):
        """Test failed login with invalid credentials."""
        page.goto("http://localhost:3000/login")
        
        page.fill('input[name="email"]', "<EMAIL>")
        page.fill('input[name="password"]', "wrong_password")
        page.click('button[type="submit"]')
        
        # Verify error message
        expect(page.locator('[role="alert"]')).to_contain_text("Invalid credentials")
        
        # Verify still on login page
        expect(page).to_have_url("http://localhost:3000/login")

class TestServiceConnection:
    """E2E tests for service connection flow."""
    
    def test_connect_to_service(self, page: Page):
        """Test connecting to a service."""
        # Login first
        page.goto("http://localhost:3000/login")
        page.fill('input[name="email"]', "<EMAIL>")
        page.fill('input[name="password"]', "password123")
        page.click('button[type="submit"]')
        page.wait_for_url("http://localhost:3000/dashboard")
        
        # Navigate to services
        page.click('a[href="/services"]')
        page.wait_for_url("http://localhost:3000/services")
        
        # Find and click on a service
        page.click('[data-testid="service-card"]:first-child')
        
        # Click connect button
        page.click('button:has-text("Connect")')
        
        # Wait for connection modal
        expect(page.locator('[role="dialog"]')).to_be_visible()
        
        # Confirm connection
        page.click('button:has-text("Confirm")')
        
        # Verify success message
        expect(page.locator('[role="alert"]')).to_contain_text("Connected successfully")
```

## Test Coverage

### Measuring Coverage

```bash
# Python coverage
pytest --cov=src --cov-report=html --cov-report=term

# JavaScript coverage
npm test -- --coverage

# View HTML reports
open htmlcov/index.html  # Python
open coverage/lcov-report/index.html  # JavaScript
```

### Coverage Goals

- **Overall**: Minimum 80% coverage
- **Critical Paths**: Minimum 95% coverage
- **New Code**: Minimum 90% coverage

### Coverage Configuration

**Python (.coveragerc):**

```ini
[run]
source = src
omit =
    */tests/*
    */migrations/*
    */__pycache__/*
    */venv/*

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:
    if TYPE_CHECKING:
```

**JavaScript (jest.config.js):**

```javascript
module.exports = {
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/index.tsx',
  ],
  coverageThresholds: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

## Continuous Integration

### GitHub Actions Workflow

```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test-python:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_DB: vm_gateway_test
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install dependencies
        run: |
          pip install -e ".[dev]"
      
      - name: Run tests
        run: |
          pytest --cov=src --cov-report=xml
        env:
          DATABASE_URL: postgresql://test:test@localhost:5432/vm_gateway_test
          REDIS_URL: redis://localhost:6379/0
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml

  test-javascript:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: |
          cd src/controller/frontend
          npm ci
      
      - name: Run tests
        run: |
          cd src/controller/frontend
          npm test -- --coverage
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./src/controller/frontend/coverage/coverage-final.json
```

## Best Practices

1. **Write Tests First**: Follow TDD when possible
2. **Test Behavior, Not Implementation**: Focus on what, not how
3. **Keep Tests Independent**: Each test should run in isolation
4. **Use Descriptive Names**: Test names should describe what they test
5. **Arrange-Act-Assert**: Structure tests clearly
6. **Mock External Dependencies**: Don't rely on external services
7. **Test Edge Cases**: Include boundary conditions and error cases
8. **Maintain Tests**: Update tests when code changes
9. **Review Test Coverage**: Aim for high coverage of critical paths
10. **Run Tests Frequently**: Run tests before committing

## Related Documentation

- [Development Setup](./01-setup.md) - Environment setup
- [Coding Standards](./02-coding-standards.md) - Code style guidelines
- [Contributing Guide](./04-contributing.md) - Contribution workflow
- [Debugging Guide](./05-debugging.md) - Debugging techniques
