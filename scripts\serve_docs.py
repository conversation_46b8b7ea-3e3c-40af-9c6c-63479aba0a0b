#!/usr/bin/env python3
"""
Documentation server entry point for VM Gateway platform.

This script starts the FastAPI documentation viewer server with proper
logging, error handling, and graceful shutdown capabilities.
"""

import argparse
import logging
import signal
import sys
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from shared.logging import setup_logging

# Global flag for graceful shutdown
shutdown_requested = False


def signal_handler(signum, frame):
    """
    Handle shutdown signals (SIGINT, SIGTERM) gracefully.
    
    Args:
        signum: Signal number
        frame: Current stack frame
    """
    global shutdown_requested
    logger = logging.getLogger("vm_gateway.doc_viewer")
    
    signal_name = signal.Signals(signum).name
    logger.info(f"Received {signal_name} signal, initiating graceful shutdown...")
    shutdown_requested = True
    
    # Exit cleanly
    sys.exit(0)


def parse_arguments():
    """
    Parse command-line arguments.
    
    Returns:
        Parsed arguments namespace
    """
    parser = argparse.ArgumentParser(
        description="VM Gateway Documentation Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run on default port 80 (requires root/administrator privileges)
  sudo python scripts/serve_docs.py
  
  # Run on custom port 8080 (no special privileges required)
  python scripts/serve_docs.py --port 8080
  
  # Run in development mode with auto-reload
  python scripts/serve_docs.py --dev --port 8080
  
  # Run with debug logging
  python scripts/serve_docs.py --log-level DEBUG --port 8080
        """
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=80,
        help='Port to bind the server to (default: 80)'
    )
    
    parser.add_argument(
        '--host',
        type=str,
        default='0.0.0.0',
        help='Host address to bind to (default: 0.0.0.0)'
    )
    
    parser.add_argument(
        '--dev',
        action='store_true',
        help='Enable development mode with auto-reload'
    )
    
    parser.add_argument(
        '--log-level',
        type=str,
        default='INFO',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        help='Logging level (default: INFO)'
    )
    
    parser.add_argument(
        '--no-log-file',
        action='store_true',
        help='Disable file logging (console only)'
    )
    
    return parser.parse_args()


def main():
    """
    Main entry point for documentation server.
    
    Sets up logging, parses arguments, and starts the Uvicorn server
    with proper error handling and graceful shutdown.
    """
    # Parse command-line arguments
    args = parse_arguments()
    
    # Setup centralized logging
    log_file = None if args.no_log_file else "logs/doc_viewer/doc_viewer.log"
    
    logger = setup_logging(
        component="doc_viewer",
        log_level=args.log_level,
        log_file=log_file
    )
    
    # Register signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Log startup information
    logger.info("=" * 60)
    logger.info("VM Gateway Documentation Server")
    logger.info("Version: a.0.0-20")
    logger.info("=" * 60)
    logger.info(f"Host: {args.host}")
    logger.info(f"Port: {args.port}")
    logger.info(f"Development mode: {args.dev}")
    logger.info(f"Log level: {args.log_level}")
    logger.info(f"Log file: {log_file if log_file else 'disabled'}")
    logger.info("=" * 60)
    
    # Check for port 80 permission warning
    if args.port == 80 and sys.platform != 'win32':
        logger.warning("Port 80 requires root/administrator privileges on Unix systems")
        logger.warning("If you encounter permission errors, try:")
        logger.warning("  - Run with sudo: sudo python scripts/serve_docs.py")
        logger.warning("  - Use a different port: python scripts/serve_docs.py --port 8080")
    
    try:
        # Import uvicorn here to catch import errors
        import uvicorn
        
        logger.info("Starting documentation server...")
        
        # Configure uvicorn
        uvicorn_config = {
            "app": "doc_viewer.main:app",
            "host": args.host,
            "port": args.port,
            "log_config": None,  # Use our centralized logging
            "access_log": True,
        }
        
        # Add development mode options
        if args.dev:
            uvicorn_config["reload"] = True
            uvicorn_config["reload_dirs"] = ["src/doc_viewer", "docs"]
            logger.info("Auto-reload enabled for src/doc_viewer and docs directories")
        
        # Start the server
        uvicorn.run(**uvicorn_config)
        
    except ImportError as e:
        logger.error(f"Failed to import required module: {e}")
        logger.error("Please ensure all dependencies are installed:")
        logger.error("  pip install -e .")
        sys.exit(1)
        
    except PermissionError:
        logger.error(f"Permission denied: Cannot bind to port {args.port}")
        logger.error("")
        logger.error("Port 80 requires root/administrator privileges.")
        logger.error("")
        logger.error("Solutions:")
        logger.error("  1. Run with elevated privileges:")
        if sys.platform == 'win32':
            logger.error("     - Run Command Prompt or PowerShell as Administrator")
        else:
            logger.error("     - Run with sudo: sudo python scripts/serve_docs.py")
        logger.error("  2. Use a different port (1024 or higher):")
        logger.error("     - python scripts/serve_docs.py --port 8080")
        logger.error("")
        sys.exit(1)
        
    except OSError as e:
        if "Address already in use" in str(e) or "Only one usage" in str(e):
            logger.error(f"Port {args.port} is already in use")
            logger.error("Solutions:")
            logger.error("  1. Stop the process using this port")
            logger.error("  2. Use a different port: python scripts/serve_docs.py --port 8080")
        else:
            logger.error(f"OS error occurred: {e}")
        sys.exit(1)
        
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received")
        sys.exit(0)
        
    except Exception as e:
        logger.error(f"Failed to start documentation server: {e}")
        logger.exception("Detailed error information:")
        sys.exit(1)
    
    finally:
        logger.info("Documentation server stopped")
        logger.info("=" * 60)


if __name__ == "__main__":
    main()
