---
title: "Coding Standards"
section: "Development"
order: 2
tags: ["development", "standards", "style", "conventions"]
last_updated: "2025-11-08"
---

# Coding Standards

This document defines the coding standards and style guidelines for the VM Network Gateway & Access Control Platform. Consistent code style improves readability, maintainability, and collaboration across the team.

## General Principles

### Code Quality Tenets

1. **Readability First**: Code is read more often than it's written
2. **Explicit Over Implicit**: Be clear about intentions
3. **DRY (Don't Repeat Yourself)**: Avoid code duplication
4. **KISS (Keep It Simple, Stupid)**: Prefer simple solutions
5. **YAGNI (You Aren't Gonna Need It)**: Don't add functionality until needed
6. **Fail Fast**: Detect and report errors early
7. **Test-Driven**: Write tests alongside code
8. **Document Intent**: Explain why, not what

## Python Standards

### Style Guide

We follow **PEP 8** with some modifications:

- **Line Length**: 100 characters (not 79)
- **Indentation**: 4 spaces (no tabs)
- **Quotes**: Double quotes for strings, single quotes for dict keys
- **Imports**: Absolute imports preferred

### Code Formatting

We use **Black** for automatic code formatting:

```bash
# Format all Python files
black src/ tests/

# Check formatting without changes
black --check src/ tests/

# Format specific file
black src/controller/main.py
```

**Black Configuration (pyproject.toml):**

```toml
[tool.black]
line-length = 100
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''
```

### Linting

We use **Ruff** for fast Python linting:

```bash
# Lint all Python files
ruff check src/ tests/

# Auto-fix issues
ruff check --fix src/ tests/

# Lint specific file
ruff check src/controller/main.py
```

**Ruff Configuration (pyproject.toml):**

```toml
[tool.ruff]
line-length = 100
target-version = "py311"

select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
]

ignore = [
    "E501",  # line too long (handled by black)
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]  # unused imports
"tests/*" = ["S101"]  # assert usage

[tool.ruff.isort]
known-first-party = ["src"]
```

### Type Hints

Use type hints for all function signatures:

```python
from typing import Optional, List, Dict, Any
from datetime import datetime

# Good
def get_user_by_id(user_id: str) -> Optional[Dict[str, Any]]:
    """Get user by ID."""
    return db.query(User).filter(User.id == user_id).first()

def create_connection(
    service_id: str,
    user_id: str,
    duration: int = 28800
) -> Dict[str, Any]:
    """Create a new connection."""
    connection = Connection(
        service_id=service_id,
        user_id=user_id,
        expires_at=datetime.utcnow() + timedelta(seconds=duration)
    )
    db.add(connection)
    db.commit()
    return connection.to_dict()

# Bad - no type hints
def get_user_by_id(user_id):
    return db.query(User).filter(User.id == user_id).first()
```

### Naming Conventions

**Variables and Functions:**
- Use `snake_case` for variables and functions
- Use descriptive names that reveal intent
- Avoid single-letter names except for iterators

```python
# Good
user_count = len(users)
def calculate_total_cost(items: List[Item]) -> float:
    pass

# Bad
uc = len(users)
def calc(x):
    pass
```

**Classes:**
- Use `PascalCase` for class names
- Use singular nouns for classes

```python
# Good
class UserService:
    pass

class ConnectionManager:
    pass

# Bad
class user_service:
    pass

class connections:
    pass
```

**Constants:**
- Use `UPPER_SNAKE_CASE` for constants
- Define at module level

```python
# Good
MAX_CONNECTIONS_PER_USER = 10
DEFAULT_SESSION_TIMEOUT = 28800
API_VERSION = "v1"

# Bad
maxConnectionsPerUser = 10
default_timeout = 28800
```

**Private Members:**
- Prefix with single underscore for internal use
- Prefix with double underscore for name mangling

```python
class UserService:
    def __init__(self):
        self._cache = {}  # Internal use
        self.__secret_key = "abc123"  # Name mangled
    
    def get_user(self, user_id: str) -> User:
        """Public method."""
        return self._fetch_from_cache(user_id)
    
    def _fetch_from_cache(self, user_id: str) -> Optional[User]:
        """Internal method."""
        return self._cache.get(user_id)
```

### Docstrings

Use **Google-style docstrings** for all public functions and classes:

```python
def create_connection(
    service_id: str,
    user_id: str,
    duration: int = 28800,
    reason: Optional[str] = None
) -> Connection:
    """Create a new connection to a service.
    
    Establishes a secure tunnel or proxy connection to the specified service
    for the given user. The connection will automatically expire after the
    specified duration.
    
    Args:
        service_id: Unique identifier of the service to connect to.
        user_id: Unique identifier of the user requesting the connection.
        duration: Connection duration in seconds. Defaults to 8 hours (28800).
        reason: Optional reason for the connection (required for approval workflows).
    
    Returns:
        Connection object with tunnel configuration and access details.
    
    Raises:
        ServiceNotFoundError: If the service does not exist.
        AccessDeniedError: If the user lacks permission to connect.
        MaxConnectionsExceededError: If user has reached connection limit.
    
    Example:
        >>> connection = create_connection(
        ...     service_id="svc_abc123",
        ...     user_id="usr_xyz789",
        ...     duration=3600,
        ...     reason="Database maintenance"
        ... )
        >>> print(connection.id)
        conn_def456
    """
    # Implementation
    pass
```

**Class Docstrings:**

```python
class ConnectionManager:
    """Manages service connections and tunnels.
    
    The ConnectionManager handles the lifecycle of connections including
    creation, monitoring, and termination. It enforces access control
    policies and manages connection limits.
    
    Attributes:
        max_connections: Maximum concurrent connections per user.
        default_duration: Default connection duration in seconds.
        tunnel_protocol: Protocol used for tunneling (wireguard, openvpn).
    
    Example:
        >>> manager = ConnectionManager(max_connections=10)
        >>> connection = manager.create(service_id="svc_123", user_id="usr_456")
        >>> manager.terminate(connection.id)
    """
    
    def __init__(self, max_connections: int = 10):
        """Initialize the connection manager.
        
        Args:
            max_connections: Maximum concurrent connections per user.
        """
        self.max_connections = max_connections
```

### Error Handling

**Use Specific Exceptions:**

```python
# Good - specific exceptions
class ServiceNotFoundError(Exception):
    """Raised when a service cannot be found."""
    pass

class AccessDeniedError(Exception):
    """Raised when user lacks permission."""
    pass

def get_service(service_id: str) -> Service:
    service = db.query(Service).filter(Service.id == service_id).first()
    if not service:
        raise ServiceNotFoundError(f"Service {service_id} not found")
    return service

# Bad - generic exceptions
def get_service(service_id: str) -> Service:
    service = db.query(Service).filter(Service.id == service_id).first()
    if not service:
        raise Exception("Service not found")
    return service
```

**Always Clean Up Resources:**

```python
# Good - use context managers
def process_file(filename: str) -> None:
    with open(filename, 'r') as f:
        data = f.read()
        process_data(data)

# Good - explicit cleanup
connection = None
try:
    connection = create_connection()
    connection.execute(query)
finally:
    if connection:
        connection.close()

# Bad - no cleanup
f = open(filename, 'r')
data = f.read()
# File never closed!
```

### Logging

Use structured logging with appropriate levels:

```python
import logging

logger = logging.getLogger(__name__)

# Good - structured logging with context
logger.info(
    "Connection established",
    extra={
        "connection_id": connection.id,
        "service_id": service.id,
        "user_id": user.id,
        "duration": duration
    }
)

logger.error(
    "Failed to establish connection",
    extra={
        "service_id": service.id,
        "user_id": user.id,
        "error": str(e)
    },
    exc_info=True
)

# Bad - unstructured logging
logger.info(f"Connection {connection.id} established")
logger.error("Connection failed")
```

**Log Levels:**
- **DEBUG**: Detailed diagnostic information
- **INFO**: General informational messages
- **WARNING**: Warning messages for potentially harmful situations
- **ERROR**: Error messages for serious problems
- **CRITICAL**: Critical messages for very serious errors

### Async/Await

Use async/await for I/O-bound operations:

```python
# Good - async for I/O operations
async def get_user(user_id: str) -> User:
    async with db.session() as session:
        result = await session.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalar_one_or_none()

async def fetch_service_metrics(service_id: str) -> Dict[str, Any]:
    async with httpx.AsyncClient() as client:
        response = await client.get(f"/api/services/{service_id}/metrics")
        return response.json()

# Use asyncio.gather for concurrent operations
async def get_all_service_metrics(service_ids: List[str]) -> List[Dict[str, Any]]:
    tasks = [fetch_service_metrics(sid) for sid in service_ids]
    return await asyncio.gather(*tasks)
```

## JavaScript/TypeScript Standards

### Style Guide

We follow **Airbnb JavaScript Style Guide** with TypeScript extensions:

- **Indentation**: 2 spaces
- **Quotes**: Single quotes for strings
- **Semicolons**: Always use semicolons
- **Line Length**: 100 characters

### Code Formatting

We use **Prettier** for automatic formatting:

```bash
# Format all files
npm run format

# Check formatting
npm run format:check
```

**Prettier Configuration (.prettierrc):**

```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2,
  "useTabs": false,
  "arrowParens": "avoid",
  "endOfLine": "lf"
}
```

### Linting

We use **ESLint** for JavaScript/TypeScript linting:

```bash
# Lint all files
npm run lint

# Auto-fix issues
npm run lint:fix
```

**ESLint Configuration (.eslintrc.json):**

```json
{
  "extends": [
    "airbnb",
    "airbnb-typescript",
    "airbnb/hooks",
    "plugin:@typescript-eslint/recommended",
    "plugin:prettier/recommended"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 2022,
    "sourceType": "module",
    "project": "./tsconfig.json"
  },
  "rules": {
    "react/react-in-jsx-scope": "off",
    "import/prefer-default-export": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off"
  }
}
```

### TypeScript

Always use TypeScript for new code:

```typescript
// Good - explicit types
interface User {
  id: string;
  email: string;
  name: string;
  roles: string[];
}

interface ConnectionRequest {
  serviceId: string;
  userId: string;
  duration?: number;
  reason?: string;
}

async function createConnection(request: ConnectionRequest): Promise<Connection> {
  const { serviceId, userId, duration = 28800, reason } = request;
  
  const connection = await api.post<Connection>('/connections', {
    service_id: serviceId,
    user_id: userId,
    duration,
    reason,
  });
  
  return connection.data;
}

// Bad - no types
async function createConnection(request) {
  const connection = await api.post('/connections', request);
  return connection.data;
}
```

### React Components

Use functional components with hooks:

```typescript
// Good - functional component with TypeScript
interface ServiceCardProps {
  service: Service;
  onConnect: (serviceId: string) => void;
  onConfigure?: (serviceId: string) => void;
}

export const ServiceCard: React.FC<ServiceCardProps> = ({
  service,
  onConnect,
  onConfigure,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  
  const handleConnect = async () => {
    setIsLoading(true);
    try {
      await onConnect(service.id);
    } catch (error) {
      console.error('Failed to connect:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="service-card">
      <h3>{service.name}</h3>
      <p>{service.description}</p>
      <button onClick={handleConnect} disabled={isLoading}>
        {isLoading ? 'Connecting...' : 'Connect'}
      </button>
      {onConfigure && (
        <button onClick={() => onConfigure(service.id)}>
          Configure
        </button>
      )}
    </div>
  );
};

// Bad - class component
class ServiceCard extends React.Component {
  // Avoid class components
}
```

### Naming Conventions

**Components:**
- Use `PascalCase` for component names
- Use descriptive names

```typescript
// Good
export const ServiceList: React.FC = () => { /* ... */ };
export const ConnectionStatus: React.FC = () => { /* ... */ };

// Bad
export const serviceList: React.FC = () => { /* ... */ };
export const CS: React.FC = () => { /* ... */ };
```

**Functions and Variables:**
- Use `camelCase` for functions and variables
- Use descriptive names

```typescript
// Good
const userCount = users.length;
const fetchServiceMetrics = async (serviceId: string) => { /* ... */ };

// Bad
const uc = users.length;
const fsm = async (sid: string) => { /* ... */ };
```

**Constants:**
- Use `UPPER_SNAKE_CASE` for constants

```typescript
// Good
const MAX_RETRY_ATTEMPTS = 3;
const API_BASE_URL = 'https://api.example.com';

// Bad
const maxRetryAttempts = 3;
const apiBaseUrl = 'https://api.example.com';
```

## SQL Standards

### Query Formatting

```sql
-- Good - readable formatting
SELECT
    u.id,
    u.email,
    u.name,
    COUNT(c.id) AS connection_count
FROM users u
LEFT JOIN connections c ON c.user_id = u.id
WHERE u.status = 'active'
    AND u.created_at >= '2025-01-01'
GROUP BY u.id, u.email, u.name
HAVING COUNT(c.id) > 0
ORDER BY connection_count DESC
LIMIT 100;

-- Bad - hard to read
SELECT u.id,u.email,u.name,COUNT(c.id) AS connection_count FROM users u LEFT JOIN connections c ON c.user_id=u.id WHERE u.status='active' AND u.created_at>='2025-01-01' GROUP BY u.id,u.email,u.name HAVING COUNT(c.id)>0 ORDER BY connection_count DESC LIMIT 100;
```

### Use Parameterized Queries

```python
# Good - parameterized query
user_id = "usr_123"
query = "SELECT * FROM users WHERE id = %s"
result = db.execute(query, (user_id,))

# Bad - SQL injection risk
user_id = "usr_123"
query = f"SELECT * FROM users WHERE id = '{user_id}'"
result = db.execute(query)
```

## Git Commit Messages

### Format

```
<type>(<scope>): <subject>

<body>

<footer>
```

### Types

- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, etc.)
- **refactor**: Code refactoring
- **perf**: Performance improvements
- **test**: Adding or updating tests
- **chore**: Maintenance tasks

### Examples

```
feat(api): add webhook support for service events

Implement webhook subscription API endpoints allowing users to
receive real-time notifications for service status changes,
connection events, and alerts.

- Add POST /api/v1/webhooks endpoint
- Add webhook delivery queue with retry logic
- Add HMAC signature verification
- Add webhook management UI

Closes #123
```

```
fix(agent): resolve memory leak in service discovery

The service discovery scanner was not properly releasing resources
after each scan cycle, causing memory usage to grow over time.

- Add proper cleanup in scanner shutdown
- Use context managers for file handles
- Clear process cache after each scan

Fixes #456
```

## Code Review Guidelines

### As a Reviewer

1. **Be Respectful**: Provide constructive feedback
2. **Be Specific**: Point to exact lines and suggest improvements
3. **Ask Questions**: Seek to understand the author's intent
4. **Approve Quickly**: Don't block on minor style issues
5. **Test Locally**: Pull and test significant changes

### As an Author

1. **Keep PRs Small**: Aim for < 400 lines changed
2. **Write Clear Descriptions**: Explain what and why
3. **Add Tests**: Include tests for new functionality
4. **Update Docs**: Update relevant documentation
5. **Respond Promptly**: Address feedback quickly

## Tools and Automation

### Pre-commit Hooks

Install pre-commit hooks to automatically check code:

```bash
# Install pre-commit
pip install pre-commit

# Install hooks
pre-commit install

# Run manually
pre-commit run --all-files
```

**.pre-commit-config.yaml:**

```yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict

  - repo: https://github.com/psf/black
    rev: 23.11.0
    hooks:
      - id: black

  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.1.6
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        types_or: [javascript, jsx, ts, tsx, json, yaml, markdown]
```

### CI/CD Checks

All pull requests must pass:

1. **Linting**: Ruff (Python), ESLint (JavaScript/TypeScript)
2. **Formatting**: Black (Python), Prettier (JavaScript/TypeScript)
3. **Type Checking**: mypy (Python), tsc (TypeScript)
4. **Tests**: pytest (Python), Jest (JavaScript/TypeScript)
5. **Coverage**: Minimum 80% code coverage

## Related Documentation

- [Development Setup](./01-setup.md) - Environment setup guide
- [Testing Guide](./03-testing.md) - Testing standards and practices
- [Contributing Guide](./04-contributing.md) - Contribution workflow
