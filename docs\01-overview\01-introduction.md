---
title: "Introduction to VM Gateway Platform"
section: "Overview"
order: 1
tags: ["overview", "introduction", "platform"]
last_updated: "2025-11-08"
---

# Introduction to VM Gateway Platform

## What is VM Gateway?

VM Gateway is a comprehensive, self-hosted VM networking and access control platform that revolutionizes how organizations manage, monitor, and secure access to services running across their virtual machine infrastructure. The platform provides automatic service discovery, granular role-based access control (RBAC), and secure remote connectivity through an intuitive centralized web interface.

Think of VM Gateway as a self-hosted alternative to commercial solutions like Twingate, but with significantly enhanced capabilities for service discovery, real-time monitoring, and infrastructure management. Unlike traditional VPN solutions that provide network-level access, VM Gateway operates at the service level, giving administrators precise control over which users can access which specific services, when, and under what conditions.

## The Problem We Solve

Modern cloud and virtualized infrastructure presents several critical challenges:

### Service Visibility Gap

Organizations often struggle to maintain an accurate inventory of services running across their VM fleet. Services are deployed, updated, and decommissioned constantly, making manual tracking nearly impossible. Without comprehensive visibility:

- Security teams cannot assess their attack surface
- Operations teams struggle to troubleshoot issues
- Compliance audits become time-consuming and error-prone
- Resource optimization opportunities are missed
- Shadow IT proliferates unchecked

### Access Control Complexity

Traditional access control mechanisms operate at the network level (VPNs, firewalls) or require manual configuration for each service. This creates several problems:

- **All-or-nothing access**: Users get access to entire networks rather than specific services
- **Manual overhead**: Each new service requires manual firewall rules and access configurations
- **Audit challenges**: Tracking who accessed what service and when is difficult
- **Security risks**: Overly broad permissions increase the blast radius of compromised credentials
- **Compliance burden**: Meeting regulatory requirements for access control and auditing is complex

### Monitoring and Troubleshooting Difficulties

When services span multiple VMs and environments, monitoring becomes fragmented:

- Metrics are scattered across different systems
- Correlating service health with infrastructure health is manual
- Identifying the root cause of issues requires jumping between multiple tools
- Historical data for capacity planning is incomplete
- Alert fatigue from poorly configured monitoring

### Remote Access Friction

Developers and operators need secure access to services for development, debugging, and maintenance:

- VPNs provide too much access and create security risks
- SSH tunneling requires manual setup and is error-prone
- Jump boxes add latency and complexity
- Temporary access is difficult to grant and revoke
- Audit trails for remote access are incomplete

## How VM Gateway Solves These Problems

VM Gateway addresses these challenges through three integrated components working in harmony:

### 1. Intelligent Agent Software

Lightweight agents installed on each VM automatically:

- **Discover all services** running on the VM by scanning listening ports and identifying processes
- **Classify services** using a sophisticated multi-tier engine that combines process name matching, port convention analysis, protocol detection, and optional machine learning
- **Collect comprehensive metrics** including resource usage, performance data, and health status
- **Monitor service health** with configurable health checks and automatic anomaly detection
- **Report changes in real-time** whenever services start, stop, or change configuration

The agent operates with minimal resource overhead and continues functioning even when disconnected from the controller, ensuring continuous monitoring and local data collection.

### 2. Centralized Controller & Web Interface

The controller provides a unified management plane with:

- **Service catalog**: Searchable, filterable inventory of all discovered services across all VMs
- **Granular RBAC**: Define precisely who can access which services, with support for time-based restrictions, IP whitelisting, approval workflows, and conditional access
- **Real-time monitoring**: Live dashboards showing system and service metrics, with customizable alerts and anomaly detection
- **Built-in proxy**: Direct browser access to HTTP/HTTPS services through an authenticated proxy, eliminating the need for VPNs for web-based tools
- **Audit logging**: Comprehensive activity logs for compliance and security investigations
- **User management**: Complete user lifecycle management with support for local authentication, SSO, MFA, and LDAP/Active Directory integration

### 3. Desktop Client Application

A cross-platform desktop client enables:

- **Secure port forwarding**: Establish encrypted tunnels to any TCP/UDP service with a single click
- **Connection profiles**: Save frequently used connections for quick access
- **Automatic authentication**: Seamless integration with the controller's authentication system
- **Connection management**: View active connections, monitor bandwidth usage, and manage multiple simultaneous tunnels
- **Offline capability**: Access previously authorized services even when the controller is temporarily unreachable

## Core Value Propositions

### For Security Teams

- **Complete visibility** into all services across the infrastructure
- **Zero-trust access control** with granular permissions and continuous verification
- **Comprehensive audit trails** for compliance and forensic investigations
- **Automated security monitoring** with alerts for suspicious activity
- **Reduced attack surface** through service-level access instead of network-level access
- **Secrets management integration** with support for HashiCorp Vault, AWS Secrets Manager, and Azure Key Vault

### For Operations Teams

- **Automatic service discovery** eliminates manual inventory management
- **Centralized monitoring** provides a single pane of glass for all services
- **Intelligent alerting** reduces noise and focuses attention on real issues
- **Historical data** enables capacity planning and trend analysis
- **Quick troubleshooting** with correlated metrics and logs
- **Self-service access** reduces operational burden of managing access requests

### For Developers

- **Frictionless access** to development and staging services
- **No VPN required** for web-based services (use built-in proxy)
- **One-click port forwarding** for databases, APIs, and other TCP services
- **Connection profiles** for frequently accessed services
- **Temporary elevated access** for production debugging with automatic expiration
- **Service documentation** and connection details in one place

### For Compliance and Audit

- **Complete audit trail** of all access and actions
- **Automated compliance reporting** for SOC 2, HIPAA, PCI-DSS, and other frameworks
- **Approval workflows** for sensitive resource access
- **Time-based access controls** enforce business hour restrictions
- **Immutable logs** with tamper detection
- **Retention policies** configurable per compliance requirements

## Key Differentiators

### Compared to Traditional VPNs

- **Service-level access** instead of network-level (principle of least privilege)
- **Automatic service discovery** vs. manual configuration
- **Built-in monitoring and metrics** vs. separate tools
- **Granular RBAC** vs. all-or-nothing access
- **No client configuration** for web services (use built-in proxy)
- **Better performance** (direct connections, no routing overhead)

### Compared to Commercial Solutions (Twingate, etc.)

- **Self-hosted** - complete control over data and infrastructure
- **Enhanced service discovery** with intelligent classification
- **Integrated monitoring** - not just access control
- **Comprehensive secrets management** with multiple backend support
- **Multi-VM deployment** with domain-based routing
- **Open architecture** - extensible and customizable
- **No per-user licensing costs** - unlimited users

### Compared to SSH Tunneling / Jump Boxes

- **User-friendly interface** vs. command-line complexity
- **Centralized access control** vs. distributed SSH keys
- **Comprehensive audit logging** vs. limited SSH logs
- **Automatic service discovery** vs. manual documentation
- **Built-in monitoring** vs. separate tools
- **Approval workflows** for sensitive access
- **Better security** (no long-lived SSH keys, MFA support)

## Architecture Overview

The platform consists of three primary components:

```
┌─────────────────────────────────────────────────────────────┐
│                     Users & Clients                         │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐      │
│  │   Browser    │  │   Browser    │  │   Desktop    │      │
│  │   (Admin)    │  │   (User)     │  │   Client     │      │
│  └──────┬───────┘  └──────┬───────┘  └──────┬───────┘      │
└─────────┼──────────────────┼──────────────────┼─────────────┘
          │                  │                  │
          │ HTTPS            │ HTTPS            │ WSS/HTTPS
          │                  │ (Proxy)          │ (Tunnel)
          ▼                  ▼                  ▼
┌─────────────────────────────────────────────────────────────┐
│              Controller & Web Interface                     │
│  ┌────────────────────────────────────────────────────┐    │
│  │  FastAPI Backend                                   │    │
│  │  - Authentication & Authorization                  │    │
│  │  - Service Catalog Management                      │    │
│  │  - Metrics Aggregation                             │    │
│  │  - Alert Management                                │    │
│  │  - HTTP/HTTPS Proxy                                │    │
│  └────────────────────────────────────────────────────┘    │
│  ┌────────────────────────────────────────────────────┐    │
│  │  React Frontend                                    │    │
│  │  - Dashboard & Monitoring                          │    │
│  │  - Service Catalog UI                              │    │
│  │  - User & Access Management                        │    │
│  └────────────────────────────────────────────────────┘    │
│  ┌────────────────────────────────────────────────────┐    │
│  │  PostgreSQL Database                               │    │
│  │  - User accounts & roles                           │    │
│  │  - Service catalog                                 │    │
│  │  - Metrics & logs                                  │    │
│  │  - Audit trail                                     │    │
│  └────────────────────────────────────────────────────┘    │
└─────────────────────────┬───────────────────────────────────┘
                          │
                          │ WebSocket + REST
                          │ (mTLS secured)
                          │
┌─────────────────────────┴───────────────────────────────────┐
│                    VM Fleet (Monitored VMs)                 │
│  ┌──────────────────┐  ┌──────────────────┐  ┌──────────┐  │
│  │  VM 1            │  │  VM 2            │  │  VM N    │  │
│  │  ┌────────────┐  │  │  ┌────────────┐  │  │  ┌────┐  │  │
│  │  │   Agent    │  │  │  │   Agent    │  │  │  │Agent│  │  │
│  │  └────────────┘  │  │  └────────────┘  │  │  └────┘  │  │
│  │  ┌────────────┐  │  │  ┌────────────┐  │  │  ┌────┐  │  │
│  │  │  Services  │  │  │  │  Services  │  │  │  │Svcs│  │  │
│  │  │  - Web     │  │  │  │  - DB      │  │  │  │... │  │  │
│  │  │  - API     │  │  │  │  - Cache   │  │  │  └────┘  │  │
│  │  │  - ...     │  │  │  │  - ...     │  │  │          │  │
│  │  └────────────┘  │  │  └────────────┘  │  │          │  │
│  └──────────────────┘  └──────────────────┘  └──────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### Component Interaction Flow

1. **Service Discovery**: Agents continuously scan their local VMs for listening ports and running processes, classify discovered services, and report findings to the controller

2. **Catalog Synchronization**: The controller maintains a centralized catalog of all services across all VMs, with real-time updates as services start, stop, or change

3. **Access Control**: When a user requests access to a service, the controller evaluates RBAC policies, approval workflows, and conditional access rules before granting or denying access

4. **Monitoring**: Agents collect metrics and health data, sending it to the controller for aggregation, visualization, and alerting

5. **Connection Establishment**: 
   - For HTTP/HTTPS services: Users access through the controller's built-in proxy
   - For other services: Desktop client establishes encrypted tunnel through the controller to the agent

## Deployment Models

VM Gateway supports flexible deployment to match your infrastructure:

### Centralized Deployment

Single controller instance managing all VMs:
- Simplest to deploy and manage
- Suitable for small to medium deployments (up to ~500 VMs)
- Single point of management
- Ideal for single-region deployments

### Distributed Deployment

Multiple controller instances with shared database:
- High availability and load balancing
- Suitable for large deployments (500+ VMs)
- Geographic distribution for reduced latency
- Fault tolerance

### Hybrid Deployment

Combination of centralized and distributed:
- Regional controllers for local VMs
- Central controller for global management
- Domain-based routing to appropriate controller
- Optimal for multi-region, large-scale deployments

## Security Model

VM Gateway implements a zero-trust security architecture:

### Authentication

- Multiple authentication methods (local, SSO, LDAP/AD)
- Multi-factor authentication (TOTP, WebAuthn, SMS)
- Strong password policies with breach detection
- Session management with device tracking

### Authorization

- Granular RBAC with resource-level permissions
- Conditional access based on time, location, device
- Approval workflows for sensitive resources
- Temporary privilege elevation with automatic expiration

### Encryption

- TLS 1.3 for all network communication
- mTLS between agents and controller
- End-to-end encryption for tunneled connections
- Encrypted storage for sensitive data (AES-256-GCM)

### Audit & Compliance

- Comprehensive audit logging of all actions
- Immutable log storage with tamper detection
- Automated compliance reporting
- Configurable retention policies

## Use Cases

### Development & Testing

- Developers access staging databases and APIs without VPN
- Temporary elevated access to production for debugging
- Service discovery eliminates outdated documentation
- Connection profiles for frequently used services

### Operations & SRE

- Centralized monitoring of all services
- Quick troubleshooting with correlated metrics
- Automated alerting for service issues
- Capacity planning with historical data

### Security & Compliance

- Complete visibility into service inventory
- Granular access control with audit trails
- Automated compliance reporting
- Secrets management integration

### Multi-Tenant Environments

- Isolate customer environments
- Per-tenant access controls
- Resource usage tracking per tenant
- Tenant-specific monitoring and alerting

## Getting Started

To begin using VM Gateway:

1. **Deploy the Controller**: Install the controller on a dedicated VM or container
2. **Install Agents**: Deploy agents to VMs you want to monitor
3. **Configure Authentication**: Set up user accounts and authentication methods
4. **Define Access Policies**: Create roles and assign permissions
5. **Access Services**: Users can now discover and access services through the web interface or desktop client

For detailed setup instructions, see the [Getting Started Guide](05-getting-started.md).

## What's Next

This introduction provides a high-level overview of the VM Gateway platform. To dive deeper:

- **[Technology Stack](/docs/01-overview/02-technology-stack.md)**: Detailed breakdown of all technologies used
- **[Version System](/docs/01-overview/03-versioning.md)**: Understanding version numbering and releases
- **[Feature Gating](/docs/01-overview/04-feature-gating.md)**: How feature flags enable safe, incremental rollouts
- **[Getting Started](/docs/01-overview/05-getting-started.md)**: Step-by-step guide to setting up your first deployment
- **[Architecture](/docs/02-architecture/01-system-overview.md)**: Deep dive into system architecture and design
- **[Agent Documentation](/docs/03-agent/01-overview.md)**: Learn about the intelligent agent software
- **[Controller Documentation](/docs/04-controller/01-overview.md)**: Explore the centralized controller features
- **[Client Documentation](/docs/05-client/01-overview.md)**: Desktop client capabilities and usage

## Summary

VM Gateway transforms VM infrastructure management by providing automatic service discovery, granular access control, and comprehensive monitoring in a single, integrated platform. Whether you're a security team seeking better visibility and control, an operations team wanting centralized monitoring, or a developer needing frictionless access to services, VM Gateway delivers a modern, self-hosted solution that scales with your organization.
