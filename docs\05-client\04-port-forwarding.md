---
title: "Local Port Forwarding"
section: "Client"
order: 4
tags: ["client", "port-forwarding", "networking"]
last_updated: "2025-11-08"
---

# Local Port Forwarding

## Overview

Local port forwarding is the mechanism by which the VM Gateway Client makes remote services accessible on the user's local machine. By binding a local port and forwarding all traffic through an encrypted tunnel to the remote service, the client enables users to interact with remote services using standard local tools without any special configuration or awareness of the underlying tunneling infrastructure.

This approach provides a transparent, secure, and user-friendly way to access remote databases, APIs, web services, and any other TCP/UDP-based applications.

## Architecture

### Data Flow

The complete data flow for a forwarded connection:

```
┌─────────────────────────────────────────────────────────────┐
│ User Application (e.g., pgAdmin, curl, browser)             │
│ Connects to: localhost:5432                                 │
└────────────────────────┬────────────────────────────────────┘
                         │ TCP connection
                         ↓
┌─────────────────────────────────────────────────────────────┐
│ Client Port Forwarder                                       │
│ Listening on: 127.0.0.1:5432                               │
│ - Accepts local connections                                 │
│ - Encrypts data                                             │
│ - Sends through tunnel                                      │
└────────────────────────┬────────────────────────────────────┘
                         │ Encrypted tunnel
                         ↓
┌─────────────────────────────────────────────────────────────┐
│ Tunnel (WireGuard/TLS/WebSocket)                           │
│ - End-to-end encryption                                     │
│ - Authentication                                            │
│ - Connection multiplexing                                   │
└────────────────────────┬────────────────────────────────────┘
                         │ Network (Internet)
                         ↓
┌─────────────────────────────────────────────────────────────┐
│ Agent on Remote VM                                          │
│ - Receives encrypted data                                   │
│ - Decrypts data                                             │
│ - Forwards to local service                                 │
└────────────────────────┬────────────────────────────────────┘
                         │ Local connection
                         ↓
┌─────────────────────────────────────────────────────────────┐
│ Remote Service (e.g., PostgreSQL)                          │
│ Listening on: 127.0.0.1:5432 or 0.0.0.0:5432              │
│ - Receives connection as if local                           │
│ - No awareness of tunneling                                 │
└─────────────────────────────────────────────────────────────┘
```

### Component Responsibilities

**Port Forwarder:**
- Binds and listens on local port
- Accepts incoming local connections
- Manages multiple simultaneous connections
- Forwards data to tunnel
- Handles connection lifecycle

**Tunnel:**
- Encrypts outgoing data
- Decrypts incoming data
- Multiplexes multiple streams
- Maintains connection to agent
- Handles reconnection

**Agent:**
- Receives tunnel connections
- Demultiplexes streams
- Connects to local services
- Forwards data bidirectionally
- Enforces access policies


## Implementation

### Port Forwarder Class

The core port forwarding implementation:

```python
import socket
import threading
import select
from typing import Dict, Optional

class PortForwarder:
    def __init__(
        self,
        local_port: int,
        tunnel: Tunnel,
        remote_host: str,
        remote_port: int,
        bind_address: str = "127.0.0.1"
    ):
        self.local_port = local_port
        self.tunnel = tunnel
        self.remote_host = remote_host
        self.remote_port = remote_port
        self.bind_address = bind_address
        
        self.server_socket: Optional[socket.socket] = None
        self.active_streams: Dict[int, StreamHandler] = {}
        self.next_stream_id = 1
        self.running = False
        self.accept_thread: Optional[threading.Thread] = None
        
        self.stats = {
            "bytes_sent": 0,
            "bytes_received": 0,
            "connections_total": 0,
            "connections_active": 0,
            "errors": 0
        }
    
    def start(self):
        """Start the port forwarder"""
        # Create server socket
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            self.server_socket.bind((self.bind_address, self.local_port))
            self.server_socket.listen(128)
            logger.info(f"Port forwarder listening on {self.bind_address}:{self.local_port}")
        except OSError as e:
            if e.errno == 48:  # Address already in use
                raise PortInUseError(f"Port {self.local_port} is already in use")
            raise
        
        # Start accept thread
        self.running = True
        self.accept_thread = threading.Thread(target=self._accept_loop, daemon=True)
        self.accept_thread.start()
    
    def stop(self):
        """Stop the port forwarder"""
        self.running = False
        
        # Close all active streams
        for stream in list(self.active_streams.values()):
            stream.close()
        
        # Close server socket
        if self.server_socket:
            self.server_socket.close()
        
        # Wait for accept thread
        if self.accept_thread:
            self.accept_thread.join(timeout=5)
        
        logger.info(f"Port forwarder stopped on port {self.local_port}")
    
    def _accept_loop(self):
        """Accept incoming local connections"""
        while self.running:
            try:
                # Use select for timeout
                readable, _, _ = select.select([self.server_socket], [], [], 1.0)
                
                if not readable:
                    continue
                
                # Accept connection
                client_socket, client_address = self.server_socket.accept()
                logger.debug(f"Accepted connection from {client_address}")
                
                # Handle connection in new thread
                stream_id = self._allocate_stream_id()
                stream = StreamHandler(
                    stream_id=stream_id,
                    client_socket=client_socket,
                    tunnel=self.tunnel,
                    remote_host=self.remote_host,
                    remote_port=self.remote_port,
                    forwarder=self
                )
                
                self.active_streams[stream_id] = stream
                stream.start()
                
                self.stats["connections_total"] += 1
                self.stats["connections_active"] += 1
                
            except Exception as e:
                if self.running:
                    logger.error(f"Error accepting connection: {e}")
                    self.stats["errors"] += 1
    
    def _allocate_stream_id(self) -> int:
        """Allocate a unique stream ID"""
        stream_id = self.next_stream_id
        self.next_stream_id += 1
        return stream_id
    
    def remove_stream(self, stream_id: int):
        """Remove a stream from active streams"""
        if stream_id in self.active_streams:
            del self.active_streams[stream_id]
            self.stats["connections_active"] -= 1
    
    def update_stats(self, bytes_sent: int = 0, bytes_received: int = 0):
        """Update statistics"""
        self.stats["bytes_sent"] += bytes_sent
        self.stats["bytes_received"] += bytes_received
```

### Stream Handler

Handles individual forwarded connections:

```python
class StreamHandler:
    def __init__(
        self,
        stream_id: int,
        client_socket: socket.socket,
        tunnel: Tunnel,
        remote_host: str,
        remote_port: int,
        forwarder: PortForwarder
    ):
        self.stream_id = stream_id
        self.client_socket = client_socket
        self.tunnel = tunnel
        self.remote_host = remote_host
        self.remote_port = remote_port
        self.forwarder = forwarder
        
        self.running = False
        self.send_thread: Optional[threading.Thread] = None
        self.recv_thread: Optional[threading.Thread] = None
        
        self.send_buffer = queue.Queue()
        self.established = threading.Event()
    
    def start(self):
        """Start forwarding data"""
        # Send stream open message to agent
        self.tunnel.send_message({
            "type": "STREAM_OPEN",
            "stream_id": self.stream_id,
            "remote_host": self.remote_host,
            "remote_port": self.remote_port
        })
        
        # Wait for acknowledgment
        if not self.established.wait(timeout=10):
            logger.error(f"Stream {self.stream_id} establishment timeout")
            self.close()
            return
        
        # Start forwarding threads
        self.running = True
        
        self.send_thread = threading.Thread(
            target=self._send_loop,
            daemon=True
        )
        self.send_thread.start()
        
        self.recv_thread = threading.Thread(
            target=self._recv_loop,
            daemon=True
        )
        self.recv_thread.start()
    
    def _send_loop(self):
        """Forward data from local client to tunnel"""
        try:
            while self.running:
                # Read from local socket
                data = self.client_socket.recv(8192)
                
                if not data:
                    # Client closed connection
                    logger.debug(f"Stream {self.stream_id} client closed")
                    break
                
                # Send through tunnel
                self.tunnel.send_message({
                    "type": "STREAM_DATA",
                    "stream_id": self.stream_id,
                    "data": data
                })
                
                self.forwarder.update_stats(bytes_sent=len(data))
                
        except Exception as e:
            logger.error(f"Stream {self.stream_id} send error: {e}")
        finally:
            self.close()
    
    def _recv_loop(self):
        """Forward data from tunnel to local client"""
        try:
            while self.running:
                # Receive from tunnel
                message = self.tunnel.receive_message(
                    stream_id=self.stream_id,
                    timeout=30
                )
                
                if not message:
                    # Timeout or tunnel closed
                    break
                
                if message["type"] == "STREAM_DATA":
                    # Write to local socket
                    self.client_socket.sendall(message["data"])
                    self.forwarder.update_stats(bytes_received=len(message["data"]))
                
                elif message["type"] == "STREAM_CLOSE":
                    # Remote closed connection
                    logger.debug(f"Stream {self.stream_id} remote closed")
                    break
                
        except Exception as e:
            logger.error(f"Stream {self.stream_id} receive error: {e}")
        finally:
            self.close()
    
    def close(self):
        """Close the stream"""
        if not self.running:
            return
        
        self.running = False
        
        # Send stream close message
        try:
            self.tunnel.send_message({
                "type": "STREAM_CLOSE",
                "stream_id": self.stream_id
            })
        except:
            pass
        
        # Close local socket
        try:
            self.client_socket.close()
        except:
            pass
        
        # Remove from forwarder
        self.forwarder.remove_stream(self.stream_id)
        
        logger.debug(f"Stream {self.stream_id} closed")
    
    def handle_stream_established(self):
        """Called when stream is established on remote side"""
        self.established.set()
```

## Port Assignment Strategies

### Automatic Port Assignment

Find any available port:

```python
def find_available_port(start_port: int = 10000, end_port: int = 65535) -> int:
    """Find an available local port"""
    for port in range(start_port, end_port):
        if is_port_available(port):
            return port
    
    raise NoAvailablePortError("No available ports in range")

def is_port_available(port: int) -> bool:
    """Check if a port is available"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        sock.bind(("127.0.0.1", port))
        sock.close()
        return True
    except OSError:
        return False
```

### Same-As-Remote Port Assignment

Try to use the same port number as the remote service:

```python
def assign_same_port(remote_port: int) -> int:
    """Try to assign the same port as remote"""
    if is_port_available(remote_port):
        return remote_port
    
    # Port not available, find alternative
    logger.warning(f"Port {remote_port} not available, finding alternative")
    return find_available_port()
```

### Persistent Port Assignment

Remember port assignments for services:

```python
class PortAssignmentManager:
    def __init__(self, db_path: str):
        self.db = sqlite3.connect(db_path)
        self._create_tables()
    
    def _create_tables(self):
        self.db.execute("""
            CREATE TABLE IF NOT EXISTS port_assignments (
                service_id TEXT PRIMARY KEY,
                local_port INTEGER NOT NULL,
                last_used TIMESTAMP
            )
        """)
    
    def get_port_for_service(self, service_id: str, remote_port: int) -> int:
        """Get persistent port assignment for service"""
        # Check if we have a previous assignment
        row = self.db.execute(
            "SELECT local_port FROM port_assignments WHERE service_id = ?",
            (service_id,)
        ).fetchone()
        
        if row:
            port = row[0]
            if is_port_available(port):
                # Update last used
                self.db.execute(
                    "UPDATE port_assignments SET last_used = ? WHERE service_id = ?",
                    (datetime.now(), service_id)
                )
                self.db.commit()
                return port
        
        # No previous assignment or port not available
        # Try same-as-remote first
        if is_port_available(remote_port):
            port = remote_port
        else:
            port = find_available_port()
        
        # Save assignment
        self.db.execute(
            """
            INSERT OR REPLACE INTO port_assignments (service_id, local_port, last_used)
            VALUES (?, ?, ?)
            """,
            (service_id, port, datetime.now())
        )
        self.db.commit()
        
        return port
```

### User-Specified Port

Allow users to specify preferred ports:

```python
def assign_user_specified_port(preferred_port: int) -> int:
    """Assign user-specified port"""
    if not (1024 <= preferred_port <= 65535):
        raise ValueError("Port must be between 1024 and 65535")
    
    if not is_port_available(preferred_port):
        raise PortInUseError(f"Port {preferred_port} is already in use")
    
    return preferred_port
```

## Connection Multiplexing

Multiple local connections can share a single tunnel:

```python
class MultiplexedTunnel:
    def __init__(self, tunnel_socket):
        self.tunnel_socket = tunnel_socket
        self.streams: Dict[int, queue.Queue] = {}
        self.lock = threading.Lock()
        
        # Start demux thread
        self.demux_thread = threading.Thread(target=self._demux_loop, daemon=True)
        self.demux_thread.start()
    
    def send_message(self, message: dict):
        """Send a message through the tunnel"""
        # Serialize message
        data = json.dumps(message).encode()
        
        # Send length prefix + data
        length = len(data)
        self.tunnel_socket.sendall(length.to_bytes(4, 'big') + data)
    
    def receive_message(self, stream_id: int, timeout: float = None) -> Optional[dict]:
        """Receive a message for a specific stream"""
        with self.lock:
            if stream_id not in self.streams:
                self.streams[stream_id] = queue.Queue()
        
        try:
            message = self.streams[stream_id].get(timeout=timeout)
            return message
        except queue.Empty:
            return None
    
    def _demux_loop(self):
        """Demultiplex incoming messages to streams"""
        while True:
            try:
                # Read length prefix
                length_bytes = self._recv_exact(4)
                if not length_bytes:
                    break
                
                length = int.from_bytes(length_bytes, 'big')
                
                # Read message data
                data = self._recv_exact(length)
                if not data:
                    break
                
                # Deserialize message
                message = json.loads(data.decode())
                
                # Route to appropriate stream
                stream_id = message.get("stream_id")
                if stream_id is not None:
                    with self.lock:
                        if stream_id not in self.streams:
                            self.streams[stream_id] = queue.Queue()
                        self.streams[stream_id].put(message)
                
            except Exception as e:
                logger.error(f"Demux error: {e}")
                break
    
    def _recv_exact(self, n: int) -> bytes:
        """Receive exactly n bytes"""
        data = b''
        while len(data) < n:
            chunk = self.tunnel_socket.recv(n - len(data))
            if not chunk:
                return b''
            data += chunk
        return data
```

## Performance Optimization

### Buffer Management

Optimize buffer sizes for different service types:

```python
def get_optimal_buffer_size(service_type: str) -> int:
    """Get optimal buffer size for service type"""
    buffer_sizes = {
        "database": 8192,      # Small queries, frequent round-trips
        "file_transfer": 65536, # Large transfers, maximize throughput
        "api": 4096,           # Small requests/responses
        "streaming": 32768,    # Video/audio streaming
        "default": 8192
    }
    
    return buffer_sizes.get(service_type, buffer_sizes["default"])
```

### Connection Pooling

Reuse tunnel connections for multiple services:

```python
class TunnelPool:
    def __init__(self, max_tunnels: int = 5):
        self.max_tunnels = max_tunnels
        self.tunnels: List[Tunnel] = []
        self.lock = threading.Lock()
    
    def get_tunnel(self, agent_id: str) -> Tunnel:
        """Get or create a tunnel to an agent"""
        with self.lock:
            # Find existing tunnel to this agent
            for tunnel in self.tunnels:
                if tunnel.agent_id == agent_id and tunnel.is_healthy():
                    return tunnel
            
            # Create new tunnel if under limit
            if len(self.tunnels) < self.max_tunnels:
                tunnel = self._create_tunnel(agent_id)
                self.tunnels.append(tunnel)
                return tunnel
            
            # Reuse least recently used tunnel
            tunnel = min(self.tunnels, key=lambda t: t.last_used)
            tunnel.reconnect(agent_id)
            return tunnel
```

### Zero-Copy Forwarding

Use sendfile() for efficient data transfer when possible:

```python
import os

def forward_with_sendfile(src_fd: int, dst_fd: int, count: int):
    """Forward data using zero-copy sendfile"""
    if hasattr(os, 'sendfile'):
        try:
            os.sendfile(dst_fd, src_fd, None, count)
            return
        except (OSError, AttributeError):
            pass
    
    # Fallback to regular read/write
    while count > 0:
        chunk_size = min(count, 65536)
        data = os.read(src_fd, chunk_size)
        if not data:
            break
        os.write(dst_fd, data)
        count -= len(data)
```

## Error Handling

### Connection Failures

Handle various connection failure scenarios:

```python
def handle_connection_error(error: Exception, stream: StreamHandler):
    """Handle connection errors"""
    if isinstance(error, ConnectionRefusedError):
        logger.error(f"Service refused connection on {stream.remote_host}:{stream.remote_port}")
        notify_user(f"Service unavailable: Connection refused")
    
    elif isinstance(error, socket.timeout):
        logger.error(f"Connection timeout to {stream.remote_host}:{stream.remote_port}")
        notify_user(f"Service unavailable: Connection timeout")
    
    elif isinstance(error, OSError) and error.errno == 113:  # No route to host
        logger.error(f"No route to {stream.remote_host}:{stream.remote_port}")
        notify_user(f"Service unreachable: No route to host")
    
    else:
        logger.error(f"Connection error: {error}")
        notify_user(f"Connection error: {str(error)}")
    
    stream.close()
```

### Tunnel Failures

Handle tunnel disconnections gracefully:

```python
def handle_tunnel_failure(forwarder: PortForwarder):
    """Handle tunnel failure"""
    logger.warning(f"Tunnel failed for port {forwarder.local_port}")
    
    # Close all active streams
    for stream in list(forwarder.active_streams.values()):
        stream.close()
    
    # Attempt reconnection
    if forwarder.auto_reconnect:
        logger.info("Attempting tunnel reconnection...")
        try:
            new_tunnel = reconnect_tunnel(forwarder.tunnel)
            forwarder.tunnel = new_tunnel
            notify_user(f"Reconnected to {forwarder.service_name}")
        except Exception as e:
            logger.error(f"Reconnection failed: {e}")
            notify_user(f"Failed to reconnect to {forwarder.service_name}")
            forwarder.stop()
```

### Port Conflicts

Handle port already in use:

```python
def handle_port_conflict(service: dict, preferred_port: int):
    """Handle port conflict"""
    logger.warning(f"Port {preferred_port} is already in use")
    
    # Try alternative ports
    alternative_port = find_available_port()
    
    # Ask user if they want to use alternative
    response = prompt_user(
        f"Port {preferred_port} is in use. Use port {alternative_port} instead?",
        options=["Yes", "No", "Choose Different Port"]
    )
    
    if response == "Yes":
        return alternative_port
    elif response == "Choose Different Port":
        return prompt_user_for_port()
    else:
        raise PortConflictError("User declined alternative port")
```

## Monitoring and Statistics

### Connection Statistics

Track detailed statistics for each forwarded connection:

```python
class ConnectionStats:
    def __init__(self):
        self.established_at = datetime.now()
        self.bytes_sent = 0
        self.bytes_received = 0
        self.packets_sent = 0
        self.packets_received = 0
        self.errors = 0
        self.reconnections = 0
        
        self.latency_samples = []
        self.bandwidth_samples = []
    
    def record_transfer(self, bytes_sent: int, bytes_received: int):
        """Record data transfer"""
        self.bytes_sent += bytes_sent
        self.bytes_received += bytes_received
        self.packets_sent += 1 if bytes_sent > 0 else 0
        self.packets_received += 1 if bytes_received > 0 else 0
    
    def record_latency(self, latency_ms: float):
        """Record latency sample"""
        self.latency_samples.append(latency_ms)
        
        # Keep only last 100 samples
        if len(self.latency_samples) > 100:
            self.latency_samples.pop(0)
    
    def get_average_latency(self) -> float:
        """Get average latency"""
        if not self.latency_samples:
            return 0.0
        return sum(self.latency_samples) / len(self.latency_samples)
    
    def get_bandwidth(self) -> tuple:
        """Get current bandwidth (upload, download) in bytes/sec"""
        duration = (datetime.now() - self.established_at).total_seconds()
        if duration == 0:
            return (0, 0)
        
        upload_bps = self.bytes_sent / duration
        download_bps = self.bytes_received / duration
        
        return (upload_bps, download_bps)
```

### Real-Time Monitoring

Monitor connection health in real-time:

```python
def monitor_connection(connection: Connection):
    """Monitor connection health"""
    while connection.is_active():
        # Measure latency
        start = time.time()
        try:
            connection.tunnel.ping()
            latency = (time.time() - start) * 1000  # ms
            connection.stats.record_latency(latency)
        except Exception as e:
            logger.warning(f"Ping failed: {e}")
            connection.stats.errors += 1
        
        # Check for high latency
        avg_latency = connection.stats.get_average_latency()
        if avg_latency > 100:  # 100ms threshold
            notify_user(f"High latency detected: {avg_latency:.1f}ms")
        
        # Check for low bandwidth
        upload, download = connection.stats.get_bandwidth()
        if upload < 1024 and connection.stats.bytes_sent > 1024 * 1024:  # < 1KB/s
            notify_user("Low upload bandwidth detected")
        
        time.sleep(10)  # Check every 10 seconds
```

## Summary

Local port forwarding is the user-facing feature that makes the VM Gateway Client practical and easy to use. By binding local ports and transparently forwarding traffic through encrypted tunnels, the client enables users to work with remote services using their familiar local tools without any special configuration.

The implementation handles multiple simultaneous connections, provides intelligent port assignment strategies, optimizes performance through connection multiplexing and buffer management, and gracefully handles errors and failures. Comprehensive monitoring and statistics provide visibility into connection health and performance, enabling users to troubleshoot issues and optimize their workflows.
