# Automatic Secret Rotation

The VM Gateway platform supports automatic rotation of secrets to enhance security by regularly changing credentials without manual intervention.

## Rotation Policies

### Time-Based Rotation

Rotate secrets on a fixed schedule:

```yaml
rotation_policy:
  enabled: true
  interval_days: 90
  next_rotation: "2026-02-06T10:00:00Z"
  grace_period_hours: 24
```

**Common Intervals**:
- Critical production: 30 days
- Standard production: 90 days
- Development: 180 days
- Long-lived: 365 days

### Event-Based Rotation

Trigger rotation based on events:

- User termination
- Security incident
- Compliance requirement
- Manual request
- Failed access attempts threshold

## Rotation Process

### Standard Rotation Flow

1. **Pre-Rotation**:
   - Validate rotation policy
   - Check dependencies
   - Notify stakeholders
   - Create backup

2. **Generation**:
   - Generate new secret value
   - Validate format/strength
   - Encrypt new value
   - Store as new version

3. **Distribution**:
   - Notify consumers
   - Update configurations
   - Verify connectivity
   - Monitor for errors

4. **Grace Period**:
   - Keep old version active
   - Allow time for updates
   - Monitor usage of old version
   - <PERSON>ert on continued use

5. **Deactivation**:
   - Disable old version
   - Update access logs
   - Archive old version
   - Complete audit trail


### Database Credential Rotation

Special handling for database credentials:

```python
def rotate_database_credential(secret_id: str):
    """Rotate database credential"""
    # Get current credential
    current = get_secret(secret_id)
    
    # Connect to database
    db = connect_database(current)
    
    # Generate new password
    new_password = generate_secure_password()
    
    # Update password in database
    db.execute(f"ALTER USER {current['username']} PASSWORD '{new_password}'")
    
    # Store new version
    store_secret_version(secret_id, {
        "username": current["username"],
        "password": new_password
    })
    
    # Test new credential
    test_db = connect_database({"username": current["username"], "password": new_password})
    test_db.execute("SELECT 1")
    
    # Mark rotation complete
    mark_rotation_complete(secret_id)
```

### API Key Rotation

Rotate API keys with zero downtime:

```python
def rotate_api_key(secret_id: str):
    """Rotate API key with dual-key period"""
    # Generate new key
    new_key = generate_api_key()
    
    # Store new version (both keys valid)
    store_secret_version(secret_id, {
        "primary_key": new_key,
        "secondary_key": get_current_key(secret_id)
    })
    
    # Wait for grace period
    time.sleep(grace_period)
    
    # Remove old key
    store_secret_version(secret_id, {
        "primary_key": new_key
    })
```

## Rotation Scheduling

### Scheduler Configuration

```yaml
rotation_scheduler:
  enabled: true
  check_interval: 3600  # Check every hour
  batch_size: 10  # Rotate 10 secrets per batch
  max_concurrent: 3  # Max 3 concurrent rotations
  retry_policy:
    max_attempts: 3
    backoff_multiplier: 2
    max_backoff_seconds: 300
```

### Rotation Queue

Secrets pending rotation are queued:

```sql
CREATE TABLE rotation_queue (
    id UUID PRIMARY KEY,
    secret_id UUID REFERENCES secrets(id),
    scheduled_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20),
    attempts INTEGER DEFAULT 0,
    last_attempt_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT
);
```

## Notification System

### Rotation Notifications

Notify stakeholders of rotation events:

**Pre-Rotation Warning**:
```json
{
  "type": "rotation.scheduled",
  "secret_name": "database/prod/password",
  "scheduled_time": "2025-11-15T02:00:00Z",
  "grace_period_hours": 24,
  "action_required": "Update configurations after rotation"
}
```

**Rotation Complete**:
```json
{
  "type": "rotation.completed",
  "secret_name": "database/prod/password",
  "rotated_at": "2025-11-15T02:05:00Z",
  "new_version": 5,
  "old_version_expires": "2025-11-16T02:05:00Z"
}
```

**Rotation Failed**:
```json
{
  "type": "rotation.failed",
  "secret_name": "database/prod/password",
  "failed_at": "2025-11-15T02:03:00Z",
  "error": "Database connection timeout",
  "retry_scheduled": "2025-11-15T03:00:00Z"
}
```

## Consumer Updates

### Automatic Consumer Updates

Agents automatically fetch new secret versions:

```python
class SecretConsumer:
    def __init__(self, secret_name: str):
        self.secret_name = secret_name
        self.current_version = None
        self.watch_for_updates()
    
    def watch_for_updates(self):
        """Watch for secret updates via WebSocket"""
        ws = connect_websocket("/api/secrets/watch")
        ws.send({"subscribe": self.secret_name})
        
        while True:
            event = ws.receive()
            if event["type"] == "secret.rotated":
                self.reload_secret()
    
    def reload_secret(self):
        """Reload secret after rotation"""
        new_secret = fetch_secret(self.secret_name)
        self.current_version = new_secret["version"]
        self.apply_new_secret(new_secret["value"])
```

### Manual Consumer Updates

For services that can't auto-update:

1. Receive rotation notification
2. Download new secret version
3. Update configuration
4. Restart service
5. Confirm successful update

## Rotation Verification

### Post-Rotation Checks

Verify rotation success:

```python
def verify_rotation(secret_id: str):
    """Verify secret rotation was successful"""
    checks = [
        verify_new_version_exists(secret_id),
        verify_new_version_works(secret_id),
        verify_old_version_disabled(secret_id),
        verify_consumers_updated(secret_id),
        verify_audit_log_complete(secret_id)
    ]
    
    return all(checks)
```

### Rollback Procedures

If rotation fails, rollback to previous version:

```python
def rollback_rotation(secret_id: str):
    """Rollback failed rotation"""
    # Get previous version
    previous = get_secret_version(secret_id, version=-1)
    
    # Reactivate previous version
    activate_secret_version(secret_id, previous["version"])
    
    # Notify consumers
    notify_rotation_rollback(secret_id)
    
    # Log rollback
    audit_log("secret.rotation.rollback", secret_id)
```

## Best Practices

1. **Test rotation in non-production first**
2. **Use grace periods for critical secrets**
3. **Monitor rotation success rates**
4. **Automate consumer updates where possible**
5. **Have rollback procedures ready**
6. **Rotate after security incidents**
7. **Document rotation procedures**
8. **Alert on rotation failures**

## Compliance

Rotation policies support compliance requirements:

- **PCI DSS**: Rotate every 90 days
- **SOC 2**: Document rotation procedures
- **HIPAA**: Rotate after personnel changes
- **ISO 27001**: Regular key rotation

## Next Steps

- [Audit](07-audit.md) - Audit logging and compliance
- [Access Control](05-access-control.md) - Permission management
- [Lifecycle](04-lifecycle.md) - Complete lifecycle management
