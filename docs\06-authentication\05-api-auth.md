---
title: "API Authentication"
section: "Authentication & Authorization"
order: 5
tags: ["api", "authentication", "tokens", "oauth"]
last_updated: "2025-11-08"
---

# API Authentication

The platform provides multiple authentication methods for programmatic API access, enabling secure integration with external systems, automation scripts, and custom applications. Each method is designed for specific use cases with appropriate security controls.

## Overview

API authentication supports various scenarios from personal scripts to enterprise integrations:

**Authentication Methods:**
- Personal Access Tokens - User-generated tokens for scripts and tools
- Service Account Tokens - For automated systems and long-running services
- OAuth 2.0 Client Credentials - For service-to-service authentication
- API Keys - Simple key-based authentication
- Mutual TLS (mTLS) - Certificate-based authentication for high-security environments

**Key Features:**
- Scoped permissions (limit token capabilities)
- Token expiration and rotation
- Rate limiting per token
- Comprehensive audit logging
- Token revocation
- IP whitelisting (optional)

## Personal Access Tokens

### Overview

Personal Access Tokens (PATs) are user-generated tokens that provide API access with the same permissions as the user. They're ideal for personal scripts, CLI tools, and development.

**Characteristics:**
- Tied to user account
- Inherit user permissions
- Can be scoped to specific operations
- Optional expiration dates
- Revocable at any time
- Multiple tokens per user

### Token Generation

```python
import secrets
import hashlib
from datetime import datetime, timedelta

class PersonalAccessTokenManager:
    """Manage personal access tokens"""
    
    async def create_token(
        self,
        user: User,
        name: str,
        scopes: List[str],
        expires_in_days: Optional[int] = None
    ) -> dict:
        """Generate a new personal access token"""
        
        # Generate token
        token = f"pat_{secrets.token_urlsafe(32)}"
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        
        # Calculate expiration
        expires_at = None
        if expires_in_days:
            expires_at = datetime.utcnow() + timedelta(days=expires_in_days)
        
        # Store token
        token_id = await db.fetchval(
            """
            INSERT INTO api_tokens (
                user_id, token_hash, name, scopes,
                expires_at, created_at, last_used_at
            )
            VALUES ($1, $2, $3, $4, $5, NOW(), NULL)
            RETURNING id
            """,
            user.id, token_hash, name, scopes, expires_at
        )
        
        # Log token creation
        await log_audit_event(
            actor=user.username,
            action="api_token_created",
            details={"token_id": token_id, "name": name, "scopes": scopes}
        )
        
        return {
            "token": token,  # Only shown once
            "token_id": token_id,
            "name": name,
            "scopes": scopes,
            "expires_at": expires_at
        }
```



    async def validate_token(self, token: str) -> Optional[User]:
        """Validate personal access token"""
        
        # Extract token hash
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        
        # Get token from database
        token_record = await db.fetchrow(
            """
            SELECT t.*, u.* FROM api_tokens t
            JOIN users u ON t.user_id = u.id
            WHERE t.token_hash = $1
            AND (t.expires_at IS NULL OR t.expires_at > NOW())
            AND u.is_active = true
            """,
            token_hash
        )
        
        if not token_record:
            return None
        
        # Update last used timestamp
        await db.execute(
            """
            UPDATE api_tokens
            SET last_used_at = NOW()
            WHERE token_hash = $1
            """,
            token_hash
        )
        
        # Return user with token scopes
        user = User(**dict(token_record))
        user.token_scopes = token_record['scopes']
        
        return user
    
    async def revoke_token(self, user: User, token_id: str) -> bool:
        """Revoke a personal access token"""
        
        result = await db.execute(
            """
            DELETE FROM api_tokens
            WHERE id = $1 AND user_id = $2
            """,
            token_id, user.id
        )
        
        if result:
            await log_audit_event(
                actor=user.username,
                action="api_token_revoked",
                details={"token_id": token_id}
            )
            return True
        
        return False
    
    async def list_tokens(self, user: User) -> List[dict]:
        """List user's personal access tokens"""
        
        tokens = await db.fetch(
            """
            SELECT id, name, scopes, created_at, last_used_at, expires_at
            FROM api_tokens
            WHERE user_id = $1
            ORDER BY created_at DESC
            """,
            user.id
        )
        
        return [dict(token) for token in tokens]
```

### Token Scopes

Scopes limit what operations a token can perform:

```python
class TokenScope(str, Enum):
    # Read operations
    READ_VMS = "vms:read"
    READ_SERVICES = "services:read"
    READ_METRICS = "metrics:read"
    READ_LOGS = "logs:read"
    
    # Write operations
    WRITE_VMS = "vms:write"
    WRITE_SERVICES = "services:write"
    WRITE_CONFIG = "config:write"
    
    # Connection operations
    CONNECT_SERVICES = "services:connect"
    
    # Admin operations
    MANAGE_USERS = "users:manage"
    MANAGE_ROLES = "roles:manage"
    
    # Full access
    ADMIN = "admin"

def check_token_scope(required_scope: TokenScope):
    """Decorator to check if token has required scope"""
    
    def decorator(func):
        async def wrapper(*args, **kwargs):
            user = kwargs.get('current_user')
            if not user or not hasattr(user, 'token_scopes'):
                raise HTTPException(status_code=401, detail="Not authenticated")
            
            # Admin scope grants all permissions
            if TokenScope.ADMIN in user.token_scopes:
                return await func(*args, **kwargs)
            
            # Check specific scope
            if required_scope not in user.token_scopes:
                raise HTTPException(
                    status_code=403,
                    detail=f"Token missing required scope: {required_scope}"
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator

# Usage example
@router.get("/api/vms")
@check_token_scope(TokenScope.READ_VMS)
async def list_vms(current_user: User = Depends(get_current_user)):
    """List VMs - requires vms:read scope"""
    return await get_vms()
```

## Service Account Tokens

### Overview

Service accounts are non-human accounts designed for automated systems, CI/CD pipelines, and long-running services.

**Characteristics:**
- Not tied to individual users
- Separate from user accounts
- Can have different permission model
- Auditable and traceable
- Can be rotated without user intervention

### Implementation

```python
class ServiceAccountManager:
    """Manage service accounts and their tokens"""
    
    async def create_service_account(
        self,
        name: str,
        description: str,
        roles: List[str],
        created_by: User
    ) -> dict:
        """Create a new service account"""
        
        # Create service account
        sa_id = await db.fetchval(
            """
            INSERT INTO service_accounts (
                name, description, created_by, created_at, is_active
            )
            VALUES ($1, $2, $3, NOW(), true)
            RETURNING id
            """,
            name, description, created_by.id
        )
        
        # Assign roles
        for role in roles:
            await assign_role_to_service_account(sa_id, role)
        
        # Generate initial token
        token = await self.generate_token(sa_id)
        
        # Log creation
        await log_audit_event(
            actor=created_by.username,
            action="service_account_created",
            details={"sa_id": sa_id, "name": name, "roles": roles}
        )
        
        return {
            "id": sa_id,
            "name": name,
            "token": token  # Only shown once
        }
    
    async def generate_token(
        self,
        sa_id: str,
        expires_in_days: Optional[int] = None
    ) -> str:
        """Generate token for service account"""
        
        # Generate token
        token = f"sa_{secrets.token_urlsafe(32)}"
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        
        # Calculate expiration
        expires_at = None
        if expires_in_days:
            expires_at = datetime.utcnow() + timedelta(days=expires_in_days)
        
        # Store token
        await db.execute(
            """
            INSERT INTO service_account_tokens (
                service_account_id, token_hash, expires_at, created_at
            )
            VALUES ($1, $2, $3, NOW())
            """,
            sa_id, token_hash, expires_at
        )
        
        return token
    
    async def rotate_token(self, sa_id: str, old_token: str) -> str:
        """Rotate service account token"""
        
        # Validate old token
        old_token_hash = hashlib.sha256(old_token.encode()).hexdigest()
        valid = await db.fetchval(
            """
            SELECT EXISTS(
                SELECT 1 FROM service_account_tokens
                WHERE service_account_id = $1 AND token_hash = $2
            )
            """,
            sa_id, old_token_hash
        )
        
        if not valid:
            raise ValueError("Invalid token")
        
        # Generate new token
        new_token = await self.generate_token(sa_id)
        
        # Revoke old token after grace period
        await db.execute(
            """
            UPDATE service_account_tokens
            SET revoked_at = NOW() + INTERVAL '1 hour'
            WHERE service_account_id = $1 AND token_hash = $2
            """,
            sa_id, old_token_hash
        )
        
        # Log rotation
        await log_audit_event(
            actor=f"service_account:{sa_id}",
            action="token_rotated"
        )
        
        return new_token
```

## OAuth 2.0 Client Credentials

### Overview

OAuth 2.0 Client Credentials flow is designed for service-to-service authentication where no user is involved.

**Flow:**
1. Client sends client_id and client_secret
2. Authorization server validates credentials
3. Server returns access token
4. Client uses token for API requests

### Implementation

```python
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

class OAuth2ClientCredentials:
    """Manage OAuth 2.0 client credentials flow"""
    
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
        self.algorithm = "HS256"
        self.access_token_expire = 3600  # 1 hour
    
    async def create_client(
        self,
        name: str,
        scopes: List[str],
        created_by: User
    ) -> dict:
        """Create OAuth 2.0 client"""
        
        # Generate client credentials
        client_id = f"client_{secrets.token_urlsafe(16)}"
        client_secret = secrets.token_urlsafe(32)
        client_secret_hash = hashlib.sha256(client_secret.encode()).hexdigest()
        
        # Store client
        await db.execute(
            """
            INSERT INTO oauth_clients (
                client_id, client_secret_hash, name, scopes, created_by, created_at
            )
            VALUES ($1, $2, $3, $4, $5, NOW())
            """,
            client_id, client_secret_hash, name, scopes, created_by.id
        )
        
        return {
            "client_id": client_id,
            "client_secret": client_secret,  # Only shown once
            "scopes": scopes
        }
    
    async def token_endpoint(
        self,
        client_id: str,
        client_secret: str,
        scope: Optional[str] = None
    ) -> dict:
        """OAuth 2.0 token endpoint"""
        
        # Validate client credentials
        client_secret_hash = hashlib.sha256(client_secret.encode()).hexdigest()
        client = await db.fetchrow(
            """
            SELECT * FROM oauth_clients
            WHERE client_id = $1 AND client_secret_hash = $2
            """,
            client_id, client_secret_hash
        )
        
        if not client:
            raise HTTPException(status_code=401, detail="Invalid client credentials")
        
        # Validate requested scopes
        requested_scopes = scope.split() if scope else []
        allowed_scopes = client['scopes']
        
        if not all(s in allowed_scopes for s in requested_scopes):
            raise HTTPException(status_code=400, detail="Invalid scope")
        
        # Generate access token
        token_data = {
            "sub": client_id,
            "scopes": requested_scopes or allowed_scopes,
            "exp": datetime.utcnow() + timedelta(seconds=self.access_token_expire)
        }
        
        access_token = jwt.encode(token_data, self.secret_key, algorithm=self.algorithm)
        
        # Log token issuance
        await log_audit_event(
            actor=f"oauth_client:{client_id}",
            action="access_token_issued",
            details={"scopes": token_data["scopes"]}
        )
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": self.access_token_expire,
            "scope": " ".join(token_data["scopes"])
        }
    
    async def validate_token(self, token: str) -> dict:
        """Validate OAuth 2.0 access token"""
        
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except JWTError:
            raise HTTPException(status_code=401, detail="Invalid token")
```

## API Keys

### Overview

API keys are simple, long-lived tokens for basic authentication. They're easy to use but less secure than other methods.

**Best Practices:**
- Use prefixes for easy identification (e.g., "pk_live_...")
- Store hashed, never plain text
- Implement rate limiting per key
- Support key rotation
- Allow IP whitelisting

### Implementation

```python
class APIKeyManager:
    """Manage API keys"""
    
    async def create_key(
        self,
        user: User,
        name: str,
        ip_whitelist: Optional[List[str]] = None
    ) -> str:
        """Create API key"""
        
        # Generate key with prefix
        key = f"pk_live_{secrets.token_urlsafe(32)}"
        key_hash = hashlib.sha256(key.encode()).hexdigest()
        
        # Store key
        await db.execute(
            """
            INSERT INTO api_keys (
                user_id, key_hash, name, ip_whitelist, created_at
            )
            VALUES ($1, $2, $3, $4, NOW())
            """,
            user.id, key_hash, name, ip_whitelist
        )
        
        return key
    
    async def validate_key(
        self,
        key: str,
        ip_address: str
    ) -> Optional[User]:
        """Validate API key"""
        
        key_hash = hashlib.sha256(key.encode()).hexdigest()
        
        record = await db.fetchrow(
            """
            SELECT k.*, u.* FROM api_keys k
            JOIN users u ON k.user_id = u.id
            WHERE k.key_hash = $1 AND u.is_active = true
            """,
            key_hash
        )
        
        if not record:
            return None
        
        # Check IP whitelist
        if record['ip_whitelist']:
            if not self._ip_in_whitelist(ip_address, record['ip_whitelist']):
                await log_security_event(
                    event_type="api_key_ip_denied",
                    key_hash=key_hash[:8],
                    ip_address=ip_address
                )
                return None
        
        # Update last used
        await db.execute(
            """
            UPDATE api_keys
            SET last_used_at = NOW()
            WHERE key_hash = $1
            """,
            key_hash
        )
        
        return User(**dict(record))
    
    def _ip_in_whitelist(self, ip: str, whitelist: List[str]) -> bool:
        """Check if IP is in whitelist (supports CIDR)"""
        import ipaddress
        
        ip_obj = ipaddress.ip_address(ip)
        
        for allowed in whitelist:
            if '/' in allowed:
                # CIDR notation
                if ip_obj in ipaddress.ip_network(allowed):
                    return True
            else:
                # Exact match
                if ip == allowed:
                    return True
        
        return False
```

## Mutual TLS (mTLS)

### Overview

Mutual TLS provides certificate-based authentication where both client and server authenticate each other using X.509 certificates.

**Use Cases:**
- High-security environments
- Service-to-service authentication
- Zero-trust architectures
- Compliance requirements

### Configuration

```yaml
mtls:
  enabled: true
  
  # Certificate authority
  ca_cert: "/path/to/ca-cert.pem"
  
  # Server certificate
  server_cert: "/path/to/server-cert.pem"
  server_key: "/path/to/server-key.pem"
  
  # Client verification
  verify_client: true
  verify_depth: 3
  
  # Certificate revocation
  crl_file: "/path/to/crl.pem"
  ocsp_enabled: true
```

### Implementation

```python
class MTLSAuthManager:
    """Manage mTLS authentication"""
    
    async def validate_client_certificate(
        self,
        cert_pem: str
    ) -> Optional[User]:
        """Validate client certificate and extract identity"""
        
        from cryptography import x509
        from cryptography.hazmat.backends import default_backend
        
        # Parse certificate
        cert = x509.load_pem_x509_certificate(
            cert_pem.encode(),
            default_backend()
        )
        
        # Extract subject
        subject = cert.subject
        common_name = subject.get_attributes_for_oid(x509.NameOID.COMMON_NAME)[0].value
        email = subject.get_attributes_for_oid(x509.NameOID.EMAIL_ADDRESS)[0].value if subject.get_attributes_for_oid(x509.NameOID.EMAIL_ADDRESS) else None
        
        # Verify certificate is not revoked
        if await self._is_certificate_revoked(cert):
            await log_security_event(
                event_type="mtls_cert_revoked",
                common_name=common_name
            )
            return None
        
        # Find user by certificate
        user = await db.fetchrow(
            """
            SELECT u.* FROM users u
            JOIN user_certificates c ON u.id = c.user_id
            WHERE c.serial_number = $1 AND c.revoked = false
            """,
            str(cert.serial_number)
        )
        
        if not user:
            return None
        
        return User(**dict(user))
    
    async def _is_certificate_revoked(self, cert) -> bool:
        """Check if certificate is revoked"""
        # Check against CRL or OCSP
        # Implementation depends on your PKI setup
        return False
```

## Rate Limiting

### Per-Token Rate Limiting

```python
from slowapi import Limiter
from slowapi.util import get_remote_address

class TokenRateLimiter:
    """Rate limit API requests per token"""
    
    def __init__(self, redis_client):
        self.redis = redis_client
    
    async def check_rate_limit(
        self,
        token_id: str,
        limit: int = 1000,
        window: int = 3600
    ) -> bool:
        """Check if token has exceeded rate limit"""
        
        key = f"rate_limit:token:{token_id}"
        
        # Increment counter
        count = await self.redis.incr(key)
        
        # Set expiration on first request
        if count == 1:
            await self.redis.expire(key, window)
        
        # Check limit
        if count > limit:
            await log_security_event(
                event_type="rate_limit_exceeded",
                token_id=token_id,
                count=count,
                limit=limit
            )
            return False
        
        return True
```

## Related Documentation

- [Authentication Overview](./01-overview.md) - System architecture
- [Session Management](./06-session-management.md) - Session handling
- [RBAC System](./07-rbac.md) - Permission model
