# External Vault Integration

The VM Gateway platform can integrate with external secret management systems, allowing organizations to leverage existing vault infrastructure while benefiting from the platform's access control and distribution capabilities.

## Supported Vault Systems

### HashiCorp Vault

**Features**:
- Dynamic secrets generation
- Secret leasing and renewal
- Multiple authentication methods
- Secret engines (KV, database, PKI, etc.)
- High availability

**Configuration**:
```yaml
# config/vault.yaml
vault:
  provider: hashicorp
  address: https://vault.example.com:8200
  namespace: vm-gateway
  authentication:
    method: approle
    role_id: ${VAULT_ROLE_ID}
    secret_id: ${VAULT_SECRET_ID}
  kv_mount: secret
  kv_version: 2
  options:
    tls_verify: true
    timeout: 30
    max_retries: 3
```

**Usage Example**:
```python
from vault_client import VaultClient

vault = VaultClient(config)

# Store secret
vault.write_secret(
    path="vm-gateway/database/prod",
    data={"username": "admin", "password": "secret123"}
)

# Retrieve secret
secret = vault.read_secret("vm-gateway/database/prod")
print(secret["username"])  # admin
```

### AWS Secrets Manager

**Features**:
- Automatic rotation
- Fine-grained IAM permissions
- Encryption with AWS KMS
- Cross-region replication
- Integration with AWS services

**Configuration**:
```yaml
# config/vault.yaml
vault:
  provider: aws_secrets_manager
  region: us-east-1
  authentication:
    method: iam_role
    role_arn: arn:aws:iam::123456789:role/vm-gateway
  options:
    kms_key_id: arn:aws:kms:us-east-1:123456789:key/abc-123
    tags:
      Environment: production
      Application: vm-gateway
```


### Azure Key Vault

**Features**:
- Integration with Azure AD
- Hardware security module (HSM) support
- Managed identities
- Soft delete and purge protection
- Private endpoint support

**Configuration**:
```yaml
# config/vault.yaml
vault:
  provider: azure_key_vault
  vault_url: https://vm-gateway.vault.azure.net/
  authentication:
    method: managed_identity
    client_id: ${AZURE_CLIENT_ID}
  options:
    api_version: "7.4"
    timeout: 30
```

### Google Cloud Secret Manager

**Features**:
- Automatic replication
- IAM integration
- Audit logging
- Secret versioning
- Encryption with Cloud KMS

**Configuration**:
```yaml
# config/vault.yaml
vault:
  provider: gcp_secret_manager
  project_id: vm-gateway-prod
  authentication:
    method: service_account
    credentials_file: /etc/vm-gateway/gcp-credentials.json
  options:
    location: us-central1
```

## Integration Architecture

### Hybrid Storage Model

Use external vault for sensitive secrets, local storage for others:

```
┌─────────────────────────────────────────────┐
│         VM Gateway Controller               │
│  ┌───────────────────────────────────────┐  │
│  │    Secrets Management Layer           │  │
│  │  - Unified API                        │  │
│  │  - Access control                     │  │
│  │  - Audit logging                      │  │
│  └──────────┬────────────────────────────┘  │
│             │                                │
│    ┌────────┴────────┐                      │
│    │                 │                      │
│  ┌─▼──────────┐  ┌──▼──────────┐           │
│  │  Local     │  │  External   │           │
│  │  Storage   │  │  Vault      │           │
│  │  (PG)      │  │  Proxy      │           │
│  └────────────┘  └──────┬──────┘           │
└────────────────────────┼───────────────────┘
                         │
              ┌──────────▼──────────┐
              │  External Vault     │
              │  (HashiCorp/AWS/    │
              │   Azure/GCP)        │
              └─────────────────────┘
```

### Storage Routing

Route secrets to appropriate backend:

```python
class SecretStorageRouter:
    def __init__(self, config):
        self.local_storage = PostgreSQLStorage(config.local)
        self.vault_storage = VaultStorage(config.vault)
        self.routing_rules = config.routing_rules
    
    def store_secret(self, secret):
        """Route secret to appropriate storage"""
        backend = self.determine_backend(secret)
        
        if backend == "vault":
            return self.vault_storage.store(secret)
        else:
            return self.local_storage.store(secret)
    
    def determine_backend(self, secret):
        """Determine storage backend based on rules"""
        for rule in self.routing_rules:
            if self.matches_rule(secret, rule):
                return rule.backend
        
        return "local"  # Default to local storage
```

**Routing Configuration**:
```yaml
routing_rules:
  - name: production_database_credentials
    condition:
      tags: ["production", "database"]
    backend: vault
  
  - name: api_keys
    condition:
      type: api_key
      tags: ["production"]
    backend: vault
  
  - name: development_secrets
    condition:
      tags: ["development"]
    backend: local
  
  - name: default
    condition: {}
    backend: local
```

## Vault-Specific Features

### HashiCorp Vault Dynamic Secrets

Generate secrets on-demand:

```python
def get_dynamic_database_credential(database_name: str):
    """Get dynamic database credential from Vault"""
    vault = VaultClient(config)
    
    # Request dynamic credential
    response = vault.read(f"database/creds/{database_name}")
    
    return {
        "username": response["username"],
        "password": response["password"],
        "lease_id": response["lease_id"],
        "lease_duration": response["lease_duration"],
        "renewable": response["renewable"]
    }
```

### AWS Secrets Manager Rotation

Use AWS automatic rotation:

```python
# Lambda function for rotation
def lambda_handler(event, context):
    """AWS Secrets Manager rotation handler"""
    secret_arn = event["SecretId"]
    token = event["ClientRequestToken"]
    step = event["Step"]
    
    if step == "createSecret":
        create_new_secret_version(secret_arn, token)
    elif step == "setSecret":
        set_secret_in_service(secret_arn, token)
    elif step == "testSecret":
        test_new_secret(secret_arn, token)
    elif step == "finishSecret":
        finish_rotation(secret_arn, token)
```

### Azure Key Vault Managed Identity

Use managed identity for authentication:

```python
from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient

credential = DefaultAzureCredential()
client = SecretClient(
    vault_url="https://vm-gateway.vault.azure.net/",
    credential=credential
)

# Store secret
client.set_secret("database-password", "secure_password_123")

# Retrieve secret
secret = client.get_secret("database-password")
print(secret.value)
```

## Migration Strategies

### Migrate from Local to Vault

Migrate existing secrets to external vault:

```python
def migrate_secrets_to_vault(tag_filter: str = None):
    """Migrate secrets from local storage to vault"""
    # Get secrets to migrate
    secrets = get_secrets(tags=tag_filter)
    
    for secret in secrets:
        try:
            # Read from local storage
            value = local_storage.get_secret(secret.id)
            
            # Write to vault
            vault_storage.store_secret(
                name=secret.name,
                value=value,
                metadata=secret.metadata
            )
            
            # Update routing
            update_secret_backend(secret.id, "vault")
            
            # Audit log
            audit_log("secret.migrated_to_vault", secret.id)
            
        except Exception as e:
            logger.error(f"Failed to migrate {secret.id}: {e}")
```

### Migrate from Vault to Local

Migrate secrets back to local storage:

```python
def migrate_secrets_to_local(tag_filter: str = None):
    """Migrate secrets from vault to local storage"""
    secrets = get_secrets(tags=tag_filter, backend="vault")
    
    for secret in secrets:
        try:
            # Read from vault
            value = vault_storage.get_secret(secret.name)
            
            # Write to local storage
            local_storage.store_secret(
                id=secret.id,
                name=secret.name,
                value=value,
                metadata=secret.metadata
            )
            
            # Update routing
            update_secret_backend(secret.id, "local")
            
            # Audit log
            audit_log("secret.migrated_to_local", secret.id)
            
        except Exception as e:
            logger.error(f"Failed to migrate {secret.id}: {e}")
```

## Synchronization

### Bidirectional Sync

Keep secrets synchronized between local and vault:

```python
class SecretSynchronizer:
    def __init__(self, local_storage, vault_storage):
        self.local = local_storage
        self.vault = vault_storage
    
    def sync_to_vault(self, secret_id: str):
        """Sync secret from local to vault"""
        secret = self.local.get_secret(secret_id)
        self.vault.store_secret(secret.name, secret.value)
        audit_log("secret.synced_to_vault", secret_id)
    
    def sync_from_vault(self, secret_name: str):
        """Sync secret from vault to local"""
        value = self.vault.get_secret(secret_name)
        secret_id = self.local.find_by_name(secret_name)
        self.local.update_secret(secret_id, value)
        audit_log("secret.synced_from_vault", secret_id)
    
    def sync_all(self):
        """Sync all secrets bidirectionally"""
        # Sync local to vault
        for secret in self.local.list_secrets():
            if secret.sync_to_vault:
                self.sync_to_vault(secret.id)
        
        # Sync vault to local
        for secret_name in self.vault.list_secrets():
            if self.should_sync_to_local(secret_name):
                self.sync_from_vault(secret_name)
```

## Failover and High Availability

### Vault Failover

Handle vault unavailability:

```python
class VaultStorageWithFailover:
    def __init__(self, primary_vault, secondary_vault, local_cache):
        self.primary = primary_vault
        self.secondary = secondary_vault
        self.cache = local_cache
    
    def get_secret(self, name: str):
        """Get secret with failover"""
        # Try primary vault
        try:
            value = self.primary.get_secret(name)
            self.cache.set(name, value, ttl=300)
            return value
        except VaultUnavailable:
            logger.warning("Primary vault unavailable, trying secondary")
        
        # Try secondary vault
        try:
            value = self.secondary.get_secret(name)
            self.cache.set(name, value, ttl=300)
            return value
        except VaultUnavailable:
            logger.warning("Secondary vault unavailable, using cache")
        
        # Fall back to cache
        value = self.cache.get(name)
        if value:
            return value
        
        raise SecretUnavailable(f"Secret {name} unavailable")
```

## Best Practices

1. **Use vault for sensitive secrets**: Production credentials, API keys
2. **Local storage for non-sensitive**: Development secrets, configuration
3. **Implement failover**: Handle vault unavailability gracefully
4. **Cache vault secrets**: Reduce vault API calls
5. **Monitor vault health**: Alert on vault connectivity issues
6. **Rotate vault tokens**: Regularly rotate authentication tokens
7. **Test disaster recovery**: Regularly test vault failover
8. **Document integration**: Clear documentation of vault setup

## Performance Considerations

### Caching

Cache vault secrets to reduce API calls:

```python
from functools import lru_cache
import time

class CachedVaultStorage:
    def __init__(self, vault_storage, cache_ttl=300):
        self.vault = vault_storage
        self.cache = {}
        self.cache_ttl = cache_ttl
    
    def get_secret(self, name: str):
        """Get secret with caching"""
        # Check cache
        if name in self.cache:
            value, timestamp = self.cache[name]
            if time.time() - timestamp < self.cache_ttl:
                return value
        
        # Fetch from vault
        value = self.vault.get_secret(name)
        
        # Update cache
        self.cache[name] = (value, time.time())
        
        return value
```

### Batch Operations

Retrieve multiple secrets in one call:

```python
def get_secrets_batch(secret_names: list[str]):
    """Retrieve multiple secrets from vault"""
    # HashiCorp Vault doesn't support batch reads natively
    # Use concurrent requests
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = {
            executor.submit(vault.get_secret, name): name
            for name in secret_names
        }
        
        results = {}
        for future in as_completed(futures):
            name = futures[future]
            try:
                results[name] = future.result()
            except Exception as e:
                logger.error(f"Failed to fetch {name}: {e}")
        
        return results
```

## Next Steps

- [Storage](02-storage.md) - Local storage implementation
- [Lifecycle](04-lifecycle.md) - Secret lifecycle management
- [Access Control](05-access-control.md) - Permission system
- [Overview](01-overview.md) - Secrets management overview
