---
title: "Setting Up SSO Authentication"
section: "Tutorials"
order: 2
tags: ["tutorial", "sso", "authentication", "saml", "okta"]
last_updated: "2025-11-09"
---

# Tutorial: Setting Up SSO Authentication

## Overview

This comprehensive tutorial walks you through setting up Single Sign-On (SSO) authentication for VM Gateway using SAML 2.0. We'll use <PERSON><PERSON> as an example Identity Provider (IdP), but the process is similar for other providers like Azure AD, Google Workspace, or OneLogin.

**Time Required**: 30-45 minutes

**Prerequisites**:
- VM Gateway Professional or Enterprise Edition
- Administrator access to VM Gateway
- Administrator access to your IdP (Okta, Azure AD, etc.)
- Basic understanding of SAML concepts

**What You'll Learn**:
- How to configure SAML 2.0 in your IdP
- How to configure VM Gateway for SSO
- How to map user attributes
- How to test SSO authentication
- How to troubleshoot common issues

## Table of Contents

1. [Understanding SAML 2.0](#understanding-saml-20)
2. [Configuring Okta](#configuring-okta)
3. [Configuring VM Gateway](#configuring-vm-gateway)
4. [Testing SSO](#testing-sso)
5. [Attribute Mapping](#attribute-mapping)
6. [Troubleshooting](#troubleshooting)
7. [Advanced Configuration](#advanced-configuration)

---

## Understanding SAML 2.0

### What is SAML?

SAML (Security Assertion Markup Language) is an XML-based standard for exchanging authentication and authorization data between an Identity Provider (IdP) and a Service Provider (SP).

**Key Components**:

1. **Identity Provider (IdP)**: Authenticates users (e.g., Okta, Azure AD)
2. **Service Provider (SP)**: The application users want to access (VM Gateway)
3. **SAML Assertion**: XML document containing user identity and attributes
4. **Metadata**: XML document describing the IdP or SP configuration

### SAML Authentication Flow

```
1. User accesses VM Gateway
   ↓
2. VM Gateway redirects to IdP (Okta) with SAML Request
   ↓
3. User authenticates with IdP
   ↓
4. IdP generates SAML Assertion
   ↓
5. IdP redirects user back to VM Gateway with SAML Response
   ↓
6. VM Gateway validates SAML Response
   ↓
7. VM Gateway creates session for user
   ↓
8. User is logged in
```

### Why Use SSO?

**Benefits**:
- **Single set of credentials**: Users remember one password
- **Centralized user management**: Manage users in one place
- **Enhanced security**: Enforce MFA, password policies centrally
- **Improved user experience**: No separate login for each app
- **Compliance**: Meet audit requirements for access control
- **Automatic provisioning**: Users created automatically on first login

---

## Configuring Okta

### Step 1: Create SAML Application in Okta

1. **Log in to Okta Admin Console**
   - Navigate to `https://your-domain.okta.com/admin`
   - Use your administrator credentials

2. **Create New Application**
   - Click **Applications** → **Applications** in the left sidebar
   - Click **Create App Integration**
   - Select **SAML 2.0**
   - Click **Next**

3. **General Settings**
   - **App name**: `VM Gateway`
   - **App logo**: Upload VM Gateway logo (optional)
   - **App visibility**: Check "Do not display application icon to users"
   - Click **Next**

### Step 2: Configure SAML Settings

**Configure SAML** page:

1. **Single sign on URL** (Assertion Consumer Service URL):
   ```
   https://your-vmgateway-domain.com/auth/saml/acs
   ```
   - Check "Use this for Recipient URL and Destination URL"

2. **Audience URI (SP Entity ID)**:
   ```
   https://your-vmgateway-domain.com
   ```

3. **Default RelayState**: Leave blank

4. **Name ID format**: `EmailAddress`

5. **Application username**: `Email`

6. **Update application username on**: `Create and update`

### Step 3: Configure Attribute Statements

Add these attribute mappings:

| Name | Name format | Value |
|------|-------------|-------|
| `email` | Unspecified | `user.email` |
| `firstName` | Unspecified | `user.firstName` |
| `lastName` | Unspecified | `user.lastName` |
| `groups` | Unspecified | `appuser.groups` |

**Screenshot of configuration**:
```
Attribute Statements:
┌──────────────┬──────────────┬─────────────────┐
│ Name         │ Name format  │ Value           │
├──────────────┼──────────────┼─────────────────┤
│ email        │ Unspecified  │ user.email      │
│ firstName    │ Unspecified  │ user.firstName  │
│ lastName     │ Unspecified  │ user.lastName   │
│ groups       │ Unspecified  │ appuser.groups  │
└──────────────┴──────────────┴─────────────────┘
```

### Step 4: Configure Group Attribute Statements (Optional)

If you want to map Okta groups to VM Gateway roles:

| Name | Name format | Filter | Value |
|------|-------------|--------|-------|
| `groups` | Unspecified | Matches regex | `.*` |

This sends all groups the user belongs to.

### Step 5: Finish Configuration

1. Click **Next**
2. Select **I'm an Okta customer adding an internal app**
3. Click **Finish**

### Step 6: Download Metadata

1. On the application page, go to **Sign On** tab
2. Scroll to **SAML Signing Certificates**
3. Click **Actions** → **View IdP metadata**
4. Save the XML file as `okta-metadata.xml`

**Alternative**: Copy the metadata URL for dynamic configuration

### Step 7: Assign Users

1. Go to **Assignments** tab
2. Click **Assign** → **Assign to People** or **Assign to Groups**
3. Select users/groups who should have access
4. Click **Assign** and **Done**

---

## Configuring VM Gateway

### Step 1: Upload Metadata to VM Gateway

**Option A: Upload Metadata File**

1. Copy `okta-metadata.xml` to VM Gateway server:
   ```bash
   scp okta-metadata.xml user@vmgateway-server:/opt/vm-gateway/config/
   ```

2. Set proper permissions:
   ```bash
   sudo chown vmgateway:vmgateway /opt/vm-gateway/config/okta-metadata.xml
   sudo chmod 600 /opt/vm-gateway/config/okta-metadata.xml
   ```

**Option B: Use Metadata URL**

Use the metadata URL from Okta (more dynamic, updates automatically):
```
https://your-domain.okta.com/app/abc123/sso/saml/metadata
```

### Step 2: Configure VM Gateway

Edit `/opt/vm-gateway/.env`:

```bash
# SSO Configuration
SSO_ENABLED=true
SSO_TYPE=saml

# SAML Configuration
SAML_METADATA_PATH=/opt/vm-gateway/config/okta-metadata.xml
# OR use metadata URL:
# SAML_METADATA_URL=https://your-domain.okta.com/app/abc123/sso/saml/metadata

# Entity ID (must match what you configured in Okta)
SAML_ENTITY_ID=https://your-vmgateway-domain.com

# Assertion Consumer Service URL
SAML_ACS_URL=https://your-vmgateway-domain.com/auth/saml/acs

# Attribute Mapping
SAML_ATTR_EMAIL=email
SAML_ATTR_FIRST_NAME=firstName
SAML_ATTR_LAST_NAME=lastName
SAML_ATTR_GROUPS=groups

# SSO Settings
SSO_AUTO_CREATE_USERS=true  # Create users on first login
SSO_UPDATE_USER_ATTRIBUTES=true  # Update user info on each login
SSO_DEFAULT_ROLE=viewer  # Default role for new users

# Group Mapping (optional)
SSO_GROUP_MAPPING_ENABLED=true
SSO_GROUP_MAPPING='{"Okta_Admins":"admin","Okta_Developers":"developer","Okta_Viewers":"viewer"}'
```

### Step 3: Configure Detailed SAML Settings (Optional)

For advanced configuration, edit `config/saml-settings.yaml`:

```yaml
# SAML Service Provider Configuration
sp:
  entityId: "https://your-vmgateway-domain.com"
  assertionConsumerService:
    url: "https://your-vmgateway-domain.com/auth/saml/acs"
    binding: "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
  singleLogoutService:
    url: "https://your-vmgateway-domain.com/auth/saml/slo"
    binding: "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
  nameIdFormat: "urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress"
  x509cert: ""  # Optional: SP certificate for signing
  privateKey: ""  # Optional: SP private key

# SAML Identity Provider Configuration
idp:
  entityId: "http://www.okta.com/abc123"  # From metadata
  singleSignOnService:
    url: "https://your-domain.okta.com/app/abc123/sso/saml"
    binding: "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
  singleLogoutService:
    url: "https://your-domain.okta.com/app/abc123/slo/saml"
    binding: "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
  x509cert: "MIIDpDCCAoygAwIBAgIGAXoO..."  # From metadata

# Security Settings
security:
  nameIdEncrypted: false
  authnRequestsSigned: false
  logoutRequestSigned: false
  logoutResponseSigned: false
  signMetadata: false
  wantMessagesSigned: false
  wantAssertionsSigned: true
  wantAssertionsEncrypted: false
  wantNameId: true
  wantNameIdEncrypted: false
  wantAttributeStatement: true
  requestedAuthnContext: true
  requestedAuthnContextComparison: "exact"
  metadataValidUntil: ""
  metadataCacheDuration: ""

# Attribute Mapping
attributeMapping:
  email: "email"
  firstName: "firstName"
  lastName: "lastName"
  displayName: "displayName"
  groups: "groups"

# Organization Info (appears in metadata)
organization:
  name: "Your Organization"
  displayName: "Your Organization Name"
  url: "https://your-organization.com"

# Contact Info (appears in metadata)
contactPerson:
  technical:
    givenName: "Technical Support"
    emailAddress: "[your-tech-support-email]"
  support:
    givenName: "User Support"
    emailAddress: "[your-user-support-email]"
```

### Step 4: Restart VM Gateway

```bash
sudo systemctl restart vm-gateway-controller
```

Check logs for any errors:
```bash
sudo journalctl -u vm-gateway-controller -f
```

Look for:
```
INFO: SSO enabled with SAML provider
INFO: SAML metadata loaded successfully
INFO: SAML configuration validated
```

---

## Testing SSO

### Step 1: Test SSO Login

1. **Open VM Gateway in incognito/private window**
   - This ensures no existing session interferes
   - Navigate to `https://your-vmgateway-domain.com`

2. **Click "Sign in with SSO"**
   - You should see a button or link for SSO login
   - If not, check that `SSO_ENABLED=true` in configuration

3. **Redirect to Okta**
   - You should be redirected to Okta login page
   - URL should be `https://your-domain.okta.com/...`

4. **Enter Okta Credentials**
   - Use credentials of a user assigned to the VM Gateway app
   - Complete MFA if enabled

5. **Redirect Back to VM Gateway**
   - After successful authentication, you should be redirected back
   - You should be logged in to VM Gateway

6. **Verify User Profile**
   - Click on your profile/avatar
   - Verify that your name and email are correct
   - Check that you have the appropriate role

### Step 2: Test User Creation

If `SSO_AUTO_CREATE_USERS=true`:

1. **Log in with a new user** (not previously in VM Gateway)
2. **Verify user was created**:
   ```bash
   # Via CLI
   python -m vm_gateway.cli list-users | grep "<EMAIL>"
   
   # Via API
   curl -H "Authorization: Bearer $TOKEN" \
     https://your-vmgateway-domain.com/api/v1/users | jq '.[] | select(.email=="<EMAIL>")'
   ```

3. **Check user has default role**:
   - User should have the role specified in `SSO_DEFAULT_ROLE`

### Step 3: Test Group Mapping

If `SSO_GROUP_MAPPING_ENABLED=true`:

1. **Assign user to Okta group** (e.g., "Okta_Admins")
2. **Log out and log back in**
3. **Verify role was updated**:
   ```bash
   python -m vm_gateway.cli get-user --email <EMAIL>
   ```
   - User should now have "admin" role

### Step 4: Test Logout

1. **Click logout in VM Gateway**
2. **Verify you're logged out**
3. **Test Single Logout (SLO)** if configured:
   - After logging out of VM Gateway, you should also be logged out of Okta
   - Try accessing another Okta app - you should need to log in again

---

## Attribute Mapping

### Understanding Attributes

SAML assertions contain user attributes sent from the IdP. VM Gateway needs to map these to its user model.

**VM Gateway User Model**:
```python
class User:
    email: str          # Required, unique identifier
    first_name: str     # Optional
    last_name: str      # Optional
    display_name: str   # Optional
    roles: List[str]    # Derived from groups
    is_active: bool     # Always true for SSO users
    sso_provider: str   # "saml"
    sso_id: str         # NameID from SAML
```

### Common Attribute Mappings

**Okta**:
```yaml
attributeMapping:
  email: "email"
  firstName: "firstName"
  lastName: "lastName"
  groups: "groups"
```

**Azure AD**:
```yaml
attributeMapping:
  email: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
  firstName: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname"
  lastName: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname"
  groups: "http://schemas.microsoft.com/ws/2008/06/identity/claims/groups"
```

**Google Workspace**:
```yaml
attributeMapping:
  email: "email"
  firstName: "first_name"
  lastName: "last_name"
  groups: "groups"
```

### Group to Role Mapping

Map IdP groups to VM Gateway roles:

```bash
# In .env
SSO_GROUP_MAPPING='{
  "VM_Gateway_Admins": "admin",
  "VM_Gateway_Developers": "developer",
  "VM_Gateway_DBAs": "dba",
  "VM_Gateway_Viewers": "viewer"
}'
```

**Mapping Logic**:
1. User logs in via SSO
2. IdP sends groups in SAML assertion
3. VM Gateway checks each group against mapping
4. User is assigned all matching roles
5. If no groups match, user gets `SSO_DEFAULT_ROLE`

**Example**:
- User is in "VM_Gateway_Admins" and "VM_Gateway_Developers"
- User gets both "admin" and "developer" roles
- User has combined permissions of both roles

---

## Troubleshooting

### Issue: "SSO login button not appearing"

**Symptoms**:
- No SSO option on login page
- Only username/password fields visible

**Causes & Solutions**:

1. **SSO not enabled**:
   ```bash
   # Check configuration
   grep SSO_ENABLED /opt/vm-gateway/.env
   
   # Should be:
   SSO_ENABLED=true
   
   # Restart if changed
   sudo systemctl restart vm-gateway-controller
   ```

2. **Frontend not updated**:
   ```bash
   # Clear browser cache
   # Or use incognito/private window
   ```

3. **Configuration not loaded**:
   ```bash
   # Check logs
   sudo journalctl -u vm-gateway-controller | grep SSO
   
   # Should see:
   # INFO: SSO enabled with SAML provider
   ```

### Issue: "SAML assertion validation failed"

**Symptoms**:
- Redirected to Okta successfully
- After authentication, error message appears
- Log shows "SAML assertion validation failed"

**Causes & Solutions**:

1. **Clock skew**:
   ```bash
   # Check system time
   date
   
   # Sync time
   sudo ntpdate pool.ntp.org
   
   # Or enable NTP
   sudo systemctl enable systemd-timesyncd
   sudo systemctl start systemd-timesyncd
   ```

2. **Certificate mismatch**:
   ```bash
   # Re-download metadata from Okta
   # Upload to VM Gateway
   # Restart controller
   ```

3. **Audience mismatch**:
   ```bash
   # Check SAML_ENTITY_ID matches Okta configuration
   grep SAML_ENTITY_ID /opt/vm-gateway/.env
   
   # Should match "Audience URI" in Okta
   ```

4. **ACS URL mismatch**:
   ```bash
   # Check SAML_ACS_URL matches Okta configuration
   grep SAML_ACS_URL /opt/vm-gateway/.env
   
   # Should match "Single sign on URL" in Okta
   ```

### Issue: "User created but has no permissions"

**Symptoms**:
- User can log in
- User sees "Access Denied" or empty service catalog
- User has no roles

**Causes & Solutions**:

1. **No default role set**:
   ```bash
   # Set default role
   echo "SSO_DEFAULT_ROLE=viewer" >> /opt/vm-gateway/.env
   sudo systemctl restart vm-gateway-controller
   ```

2. **Group mapping not working**:
   ```bash
   # Check group attribute is being sent
   # Enable debug logging
   echo "LOG_LEVEL=DEBUG" >> /opt/vm-gateway/.env
   sudo systemctl restart vm-gateway-controller
   
   # Check logs for group attribute
   sudo journalctl -u vm-gateway-controller | grep "SAML attributes"
   ```

3. **Group mapping configuration incorrect**:
   ```bash
   # Verify JSON syntax
   python3 -c "import json; print(json.loads('$SSO_GROUP_MAPPING'))"
   
   # Should not error
   ```

### Issue: "Infinite redirect loop"

**Symptoms**:
- Redirected to Okta
- Redirected back to VM Gateway
- Immediately redirected to Okta again
- Loop continues

**Causes & Solutions**:

1. **Session cookie not being set**:
   ```bash
   # Check cookie settings
   grep SECURE_COOKIES /opt/vm-gateway/.env
   
   # If using HTTP (development only):
   SECURE_COOKIES=false
   
   # For production (HTTPS):
   SECURE_COOKIES=true
   ```

2. **Domain mismatch**:
   ```bash
   # Ensure all URLs use same domain
   # Check SAML_ENTITY_ID, SAML_ACS_URL, and actual access URL
   ```

3. **RelayState issue**:
   ```bash
   # Check logs for RelayState errors
   sudo journalctl -u vm-gateway-controller | grep RelayState
   ```

### Debugging Tips

**Enable Debug Logging**:
```bash
# In .env
LOG_LEVEL=DEBUG
SAML_DEBUG=true

# Restart
sudo systemctl restart vm-gateway-controller

# Watch logs
sudo journalctl -u vm-gateway-controller -f
```

**Capture SAML Response**:

Use browser developer tools:
1. Open DevTools (F12)
2. Go to Network tab
3. Log in via SSO
4. Find POST request to `/auth/saml/acs`
5. View Form Data → `SAMLResponse`
6. Decode Base64:
   ```bash
   echo "SAML_RESPONSE_HERE" | base64 -d | xmllint --format -
   ```

**Validate SAML Response**:

Use online tools:
- https://www.samltool.com/decode.php
- https://www.samltool.com/validate.php

Paste decoded SAML response to check for issues.

---

## Advanced Configuration

### Signed Requests

For enhanced security, sign SAML requests:

1. **Generate certificate and key**:
   ```bash
   openssl req -new -x509 -days 3650 -nodes \
     -out /opt/vm-gateway/config/saml-sp.crt \
     -keyout /opt/vm-gateway/config/saml-sp.key
   ```

2. **Configure VM Gateway**:
   ```yaml
   # In saml-settings.yaml
   sp:
     x509cert: "-----BEGIN CERTIFICATE-----\nMIID...\n-----END CERTIFICATE-----"
     privateKey: "-----BEGIN PRIVATE KEY-----\nMIIE...\n-----END PRIVATE KEY-----"
   
   security:
     authnRequestsSigned: true
   ```

3. **Upload certificate to Okta**:
   - In Okta app, go to **Sign On** tab
   - Click **Edit** in SAML Settings
   - Upload certificate in **Signature Certificate**

### Single Logout (SLO)

Enable single logout to log users out of all apps:

1. **Configure VM Gateway**:
   ```yaml
   # In saml-settings.yaml
   sp:
     singleLogoutService:
       url: "https://your-vmgateway-domain.com/auth/saml/slo"
       binding: "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
   ```

2. **Configure Okta**:
   - In Okta app, go to **General** tab
   - Click **Edit** in SAML Settings
   - Set **Single Logout URL**: `https://your-vmgateway-domain.com/auth/saml/slo`
   - Set **SP Issuer**: `https://your-vmgateway-domain.com`

3. **Test**:
   - Log in to VM Gateway via SSO
   - Log out of VM Gateway
   - Try accessing another Okta app
   - You should need to log in again

### Just-In-Time (JIT) Provisioning

Automatically create and update users on login:

```bash
# In .env
SSO_AUTO_CREATE_USERS=true
SSO_UPDATE_USER_ATTRIBUTES=true
SSO_JIT_PROVISIONING=true

# Update user attributes on every login
SSO_UPDATE_ON_LOGIN=true

# Deactivate users not seen in X days
SSO_DEACTIVATE_AFTER_DAYS=90
```

### Multiple IdPs

Support multiple identity providers:

```yaml
# In config/sso-providers.yaml
providers:
  - name: "okta"
    type: "saml"
    metadata_url: "https://your-domain.okta.com/app/abc123/sso/saml/metadata"
    button_text: "Sign in with Okta"
    enabled: true
  
  - name: "azure"
    type: "saml"
    metadata_url: "https://login.microsoftonline.com/tenant-id/federationmetadata/2007-06/federationmetadata.xml"
    button_text: "Sign in with Microsoft"
    enabled: true
  
  - name: "google"
    type: "oidc"
    client_id: "your-client-id"
    client_secret: "your-client-secret"
    discovery_url: "https://accounts.google.com/.well-known/openid-configuration"
    button_text: "Sign in with Google"
    enabled: true
```

Users will see multiple SSO buttons on login page.

---

## Related Documentation

- **[Authentication Overview](/docs/06-authentication/01-overview.md)**: Authentication architecture
- **[SSO Configuration](/docs/06-authentication/04-sso.md)**: Detailed SSO configuration reference
- **[User Management](/docs/04-controller/02-web-interface.md#user-management)**: Managing users in VM Gateway
- **[RBAC Configuration](/docs/06-authentication/07-rbac.md)**: Role-based access control
- **[Security Best Practices](/docs/10-development/07-security.md)**: Security recommendations
- **[Troubleshooting Authentication](/docs/11-troubleshooting/01-common-issues.md#authentication-issues)**: Common auth issues

## Summary

You've successfully configured SSO authentication for VM Gateway! Users can now log in using their existing corporate credentials, and user management is centralized in your IdP.

**Key Takeaways**:
- SAML 2.0 provides secure, standardized SSO
- Proper attribute mapping ensures user data is correct
- Group mapping automates role assignment
- JIT provisioning eliminates manual user creation
- Debug logging helps troubleshoot issues

**Next Steps**:
- Configure MFA in your IdP for enhanced security
- Set up group-based role mapping
- Test with different user scenarios
- Document your SSO configuration for your team
- Consider enabling Single Logout (SLO)

For production deployments, ensure you:
- Use HTTPS everywhere
- Enable signed SAML requests
- Regularly rotate certificates
- Monitor SSO login success/failure rates
- Have a backup local admin account

