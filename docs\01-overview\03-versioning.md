---
title: "Version Numbering System"
section: "Overview"
order: 3
tags: ["versioning", "releases", "builds"]
last_updated: "2025-11-08"
---

# Version Numbering System

## Overview

VM Gateway uses a comprehensive version numbering system that clearly communicates the development phase, feature completeness, and build iteration of each release. This system helps users, developers, and operators understand exactly what to expect from any given version of the platform.

## Version Format

The version format follows this structure:

```
[PHASE].[MAJOR].[MINOR]-[BUILD]
```

### Format Components

Each component serves a specific purpose:

- **PHASE**: Single letter indicating development phase (a, b, c, or r)
- **MAJOR**: Major version number (increments for breaking changes or complete platform overhauls)
- **MINOR**: Minor version number (increments for new features or significant enhancements)
- **BUILD**: Incremental build number (increments with each build, resets when MAJOR changes)

### Example Versions

```
a.0.0-1      First alpha build, initial project structure
a.0.0-20     Alpha build 20, foundation complete
a.1.0-1      Alpha with first major feature set
a.1.0-47     Alpha build 47 of version 1.0
a.1.1-1      Alpha with additional minor feature
a.1.1-156    Alpha build 156 of version 1.1
b.1.0-1      First beta build (feature complete, testing phase)
b.1.2-89     Beta build 89 with multiple refinements
c.1.0-1      First release candidate
c.1.0-8      Release candidate build 8
r.1.0-1      First stable production release
r.1.5-454    Production release build 454 of version 1.5
r.2.0-1      Major version 2 release (build counter RESETS)
```

## Phase Identifiers

### Alpha Phase (a)

**Purpose**: Early development and experimentation

**Characteristics**:
- Core features under active development
- APIs may change frequently
- Breaking changes expected between builds
- Not recommended for production use
- Limited documentation
- Focus on functionality over stability

**Typical Duration**: Several months to a year

**When to Use**:
- Internal development and testing
- Proof of concept deployments
- Early adopter testing in non-critical environments

**Example Milestones**:
- `a.0.0-20`: Project foundation and documentation complete
- `a.1.0-1`: Agent service discovery working
- `a.1.1-1`: Controller web interface functional
- `a.1.2-1`: Client application basic functionality

### Beta Phase (b)

**Purpose**: Feature complete, focus on testing and refinement

**Characteristics**:
- All planned features implemented
- API relatively stable (minor changes possible)
- Focus on bug fixes and performance
- Suitable for staging environments
- Comprehensive documentation available
- User feedback actively incorporated

**Typical Duration**: Several weeks to months

**When to Use**:
- Staging environment deployments
- User acceptance testing
- Performance testing
- Security audits
- Beta testing programs

**Example Milestones**:
- `b.1.0-1`: Feature complete, begin testing
- `b.1.0-50`: Major bugs fixed
- `b.1.1-1`: Performance optimizations added
- `b.1.2-1`: Security hardening complete

**Graduation Criteria**:
- All critical bugs resolved
- Performance meets requirements
- Security audit passed
- Documentation complete
- No known data loss or corruption issues

### Release Candidate Phase (c)

**Purpose**: Final testing before production release

**Characteristics**:
- Production-ready code
- No new features (bug fixes only)
- API frozen (no changes unless critical)
- Extensive testing completed
- Full documentation and migration guides
- Ready for production evaluation

**Typical Duration**: Days to weeks

**When to Use**:
- Production-like environment testing
- Final validation before rollout
- Early production deployments (low-risk)
- Pilot programs

**Example Milestones**:
- `c.1.0-1`: First release candidate
- `c.1.0-3`: Critical bug fix
- `c.1.0-8`: Final release candidate

**Graduation Criteria**:
- No critical or high-priority bugs
- All tests passing
- Performance validated in production-like environment
- Security review complete
- Upgrade path tested
- Rollback procedures verified

### Release Phase (r)

**Purpose**: Stable, production-ready software

**Characteristics**:
- Thoroughly tested and validated
- Stable APIs (breaking changes only in major versions)
- Production support available
- Regular security updates
- Long-term maintenance
- Comprehensive documentation

**When to Use**:
- Production deployments
- Mission-critical systems
- Customer-facing environments

**Example Milestones**:
- `r.1.0-1`: First stable release
- `r.1.0-50`: Bug fixes and minor improvements
- `r.1.5-1`: New minor features added
- `r.1.5-454`: Mature, well-tested version
- `r.2.0-1`: Major version upgrade

## Version Components in Detail

### MAJOR Version

**When to Increment**:
- Breaking API changes that require code modifications
- Fundamental architecture changes
- Complete platform overhauls
- Removal of deprecated features
- Changes that break backward compatibility

**Special Rule**: MAJOR stays at 1 until all features in the original design brief are complete and stable. This ensures version 1.x represents the complete vision of the platform.

**Examples**:
- `r.1.x-x` → `r.2.0-1`: Complete redesign of authentication system (breaking change)
- `r.2.x-x` → `r.3.0-1`: New agent protocol incompatible with previous versions

**Impact**:
- Users must plan for migration
- Upgrade documentation required
- Potential downtime during upgrade
- Testing required before deployment

### MINOR Version

**When to Increment**:
- New features added
- Significant enhancements to existing features
- New API endpoints (backward compatible)
- Performance improvements
- New integrations

**Backward Compatibility**: MINOR version changes maintain backward compatibility. Existing deployments should work without modification.

**Examples**:
- `r.1.0-x` → `r.1.1-1`: Added support for WebAuthn authentication
- `r.1.1-x` → `r.1.2-1`: Implemented advanced alerting system
- `r.1.2-x` → `r.1.3-1`: Added Kubernetes deployment support

**Impact**:
- Optional upgrade (no breaking changes)
- New features available after upgrade
- Minimal testing required
- Can usually upgrade without downtime

### BUILD Number

**When to Increment**:
- Every build/release
- Bug fixes
- Documentation updates
- Dependency updates
- Configuration changes
- Any code change

**Reset Behavior**: BUILD resets to 1 when MAJOR version increments

**Examples**:
- `r.1.5-454` → `r.1.5-455`: Bug fix
- `r.1.5-455` → `r.1.5-456`: Security patch
- `r.1.5-999` → `r.2.0-1`: Major version change (BUILD resets)

**Impact**:
- Minimal (usually bug fixes)
- Low-risk upgrades
- Can often be automated

## Build Metadata

Each build includes comprehensive metadata embedded in the application:

### Metadata Fields

```json
{
  "version": "r.1.5-454",
  "phase": "release",
  "major": 1,
  "minor": 5,
  "build": 454,
  "git_commit": "a3f5c2d8e1b4f6a9c7e2d5b8f1a4c7e9d2b5f8a1",
  "git_branch": "main",
  "build_timestamp": "2025-11-08T14:32:15Z",
  "build_environment": "production",
  "python_version": "3.11.6",
  "dependencies": {
    "fastapi": "0.104.1",
    "sqlalchemy": "2.0.23",
    "redis": "5.0.1",
    ...
  },
  "feature_flags": {
    "advanced_monitoring": true,
    "experimental_ai_classification": false,
    ...
  }
}
```

### Accessing Build Metadata

**Via API**:
```bash
curl https://controller.example.com/api/version
```

**Via Web Interface**:
- Displayed in footer of every page
- Detailed view in Settings → About

**Via CLI**:
```bash
vm-gateway --version
vm-gateway version --detailed
```

**Via Python**:
```python
from vm_gateway import __version__, __build_info__

print(__version__)  # "r.1.5-454"
print(__build_info__)  # Full metadata dict
```

## Version Comparison and Compatibility

### Semantic Comparison

Versions can be compared programmatically:

```python
from vm_gateway.version import Version

v1 = Version("r.1.5-454")
v2 = Version("r.1.6-1")

v1 < v2  # True
v1.is_compatible_with(v2)  # True (same major version)

v3 = Version("r.2.0-1")
v1.is_compatible_with(v3)  # False (different major version)
```

### Compatibility Matrix

| Component | Controller Version | Compatible Agent Versions | Compatible Client Versions |
|-----------|-------------------|---------------------------|----------------------------|
| Controller r.1.5-x | r.1.5-x | r.1.0-x to r.1.5-x | r.1.0-x to r.1.5-x |
| Controller r.1.6-x | r.1.6-x | r.1.0-x to r.1.6-x | r.1.0-x to r.1.6-x |
| Controller r.2.0-x | r.2.0-x | r.2.0-x only | r.2.0-x only |

**Rule**: Components with the same MAJOR version are compatible. Agents and clients should be at or below the controller's MINOR version.

## Release Cadence

### Alpha Phase

- **Frequency**: Multiple builds per day during active development
- **Schedule**: No fixed schedule
- **Announcement**: Internal team only

### Beta Phase

- **Frequency**: Weekly or bi-weekly builds
- **Schedule**: Regular schedule for testing cycles
- **Announcement**: Beta testers notified via email

### Release Candidate Phase

- **Frequency**: As needed (typically 1-3 candidates)
- **Schedule**: Based on testing results
- **Announcement**: Public announcement, release notes published

### Release Phase

- **Major Releases**: Annually or when significant features complete
- **Minor Releases**: Quarterly or when new features are ready
- **Patch Releases**: As needed for bug fixes and security updates
- **Security Updates**: Immediately upon discovery of vulnerabilities

## Version Lifecycle

### Support Policy

| Phase | Duration | Updates | Support |
|-------|----------|---------|---------|
| Alpha | Until beta | Frequent | Best effort |
| Beta | 1-3 months | Weekly | Community |
| Release Candidate | 1-4 weeks | As needed | Full |
| Current Release | Until next minor | Regular | Full |
| Previous Minor | 6 months | Security only | Limited |
| Previous Major | 1 year | Critical security | Limited |
| End of Life | - | None | None |

### Example Timeline

```
r.1.0-1 released: 2025-01-01
  ├─ Full support until r.1.1-1
  │
r.1.1-1 released: 2025-04-01
  ├─ r.1.0-x: Security updates only
  ├─ Full support until r.1.2-1
  │
r.1.2-1 released: 2025-07-01
  ├─ r.1.0-x: End of life
  ├─ r.1.1-x: Security updates only
  ├─ Full support until r.1.3-1
  │
r.2.0-1 released: 2026-01-01
  ├─ r.1.x-x: Security updates for 1 year
  ├─ Full support for r.2.0-x
```

## Upgrade Paths

### Within Same MAJOR Version

**Example**: r.1.3-100 → r.1.5-1

**Process**:
1. Review release notes
2. Backup database
3. Update controller
4. Update agents (can be gradual)
5. Update clients (can be gradual)
6. Verify functionality

**Downtime**: Minimal (rolling updates possible)

### Across MAJOR Versions

**Example**: r.1.5-454 → r.2.0-1

**Process**:
1. Review migration guide
2. Test in staging environment
3. Backup all data
4. Run migration scripts
5. Update all components simultaneously
6. Verify functionality
7. Monitor for issues

**Downtime**: Required (coordinate maintenance window)

## Version Tagging in Git

### Tag Format

```
v[PHASE].[MAJOR].[MINOR]-[BUILD]
```

### Examples

```bash
git tag v.a.0.0-20
git tag v.a.1.0-1
git tag v.b.1.0-1
git tag v.r.1.5-454
```

### Tag Annotations

Tags include detailed information:

```bash
git tag -a v.r.1.5-454 -m "Release version 1.5 build 454

Features:
- Added WebAuthn support
- Improved performance by 30%
- Enhanced security logging

Bug Fixes:
- Fixed memory leak in agent
- Corrected timezone handling
- Resolved race condition in auth

Breaking Changes:
- None

Migration Notes:
- No special steps required
"
```

## Version in Documentation

### Documentation Versioning

Documentation is versioned alongside code:

- Each release has corresponding documentation
- Documentation includes version-specific information
- API documentation generated from code
- Migration guides for major versions

### Version Indicators

Documentation clearly indicates:
- Which version it applies to
- When features were added (e.g., "Added in v1.5")
- When features were deprecated (e.g., "Deprecated in v1.8, removed in v2.0")
- Version-specific configuration examples

## Deprecation Policy

### Deprecation Process

1. **Announcement**: Feature marked as deprecated in release notes
2. **Warning Period**: Minimum one MINOR version (e.g., deprecated in r.1.5, removed in r.1.6 or later)
3. **Documentation**: Deprecation warnings in docs and API responses
4. **Removal**: Feature removed in next MAJOR version

### Example

```
r.1.5-1: Feature X deprecated, warning added
r.1.6-1: Feature X still available, warning remains
r.1.7-1: Feature X still available, warning remains
r.2.0-1: Feature X removed
```

## Version Display

### User Interface

Version displayed in:
- Footer of every page
- About dialog (Settings → About)
- Login page footer
- Error pages

### API Responses

Version included in API responses:

```json
{
  "data": {...},
  "meta": {
    "version": "r.1.5-454",
    "api_version": "v1"
  }
}
```

### Logs

Version logged on startup:

```
2025-11-08 14:32:15 INFO [vm_gateway.controller] Starting VM Gateway Controller
2025-11-08 14:32:15 INFO [vm_gateway.controller] Version: r.1.5-454
2025-11-08 14:32:15 INFO [vm_gateway.controller] Build: a3f5c2d8e1b4f6a9c7e2d5b8f1a4c7e9d2b5f8a1
2025-11-08 14:32:15 INFO [vm_gateway.controller] Python: 3.11.6
```

## Best Practices

### For Developers

1. **Increment BUILD** for every commit to main branch
2. **Update version** in pyproject.toml and version.py
3. **Tag releases** in Git with annotated tags
4. **Document changes** in CHANGELOG.md
5. **Test upgrades** from previous versions

### For Operators

1. **Track versions** of all deployed components
2. **Test upgrades** in staging before production
3. **Read release notes** before upgrading
4. **Backup data** before major upgrades
5. **Monitor** after upgrades for issues
6. **Keep agents updated** to match controller version

### For Users

1. **Check version** before reporting bugs
2. **Review release notes** for new features
3. **Plan upgrades** during maintenance windows
4. **Test new versions** in non-production first
5. **Subscribe** to release announcements

## Summary

VM Gateway's version numbering system provides clear communication about the development phase, feature completeness, and build iteration of each release. The four-part version format (PHASE.MAJOR.MINOR-BUILD) combined with comprehensive build metadata ensures that users, developers, and operators always know exactly what to expect from any given version of the platform.

Understanding this versioning system is crucial for planning deployments, managing upgrades, and ensuring compatibility across components. The system balances the need for rapid iteration during development with the stability requirements of production deployments.
