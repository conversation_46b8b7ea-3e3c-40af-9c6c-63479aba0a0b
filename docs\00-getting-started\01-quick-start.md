# Quick Start Guide

Welcome to VM Gateway! This guide will help you get up and running with the platform in minutes.

## What You'll Need

Before you begin, ensure you have:

- **Windows, macOS, or Linux** operating system
- **Administrator/root access** for installation
- **Network connectivity** between your VMs and the controller
- **PostgreSQL 14+** database server
- **Redis 7+** for real-time features

## Installation Overview

VM Gateway consists of three main components:

1. **Controller** - The central management server (install first)
2. **Agent** - Lightweight service discovery daemon (install on each VM)
3. **Client** - Desktop application for port forwarding (install on user workstations)

## Step 1: Download VM Gateway

### For Production Use (Recommended)

Download the pre-built executables for your platform:

**Controller:**
- Windows: `vm-gateway-controller-windows-amd64.exe`
- Linux: `vm-gateway-controller-linux-amd64`
- macOS: `vm-gateway-controller-darwin-amd64`

**Agent:**
- Windows: `vm-gateway-agent-windows-amd64.exe`
- Linux: `vm-gateway-agent-linux-amd64`
- macOS: `vm-gateway-agent-darwin-amd64`

**Client:**
- Windows: `vm-gateway-client-windows-amd64.exe`
- Linux: `vm-gateway-client-linux-amd64`
- macOS: `vm-gateway-client-darwin-amd64.dmg`

> **Note:** Source code access is available only with Enterprise tier licensing. See [Pricing & Tiers](/docs/01-overview/06-pricing-tiers.md) for details.

### For Development (Enterprise Tier Only)

If you have source code access:

```bash
# Clone the repository
git clone <repository-url>
cd vm-gateway

# Install dependencies
pip install -e .
```

## Step 2: Install the Controller

The controller is the heart of VM Gateway. Install it on a dedicated server or VM.

### Windows Installation

```powershell
# Download the controller executable
# Place it in C:\Program Files\VMGateway\

# Create configuration directory
New-Item -ItemType Directory -Path "C:\ProgramData\VMGateway\config"

# Run the controller
.\vm-gateway-controller-windows-amd64.exe --config "C:\ProgramData\VMGateway\config\controller.yaml"
```

### Linux Installation

```bash
# Download the controller executable
sudo mv vm-gateway-controller-linux-amd64 /usr/local/bin/vm-gateway-controller
sudo chmod +x /usr/local/bin/vm-gateway-controller

# Create configuration directory
sudo mkdir -p /etc/vm-gateway

# Create systemd service
sudo tee /etc/systemd/system/vm-gateway-controller.service > /dev/null <<EOF
[Unit]
Description=VM Gateway Controller
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=vm-gateway
ExecStart=/usr/local/bin/vm-gateway-controller --config /etc/vm-gateway/controller.yaml
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the service
sudo systemctl enable vm-gateway-controller
sudo systemctl start vm-gateway-controller
```

### macOS Installation

```bash
# Download the controller executable
sudo mv vm-gateway-controller-darwin-amd64 /usr/local/bin/vm-gateway-controller
sudo chmod +x /usr/local/bin/vm-gateway-controller

# Create configuration directory
sudo mkdir -p /usr/local/etc/vm-gateway

# Create launchd plist
sudo tee /Library/LaunchDaemons/com.vmgateway.controller.plist > /dev/null <<EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.vmgateway.controller</string>
    <key>ProgramArguments</key>
    <array>
        <string>/usr/local/bin/vm-gateway-controller</string>
        <string>--config</string>
        <string>/usr/local/etc/vm-gateway/controller.yaml</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
</dict>
</plist>
EOF

# Load and start the service
sudo launchctl load /Library/LaunchDaemons/com.vmgateway.controller.plist
```

## Step 3: Configure the Controller

Create a configuration file for the controller. See [Controller Configuration](/docs/04-controller/08-configuration.md) for detailed options.

**Minimal configuration** (`controller.yaml`):

```yaml
# Database configuration
database:
  host: localhost
  port: 5432
  name: vm_gateway
  user: vm_gateway
  password: your_secure_password

# Redis configuration
redis:
  host: localhost
  port: 6379
  password: your_redis_password

# Web interface
web:
  host: 0.0.0.0
  port: 8443
  tls:
    enabled: true
    cert_file: /path/to/cert.pem
    key_file: /path/to/key.pem

# Authentication
auth:
  secret_key: your_secret_key_here_min_32_chars
  session_timeout: 3600
  
# Initial admin user
admin:
  username: admin
  password: change_me_immediately
  email: <EMAIL>
```

## Step 4: Access the Web Interface

Once the controller is running:

1. Open your browser and navigate to `https://your-controller-ip:8443`
2. Accept the self-signed certificate (or use your own TLS certificate)
3. Log in with the admin credentials from your configuration
4. **Change the default password immediately!**

You'll see the VM Gateway dashboard with:
- Service catalog (empty until agents are installed)
- System health overview
- Quick start wizard

## Step 5: Install Agents on Your VMs

Install the agent on each VM you want to monitor.

### Windows Agent Installation

```powershell
# Download the agent executable
# Place it in C:\Program Files\VMGateway\

# Create configuration
$config = @"
controller:
  url: https://your-controller-ip:8443
  api_key: your_agent_api_key
  
agent:
  vm_name: windows-vm-01
  scan_interval: 60
"@

$config | Out-File -FilePath "C:\ProgramData\VMGateway\config\agent.yaml" -Encoding UTF8

# Install as Windows service
.\vm-gateway-agent-windows-amd64.exe install --config "C:\ProgramData\VMGateway\config\agent.yaml"

# Start the service
Start-Service VMGatewayAgent
```

### Linux Agent Installation

```bash
# Download the agent executable
sudo mv vm-gateway-agent-linux-amd64 /usr/local/bin/vm-gateway-agent
sudo chmod +x /usr/local/bin/vm-gateway-agent

# Create configuration
sudo tee /etc/vm-gateway/agent.yaml > /dev/null <<EOF
controller:
  url: https://your-controller-ip:8443
  api_key: your_agent_api_key
  
agent:
  vm_name: linux-vm-01
  scan_interval: 60
EOF

# Create systemd service
sudo tee /etc/systemd/system/vm-gateway-agent.service > /dev/null <<EOF
[Unit]
Description=VM Gateway Agent
After=network.target

[Service]
Type=simple
User=root
ExecStart=/usr/local/bin/vm-gateway-agent --config /etc/vm-gateway/agent.yaml
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the service
sudo systemctl enable vm-gateway-agent
sudo systemctl start vm-gateway-agent
```

### Generating Agent API Keys

To generate an API key for agents:

1. Log into the controller web interface
2. Navigate to **Settings** → **Agent Management**
3. Click **Generate New Agent Key**
4. Copy the key and use it in your agent configuration
5. Optionally, set restrictions (IP whitelist, expiration, etc.)

## Step 6: Verify Agent Connection

After installing agents:

1. Return to the controller web interface
2. Navigate to **Infrastructure** → **VMs**
3. You should see your VMs listed with status "Connected"
4. Click on a VM to see discovered services

The agent will automatically:
- Scan for listening ports
- Identify running services
- Classify services by type
- Report metrics and health status
- Update the controller in real-time

## Step 7: Install the Desktop Client (Optional)

The desktop client enables secure port forwarding to services.

### Windows Client Installation

1. Download `vm-gateway-client-windows-amd64.exe`
2. Run the installer
3. Follow the installation wizard
4. Launch VM Gateway Client from the Start menu

### Linux Client Installation

```bash
# Download the client executable
sudo mv vm-gateway-client-linux-amd64 /usr/local/bin/vm-gateway-client
sudo chmod +x /usr/local/bin/vm-gateway-client

# Create desktop entry
cat > ~/.local/share/applications/vm-gateway-client.desktop <<EOF
[Desktop Entry]
Name=VM Gateway Client
Exec=/usr/local/bin/vm-gateway-client
Icon=vm-gateway
Type=Application
Categories=Network;
EOF
```

### macOS Client Installation

1. Download `vm-gateway-client-darwin-amd64.dmg`
2. Open the DMG file
3. Drag VM Gateway Client to Applications
4. Launch from Applications folder

### Client Configuration

On first launch:

1. Enter your controller URL: `https://your-controller-ip:8443`
2. Log in with your user credentials
3. The client will download available services
4. You're ready to create port forwards!

## Step 8: Create Your First Port Forward

Let's forward a database service to your local machine:

1. Open the VM Gateway Client
2. Navigate to **Services** tab
3. Find your database service (e.g., PostgreSQL on port 5432)
4. Click **Create Forward**
5. Choose a local port (e.g., 15432)
6. Click **Start Forward**

Now you can connect to `localhost:15432` and it will securely tunnel to your remote database!

## Step 9: Configure Access Control

Set up proper access controls:

1. In the web interface, go to **Access Control** → **Roles**
2. Create roles for different user types:
   - **Developers**: Access to dev/staging services
   - **Operators**: Access to all services, read-only production
   - **Admins**: Full access to everything
3. Go to **Access Control** → **Users**
4. Create user accounts and assign roles
5. Configure approval workflows for production access

See [RBAC Configuration](/docs/06-authentication/07-rbac.md) for detailed access control setup.

## Step 10: Set Up Monitoring & Alerts

Configure monitoring to stay informed:

1. Navigate to **Monitoring** → **Alerts**
2. Create alert rules:
   - Service down alerts
   - High resource usage alerts
   - Unauthorized access attempts
3. Configure notification channels:
   - Email
   - Slack (see [Slack Integration](/docs/13-integrations/01-slack.md))
   - PagerDuty (see [PagerDuty Integration](/docs/13-integrations/02-pagerduty.md))
4. Set up dashboards for your team

## Next Steps

Congratulations! You now have a working VM Gateway installation. Here's what to explore next:

### Essential Reading

- **[Architecture Overview](/docs/02-architecture/01-system-overview.md)** - Understand how VM Gateway works
- **[Security Model](/docs/02-architecture/04-security-model.md)** - Learn about security features
- **[Service Discovery](/docs/03-agent/02-service-discovery.md)** - How agents discover services
- **[RBAC Configuration](/docs/06-authentication/07-rbac.md)** - Set up granular access control

### Common Tasks

- **[Adding More VMs](/docs/00-getting-started/03-adding-vms.md)** - Scale your deployment
- **[User Management](/docs/00-getting-started/04-user-management.md)** - Create and manage users
- **[Backup & Recovery](/docs/08-deployment/08-backup-recovery.md)** - Protect your data
- **[High Availability](/docs/08-deployment/07-high-availability.md)** - Deploy for production

### Advanced Features

- **[SSO Integration](/docs/06-authentication/04-sso.md)** - Integrate with your identity provider
- **[Secrets Management](/docs/07-secrets-management/01-overview.md)** - Secure credential storage
- **[Multi-VM Deployment](/docs/08-deployment/04-multi-vm.md)** - Distributed architecture
- **[API Usage](/docs/09-api-reference/01-overview.md)** - Automate with the API

### Tutorials

- **[First Deployment Tutorial](/docs/12-tutorials/01-first-deployment.md)** - Detailed walkthrough
- **[Setting Up SSO](/docs/12-tutorials/02-setting-up-sso.md)** - Configure single sign-on
- **[Database Access Workflow](/docs/12-tutorials/03-database-access-workflow.md)** - Secure database access

## Getting Help

If you encounter issues:

1. Check the [Troubleshooting Guide](/docs/11-troubleshooting/01-common-issues.md)
2. Review the [FAQ](/docs/14-faq/01-general.md)
3. Check logs:
   - Controller: `/var/log/vm-gateway/controller.log` (Linux) or `C:\ProgramData\VMGateway\logs\controller.log` (Windows)
   - Agent: `/var/log/vm-gateway/agent.log` (Linux) or `C:\ProgramData\VMGateway\logs\agent.log` (Windows)
4. Consult the [Development Guide](/docs/10-development/01-setup.md) if you have source access

## Summary

You've successfully:
- Installed the VM Gateway controller
- Configured the database and Redis
- Accessed the web interface
- Installed agents on your VMs
- Verified service discovery
- Installed the desktop client
- Created your first port forward
- Configured access control
- Set up monitoring and alerts

VM Gateway is now protecting and managing access to your infrastructure. Explore the documentation to unlock more features!
