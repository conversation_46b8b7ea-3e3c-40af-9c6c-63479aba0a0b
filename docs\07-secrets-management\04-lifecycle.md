# Secret Lifecycle Management

Secrets in the VM Gateway platform follow a well-defined lifecycle from creation to deletion, with automated processes handling rotation, expiration, and cleanup.

## Lifecycle Stages

### 1. Creation

**Process**:
1. Validate secret name and type
2. Check permissions
3. Generate or accept secret value
4. Encrypt secret
5. Store in database
6. Create audit log entry
7. Return secret metadata

**API Example**:
```python
POST /api/secrets
{
  "name": "database/prod/password",
  "type": "database_credential",
  "value": "secure_password_123",
  "description": "Production database password",
  "tags": ["production", "database"],
  "rotation_policy": {
    "enabled": true,
    "interval_days": 90
  }
}
```

### 2. Active Use

During active use, secrets are:
- Accessed by authorized services
- Cached for performance
- Monitored for usage patterns
- Tracked for rotation scheduling

**Access Tracking**:
```python
{
  "secret_id": "secret_abc123",
  "accessed_by": "service_agent_001",
  "accessed_at": "2025-11-08T10:30:00Z",
  "access_type": "read",
  "ip_address": "*********"
}
```

### 3. Rotation

**Automatic Rotation**:
- Triggered by time-based policy
- Generates new secret value
- Updates all consumers
- Maintains old version temporarily
- Verifies successful rotation

**Rotation Process**:
1. Generate new secret value
2. Store as new version
3. Notify consumers
4. Wait for grace period
5. Deactivate old version
6. Clean up after retention period


### 4. Expiration

Secrets can have expiration dates:

**Expiration Policy**:
```yaml
expiration:
  enabled: true
  expires_at: "2026-11-08T00:00:00Z"
  warning_days: 30
  auto_renew: false
```

**Expiration Handling**:
1. Warning notifications before expiration
2. Automatic renewal (if configured)
3. Deactivation on expiration
4. Grace period for emergency access
5. Cleanup after retention period

### 5. Deprecation

Mark secrets as deprecated before removal:

**Deprecation Process**:
```python
def deprecate_secret(secret_id: str, replacement_id: str = None):
    """Mark secret as deprecated"""
    update_secret(secret_id, {
        "status": "deprecated",
        "deprecated_at": now(),
        "replacement_id": replacement_id,
        "deprecation_notice": "This secret will be removed on 2026-01-01"
    })
    
    # Notify consumers
    notify_secret_deprecation(secret_id, replacement_id)
```

### 6. Deletion

**Soft Delete** (Default):
- Mark as deleted
- Retain for recovery period (30 days)
- Hide from normal queries
- Allow recovery by administrators

**Hard Delete** (Permanent):
- Cryptographic erasure (delete encryption keys)
- Overwrite data
- Remove from database
- Cannot be recovered

**Deletion API**:
```python
DELETE /api/secrets/{secret_id}?hard_delete=false

Response:
{
  "deleted": true,
  "deletion_type": "soft",
  "recoverable_until": "2025-12-08T10:00:00Z"
}
```

## Lifecycle Automation

### Automated Lifecycle Management

Configure automatic lifecycle actions:

```yaml
lifecycle_policies:
  - name: auto_rotate_production
    condition:
      tags: ["production"]
      age_days: 90
    action:
      type: rotate
      notify: true
  
  - name: expire_temporary_secrets
    condition:
      tags: ["temporary"]
      age_days: 7
    action:
      type: expire
      notify: true
  
  - name: cleanup_old_versions
    condition:
      version_age_days: 365
    action:
      type: delete_version
      keep_latest: 5
```

### Lifecycle Events

Track lifecycle transitions:

```python
class SecretLifecycleEvent:
    def __init__(self, secret_id, event_type, metadata):
        self.secret_id = secret_id
        self.event_type = event_type
        self.timestamp = now()
        self.metadata = metadata
    
    def publish(self):
        """Publish lifecycle event"""
        event_bus.publish("secret.lifecycle", {
            "secret_id": self.secret_id,
            "event_type": self.event_type,
            "timestamp": self.timestamp,
            "metadata": self.metadata
        })
```

## Version Management

### Version History

Maintain complete version history:

```sql
SELECT 
    version,
    created_at,
    created_by,
    LENGTH(encrypted_value) as size_bytes
FROM secret_versions
WHERE secret_id = 'secret_abc123'
ORDER BY version DESC;
```

### Version Retention

Configure version retention policies:

```yaml
version_retention:
  keep_latest: 10
  keep_days: 365
  keep_tagged: true  # Keep versions with specific tags
  compress_old: true  # Compress versions older than 90 days
```

### Version Comparison

Compare secret versions:

```python
def compare_versions(secret_id: str, version1: int, version2: int):
    """Compare two secret versions"""
    v1 = get_secret_version(secret_id, version1)
    v2 = get_secret_version(secret_id, version2)
    
    return {
        "version1": version1,
        "version2": version2,
        "created_at_diff": v2["created_at"] - v1["created_at"],
        "size_diff": len(v2["value"]) - len(v1["value"]),
        "metadata_changes": diff_metadata(v1["metadata"], v2["metadata"])
    }
```

## Lifecycle Monitoring

### Metrics

Track lifecycle metrics:

- Secrets created per day
- Secrets rotated per day
- Secrets expired per day
- Secrets deleted per day
- Average secret age
- Rotation compliance rate
- Expiration warning rate

### Dashboards

Visualize lifecycle status:

```python
def get_lifecycle_dashboard():
    """Get lifecycle dashboard data"""
    return {
        "total_secrets": count_secrets(),
        "active_secrets": count_secrets(status="active"),
        "deprecated_secrets": count_secrets(status="deprecated"),
        "expired_secrets": count_secrets(status="expired"),
        "rotation_due": count_secrets_due_for_rotation(),
        "expiring_soon": count_secrets_expiring_soon(days=30),
        "recent_rotations": get_recent_rotations(days=7),
        "lifecycle_events": get_lifecycle_events(days=30)
    }
```

## Best Practices

1. **Plan lifecycle from creation**: Define rotation and expiration policies upfront
2. **Automate where possible**: Use automated lifecycle management
3. **Monitor lifecycle health**: Track metrics and alerts
4. **Document lifecycle policies**: Clear documentation for each secret type
5. **Test lifecycle processes**: Regularly test rotation and expiration
6. **Maintain version history**: Keep adequate version history for rollback
7. **Clean up old secrets**: Remove unused secrets regularly
8. **Notify stakeholders**: Alert on lifecycle transitions

## Lifecycle Templates

### Secret Templates

Define lifecycle templates for common secret types:

```yaml
templates:
  database_credential:
    rotation_policy:
      enabled: true
      interval_days: 90
    expiration:
      enabled: false
    version_retention:
      keep_latest: 5
      keep_days: 365
  
  api_key:
    rotation_policy:
      enabled: true
      interval_days: 180
    expiration:
      enabled: true
      warning_days: 30
    version_retention:
      keep_latest: 3
      keep_days: 180
  
  temporary_token:
    rotation_policy:
      enabled: false
    expiration:
      enabled: true
      expires_after_days: 7
    version_retention:
      keep_latest: 1
      keep_days: 30
```

## Next Steps

- [Rotation](06-rotation.md) - Automatic rotation policies
- [Access Control](05-access-control.md) - Permission management
- [Audit](07-audit.md) - Audit logging and compliance
- [Storage](02-storage.md) - Encryption and storage mechanisms
