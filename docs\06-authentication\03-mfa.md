---
title: "Multi-Factor Authentication"
section: "Authentication & Authorization"
order: 3
tags: ["mfa", "totp", "webauthn", "security"]
last_updated: "2025-11-08"
---

# Multi-Factor Authentication (MFA)

Multi-Factor Authentication adds an additional layer of security beyond username and password by requiring users to provide a second form of verification. The platform supports multiple MFA methods including TOTP, WebAuthn/FIDO2, SMS, and email-based codes.

## Overview

MFA significantly reduces the risk of unauthorized access even if passwords are compromised. The platform implements industry-standard MFA protocols and supports multiple authentication factors to accommodate different security requirements and user preferences.

**Supported MFA Methods:**
- Time-based One-Time Password (TOTP) - RFC 6238 compliant
- WebAuthn/FIDO2 - Hardware security keys and platform authenticators
- SMS-based codes - Via Twilio or similar providers
- Email-based codes - Fallback option
- Backup codes - Single-use recovery codes

**Key Features:**
- Per-user MFA enrollment
- Multiple MFA methods per user
- Trusted device management
- Backup codes for account recovery
- Admin-enforced MFA policies
- MFA bypass for emergency access
- Comprehensive audit logging

## TOTP (Time-based One-Time Password)

### Overview

TOTP is the most common MFA method, compatible with authenticator apps like Google Authenticator, Authy, 1Password, and Microsoft Authenticator. It generates 6-digit codes that change every 30 seconds.

**Advantages:**
- Works offline (no network required)
- Widely supported by authenticator apps
- No additional hardware required
- Free to implement and use

**Implementation:**
- Algorithm: HMAC-SHA1 (RFC 6238)
- Code length: 6 digits
- Time step: 30 seconds
- Window: ±1 time step (allows for clock drift)

### TOTP Enrollment

```python
import pyotp
import qrcode
from io import BytesIO
import base64

class TOTPManager:
    """Manage TOTP enrollment and verification"""
    
    def __init__(self, issuer: str = "VM Gateway"):
        self.issuer = issuer
    
    async def enroll_totp(self, user: User) -> dict:
        """Generate TOTP secret and QR code for enrollment"""
        
        # Generate secret
        secret = pyotp.random_base32()
        
        # Create TOTP URI
        totp = pyotp.TOTP(secret)
        provisioning_uri = totp.provisioning_uri(
            name=user.email,
            issuer_name=self.issuer
        )
        
        # Generate QR code
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(provisioning_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        qr_code_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        # Generate backup codes
        backup_codes = [secrets.token_hex(4) for _ in range(10)]
        backup_codes_hashed = [hash_backup_code(code) for code in backup_codes]
        
        # Store secret (encrypted) and backup codes
        await db.execute(
            """
            INSERT INTO user_mfa (user_id, method, secret, backup_codes, enabled)
            VALUES ($1, 'totp', $2, $3, false)
            """,
            user.id,
            encrypt_secret(secret),
            backup_codes_hashed
        )
        
        return {
            "secret": secret,
            "qr_code": qr_code_base64,
            "provisioning_uri": provisioning_uri,
            "backup_codes": backup_codes
        }
```


    async def verify_totp_enrollment(self, user: User, code: str) -> bool:
        """Verify TOTP code during enrollment to ensure proper setup"""
        
        # Get pending TOTP secret
        mfa_record = await db.fetchrow(
            """
            SELECT secret FROM user_mfa
            WHERE user_id = $1 AND method = 'totp' AND enabled = false
            """,
            user.id
        )
        
        if not mfa_record:
            return False
        
        secret = decrypt_secret(mfa_record['secret'])
        totp = pyotp.TOTP(secret)
        
        # Verify code
        if totp.verify(code, valid_window=1):
            # Enable TOTP
            await db.execute(
                """
                UPDATE user_mfa
                SET enabled = true, enabled_at = NOW()
                WHERE user_id = $1 AND method = 'totp'
                """,
                user.id
            )
            
            # Log enrollment
            await log_audit_event(
                actor=user.username,
                action="mfa_enrolled",
                details={"method": "totp"}
            )
            
            return True
        
        return False
    
    async def verify_totp(self, user: User, code: str) -> bool:
        """Verify TOTP code during login"""
        
        # Get TOTP secret
        mfa_record = await db.fetchrow(
            """
            SELECT secret FROM user_mfa
            WHERE user_id = $1 AND method = 'totp' AND enabled = true
            """,
            user.id
        )
        
        if not mfa_record:
            return False
        
        secret = decrypt_secret(mfa_record['secret'])
        totp = pyotp.TOTP(secret)
        
        # Verify with window for clock drift
        is_valid = totp.verify(code, valid_window=1)
        
        # Log verification attempt
        await log_audit_event(
            actor=user.username,
            action="mfa_verification",
            details={"method": "totp", "success": is_valid}
        )
        
        return is_valid
```

### TOTP API Endpoints

```python
@router.post("/auth/mfa/totp/enroll")
async def enroll_totp(user: User = Depends(get_current_user)):
    """Start TOTP enrollment"""
    
    # Check if TOTP already enabled
    existing = await db.fetchrow(
        "SELECT id FROM user_mfa WHERE user_id = $1 AND method = 'totp' AND enabled = true",
        user.id
    )
    if existing:
        raise HTTPException(status_code=400, detail="TOTP already enabled")
    
    totp_manager = TOTPManager()
    enrollment_data = await totp_manager.enroll_totp(user)
    
    return {
        "qr_code": enrollment_data["qr_code"],
        "secret": enrollment_data["secret"],
        "backup_codes": enrollment_data["backup_codes"]
    }

@router.post("/auth/mfa/totp/verify-enrollment")
async def verify_totp_enrollment(
    code: str,
    user: User = Depends(get_current_user)
):
    """Complete TOTP enrollment by verifying a code"""
    
    totp_manager = TOTPManager()
    if await totp_manager.verify_totp_enrollment(user, code):
        return {"message": "TOTP enabled successfully"}
    
    raise HTTPException(status_code=400, detail="Invalid code")

@router.post("/auth/mfa/totp/verify")
async def verify_totp_login(
    code: str,
    mfa_session_token: str
):
    """Verify TOTP code during login"""
    
    # Validate MFA session
    mfa_session = await get_mfa_session(mfa_session_token)
    if not mfa_session or mfa_session.expired:
        raise HTTPException(status_code=401, detail="Invalid or expired MFA session")
    
    user = await get_user_by_id(mfa_session.user_id)
    
    totp_manager = TOTPManager()
    if await totp_manager.verify_totp(user, code):
        # Create full session
        session = await create_session(user, ip_address=mfa_session.ip_address)
        await delete_mfa_session(mfa_session_token)
        
        return {"session_token": session.token}
    
    # Increment failed attempts
    mfa_session.failed_attempts += 1
    if mfa_session.failed_attempts >= 3:
        await delete_mfa_session(mfa_session_token)
        raise HTTPException(status_code=401, detail="Too many failed attempts")
    
    await update_mfa_session(mfa_session)
    raise HTTPException(status_code=400, detail="Invalid code")
```

## WebAuthn / FIDO2

### Overview

WebAuthn (Web Authentication) is a modern, phishing-resistant authentication standard that supports hardware security keys (YubiKey, etc.) and platform authenticators (Windows Hello, Touch ID, Face ID).

**Advantages:**
- Phishing-resistant (cryptographic challenge-response)
- No shared secrets (public key cryptography)
- Hardware-backed security
- Passwordless authentication option
- User-friendly (biometric or PIN)

**Supported Authenticators:**
- Hardware security keys (YubiKey, Titan, etc.)
- Platform authenticators (Windows Hello, Touch ID, Face ID)
- Bluetooth/NFC authenticators

### WebAuthn Registration

```python
from webauthn import (
    generate_registration_options,
    verify_registration_response,
    options_to_json
)
from webauthn.helpers.structs import (
    PublicKeyCredentialDescriptor,
    AuthenticatorSelectionCriteria,
    UserVerificationRequirement
)

class WebAuthnManager:
    """Manage WebAuthn registration and authentication"""
    
    def __init__(self, rp_id: str, rp_name: str, origin: str):
        self.rp_id = rp_id  # e.g., "gateway.example.com"
        self.rp_name = rp_name  # e.g., "VM Gateway"
        self.origin = origin  # e.g., "https://gateway.example.com"
    
    async def start_registration(self, user: User) -> dict:
        """Generate registration options for WebAuthn"""
        
        # Get existing credentials to exclude
        existing_creds = await db.fetch(
            """
            SELECT credential_id FROM user_mfa
            WHERE user_id = $1 AND method = 'webauthn' AND enabled = true
            """,
            user.id
        )
        
        exclude_credentials = [
            PublicKeyCredentialDescriptor(id=cred['credential_id'])
            for cred in existing_creds
        ]
        
        # Generate registration options
        options = generate_registration_options(
            rp_id=self.rp_id,
            rp_name=self.rp_name,
            user_id=user.id.encode(),
            user_name=user.username,
            user_display_name=user.display_name or user.username,
            exclude_credentials=exclude_credentials,
            authenticator_selection=AuthenticatorSelectionCriteria(
                user_verification=UserVerificationRequirement.PREFERRED
            )
        )
        
        # Store challenge for verification
        await redis.setex(
            f"webauthn_challenge:{user.id}",
            300,  # 5 minutes
            options.challenge
        )
        
        return options_to_json(options)
    
    async def complete_registration(
        self,
        user: User,
        credential: dict,
        device_name: str
    ) -> bool:
        """Verify and store WebAuthn credential"""
        
        # Get stored challenge
        challenge = await redis.get(f"webauthn_challenge:{user.id}")
        if not challenge:
            raise ValueError("Challenge not found or expired")
        
        # Verify registration response
        try:
            verification = verify_registration_response(
                credential=credential,
                expected_challenge=challenge,
                expected_origin=self.origin,
                expected_rp_id=self.rp_id
            )
        except Exception as e:
            await log_security_event(
                event_type="webauthn_registration_failed",
                user_id=user.id,
                error=str(e)
            )
            return False
        
        # Store credential
        await db.execute(
            """
            INSERT INTO user_mfa (
                user_id, method, credential_id, public_key,
                sign_count, device_name, enabled, enabled_at
            )
            VALUES ($1, 'webauthn', $2, $3, $4, $5, true, NOW())
            """,
            user.id,
            verification.credential_id,
            verification.credential_public_key,
            verification.sign_count,
            device_name
        )
        
        # Delete challenge
        await redis.delete(f"webauthn_challenge:{user.id}")
        
        # Log registration
        await log_audit_event(
            actor=user.username,
            action="mfa_enrolled",
            details={"method": "webauthn", "device": device_name}
        )
        
        return True
```



### WebAuthn Authentication

```python
from webauthn import generate_authentication_options, verify_authentication_response

async def start_authentication(self, user: User) -> dict:
    """Generate authentication options for WebAuthn login"""
    
    # Get user's registered credentials
    credentials = await db.fetch(
        """
        SELECT credential_id, public_key, sign_count
        FROM user_mfa
        WHERE user_id = $1 AND method = 'webauthn' AND enabled = true
        """,
        user.id
    )
    
    if not credentials:
        raise ValueError("No WebAuthn credentials registered")
    
    allow_credentials = [
        PublicKeyCredentialDescriptor(id=cred['credential_id'])
        for cred in credentials
    ]
    
    # Generate authentication options
    options = generate_authentication_options(
        rp_id=self.rp_id,
        allow_credentials=allow_credentials,
        user_verification=UserVerificationRequirement.PREFERRED
    )
    
    # Store challenge
    await redis.setex(
        f"webauthn_auth_challenge:{user.id}",
        300,
        options.challenge
    )
    
    return options_to_json(options)

async def complete_authentication(
    self,
    user: User,
    credential: dict
) -> bool:
    """Verify WebAuthn authentication response"""
    
    # Get stored challenge
    challenge = await redis.get(f"webauthn_auth_challenge:{user.id}")
    if not challenge:
        raise ValueError("Challenge not found or expired")
    
    # Get credential from database
    cred_record = await db.fetchrow(
        """
        SELECT credential_id, public_key, sign_count
        FROM user_mfa
        WHERE user_id = $1 AND credential_id = $2 AND method = 'webauthn'
        """,
        user.id,
        credential['id']
    )
    
    if not cred_record:
        return False
    
    # Verify authentication response
    try:
        verification = verify_authentication_response(
            credential=credential,
            expected_challenge=challenge,
            expected_origin=self.origin,
            expected_rp_id=self.rp_id,
            credential_public_key=cred_record['public_key'],
            credential_current_sign_count=cred_record['sign_count']
        )
    except Exception as e:
        await log_security_event(
            event_type="webauthn_auth_failed",
            user_id=user.id,
            error=str(e)
        )
        return False
    
    # Update sign count (prevents replay attacks)
    await db.execute(
        """
        UPDATE user_mfa
        SET sign_count = $1, last_used_at = NOW()
        WHERE user_id = $2 AND credential_id = $3
        """,
        verification.new_sign_count,
        user.id,
        credential['id']
    )
    
    # Delete challenge
    await redis.delete(f"webauthn_auth_challenge:{user.id}")
    
    return True
```

## SMS-Based MFA

### Overview

SMS-based MFA sends one-time codes via text message. While less secure than TOTP or WebAuthn, it provides a familiar fallback option for users without smartphones or security keys.

**Advantages:**
- Familiar to most users
- No app installation required
- Works on basic phones

**Disadvantages:**
- Vulnerable to SIM swapping attacks
- Requires phone signal
- Costs per SMS
- Not recommended for high-security scenarios

### Implementation

```python
from twilio.rest import Client

class SMSMFAManager:
    """Manage SMS-based MFA"""
    
    def __init__(self, twilio_account_sid: str, twilio_auth_token: str, from_number: str):
        self.client = Client(twilio_account_sid, twilio_auth_token)
        self.from_number = from_number
    
    async def send_code(self, user: User, phone_number: str) -> bool:
        """Send MFA code via SMS"""
        
        # Generate 6-digit code
        code = ''.join([str(secrets.randbelow(10)) for _ in range(6)])
        
        # Store code with expiration
        await redis.setex(
            f"sms_mfa:{user.id}",
            300,  # 5 minutes
            code
        )
        
        # Send SMS
        try:
            message = self.client.messages.create(
                body=f"Your VM Gateway verification code is: {code}. Valid for 5 minutes.",
                from_=self.from_number,
                to=phone_number
            )
            
            # Log SMS sent
            await log_audit_event(
                actor=user.username,
                action="sms_mfa_sent",
                details={"phone": phone_number[-4:], "message_sid": message.sid}
            )
            
            return True
        except Exception as e:
            await log_security_event(
                event_type="sms_mfa_failed",
                user_id=user.id,
                error=str(e)
            )
            return False
    
    async def verify_code(self, user: User, code: str) -> bool:
        """Verify SMS MFA code"""
        
        stored_code = await redis.get(f"sms_mfa:{user.id}")
        if not stored_code:
            return False
        
        # Constant-time comparison
        is_valid = secrets.compare_digest(code, stored_code)
        
        if is_valid:
            # Delete code after successful verification
            await redis.delete(f"sms_mfa:{user.id}")
        
        # Log verification attempt
        await log_audit_event(
            actor=user.username,
            action="sms_mfa_verification",
            details={"success": is_valid}
        )
        
        return is_valid
```

## Email-Based MFA

### Overview

Email-based MFA sends verification codes to the user's registered email address. This serves as a fallback when other MFA methods are unavailable.

**Use Cases:**
- Account recovery
- Fallback when primary MFA unavailable
- Low-security scenarios

```python
class EmailMFAManager:
    """Manage email-based MFA"""
    
    async def send_code(self, user: User) -> bool:
        """Send MFA code via email"""
        
        # Generate 8-character alphanumeric code
        code = secrets.token_urlsafe(6)[:8].upper()
        
        # Store code
        await redis.setex(
            f"email_mfa:{user.id}",
            600,  # 10 minutes
            code
        )
        
        # Send email
        await send_email(
            to=user.email,
            subject="VM Gateway Verification Code",
            template="mfa_code",
            context={"user": user, "code": code}
        )
        
        return True
    
    async def verify_code(self, user: User, code: str) -> bool:
        """Verify email MFA code"""
        
        stored_code = await redis.get(f"email_mfa:{user.id}")
        if not stored_code:
            return False
        
        is_valid = secrets.compare_digest(code.upper(), stored_code)
        
        if is_valid:
            await redis.delete(f"email_mfa:{user.id}")
        
        return is_valid
```

## Backup Codes

### Overview

Backup codes are single-use recovery codes generated during MFA enrollment. They allow users to access their account if they lose access to their primary MFA method.

**Best Practices:**
- Generate 10 codes per user
- Each code is single-use
- Codes are 8-16 characters
- Store hashed (like passwords)
- Regenerate after use or on request

```python
class BackupCodeManager:
    """Manage MFA backup codes"""
    
    async def generate_codes(self, user: User, count: int = 10) -> List[str]:
        """Generate backup codes for user"""
        
        codes = []
        hashed_codes = []
        
        for _ in range(count):
            # Generate code (format: XXXX-XXXX)
            code = f"{secrets.token_hex(2).upper()}-{secrets.token_hex(2).upper()}"
            codes.append(code)
            
            # Hash code for storage
            hashed = hashlib.sha256(code.encode()).hexdigest()
            hashed_codes.append(hashed)
        
        # Store hashed codes
        await db.execute(
            """
            INSERT INTO mfa_backup_codes (user_id, code_hash, used)
            SELECT $1, unnest($2::text[]), false
            """,
            user.id,
            hashed_codes
        )
        
        # Log generation
        await log_audit_event(
            actor=user.username,
            action="backup_codes_generated",
            details={"count": count}
        )
        
        return codes
    
    async def verify_code(self, user: User, code: str) -> bool:
        """Verify and consume a backup code"""
        
        code_hash = hashlib.sha256(code.encode()).hexdigest()
        
        # Check if code exists and is unused
        result = await db.fetchrow(
            """
            SELECT id FROM mfa_backup_codes
            WHERE user_id = $1 AND code_hash = $2 AND used = false
            """,
            user.id,
            code_hash
        )
        
        if not result:
            return False
        
        # Mark code as used
        await db.execute(
            """
            UPDATE mfa_backup_codes
            SET used = true, used_at = NOW()
            WHERE id = $1
            """,
            result['id']
        )
        
        # Log usage
        await log_audit_event(
            actor=user.username,
            action="backup_code_used",
            details={"code_id": result['id']}
        )
        
        # Check remaining codes
        remaining = await db.fetchval(
            """
            SELECT COUNT(*) FROM mfa_backup_codes
            WHERE user_id = $1 AND used = false
            """,
            user.id
        )
        
        # Warn if running low
        if remaining <= 2:
            await send_email(
                to=user.email,
                subject="Low Backup Codes Warning",
                template="backup_codes_low",
                context={"user": user, "remaining": remaining}
            )
        
        return True
```

## Trusted Devices

### Overview

The platform can remember trusted devices to reduce MFA friction for users while maintaining security.

```python
class TrustedDeviceManager:
    """Manage trusted devices for MFA"""
    
    async def mark_device_trusted(
        self,
        user: User,
        device_fingerprint: str,
        ip_address: str,
        user_agent: str,
        duration_days: int = 30
    ) -> str:
        """Mark a device as trusted"""
        
        # Generate device token
        device_token = secrets.token_urlsafe(32)
        device_id = hashlib.sha256(device_token.encode()).hexdigest()
        
        expires_at = datetime.utcnow() + timedelta(days=duration_days)
        
        # Store trusted device
        await db.execute(
            """
            INSERT INTO trusted_devices (
                user_id, device_id, device_fingerprint,
                ip_address, user_agent, expires_at, created_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, NOW())
            """,
            user.id, device_id, device_fingerprint,
            ip_address, user_agent, expires_at
        )
        
        return device_token
    
    async def is_device_trusted(
        self,
        user: User,
        device_token: str,
        device_fingerprint: str
    ) -> bool:
        """Check if device is trusted"""
        
        device_id = hashlib.sha256(device_token.encode()).hexdigest()
        
        result = await db.fetchrow(
            """
            SELECT id FROM trusted_devices
            WHERE user_id = $1 AND device_id = $2
            AND device_fingerprint = $3
            AND expires_at > NOW()
            """,
            user.id, device_id, device_fingerprint
        )
        
        if result:
            # Update last seen
            await db.execute(
                """
                UPDATE trusted_devices
                SET last_seen_at = NOW()
                WHERE id = $1
                """,
                result['id']
            )
            return True
        
        return False
```

## MFA Enforcement Policies

### Configuration

```yaml
mfa_policy:
  # Global enforcement
  enforce_for_all_users: false
  enforce_for_admins: true
  enforce_for_roles:
    - "admin"
    - "operator"
    - "service_owner"
  
  # Grace period for new users
  grace_period_days: 7
  
  # Allowed methods
  allowed_methods:
    - "totp"
    - "webauthn"
    - "sms"
    - "email"
  
  # Trusted devices
  allow_trusted_devices: true
  trusted_device_duration_days: 30
  max_trusted_devices: 5
  
  # Backup codes
  backup_codes_count: 10
  warn_when_codes_below: 3
  
  # Security settings
  max_mfa_attempts: 3
  mfa_session_timeout: 300  # 5 minutes
```

## Related Documentation

- [Authentication Overview](./01-overview.md) - System architecture
- [Local Authentication](./02-local-auth.md) - Password authentication
- [Session Management](./06-session-management.md) - Session handling
- [API Authentication](./05-api-auth.md) - API access methods
