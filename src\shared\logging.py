"""
Centralized logging system for VM Gateway components.

This module provides a unified logging configuration across all platform components
(agent, controller, client, doc_viewer) with consistent formatting, log rotation,
and centralized log management.
"""

import logging
import sys
from pathlib import Path
from logging.handlers import RotatingFileHandler
from typing import Optional


def setup_logging(
    component: str,
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    max_bytes: int = 10485760,  # 10MB
    backup_count: int = 5
) -> logging.Logger:
    """
    Setup centralized logging for VM Gateway components.
    
    This function configures a logger with both console and file handlers (if specified),
    implements log rotation to prevent disk space issues, and ensures consistent
    formatting across all platform components.
    
    Args:
        component: Component name (agent, controller, client, doc_viewer)
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file (optional). If provided, logs will be written
                  to this file with automatic rotation.
        max_bytes: Maximum log file size in bytes before rotation (default: 10MB)
        backup_count: Number of backup files to keep during rotation (default: 5)
    
    Returns:
        Configured logger instance for the specified component
    
    Example:
        >>> from shared.logging import setup_logging
        >>> logger = setup_logging("agent", log_level="DEBUG", log_file="logs/agent/agent.log")
        >>> logger.info("Agent started successfully")
    """
    # Create logger with component-specific name
    logger = logging.getLogger(f"vm_gateway.{component}")
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Prevent duplicate handlers if setup_logging is called multiple times
    if logger.handlers:
        logger.handlers.clear()
    
    # Console handler - outputs to stdout
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.DEBUG)
    
    # File handler with rotation (if log file specified)
    file_handler = None
    if log_file:
        log_path = Path(log_file)
        # Create log directory if it doesn't exist
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
    
    # Formatter - consistent format across all components
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Apply formatter to handlers
    console_handler.setFormatter(formatter)
    if file_handler:
        file_handler.setFormatter(formatter)
    
    # Add handlers to logger
    logger.addHandler(console_handler)
    if file_handler:
        logger.addHandler(file_handler)
    
    return logger
