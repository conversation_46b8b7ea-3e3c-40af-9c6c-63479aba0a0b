# VM Gateway Environment Configuration Template
# Copy this file to .env and update with your actual values

# ============================================================================
# General Configuration
# ============================================================================

# Environment (development, staging, production)
ENVIRONMENT=development

# Application version
VERSION=a.0.0-20

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# ============================================================================
# Documentation Viewer Configuration
# ============================================================================

# Documentation viewer port (default: 80)
DOC_VIEWER_PORT=80

# Documentation viewer host
DOC_VIEWER_HOST=0.0.0.0

# ============================================================================
# Controller Configuration
# ============================================================================

# Controller API host
CONTROLLER_HOST=0.0.0.0

# Controller API port
CONTROLLER_PORT=8000

# Controller web UI port
CONTROLLER_WEB_PORT=3000

# Secret key for JWT tokens (generate with: openssl rand -hex 32)
SECRET_KEY=your-secret-key-here

# JWT token expiration (in seconds)
JWT_EXPIRATION=3600

# ============================================================================
# Database Configuration
# ============================================================================

# PostgreSQL connection string
DATABASE_URL=postgresql://user:password@localhost:5432/vm_gateway

# Database pool size
DB_POOL_SIZE=10

# Database max overflow
DB_MAX_OVERFLOW=20

# ============================================================================
# Redis Configuration
# ============================================================================

# Redis connection URL
REDIS_URL=redis://localhost:6379/0

# Redis password (if required)
REDIS_PASSWORD=

# ============================================================================
# Agent Configuration
# ============================================================================

# Agent heartbeat interval (in seconds)
AGENT_HEARTBEAT_INTERVAL=30

# Agent metrics collection interval (in seconds)
AGENT_METRICS_INTERVAL=60

# Agent service discovery interval (in seconds)
AGENT_DISCOVERY_INTERVAL=300

# ============================================================================
# Client Configuration
# ============================================================================

# Client connection timeout (in seconds)
CLIENT_TIMEOUT=30

# Client reconnect interval (in seconds)
CLIENT_RECONNECT_INTERVAL=5

# ============================================================================
# Security Configuration
# ============================================================================

# Enable mTLS authentication
MTLS_ENABLED=true

# Path to TLS certificate
TLS_CERT_PATH=/path/to/cert.pem

# Path to TLS private key
TLS_KEY_PATH=/path/to/key.pem

# Path to CA certificate
TLS_CA_PATH=/path/to/ca.pem

# Enable CORS (for development)
CORS_ENABLED=false

# CORS allowed origins (comma-separated)
CORS_ORIGINS=http://localhost:3000

# ============================================================================
# Secrets Management Configuration
# ============================================================================

# Secrets encryption key (generate with: openssl rand -hex 32)
SECRETS_ENCRYPTION_KEY=your-encryption-key-here

# External vault integration (hashicorp, aws, azure, gcp, none)
VAULT_PROVIDER=none

# Vault connection URL
VAULT_URL=

# Vault authentication token
VAULT_TOKEN=

# ============================================================================
# Monitoring & Alerting Configuration
# ============================================================================

# Enable Prometheus metrics
PROMETHEUS_ENABLED=true

# Prometheus metrics port
PROMETHEUS_PORT=9090

# Enable alerting
ALERTING_ENABLED=false

# Alert webhook URL
ALERT_WEBHOOK_URL=

# ============================================================================
# Feature Flags
# ============================================================================

# Enable service discovery
FEATURE_SERVICE_DISCOVERY=true

# Enable ML-based classification
FEATURE_ML_CLASSIFICATION=false

# Enable approval workflows
FEATURE_APPROVAL_WORKFLOWS=true

# Enable SSO integration
FEATURE_SSO=false

# Enable multi-factor authentication
FEATURE_MFA=true
