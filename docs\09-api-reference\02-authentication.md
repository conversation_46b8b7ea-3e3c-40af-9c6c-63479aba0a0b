---
title: "Authentication API"
section: "API Reference"
order: 2
tags: ["api", "authentication", "endpoints"]
last_updated: "2025-11-08"
---

# Authentication API

The Authentication API provides endpoints for user authentication, session management, token operations, and multi-factor authentication. All authentication endpoints use HTTPS and implement rate limiting to prevent brute force attacks.

## Base URL

```
https://your-gateway.example.com/api/v1/auth
```

## Authentication Methods

The API supports multiple authentication methods:

1. **Session-based**: HTTP-only cookies for web interface
2. **Token-based**: JWT Bearer tokens for API access
3. **API Keys**: Long-lived keys for service accounts
4. **mTLS**: Certificate-based authentication

## Endpoints

### POST /auth/login

Authenticate a user with username/email and password.

**Request:**

```http
POST /api/v1/auth/login HTTP/1.1
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "SecurePassword123!",
  "remember_me": false
}
```

**Response (Success - No MFA):**

```http
HTTP/1.1 200 OK
Content-Type: application/json
Set-Cookie: session_id=abc123...; HttpOnly; Secure; SameSite=Strict

{
  "success": true,
  "user": {
    "id": "usr_1234567890",
    "email": "<EMAIL>",
    "name": "John Doe",
    "roles": ["developer", "viewer"],
    "mfa_enabled": false
  },
  "tokens": {
    "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "rt_abc123def456...",
    "expires_in": 900,
    "token_type": "Bearer"
  },
  "session": {
    "id": "ses_abc123",
    "expires_at": "2025-11-08T20:00:00Z"
  }
}
```

**Response (MFA Required):**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": false,
  "mfa_required": true,
  "mfa_token": "mfa_temp_token_abc123",
  "mfa_methods": ["totp", "webauthn", "sms"],
  "message": "Multi-factor authentication required"
}
```

**Response (Error):**

```http
HTTP/1.1 401 Unauthorized
Content-Type: application/json

{
  "success": false,
  "error": {
    "code": "INVALID_CREDENTIALS",
    "message": "Invalid username or password",
    "attempts_remaining": 3
  }
}
```

**Rate Limiting:**
- 5 attempts per 5 minutes per IP address
- Account lockout after 5 failed attempts for 15 minutes

---

### POST /auth/mfa/verify

Verify multi-factor authentication code.

**Request:**

```http
POST /api/v1/auth/mfa/verify HTTP/1.1
Content-Type: application/json

{
  "mfa_token": "mfa_temp_token_abc123",
  "method": "totp",
  "code": "123456"
}
```

**Response (Success):**

```http
HTTP/1.1 200 OK
Content-Type: application/json
Set-Cookie: session_id=abc123...; HttpOnly; Secure; SameSite=Strict

{
  "success": true,
  "user": {
    "id": "usr_1234567890",
    "email": "<EMAIL>",
    "name": "John Doe",
    "roles": ["developer", "viewer"]
  },
  "tokens": {
    "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "rt_abc123def456...",
    "expires_in": 900,
    "token_type": "Bearer"
  }
}
```

---

### POST /auth/logout

Terminate the current session.

**Request:**

```http
POST /api/v1/auth/logout HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "message": "Successfully logged out"
}
```

---

### POST /auth/token/refresh

Refresh an expired access token using a refresh token.

**Request:**

```http
POST /api/v1/auth/token/refresh HTTP/1.1
Content-Type: application/json

{
  "refresh_token": "rt_abc123def456..."
}
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "tokens": {
    "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "rt_new_token_xyz789...",
    "expires_in": 900,
    "token_type": "Bearer"
  }
}
```

---

### GET /auth/me

Get current authenticated user information.

**Request:**

```http
GET /api/v1/auth/me HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "user": {
    "id": "usr_1234567890",
    "email": "<EMAIL>",
    "name": "John Doe",
    "avatar_url": "https://cdn.example.com/avatars/john.jpg",
    "roles": ["developer", "viewer"],
    "groups": ["engineering", "backend-team"],
    "permissions": {
      "vms": ["view", "connect"],
      "services": ["view", "connect"],
      "users": ["view"]
    },
    "mfa_enabled": true,
    "mfa_methods": ["totp", "webauthn"],
    "created_at": "2024-01-15T10:30:00Z",
    "last_login": "2025-11-08T12:00:00Z"
  }
}
```

---

### POST /auth/api-keys

Create a new API key for programmatic access.

**Request:**

```http
POST /api/v1/auth/api-keys HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "name": "CI/CD Pipeline Key",
  "scopes": ["vms:read", "services:read", "connections:write"],
  "expires_at": "2026-11-08T00:00:00Z"
}
```

**Response:**

```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,
  "api_key": {
    "id": "key_abc123def456",
    "name": "CI/CD Pipeline Key",
    "key": "pk_live_1234567890abcdef1234567890abcdef",
    "scopes": ["vms:read", "services:read", "connections:write"],
    "created_at": "2025-11-08T12:00:00Z",
    "expires_at": "2026-11-08T00:00:00Z",
    "last_used": null
  },
  "warning": "This key will only be shown once. Store it securely."
}
```

---

### GET /auth/api-keys

List all API keys for the current user.

**Request:**

```http
GET /api/v1/auth/api-keys HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "api_keys": [
    {
      "id": "key_abc123def456",
      "name": "CI/CD Pipeline Key",
      "key_prefix": "pk_live_1234",
      "scopes": ["vms:read", "services:read", "connections:write"],
      "created_at": "2025-11-08T12:00:00Z",
      "expires_at": "2026-11-08T00:00:00Z",
      "last_used": "2025-11-08T14:30:00Z"
    }
  ],
  "total": 1
}
```

---

### DELETE /auth/api-keys/{key_id}

Revoke an API key.

**Request:**

```http
DELETE /api/v1/auth/api-keys/key_abc123def456 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "message": "API key revoked successfully"
}
```

---

### GET /auth/sessions

List all active sessions for the current user.

**Request:**

```http
GET /api/v1/auth/sessions HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "sessions": [
    {
      "id": "ses_abc123",
      "device": {
        "type": "desktop",
        "os": "Windows 11",
        "browser": "Chrome 119",
        "name": "Work Laptop"
      },
      "location": {
        "ip": "************",
        "city": "San Francisco",
        "country": "United States"
      },
      "created_at": "2025-11-08T12:00:00Z",
      "last_activity": "2025-11-08T14:45:00Z",
      "expires_at": "2025-11-08T20:00:00Z",
      "is_current": true
    },
    {
      "id": "ses_def456",
      "device": {
        "type": "mobile",
        "os": "iOS 17",
        "browser": "Safari",
        "name": "iPhone"
      },
      "location": {
        "ip": "*************",
        "city": "New York",
        "country": "United States"
      },
      "created_at": "2025-11-07T08:00:00Z",
      "last_activity": "2025-11-07T18:00:00Z",
      "expires_at": "2025-11-07T20:00:00Z",
      "is_current": false
    }
  ],
  "total": 2
}
```

---

### DELETE /auth/sessions/{session_id}

Terminate a specific session.

**Request:**

```http
DELETE /api/v1/auth/sessions/ses_def456 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "message": "Session terminated successfully"
}
```

---

## Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `INVALID_CREDENTIALS` | 401 | Username or password is incorrect |
| `ACCOUNT_LOCKED` | 403 | Account is locked due to failed login attempts |
| `ACCOUNT_DISABLED` | 403 | Account has been disabled by administrator |
| `MFA_REQUIRED` | 200 | Multi-factor authentication is required |
| `INVALID_MFA_CODE` | 401 | MFA code is incorrect or expired |
| `INVALID_TOKEN` | 401 | Access token is invalid or expired |
| `INVALID_REFRESH_TOKEN` | 401 | Refresh token is invalid or expired |
| `SESSION_EXPIRED` | 401 | Session has expired |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests, try again later |
| `INSUFFICIENT_PERMISSIONS` | 403 | User lacks required permissions |

## Code Examples

### Python

```python
import requests

# Login
response = requests.post(
    "https://your-gateway.example.com/api/v1/auth/login",
    json={
        "username": "<EMAIL>",
        "password": "SecurePassword123!"
    }
)

data = response.json()
access_token = data["tokens"]["access_token"]

# Use token for authenticated requests
headers = {"Authorization": f"Bearer {access_token}"}
response = requests.get(
    "https://your-gateway.example.com/api/v1/auth/me",
    headers=headers
)

print(response.json())
```

### JavaScript

```javascript
// Login
const response = await fetch('https://your-gateway.example.com/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: '<EMAIL>',
    password: 'SecurePassword123!'
  })
});

const data = await response.json();
const accessToken = data.tokens.access_token;

// Use token for authenticated requests
const meResponse = await fetch('https://your-gateway.example.com/api/v1/auth/me', {
  headers: {
    'Authorization': `Bearer ${accessToken}`
  }
});

const userData = await meResponse.json();
console.log(userData);
```

### cURL

```bash
# Login
curl -X POST https://your-gateway.example.com/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "password": "SecurePassword123!"
  }'

# Use token for authenticated requests
curl -X GET https://your-gateway.example.com/api/v1/auth/me \
  -H "Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
```

## Related Documentation

- [API Overview](./01-overview.md) - API design principles and conventions
- [VM Management API](./03-vm-management.md) - VM management endpoints
- [Service Catalog API](./04-service-catalog.md) - Service discovery and management
- [Connections API](./05-connections.md) - Connection management endpoints
