---
title: "Agent-Controller Communication Protocol"
section: "Agent"
order: 5
tags: ["agent", "controller", "protocol", "websocket", "mtls"]
last_updated: "2025-11-08"
---

# Agent-Controller Communication Protocol

## Introduction

The communication protocol between the VM Gateway Agent and Controller is designed for real-time, bidirectional communication with strong security guarantees. The protocol uses WebSocket for persistent connections, mTLS for mutual authentication, and a structured message format for reliable data exchange.

This document details the connection lifecycle, message types, security mechanisms, and error handling strategies that enable seamless agent-controller communication.

## Protocol Overview

### Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Agent                                    │
│  ┌───────────────────────────────────────────────────────┐ │
│  │  WebSocket Client                                     │ │
│  │  - Persistent connection to controller                │ │
│  │  - Automatic reconnection with backoff                │ │
│  │  - Message queue for offline operation                │ │
│  └───────────────────┬───────────────────────────────────┘ │
│                      │                                       │
│                      │ mTLS over WebSocket                   │
│                      │ (wss://controller:8443/agent/ws)      │
│                      │                                       │
└──────────────────────┼───────────────────────────────────────┘
                       │
                       │ Encrypted, Authenticated
                       │
┌──────────────────────▼───────────────────────────────────────┐
│                    Controller                               │
│  ┌───────────────────────────────────────────────────────┐ │
│  │  WebSocket Server                                     │ │
│  │  - Accept agent connections                           │ │
│  │  - Validate mTLS certificates                         │ │
│  │  - Route messages to handlers                         │ │
│  │  - Broadcast updates to agents                        │ │
│  └───────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Connection Characteristics

- **Protocol**: WebSocket (WSS) over TLS 1.3
- **Authentication**: Mutual TLS (mTLS) with client certificates
- **Port**: 8443 (configurable)
- **Endpoint**: `/agent/ws`
- **Heartbeat**: 30 seconds (configurable)
- **Reconnection**: Exponential backoff (1s, 2s, 4s, 8s, 16s, max 60s)
- **Message Format**: JSON with schema validation
- **Compression**: Optional WebSocket compression (permessage-deflate)

## Connection Lifecycle

### 1. Initial Connection

```python
import asyncio
import websockets
import ssl
import json
from typing import Optional

class AgentCommunicator:
    def __init__(self, controller_url: str, cert_path: str, key_path: str, ca_path: str):
        self.controller_url = controller_url
        self.cert_path = cert_path
        self.key_path = key_path
        self.ca_path = ca_path
        self.websocket: Optional[websockets.WebSocketClientProtocol] = None
        self.connected = False
        self.agent_id = self.load_agent_id()
    
    async def connect(self):
        """
        Establish WebSocket connection with mTLS.
        """
        # Create SSL context for mTLS
        ssl_context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH, cafile=self.ca_path)
        ssl_context.load_cert_chain(certfile=self.cert_path, keyfile=self.key_path)
        ssl_context.check_hostname = True
        ssl_context.verify_mode = ssl.CERT_REQUIRED
        
        try:
            # Connect to controller
            self.websocket = await websockets.connect(
                self.controller_url,
                ssl=ssl_context,
                compression='deflate',
                ping_interval=30,
                ping_timeout=10
            )
            
            self.connected = True
            logger.info(f"Connected to controller: {self.controller_url}")
            
            # Send registration message
            await self.register()
            
            # Start message handling loop
            asyncio.create_task(self.message_loop())
            asyncio.create_task(self.heartbeat_loop())
            
        except (websockets.exceptions.WebSocketException, ssl.SSLError, OSError) as e:
            logger.error(f"Connection failed: {e}")
            self.connected = False
            raise
```

### 2. Registration

Upon connection, the agent sends a registration message:

```python
async def register(self):
    """
    Register agent with controller.
    """
    registration_msg = {
        'type': 'REGISTER',
        'agent_id': self.agent_id,
        'timestamp': datetime.now().isoformat(),
        'payload': {
            'hostname': socket.gethostname(),
            'ip_addresses': self.get_ip_addresses(),
            'os': platform.system(),
            'os_version': platform.release(),
            'agent_version': self.get_agent_version(),
            'capabilities': ['discovery', 'metrics', 'tunneling'],
            'last_boot_time': psutil.boot_time()
        }
    }
    
    await self.send_message(registration_msg)
    
    # Wait for acknowledgment
    response = await self.receive_message(timeout=10)
    
    if response['type'] == 'REGISTER_ACK':
        logger.info("Registration acknowledged by controller")
        # Apply configuration from controller
        self.apply_configuration(response['payload'].get('configuration', {}))
    else:
        raise Exception(f"Unexpected response to registration: {response['type']}")
```

### 3. Heartbeat Mechanism

```python
async def heartbeat_loop(self):
    """
    Send periodic heartbeats to controller.
    """
    while self.connected:
        try:
            heartbeat_msg = {
                'type': 'HEARTBEAT',
                'agent_id': self.agent_id,
                'timestamp': datetime.now().isoformat(),
                'payload': {
                    'uptime_seconds': time.time() - psutil.boot_time(),
                    'cpu_percent': psutil.cpu_percent(interval=1),
                    'memory_percent': psutil.virtual_memory().percent,
                    'disk_percent': psutil.disk_usage('/').percent,
                    'service_count': len(self.discovered_services),
                    'error_count': self.error_count,
                    'last_scan_time': self.last_scan_time.isoformat() if self.last_scan_time else None
                }
            }
            
            await self.send_message(heartbeat_msg)
            await asyncio.sleep(30)  # Heartbeat interval
            
        except Exception as e:
            logger.error(f"Heartbeat failed: {e}")
            break
```

### 4. Graceful Disconnection

```python
async def disconnect(self):
    """
    Gracefully disconnect from controller.
    """
    if self.websocket and self.connected:
        try:
            # Send disconnect message
            disconnect_msg = {
                'type': 'DISCONNECT',
                'agent_id': self.agent_id,
                'timestamp': datetime.now().isoformat(),
                'payload': {
                    'reason': 'graceful_shutdown'
                }
            }
            
            await self.send_message(disconnect_msg)
            await self.websocket.close()
            
        except Exception as e:
            logger.error(f"Error during disconnect: {e}")
        finally:
            self.connected = False
            logger.info("Disconnected from controller")
```

## Message Types

### Agent-to-Controller Messages

**REGISTER** - Initial agent registration
```json
{
  "type": "REGISTER",
  "agent_id": "agent-abc123",
  "timestamp": "2025-11-08T10:30:00Z",
  "payload": {
    "hostname": "web-server-01",
    "ip_addresses": ["*********", "*************"],
    "os": "Linux",
    "os_version": "5.15.0-91-generic",
    "agent_version": "a.1.0-15",
    "capabilities": ["discovery", "metrics", "tunneling"]
  }
}
```

**HEARTBEAT** - Periodic keepalive
```json
{
  "type": "HEARTBEAT",
  "agent_id": "agent-abc123",
  "timestamp": "2025-11-08T10:30:30Z",
  "payload": {
    "uptime_seconds": 86400,
    "cpu_percent": 15.2,
    "memory_percent": 45.8,
    "service_count": 12,
    "error_count": 0
  }
}
```

**SYNC_CATALOG** - Push discovered services
```json
{
  "type": "SYNC_CATALOG",
  "agent_id": "agent-abc123",
  "timestamp": "2025-11-08T10:31:00Z",
  "payload": {
    "services": [
      {
        "service_id": "svc-001",
        "port": 80,
        "protocol": "tcp",
        "process_name": "nginx",
        "classification": {
          "type": "web_server",
          "name": "Nginx",
          "confidence": 0.95
        }
      }
    ],
    "changes": [
      {"type": "added", "service_id": "svc-001"},
      {"type": "removed", "service_id": "svc-002"}
    ]
  }
}
```

**METRICS_BATCH** - Upload metrics
```json
{
  "type": "METRICS_BATCH",
  "agent_id": "agent-abc123",
  "timestamp": "2025-11-08T10:35:00Z",
  "payload": {
    "system_metrics": [...],
    "service_metrics": [...],
    "health_checks": [...]
  }
}
```

**LOG** - Send log entries
```json
{
  "type": "LOG",
  "agent_id": "agent-abc123",
  "timestamp": "2025-11-08T10:30:15Z",
  "payload": {
    "level": "ERROR",
    "message": "Failed to classify service on port 9999",
    "context": {
      "port": 9999,
      "process": "unknown"
    }
  }
}
```

### Controller-to-Agent Messages

**REGISTER_ACK** - Registration acknowledgment
```json
{
  "type": "REGISTER_ACK",
  "timestamp": "2025-11-08T10:30:01Z",
  "payload": {
    "agent_id": "agent-abc123",
    "configuration": {
      "scan_interval": 60,
      "metrics_interval": 30,
      "log_level": "INFO"
    }
  }
}
```

**COMMAND** - Execute command
```json
{
  "type": "COMMAND",
  "command_id": "cmd-xyz789",
  "timestamp": "2025-11-08T10:32:00Z",
  "payload": {
    "command": "rescan",
    "parameters": {
      "force": true
    }
  }
}
```

**CONFIG_UPDATE** - Update configuration
```json
{
  "type": "CONFIG_UPDATE",
  "timestamp": "2025-11-08T10:33:00Z",
  "payload": {
    "configuration": {
      "scan_interval": 30,
      "enable_deep_packet_inspection": true
    }
  }
}
```

**TUNNEL_REQUEST** - Establish tunnel
```json
{
  "type": "TUNNEL_REQUEST",
  "tunnel_id": "tunnel-123",
  "timestamp": "2025-11-08T10:34:00Z",
  "payload": {
    "service_id": "svc-001",
    "client_id": "client-456",
    "authorization_token": "eyJhbGc...",
    "tunnel_config": {
      "protocol": "tcp",
      "encryption": "aes-256-gcm"
    }
  }
}
```

## Security

### Mutual TLS (mTLS)

Both agent and controller authenticate each other using X.509 certificates:

**Certificate Generation:**
```bash
# Generate CA certificate
openssl req -x509 -newkey rsa:4096 -keyout ca-key.pem -out ca-cert.pem -days 3650 -nodes

# Generate agent certificate
openssl req -newkey rsa:4096 -keyout agent-key.pem -out agent-csr.pem -nodes
openssl x509 -req -in agent-csr.pem -CA ca-cert.pem -CAkey ca-key.pem -CAcreateserial -out agent-cert.pem -days 365

# Generate controller certificate
openssl req -newkey rsa:4096 -keyout controller-key.pem -out controller-csr.pem -nodes
openssl x509 -req -in controller-csr.pem -CA ca-cert.pem -CAkey ca-key.pem -CAcreateserial -out controller-cert.pem -days 365
```

**Certificate Validation:**
- Verify certificate chain to trusted CA
- Check certificate expiration
- Validate certificate hostname/SAN
- Verify certificate revocation status (optional CRL/OCSP)

### Message Encryption

All messages are encrypted using TLS 1.3:
- Cipher suites: TLS_AES_256_GCM_SHA384, TLS_CHACHA20_POLY1305_SHA256
- Perfect forward secrecy (PFS)
- No support for older TLS versions (1.0, 1.1, 1.2)

### Message Authentication

Each message includes:
- **Timestamp**: Prevent replay attacks (reject messages > 5 minutes old)
- **Agent ID**: Identify sender
- **Message ID**: Unique identifier for deduplication
- **Signature**: HMAC-SHA256 of message payload (optional additional layer)

## Error Handling

### Connection Failures

```python
async def connect_with_retry(self, max_retries: int = None):
    """
    Connect with exponential backoff retry.
    """
    retry_count = 0
    backoff_seconds = 1
    
    while max_retries is None or retry_count < max_retries:
        try:
            await self.connect()
            return
        except Exception as e:
            retry_count += 1
            logger.warning(f"Connection attempt {retry_count} failed: {e}")
            
            if max_retries and retry_count >= max_retries:
                logger.error("Max retries reached, giving up")
                raise
            
            # Exponential backoff with jitter
            sleep_time = min(backoff_seconds + random.uniform(0, 1), 60)
            logger.info(f"Retrying in {sleep_time:.1f} seconds...")
            await asyncio.sleep(sleep_time)
            
            backoff_seconds = min(backoff_seconds * 2, 60)
```

### Message Delivery Failures

```python
class MessageQueue:
    """
    Queue messages for delivery when connection is unavailable.
    """
    def __init__(self, max_size: int = 10000):
        self.queue = []
        self.max_size = max_size
    
    def enqueue(self, message: dict):
        """
        Add message to queue.
        """
        if len(self.queue) >= self.max_size:
            # Drop oldest message
            self.queue.pop(0)
            logger.warning("Message queue full, dropping oldest message")
        
        self.queue.append(message)
    
    async def flush(self, communicator):
        """
        Send all queued messages.
        """
        while self.queue:
            message = self.queue[0]
            try:
                await communicator.send_message(message)
                self.queue.pop(0)
            except Exception as e:
                logger.error(f"Failed to send queued message: {e}")
                break
```

### Offline Mode

When the controller is unreachable:
1. Agent continues normal operation (discovery, metrics collection)
2. Messages are queued locally
3. Data is stored in SQLite database
4. Agent attempts reconnection with exponential backoff
5. Upon reconnection, queued messages are transmitted
6. Full catalog sync is performed

## Performance Optimization

### Message Batching

```python
class MessageBatcher:
    """
    Batch multiple messages for efficient transmission.
    """
    def __init__(self, max_batch_size: int = 100, max_wait_seconds: float = 5.0):
        self.batch = []
        self.max_batch_size = max_batch_size
        self.max_wait_seconds = max_wait_seconds
        self.last_flush = time.time()
    
    def add(self, message: dict):
        """
        Add message to batch.
        """
        self.batch.append(message)
        
        if len(self.batch) >= self.max_batch_size:
            return True  # Flush needed
        
        if time.time() - self.last_flush >= self.max_wait_seconds:
            return True  # Flush needed
        
        return False
    
    def get_batch(self) -> list:
        """
        Get current batch and reset.
        """
        batch = self.batch
        self.batch = []
        self.last_flush = time.time()
        return batch
```

### Compression

WebSocket compression reduces bandwidth:
- Typical compression ratio: 60-80% for JSON messages
- Minimal CPU overhead
- Configurable compression level

### Connection Pooling

For REST API calls (non-WebSocket):
```python
import aiohttp

class HTTPClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        connector = aiohttp.TCPConnector(
            limit=100,  # Max connections
            limit_per_host=10,
            ttl_dns_cache=300
        )
        self.session = aiohttp.ClientSession(connector=connector)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.session.close()
```

## Configuration

```yaml
communication:
  # Connection settings
  controller_url: "wss://controller.example.com:8443/agent/ws"
  connection_timeout: 10s
  reconnect_max_retries: null  # Infinite
  reconnect_backoff_max: 60s
  
  # mTLS certificates
  cert_file: "/etc/vm-gateway/agent-cert.pem"
  key_file: "/etc/vm-gateway/agent-key.pem"
  ca_file: "/etc/vm-gateway/ca-cert.pem"
  
  # Heartbeat
  heartbeat_interval: 30s
  heartbeat_timeout: 10s
  
  # Message handling
  message_queue_max_size: 10000
  batch_size: 100
  batch_max_wait: 5s
  
  # Compression
  enable_compression: true
  compression_level: 6
  
  # Performance
  max_concurrent_requests: 10
  request_timeout: 30s
```

## Summary

The Agent-Controller communication protocol provides secure, reliable, and efficient bidirectional communication. Using WebSocket with mTLS ensures real-time updates with strong security guarantees, while offline queuing and automatic reconnection provide resilience.

Key features:

- **Secure**: mTLS authentication and TLS 1.3 encryption
- **Real-Time**: WebSocket for instant updates
- **Resilient**: Automatic reconnection and offline queuing
- **Efficient**: Message batching and compression
- **Reliable**: Message acknowledgment and deduplication

## Next Steps

- **[Installation](06-installation.md)**: Configure agent communication during installation
- **[API Reference](07-api-reference.md)**: Explore the complete agent API
