---
title: "Local Authentication"
section: "Authentication & Authorization"
order: 2
tags: ["authentication", "password", "security"]
last_updated: "2025-11-08"
---

# Local Authentication

Local authentication provides username/email and password-based authentication for users who don't use external identity providers. This is the foundational authentication method that supports direct user account management within the platform.

## Overview

Local authentication is implemented using industry-standard security practices including strong password hashing, breach detection, account lockout mechanisms, and comprehensive password policies. It serves as both a standalone authentication method and a fallback when SS<PERSON> is unavailable.

**Key Features:**
- Secure password storage with bcrypt or Argon2 hashing
- Configurable password complexity requirements
- Password breach detection via Have I Been Pwned API
- Account lockout after failed login attempts
- Password history tracking to prevent reuse
- Configurable password expiration
- Rate limiting on authentication attempts
- Email verification for new accounts
- Password reset workflows

## Password Storage

### Hashing Algorithms

The platform supports two industry-standard password hashing algorithms:

**Bcrypt (Default):**
- Work factor: 12 rounds (configurable)
- Automatic salt generation
- Resistant to rainbow table attacks
- Adaptive cost factor for future-proofing

**Argon2 (Recommended for high-security):**
- Argon2id variant (hybrid of Argon2i and Argon2d)
- Memory cost: 64 MB (configurable)
- Time cost: 3 iterations (configurable)
- Parallelism: 4 threads (configurable)
- Winner of Password Hashing Competition (2015)

### Implementation Example

```python
from passlib.context import CryptContext

# Password hashing context
pwd_context = CryptContext(
    schemes=["argon2", "bcrypt"],
    deprecated="auto",
    argon2__memory_cost=65536,  # 64 MB
    argon2__time_cost=3,
    argon2__parallelism=4,
    bcrypt__rounds=12
)

def hash_password(password: str) -> str:
    """Hash a password using the configured algorithm"""
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)
```


### Password Migration

When upgrading hashing algorithms, the platform automatically rehashes passwords on successful login:

```python
async def authenticate_user(username: str, password: str) -> Optional[User]:
    """Authenticate user and upgrade password hash if needed"""
    user = await get_user_by_username(username)
    if not user:
        return None
    
    # Verify password
    if not verify_password(password, user.hashed_password):
        await log_failed_login(user.id, "invalid_password")
        return None
    
    # Check if hash needs upgrade
    if pwd_context.needs_update(user.hashed_password):
        user.hashed_password = hash_password(password)
        await update_user(user)
        await log_event(user.id, "password_hash_upgraded")
    
    return user
```

## Password Policy

### Complexity Requirements

The platform enforces configurable password complexity rules:

**Default Requirements:**
- Minimum length: 12 characters
- Maximum length: 128 characters
- Must contain at least one uppercase letter (A-Z)
- Must contain at least one lowercase letter (a-z)
- Must contain at least one number (0-9)
- Must contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)
- Cannot contain username or email address
- Cannot contain common patterns (123456, password, qwerty, etc.)

**Configuration:**

```yaml
password_policy:
  min_length: 12
  max_length: 128
  require_uppercase: true
  require_lowercase: true
  require_numbers: true
  require_special: true
  forbidden_patterns:
    - "password"
    - "123456"
    - "qwerty"
    - "admin"
  check_username_similarity: true
  check_email_similarity: true
```

### Password Validation

```python
import re
from typing import List, Tuple

def validate_password(password: str, username: str, email: str) -> Tuple[bool, List[str]]:
    """
    Validate password against policy requirements
    Returns (is_valid, list_of_errors)
    """
    errors = []
    
    # Length check
    if len(password) < 12:
        errors.append("Password must be at least 12 characters long")
    if len(password) > 128:
        errors.append("Password must not exceed 128 characters")
    
    # Complexity checks
    if not re.search(r'[A-Z]', password):
        errors.append("Password must contain at least one uppercase letter")
    if not re.search(r'[a-z]', password):
        errors.append("Password must contain at least one lowercase letter")
    if not re.search(r'\d', password):
        errors.append("Password must contain at least one number")
    if not re.search(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]', password):
        errors.append("Password must contain at least one special character")
    
    # Username/email similarity
    if username.lower() in password.lower():
        errors.append("Password cannot contain your username")
    if email.split('@')[0].lower() in password.lower():
        errors.append("Password cannot contain your email address")
    
    # Common patterns
    forbidden = ["password", "123456", "qwerty", "admin", "letmein"]
    if any(pattern in password.lower() for pattern in forbidden):
        errors.append("Password contains a common pattern that is not allowed")
    
    return (len(errors) == 0, errors)
```

## Breach Detection

### Have I Been Pwned Integration

The platform integrates with the Have I Been Pwned (HIBP) API to check if passwords have been exposed in known data breaches:

```python
import hashlib
import httpx

async def check_password_breach(password: str) -> Tuple[bool, int]:
    """
    Check if password appears in known breaches
    Returns (is_breached, occurrence_count)
    """
    # Hash password with SHA-1
    sha1_hash = hashlib.sha1(password.encode('utf-8')).hexdigest().upper()
    prefix = sha1_hash[:5]
    suffix = sha1_hash[5:]
    
    # Query HIBP API with k-anonymity
    url = f"https://api.pwnedpasswords.com/range/{prefix}"
    async with httpx.AsyncClient() as client:
        response = await client.get(url)
        if response.status_code != 200:
            # Fail open - don't block if API is unavailable
            return (False, 0)
        
        # Check if our hash suffix appears in results
        for line in response.text.splitlines():
            hash_suffix, count = line.split(':')
            if hash_suffix == suffix:
                return (True, int(count))
    
    return (False, 0)
```

**Privacy Protection:**
- Uses k-anonymity model (only sends first 5 characters of hash)
- Password never sent to external service
- API calls are rate-limited
- Fails open if service unavailable (doesn't block legitimate users)

### Breach Response

When a breached password is detected:

1. **During Registration**: User is warned and must choose a different password
2. **During Login**: User is allowed to log in but forced to change password
3. **Periodic Checks**: Existing passwords checked against new breaches monthly
4. **Notification**: Users notified via email if their password appears in new breach

```python
async def handle_breached_password_login(user: User) -> LoginResponse:
    """Handle login with breached password"""
    # Allow login but mark for password change
    user.force_password_change = True
    user.password_change_reason = "breach_detected"
    await update_user(user)
    
    # Send notification
    await send_email(
        to=user.email,
        subject="Password Change Required",
        template="password_breach_notification",
        context={"user": user}
    )
    
    # Create session with limited permissions
    session = await create_session(user, restricted=True)
    
    return LoginResponse(
        success=True,
        session_token=session.token,
        force_password_change=True,
        message="Your password was found in a data breach. Please change it immediately."
    )
```


## Account Lockout

### Failed Login Protection

To prevent brute force attacks, the platform implements account lockout after repeated failed login attempts:

**Default Configuration:**
- Maximum failed attempts: 5
- Lockout duration: 15 minutes
- Lockout window: 5 minutes (failed attempts must occur within this window)
- Progressive lockout: Increases duration for repeated lockouts

```python
from datetime import datetime, timedelta
from typing import Optional

class LoginAttemptTracker:
    """Track and manage failed login attempts"""
    
    def __init__(self, redis_client):
        self.redis = redis_client
        self.max_attempts = 5
        self.lockout_duration = 900  # 15 minutes
        self.attempt_window = 300  # 5 minutes
    
    async def record_failed_attempt(self, username: str, ip_address: str) -> None:
        """Record a failed login attempt"""
        key = f"login_attempts:{username}"
        
        # Add attempt with timestamp
        await self.redis.zadd(key, {ip_address: datetime.utcnow().timestamp()})
        
        # Set expiration on key
        await self.redis.expire(key, self.attempt_window)
        
        # Check if lockout threshold reached
        attempts = await self.get_recent_attempts(username)
        if len(attempts) >= self.max_attempts:
            await self.lock_account(username)
    
    async def get_recent_attempts(self, username: str) -> List[str]:
        """Get failed attempts within the window"""
        key = f"login_attempts:{username}"
        cutoff = datetime.utcnow().timestamp() - self.attempt_window
        
        # Get attempts after cutoff time
        attempts = await self.redis.zrangebyscore(key, cutoff, '+inf')
        return attempts
    
    async def lock_account(self, username: str) -> None:
        """Lock account after too many failed attempts"""
        key = f"account_locked:{username}"
        lockout_count = await self.redis.get(f"lockout_count:{username}") or 0
        
        # Progressive lockout duration
        duration = self.lockout_duration * (2 ** int(lockout_count))
        duration = min(duration, 86400)  # Max 24 hours
        
        await self.redis.setex(key, duration, "locked")
        await self.redis.incr(f"lockout_count:{username}")
        
        # Log security event
        await log_security_event(
            event_type="account_locked",
            username=username,
            duration=duration,
            reason="too_many_failed_attempts"
        )
    
    async def is_locked(self, username: str) -> Tuple[bool, Optional[int]]:
        """Check if account is locked and return remaining time"""
        key = f"account_locked:{username}"
        ttl = await self.redis.ttl(key)
        
        if ttl > 0:
            return (True, ttl)
        return (False, None)
    
    async def clear_attempts(self, username: str) -> None:
        """Clear failed attempts after successful login"""
        await self.redis.delete(f"login_attempts:{username}")
        await self.redis.delete(f"lockout_count:{username}")
```

### Manual Unlock

Administrators can manually unlock accounts:

```python
async def unlock_account(admin_user: User, target_username: str) -> None:
    """Manually unlock a locked account"""
    # Verify admin has permission
    if not await has_permission(admin_user, "users.unlock"):
        raise PermissionDenied("You don't have permission to unlock accounts")
    
    # Clear lockout
    await redis.delete(f"account_locked:{target_username}")
    await redis.delete(f"login_attempts:{target_username}")
    await redis.delete(f"lockout_count:{target_username}")
    
    # Log admin action
    await log_audit_event(
        actor=admin_user.username,
        action="account_unlocked",
        target=target_username,
        reason="manual_unlock_by_admin"
    )
    
    # Notify user
    target_user = await get_user_by_username(target_username)
    await send_email(
        to=target_user.email,
        subject="Account Unlocked",
        template="account_unlocked",
        context={"user": target_user, "admin": admin_user.username}
    )
```

## Password History

### Preventing Password Reuse

The platform tracks password history to prevent users from reusing recent passwords:

```python
class PasswordHistory:
    """Manage password history for users"""
    
    def __init__(self, history_count: int = 10):
        self.history_count = history_count
    
    async def add_password(self, user_id: str, hashed_password: str) -> None:
        """Add password to history"""
        await db.execute(
            """
            INSERT INTO password_history (user_id, hashed_password, created_at)
            VALUES ($1, $2, NOW())
            """,
            user_id, hashed_password
        )
        
        # Keep only recent passwords
        await db.execute(
            """
            DELETE FROM password_history
            WHERE user_id = $1
            AND id NOT IN (
                SELECT id FROM password_history
                WHERE user_id = $1
                ORDER BY created_at DESC
                LIMIT $2
            )
            """,
            user_id, self.history_count
        )
    
    async def is_password_reused(self, user_id: str, new_password: str) -> bool:
        """Check if password was used recently"""
        history = await db.fetch(
            """
            SELECT hashed_password FROM password_history
            WHERE user_id = $1
            ORDER BY created_at DESC
            LIMIT $2
            """,
            user_id, self.history_count
        )
        
        for record in history:
            if verify_password(new_password, record['hashed_password']):
                return True
        
        return False
```

## Password Expiration

### Automatic Expiration

Passwords can be configured to expire after a certain period:

```python
async def check_password_expiration(user: User) -> bool:
    """Check if user's password has expired"""
    if not config.password_expiration_enabled:
        return False
    
    expiration_days = config.password_expiration_days
    password_age = (datetime.utcnow() - user.password_changed_at).days
    
    if password_age >= expiration_days:
        user.force_password_change = True
        user.password_change_reason = "expired"
        await update_user(user)
        
        await send_email(
            to=user.email,
            subject="Password Expired",
            template="password_expired",
            context={"user": user, "days": password_age}
        )
        
        return True
    
    # Warn user before expiration
    warning_days = 7
    if password_age >= (expiration_days - warning_days):
        days_remaining = expiration_days - password_age
        await send_email(
            to=user.email,
            subject=f"Password Expires in {days_remaining} Days",
            template="password_expiring_soon",
            context={"user": user, "days_remaining": days_remaining}
        )
    
    return False
```


## Login Flow

### Standard Login Process

```python
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

router = APIRouter()

class LoginRequest(BaseModel):
    username: str
    password: str
    remember_me: bool = False

class LoginResponse(BaseModel):
    success: bool
    session_token: Optional[str] = None
    requires_mfa: bool = False
    mfa_methods: List[str] = []
    force_password_change: bool = False
    message: Optional[str] = None

@router.post("/auth/login")
async def login(request: LoginRequest, ip_address: str = Depends(get_client_ip)):
    """Handle user login"""
    
    # Check if account is locked
    is_locked, remaining_time = await attempt_tracker.is_locked(request.username)
    if is_locked:
        raise HTTPException(
            status_code=423,
            detail=f"Account locked. Try again in {remaining_time} seconds."
        )
    
    # Authenticate user
    user = await authenticate_user(request.username, request.password)
    if not user:
        await attempt_tracker.record_failed_attempt(request.username, ip_address)
        await log_failed_login(request.username, ip_address, "invalid_credentials")
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    # Clear failed attempts
    await attempt_tracker.clear_attempts(request.username)
    
    # Check if account is disabled
    if not user.is_active:
        raise HTTPException(status_code=403, detail="Account is disabled")
    
    # Check password expiration
    if await check_password_expiration(user):
        return LoginResponse(
            success=True,
            force_password_change=True,
            message="Your password has expired. Please change it."
        )
    
    # Check if MFA is required
    if user.mfa_enabled:
        # Create temporary MFA session
        mfa_session = await create_mfa_session(user)
        return LoginResponse(
            success=True,
            requires_mfa=True,
            mfa_methods=user.mfa_methods,
            session_token=mfa_session.token
        )
    
    # Create full session
    session_duration = 2592000 if request.remember_me else 28800  # 30 days or 8 hours
    session = await create_session(user, duration=session_duration, ip_address=ip_address)
    
    # Log successful login
    await log_successful_login(user.id, ip_address)
    
    return LoginResponse(
        success=True,
        session_token=session.token
    )
```

### Rate Limiting

Implement rate limiting to prevent abuse:

```python
from fastapi import Request
from slowapi import Limiter
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

@router.post("/auth/login")
@limiter.limit("5/minute")  # 5 attempts per minute per IP
async def login(request: Request, login_data: LoginRequest):
    """Rate-limited login endpoint"""
    # ... login logic
```

## Password Reset

### Reset Request Flow

```python
class PasswordResetRequest(BaseModel):
    email: str

@router.post("/auth/password-reset/request")
@limiter.limit("3/hour")  # Prevent abuse
async def request_password_reset(request: PasswordResetRequest):
    """Request password reset email"""
    
    user = await get_user_by_email(request.email)
    
    # Always return success to prevent email enumeration
    response = {"message": "If an account exists, a reset email has been sent"}
    
    if not user:
        # Log potential enumeration attempt
        await log_security_event(
            event_type="password_reset_unknown_email",
            email=request.email
        )
        return response
    
    # Generate reset token
    reset_token = secrets.token_urlsafe(32)
    reset_token_hash = hashlib.sha256(reset_token.encode()).hexdigest()
    
    # Store token with expiration
    await redis.setex(
        f"password_reset:{reset_token_hash}",
        3600,  # 1 hour expiration
        user.id
    )
    
    # Send reset email
    reset_url = f"{config.base_url}/auth/password-reset?token={reset_token}"
    await send_email(
        to=user.email,
        subject="Password Reset Request",
        template="password_reset",
        context={"user": user, "reset_url": reset_url}
    )
    
    # Log reset request
    await log_audit_event(
        actor=user.username,
        action="password_reset_requested",
        ip_address=request.client.host
    )
    
    return response
```

### Reset Completion

```python
class PasswordResetComplete(BaseModel):
    token: str
    new_password: str

@router.post("/auth/password-reset/complete")
async def complete_password_reset(request: PasswordResetComplete):
    """Complete password reset with new password"""
    
    # Validate token
    token_hash = hashlib.sha256(request.token.encode()).hexdigest()
    user_id = await redis.get(f"password_reset:{token_hash}")
    
    if not user_id:
        raise HTTPException(status_code=400, detail="Invalid or expired reset token")
    
    user = await get_user_by_id(user_id)
    
    # Validate new password
    is_valid, errors = validate_password(request.new_password, user.username, user.email)
    if not is_valid:
        raise HTTPException(status_code=400, detail={"errors": errors})
    
    # Check breach
    is_breached, count = await check_password_breach(request.new_password)
    if is_breached:
        raise HTTPException(
            status_code=400,
            detail=f"This password has been exposed in {count} data breaches. Please choose a different password."
        )
    
    # Check password history
    if await password_history.is_password_reused(user.id, request.new_password):
        raise HTTPException(
            status_code=400,
            detail="You cannot reuse a recent password. Please choose a different password."
        )
    
    # Update password
    new_hash = hash_password(request.new_password)
    user.hashed_password = new_hash
    user.password_changed_at = datetime.utcnow()
    user.force_password_change = False
    await update_user(user)
    
    # Add to password history
    await password_history.add_password(user.id, new_hash)
    
    # Invalidate reset token
    await redis.delete(f"password_reset:{token_hash}")
    
    # Invalidate all existing sessions (force re-login)
    await invalidate_all_sessions(user.id)
    
    # Log password change
    await log_audit_event(
        actor=user.username,
        action="password_reset_completed",
        details="Password changed via reset flow"
    )
    
    # Send confirmation email
    await send_email(
        to=user.email,
        subject="Password Changed Successfully",
        template="password_changed",
        context={"user": user}
    )
    
    return {"message": "Password reset successful. Please log in with your new password."}
```

## Email Verification

### New Account Verification

```python
async def create_user_with_verification(
    username: str,
    email: str,
    password: str
) -> User:
    """Create new user and send verification email"""
    
    # Create user (inactive until verified)
    user = await create_user(
        username=username,
        email=email,
        password=hash_password(password),
        is_active=False,
        email_verified=False
    )
    
    # Generate verification token
    verification_token = secrets.token_urlsafe(32)
    token_hash = hashlib.sha256(verification_token.encode()).hexdigest()
    
    # Store token
    await redis.setex(
        f"email_verification:{token_hash}",
        86400,  # 24 hours
        user.id
    )
    
    # Send verification email
    verification_url = f"{config.base_url}/auth/verify-email?token={verification_token}"
    await send_email(
        to=user.email,
        subject="Verify Your Email Address",
        template="email_verification",
        context={"user": user, "verification_url": verification_url}
    )
    
    return user

@router.get("/auth/verify-email")
async def verify_email(token: str):
    """Verify user email address"""
    
    token_hash = hashlib.sha256(token.encode()).hexdigest()
    user_id = await redis.get(f"email_verification:{token_hash}")
    
    if not user_id:
        raise HTTPException(status_code=400, detail="Invalid or expired verification token")
    
    # Activate user
    user = await get_user_by_id(user_id)
    user.is_active = True
    user.email_verified = True
    user.email_verified_at = datetime.utcnow()
    await update_user(user)
    
    # Delete token
    await redis.delete(f"email_verification:{token_hash}")
    
    # Log verification
    await log_audit_event(
        actor=user.username,
        action="email_verified"
    )
    
    return {"message": "Email verified successfully. You can now log in."}
```

## Security Best Practices

### Implementation Guidelines

1. **Always hash passwords** - Never store plain text passwords
2. **Use timing-safe comparisons** - Prevent timing attacks on password verification
3. **Implement rate limiting** - Prevent brute force attacks
4. **Log security events** - Track failed logins, lockouts, password changes
5. **Use HTTPS only** - Never transmit credentials over unencrypted connections
6. **Validate input** - Sanitize and validate all user input
7. **Fail securely** - Don't leak information in error messages
8. **Monitor for anomalies** - Detect unusual login patterns
9. **Educate users** - Provide guidance on creating strong passwords
10. **Regular security audits** - Review and update security measures

### Common Vulnerabilities to Avoid

- **SQL Injection**: Use parameterized queries
- **Timing Attacks**: Use constant-time comparison for passwords
- **Session Fixation**: Regenerate session ID after login
- **Credential Stuffing**: Implement rate limiting and breach detection
- **Password Spraying**: Track failed attempts across all accounts
- **Account Enumeration**: Return generic messages for invalid users
- **Brute Force**: Implement account lockout and CAPTCHA

## Related Documentation

- [Authentication Overview](./01-overview.md) - System architecture and concepts
- [Multi-Factor Authentication](./03-mfa.md) - MFA implementation
- [Session Management](./06-session-management.md) - Session handling
- [API Authentication](./05-api-auth.md) - API authentication methods
