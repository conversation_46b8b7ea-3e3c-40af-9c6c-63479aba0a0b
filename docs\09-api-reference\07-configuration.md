---
title: "Configuration API"
section: "API Reference"
order: 7
tags: ["api", "configuration", "settings", "endpoints"]
last_updated: "2025-11-08"
---

# Configuration API

The Configuration API provides endpoints for managing platform settings, agent configurations, feature flags, and system preferences. This API allows administrators to configure the platform programmatically and maintain configuration as code.

## Base URL

```
https://your-gateway.example.com/api/v1/config
```

## Configuration Scopes

Configuration settings are organized into scopes:

1. **Global**: Platform-wide settings affecting all components
2. **VM**: Per-VM agent configuration
3. **Service**: Per-service settings and overrides
4. **User**: User-specific preferences
5. **Feature Flags**: Feature toggle configuration

## Endpoints

### GET /config/global

Get global platform configuration.

**Request:**

```http
GET /api/v1/config/global HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "config": {
    "platform": {
      "name": "VM Gateway",
      "version": "a.1.0-15",
      "environment": "production",
      "timezone": "UTC"
    },
    "authentication": {
      "session_timeout": 28800,
      "absolute_timeout": 86400,
      "max_concurrent_sessions": 5,
      "mfa_enforcement": "admins_only",
      "password_policy": {
        "min_length": 12,
        "require_uppercase": true,
        "require_lowercase": true,
        "require_numbers": true,
        "require_special": true,
        "check_breaches": true,
        "history_count": 10,
        "expiration_days": 90
      }
    },
    "connections": {
      "default_duration": 28800,
      "max_duration": 86400,
      "max_concurrent_per_user": 10,
      "tunnel_protocol": "wireguard",
      "tunnel_port": 51820
    },
    "monitoring": {
      "metrics_retention_days": 7,
      "logs_retention_days": 30,
      "audit_logs_retention_days": 365,
      "health_check_interval": 60
    },
    "notifications": {
      "email_enabled": true,
      "slack_enabled": true,
      "webhook_enabled": true
    }
  },
  "last_updated": "2025-11-08T10:00:00Z",
  "updated_by": "usr_admin123"
}
```

---

### PATCH /config/global

Update global platform configuration (admin only).

**Request:**

```http
PATCH /api/v1/config/global HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "authentication": {
    "session_timeout": 14400,
    "mfa_enforcement": "all_users"
  },
  "connections": {
    "default_duration": 14400,
    "max_concurrent_per_user": 5
  }
}
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "config": {
    "authentication": {
      "session_timeout": 14400,
      "mfa_enforcement": "all_users"
    },
    "connections": {
      "default_duration": 14400,
      "max_concurrent_per_user": 5
    }
  },
  "message": "Configuration updated successfully",
  "updated_at": "2025-11-08T15:40:00Z"
}
```

---

### GET /config/vms/{vm_id}

Get agent configuration for a specific VM.

**Request:**

```http
GET /api/v1/config/vms/vm_abc123 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "vm_id": "vm_abc123",
  "config": {
    "agent": {
      "scan_interval": 60,
      "metrics_interval": 30,
      "log_level": "INFO",
      "max_cpu_percent": 10,
      "max_memory_mb": 512
    },
    "discovery": {
      "enabled": true,
      "scan_ports": "1-65535",
      "exclude_ports": [],
      "protocol_detection": true,
      "deep_inspection": false
    },
    "classification": {
      "use_ml": false,
      "custom_rules": [
        {
          "port": 9999,
          "process": "custom-app",
          "name": "Internal API",
          "type": "api"
        }
      ]
    },
    "health_checks": {
      "enabled": true,
      "interval": 60,
      "timeout": 5,
      "retries": 3
    }
  },
  "last_updated": "2025-11-08T14:00:00Z"
}
```

---

### PATCH /config/vms/{vm_id}

Update agent configuration for a specific VM.

**Request:**

```http
PATCH /api/v1/config/vms/vm_abc123 HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "agent": {
    "scan_interval": 30,
    "log_level": "DEBUG"
  },
  "discovery": {
    "deep_inspection": true
  }
}
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "vm_id": "vm_abc123",
  "config": {
    "agent": {
      "scan_interval": 30,
      "log_level": "DEBUG"
    },
    "discovery": {
      "deep_inspection": true
    }
  },
  "message": "VM configuration updated successfully",
  "updated_at": "2025-11-08T15:45:00Z",
  "restart_required": true
}
```

---

### GET /config/feature-flags

List all feature flags and their current states.

**Request:**

```http
GET /api/v1/config/feature-flags HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "feature_flags": [
    {
      "key": "advanced_monitoring",
      "name": "Advanced Monitoring",
      "description": "Enhanced metrics collection and visualization",
      "enabled": true,
      "type": "boolean",
      "environments": ["staging", "production"],
      "created_at": "2025-10-01T10:00:00Z",
      "updated_at": "2025-11-01T12:00:00Z"
    },
    {
      "key": "new_dashboard",
      "name": "New Dashboard UI",
      "description": "Redesigned dashboard interface",
      "enabled": false,
      "type": "percentage",
      "rollout_percentage": 25,
      "target_roles": ["beta_testers"],
      "environments": ["staging"],
      "created_at": "2025-11-01T10:00:00Z",
      "updated_at": "2025-11-08T10:00:00Z"
    },
    {
      "key": "ml_classification",
      "name": "ML-Based Service Classification",
      "description": "Use machine learning for service classification",
      "enabled": true,
      "type": "boolean",
      "environments": ["development"],
      "expires_at": "2025-12-31T23:59:59Z",
      "created_at": "2025-10-15T10:00:00Z",
      "updated_at": "2025-10-15T10:00:00Z"
    }
  ],
  "total": 3
}
```

---

### PATCH /config/feature-flags/{flag_key}

Update a feature flag configuration.

**Request:**

```http
PATCH /api/v1/config/feature-flags/new_dashboard HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "enabled": true,
  "rollout_percentage": 50,
  "environments": ["staging", "production"]
}
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "feature_flag": {
    "key": "new_dashboard",
    "enabled": true,
    "rollout_percentage": 50,
    "environments": ["staging", "production"],
    "updated_at": "2025-11-08T15:50:00Z"
  },
  "message": "Feature flag updated successfully"
}
```

---

### GET /config/user

Get current user's preferences.

**Request:**

```http
GET /api/v1/config/user HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "preferences": {
    "theme": "dark",
    "language": "en",
    "timezone": "America/New_York",
    "date_format": "YYYY-MM-DD",
    "time_format": "24h",
    "dashboard_refresh_interval": 30,
    "notifications": {
      "email": true,
      "slack": true,
      "browser": true
    },
    "default_views": {
      "vms_per_page": 50,
      "services_per_page": 50,
      "connections_per_page": 25
    }
  }
}
```

---

### PATCH /config/user

Update current user's preferences.

**Request:**

```http
PATCH /api/v1/config/user HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "theme": "light",
  "timezone": "America/Los_Angeles",
  "dashboard_refresh_interval": 60
}
```

**Response:**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "preferences": {
    "theme": "light",
    "timezone": "America/Los_Angeles",
    "dashboard_refresh_interval": 60
  },
  "message": "Preferences updated successfully"
}
```

---

### GET /config/export

Export all configuration as code (admin only).

**Request:**

```http
GET /api/v1/config/export?format=yaml HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `format` | string | Export format: `json`, `yaml`, `toml` | `json` |
| `scope` | string | Configuration scope: `global`, `vms`, `feature_flags`, `all` | `all` |

**Response (YAML format):**

```http
HTTP/1.1 200 OK
Content-Type: application/x-yaml
Content-Disposition: attachment; filename="vm-gateway-config-2025-11-08.yaml"

platform:
  name: VM Gateway
  version: a.1.0-15
  environment: production
  timezone: UTC

authentication:
  session_timeout: 28800
  absolute_timeout: 86400
  max_concurrent_sessions: 5
  mfa_enforcement: admins_only
  password_policy:
    min_length: 12
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_special: true

connections:
  default_duration: 28800
  max_duration: 86400
  max_concurrent_per_user: 10
  tunnel_protocol: wireguard
  tunnel_port: 51820

feature_flags:
  advanced_monitoring:
    enabled: true
    environments: [staging, production]
  new_dashboard:
    enabled: false
    rollout_percentage: 25
    target_roles: [beta_testers]
```

---

### POST /config/import

Import configuration from file (admin only).

**Request:**

```http
POST /api/v1/config/import HTTP/1.1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: multipart/form-data

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="config.yaml"
Content-Type: application/x-yaml

[configuration content]
------WebKitFormBoundary
Content-Disposition: form-data; name="dry_run"

true
------WebKitFormBoundary--
```

**Response (Dry Run):**

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "dry_run": true,
  "changes": {
    "global": {
      "modified": ["authentication.session_timeout", "connections.default_duration"],
      "added": [],
      "removed": []
    },
    "feature_flags": {
      "modified": ["new_dashboard.rollout_percentage"],
      "added": ["experimental_feature"],
      "removed": []
    }
  },
  "validation": {
    "valid": true,
    "errors": [],
    "warnings": [
      "Feature flag 'experimental_feature' does not exist and will be created"
    ]
  },
  "message": "Configuration validated successfully (dry run)"
}
```

---

## Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `CONFIG_NOT_FOUND` | 404 | Configuration key does not exist |
| `INVALID_CONFIG_VALUE` | 400 | Configuration value is invalid |
| `CONFIG_VALIDATION_FAILED` | 400 | Configuration failed validation |
| `INSUFFICIENT_PERMISSIONS` | 403 | User lacks permission to modify configuration |
| `CONFIG_LOCKED` | 423 | Configuration is locked and cannot be modified |

## Code Examples

### Python

```python
import requests

headers = {"Authorization": "Bearer YOUR_ACCESS_TOKEN"}

# Get global configuration
response = requests.get(
    "https://your-gateway.example.com/api/v1/config/global",
    headers=headers
)

config = response.json()["config"]
print(f"Session timeout: {config['authentication']['session_timeout']} seconds")

# Update configuration
response = requests.patch(
    "https://your-gateway.example.com/api/v1/config/global",
    headers=headers,
    json={
        "authentication": {
            "session_timeout": 14400
        }
    }
)

print(response.json()["message"])

# Export configuration
response = requests.get(
    "https://your-gateway.example.com/api/v1/config/export",
    headers=headers,
    params={"format": "yaml"}
)

with open("vm-gateway-config.yaml", "w") as f:
    f.write(response.text)
```

### JavaScript

```javascript
const headers = {
  'Authorization': 'Bearer YOUR_ACCESS_TOKEN',
  'Content-Type': 'application/json'
};

// Get feature flags
const response = await fetch(
  'https://your-gateway.example.com/api/v1/config/feature-flags',
  { headers }
);

const data = await response.json();
data.feature_flags.forEach(flag => {
  console.log(`${flag.name}: ${flag.enabled ? 'enabled' : 'disabled'}`);
});

// Update feature flag
await fetch(
  'https://your-gateway.example.com/api/v1/config/feature-flags/new_dashboard',
  {
    method: 'PATCH',
    headers,
    body: JSON.stringify({
      enabled: true,
      rollout_percentage: 50
    })
  }
);
```

## Related Documentation

- [API Overview](./01-overview.md) - API design principles
- [Feature Gating](../01-overview/04-feature-gating.md) - Feature flag system
- [Agent Installation](../03-agent/06-installation.md) - Agent configuration
