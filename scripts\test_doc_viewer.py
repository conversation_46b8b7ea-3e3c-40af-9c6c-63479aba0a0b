#!/usr/bin/env python3
"""
Test script for documentation viewer
Tests all required functionality for task 019
"""

import sys
import requests
import json
from typing import Dict, List, Tuple

# Test configuration
BASE_URL = "http://localhost:8080"
TESTS_PASSED = 0
TESTS_FAILED = 0

def print_test(name: str, passed: bool, message: str = ""):
    """Print test result"""
    global TESTS_PASSED, TESTS_FAILED
    
    status = "✓ PASS" if passed else "✗ FAIL"
    print(f"{status}: {name}")
    if message:
        print(f"  {message}")
    
    if passed:
        TESTS_PASSED += 1
    else:
        TESTS_FAILED += 1

def test_main_page():
    """Test 1: Documentation viewer accessibility"""
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        passed = response.status_code == 200 and "VM Gateway Documentation" in response.text
        print_test("Main page loads", passed, f"Status: {response.status_code}")
        
        # Check for responsive meta tag
        has_viewport = 'name="viewport"' in response.text
        print_test("Responsive viewport meta tag present", has_viewport)
        
        return passed
    except Exception as e:
        print_test("Main page loads", False, str(e))
        return False

def test_api_list():
    """Test 2: API endpoint for listing documentation"""
    try:
        response = requests.get(f"{BASE_URL}/api/docs/list", timeout=5)
        passed = response.status_code == 200
        
        if passed:
            data = response.json()
            has_sections = "sections" in data
            section_count = data.get("total_sections", 0)
            file_count = data.get("total_files", 0)
            
            print_test("API /api/docs/list works", True, 
                      f"Sections: {section_count}, Files: {file_count}")
            print_test("Has sections data", has_sections)
            print_test("Has 10 sections", section_count == 10)
            print_test("Has documentation files", file_count > 0)
        else:
            print_test("API /api/docs/list works", False, f"Status: {response.status_code}")
        
        return passed
    except Exception as e:
        print_test("API /api/docs/list works", False, str(e))
        return False

def test_document_retrieval():
    """Test 3: Document retrieval and markdown rendering"""
    try:
        response = requests.get(
            f"{BASE_URL}/api/docs/01-overview/01-introduction.md",
            timeout=5
        )
        passed = response.status_code == 200
        
        if passed:
            data = response.json()
            has_html = "html" in data
            has_metadata = "metadata" in data
            
            print_test("Document retrieval works", True)
            print_test("Document has HTML content", has_html)
            print_test("Document has metadata", has_metadata)
            
            if has_html:
                html = data["html"]
                has_headers = "<h1" in html or "<h2" in html
                has_links = "<a" in html
                print_test("Markdown rendered to HTML", has_headers)
                print_test("HTML has links", has_links)
        else:
            print_test("Document retrieval works", False, f"Status: {response.status_code}")
        
        return passed
    except Exception as e:
        print_test("Document retrieval works", False, str(e))
        return False

def test_search():
    """Test 4: Search functionality"""
    try:
        response = requests.get(
            f"{BASE_URL}/api/docs/search?q=authentication",
            timeout=5
        )
        passed = response.status_code == 200
        
        if passed:
            data = response.json()
            has_results = "results" in data
            result_count = len(data.get("results", []))
            
            print_test("Search endpoint works", True, f"Found {result_count} results")
            print_test("Search returns results", result_count > 0)
            
            if result_count > 0:
                first_result = data["results"][0]
                has_file = "file" in first_result
                has_section = "section" in first_result
                has_snippet = "snippet" in first_result
                
                print_test("Search results have file info", has_file)
                print_test("Search results have section info", has_section)
                print_test("Search results have snippets", has_snippet)
        else:
            print_test("Search endpoint works", False, f"Status: {response.status_code}")
        
        return passed
    except Exception as e:
        print_test("Search endpoint works", False, str(e))
        return False

def test_navigation():
    """Test 5: Navigation between sections"""
    try:
        # Test multiple documents from different sections
        docs_to_test = [
            "01-overview/01-introduction.md",
            "02-architecture/01-system-overview.md",
            "03-agent/01-overview.md",
        ]
        
        all_passed = True
        for doc in docs_to_test:
            response = requests.get(f"{BASE_URL}/api/docs/{doc}", timeout=5)
            if response.status_code != 200:
                all_passed = False
                break
        
        print_test("Navigation between sections works", all_passed)
        return all_passed
    except Exception as e:
        print_test("Navigation between sections works", False, str(e))
        return False

def test_static_assets():
    """Test 6: Static assets (CSS, JS)"""
    try:
        css_response = requests.get(f"{BASE_URL}/static/css/styles.css", timeout=5)
        js_app_response = requests.get(f"{BASE_URL}/static/js/app.js", timeout=5)
        js_nav_response = requests.get(f"{BASE_URL}/static/js/nav.js", timeout=5)
        js_search_response = requests.get(f"{BASE_URL}/static/js/search.js", timeout=5)
        
        css_ok = css_response.status_code == 200
        js_app_ok = js_app_response.status_code == 200
        js_nav_ok = js_nav_response.status_code == 200
        js_search_ok = js_search_response.status_code == 200
        
        print_test("CSS file loads", css_ok)
        print_test("app.js loads", js_app_ok)
        print_test("nav.js loads", js_nav_ok)
        print_test("search.js loads", js_search_ok)
        
        # Check for responsive CSS
        if css_ok:
            css_content = css_response.text
            has_media_queries = "@media" in css_content
            print_test("CSS has media queries (responsive)", has_media_queries)
        
        return css_ok and js_app_ok and js_nav_ok and js_search_ok
    except Exception as e:
        print_test("Static assets load", False, str(e))
        return False

def test_syntax_highlighting():
    """Test 7: Syntax highlighting support"""
    try:
        # Get a document that should have code blocks
        response = requests.get(
            f"{BASE_URL}/api/docs/10-development/01-setup.md",
            timeout=5
        )
        
        if response.status_code == 200:
            data = response.json()
            html = data.get("html", "")
            
            # Check for code block elements
            has_code_blocks = "<code>" in html or "<pre>" in html
            has_highlight_class = "highlight" in html or "language-" in html
            
            print_test("Document has code blocks", has_code_blocks)
            print_test("Code blocks have syntax highlighting classes", has_highlight_class)
            
            return has_code_blocks
        else:
            print_test("Syntax highlighting test", False, f"Status: {response.status_code}")
            return False
    except Exception as e:
        print_test("Syntax highlighting test", False, str(e))
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("Documentation Viewer Test Suite")
    print("=" * 60)
    print()
    
    print("Testing documentation viewer functionality...")
    print()
    
    # Run all tests
    test_main_page()
    print()
    
    test_api_list()
    print()
    
    test_document_retrieval()
    print()
    
    test_search()
    print()
    
    test_navigation()
    print()
    
    test_static_assets()
    print()
    
    test_syntax_highlighting()
    print()
    
    # Print summary
    print("=" * 60)
    print(f"Tests Passed: {TESTS_PASSED}")
    print(f"Tests Failed: {TESTS_FAILED}")
    print(f"Total Tests: {TESTS_PASSED + TESTS_FAILED}")
    print("=" * 60)
    
    # Exit with appropriate code
    sys.exit(0 if TESTS_FAILED == 0 else 1)

if __name__ == "__main__":
    main()
