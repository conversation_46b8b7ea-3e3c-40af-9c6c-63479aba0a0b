# Multi-VM Deployment

Deploy the VM Gateway platform across multiple virtual machines for improved performance, scalability, and fault tolerance.

## Architecture

```
┌──────────────────────────────────────────────────────────┐
│                    Load Balancer                         │
│              (HAProxy / Nginx / Cloud LB)                │
└────────────┬─────────────────────────────┬───────────────┘
             │                             │
┌────────────▼──────────┐     ┌───────────▼──────────────┐
│  Controller VM 1      │     │  Controller VM 2         │
│  - Web Interface      │     │  - Web Interface         │
│  - API Server         │     │  - API Server            │
│  - WebSocket          │     │  - WebSocket             │
│  IP: *********        │     │  IP: *********           │
└────────────┬──────────┘     └───────────┬──────────────┘
             │                            │
             └──────────┬─────────────────┘
                        │
           ┌────────────▼────────────┐
           │  Database VM (Primary)  │
           │  PostgreSQL 15          │
           │  IP: *********          │
           └────────────┬────────────┘
                        │
        ┌───────────────┼───────────────┐
        │               │               │
┌───────▼──────┐ ┌─────▼──────┐ ┌─────▼──────┐
│ DB Replica 1 │ │ DB Replica2│ │ Redis VM   │
│ *********    │ │ *********  │ │ *********  │
└──────────────┘ └────────────┘ └────────────┘
```

## VM Specifications

### Controller VMs

**Minimum**:
- 4 vCPUs
- 8 GB RAM
- 100 GB SSD
- Ubuntu 22.04 LTS

**Recommended**:
- 8 vCPUs
- 16 GB RAM
- 200 GB SSD
- Ubuntu 22.04 LTS

### Database VM (Primary)

**Minimum**:
- 4 vCPUs
- 16 GB RAM
- 500 GB SSD (high IOPS)
- Ubuntu 22.04 LTS

**Recommended**:
- 8 vCPUs
- 32 GB RAM
- 1 TB NVMe SSD
- Ubuntu 22.04 LTS

### Redis VM

**Minimum**:
- 2 vCPUs
- 4 GB RAM
- 50 GB SSD
- Ubuntu 22.04 LTS

## Installation Steps

### 1. Prepare VMs

```bash
# On all VMs
sudo apt update && sudo apt upgrade -y
sudo apt install -y python3.11 python3-pip git

# Set hostnames
sudo hostnamectl set-hostname controller-01  # On controller 1
sudo hostnamectl set-hostname controller-02  # On controller 2
sudo hostnamectl set-hostname database-01    # On database
sudo hostnamectl set-hostname redis-01       # On Redis
```

### 2. Install PostgreSQL (Database VM)

```bash
# Install PostgreSQL
sudo apt install -y postgresql-15 postgresql-contrib-15

# Configure PostgreSQL
sudo -u postgres psql <<EOF
CREATE DATABASE vm_gateway;
CREATE USER vm_gateway WITH ENCRYPTED PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE vm_gateway TO vm_gateway;
EOF

# Configure remote access
sudo nano /etc/postgresql/15/main/postgresql.conf
# Set: listen_addresses = '*'

sudo nano /etc/postgresql/15/main/pg_hba.conf
# Add: host all all 10.0.0.0/8 md5

sudo systemctl restart postgresql
```

### 3. Install Redis (Redis VM)

```bash
# Install Redis
sudo apt install -y redis-server

# Configure Redis
sudo nano /etc/redis/redis.conf
# Set: bind 0.0.0.0
# Set: requirepass your_redis_password

sudo systemctl restart redis-server
```

### 4. Install Controller (Controller VMs)

```bash
# Clone repository
git clone https://github.com/your-org/vm-gateway.git
cd vm-gateway

# Install dependencies
pip3 install -r requirements.txt

# Configure environment
cp .env.example .env
nano .env
# Set DATABASE_URL, REDIS_URL, etc.

# Run database migrations (on one controller only)
python3 -m alembic upgrade head

# Start controller
sudo systemctl enable vm-gateway-controller
sudo systemctl start vm-gateway-controller
```

### 5. Configure Load Balancer

**HAProxy Configuration**:
```
# /etc/haproxy/haproxy.cfg
frontend http_front
    bind *:80
    bind *:443 ssl crt /etc/ssl/certs/vm-gateway.pem
    default_backend http_back

backend http_back
    balance roundrobin
    option httpchk GET /health
    server controller1 *********:80 check
    server controller2 *********:80 check
```

## Network Configuration

### Firewall Rules

**Controller VMs**:
```bash
sudo ufw allow 22/tcp   # SSH
sudo ufw allow 80/tcp   # HTTP
sudo ufw allow 443/tcp  # HTTPS
sudo ufw allow 8080/tcp # WebSocket
sudo ufw enable
```

**Database VM**:
```bash
sudo ufw allow 22/tcp
sudo ufw allow from ********/24 to any port 5432
sudo ufw enable
```

**Redis VM**:
```bash
sudo ufw allow 22/tcp
sudo ufw allow from ********/24 to any port 6379
sudo ufw enable
```

### DNS Configuration

```
vm-gateway.example.com    A    <load_balancer_ip>
controller-01.internal    A    *********
controller-02.internal    A    *********
database-01.internal      A    *********
redis-01.internal         A    *********
```

## High Availability Setup

### PostgreSQL Replication

**On Primary**:
```sql
-- Create replication user
CREATE USER replicator WITH REPLICATION ENCRYPTED PASSWORD 'repl_password';

-- Configure pg_hba.conf
-- Add: host replication replicator ********/24 md5
```

**On Replicas**:
```bash
# Stop PostgreSQL
sudo systemctl stop postgresql

# Remove data directory
sudo rm -rf /var/lib/postgresql/15/main/*

# Create base backup
sudo -u postgres pg_basebackup -h ********* -D /var/lib/postgresql/15/main -U replicator -P -v -R

# Start PostgreSQL
sudo systemctl start postgresql
```

### Redis Sentinel

```bash
# Install Sentinel on 3 VMs
sudo apt install -y redis-sentinel

# Configure Sentinel
sudo nano /etc/redis/sentinel.conf
# Add:
sentinel monitor mymaster ********* 6379 2
sentinel auth-pass mymaster your_redis_password
sentinel down-after-milliseconds mymaster 5000
sentinel parallel-syncs mymaster 1
sentinel failover-timeout mymaster 10000

sudo systemctl start redis-sentinel
```

## Monitoring

### Install Prometheus

```bash
# On monitoring VM
wget https://github.com/prometheus/prometheus/releases/download/v2.45.0/prometheus-2.45.0.linux-amd64.tar.gz
tar xvfz prometheus-*.tar.gz
cd prometheus-*

# Configure prometheus.yml
cat > prometheus.yml <<EOF
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'vm-gateway'
    static_configs:
      - targets:
        - '*********:9090'
        - '*********:9090'
EOF

./prometheus --config.file=prometheus.yml
```

## Backup Strategy

### Database Backups

```bash
# Daily backup script
#!/bin/bash
BACKUP_DIR=/backups/postgres
DATE=$(date +%Y%m%d_%H%M%S)

pg_dump -h ********* -U vm_gateway vm_gateway | \
  gzip > $BACKUP_DIR/vm_gateway_$DATE.sql.gz

# Keep last 30 days
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
```

### Configuration Backups

```bash
# Backup configuration files
tar czf /backups/config_$(date +%Y%m%d).tar.gz \
  /etc/vm-gateway/ \
  /etc/haproxy/ \
  /etc/postgresql/
```

## Best Practices

1. **Separate networks**: Use VLANs for different tiers
2. **Automated provisioning**: Use Ansible/Terraform
3. **Configuration management**: Version control all configs
4. **Monitoring**: Deploy comprehensive monitoring
5. **Backups**: Automated daily backups with testing
6. **Security**: Regular security updates
7. **Documentation**: Maintain runbooks
8. **Disaster recovery**: Test recovery procedures

## Next Steps

- [High Availability](07-high-availability.md) - HA configuration
- [Backup Recovery](08-backup-recovery.md) - Backup strategies
- [DNS Routing](05-dns-routing.md) - Domain configuration
- [TLS Certificates](06-tls-certificates.md) - Certificate management
