---
title: "Pricing & Feature Tiers"
section: "Overview"
order: 6
tags: ["pricing", "features", "tiers", "licensing"]
last_updated: "2025-11-09"
---

# Pricing & Feature Tiers

## Overview

VM Gateway is a self-hosted, open-source platform with a flexible feature tier system. Unlike commercial SaaS solutions that charge per-user or per-connection, VM Gateway's pricing model is based on feature access, allowing you to scale your deployment without worrying about user count limitations.

## Licensing Model

VM Gateway uses a **feature-based licensing model** where:

- **Core platform is free and open-source** (MIT License)
- **Advanced features** are unlocked through feature tiers
- **No per-user fees** - unlimited users at any tier
- **No per-VM fees** - monitor as many VMs as you need
- **Self-hosted** - you control your data and infrastructure
- **Perpetual licenses** - pay once, use forever (with optional support/updates subscription)

## Feature Tiers

### Community Edition (Free)

**Perfect for**: Small teams, personal projects, development environments

**Price**: Free forever

**Features**:
- ✅ Unlimited VMs (up to 25)
- ✅ Unlimited users
- ✅ Automatic service discovery
- ✅ Basic service classification
- ✅ Web-based dashboard
- ✅ Local authentication
- ✅ Basic RBAC (roles and permissions)
- ✅ Service catalog
- ✅ HTTP/HTTPS proxy
- ✅ Desktop client (basic)
- ✅ Basic metrics collection
- ✅ Simple alerting
- ✅ Audit logging (30 days retention)
- ✅ Community support (forums, GitHub issues)
- ✅ Documentation access

**Limitations**:
- ❌ No SSO/SAML integration
- ❌ No advanced approval workflows
- ❌ No secrets management integration
- ❌ No high availability
- ❌ No advanced monitoring
- ❌ No compliance reporting
- ❌ Limited to 25 VMs
- ❌ 30-day audit log retention

**Use Cases**:
- Development and testing environments
- Small startups (< 25 VMs)
- Personal projects
- Proof of concept deployments
- Learning and evaluation

---

### Professional Edition

**Perfect for**: Growing companies, production deployments, teams requiring advanced security

**Price**: $2,999/year (perpetual license: $9,999 one-time)

**Everything in Community, plus**:
- ✅ Unlimited VMs
- ✅ SSO integration (SAML 2.0, OAuth 2.0, OIDC)
- ✅ LDAP/Active Directory integration
- ✅ Advanced RBAC with conditional access
- ✅ Multi-level approval workflows
- ✅ Time-based access controls
- ✅ IP whitelisting and geofencing
- ✅ Advanced service classification (ML-powered)
- ✅ Secrets management integration (Vault, AWS Secrets Manager, Azure Key Vault)
- ✅ Advanced metrics and monitoring
- ✅ Custom dashboards
- ✅ Advanced alerting with integrations (Slack, PagerDuty, email)
- ✅ Audit logging (1 year retention)
- ✅ Compliance reporting (SOC 2, HIPAA, PCI-DSS templates)
- ✅ API access with rate limiting
- ✅ Webhook support
- ✅ Priority email support (48-hour response)
- ✅ Quarterly security updates

**Additional Features**:
- 🔐 **Enhanced Security**: Device trust, session management, anomaly detection
- 📊 **Advanced Analytics**: Historical trends, capacity planning, usage reports
- 🔄 **Integrations**: Jira, ServiceNow, Splunk, Datadog
- 🎯 **Custom Policies**: Fine-grained access control with policy engine
- 📱 **Mobile App**: iOS and Android clients for on-the-go access
- 🔔 **Smart Alerting**: ML-powered anomaly detection and intelligent alert routing

**Use Cases**:
- Production environments
- Companies with 25-500 VMs
- Teams requiring SSO and advanced security
- Compliance-driven organizations
- Multi-team deployments

---

### Enterprise Edition

**Perfect for**: Large organizations, multi-region deployments, mission-critical infrastructure

**Price**: $14,999/year (perpetual license: $49,999 one-time)

**Everything in Professional, plus**:
- ✅ High availability (multi-controller deployment)
- ✅ Geographic distribution and load balancing
- ✅ Database replication and failover
- ✅ Advanced disaster recovery
- ✅ Multi-tenancy support
- ✅ White-labeling and custom branding
- ✅ Advanced compliance reporting (custom frameworks)
- ✅ Audit logging (unlimited retention)
- ✅ Dedicated account manager
- ✅ 24/7 phone and email support (4-hour response)
- ✅ On-site training and onboarding
- ✅ Custom feature development (limited hours)
- ✅ Architecture review and optimization
- ✅ Quarterly business reviews
- ✅ Early access to new features

**Additional Features**:
- 🏢 **Multi-Tenancy**: Isolate customers/departments with dedicated environments
- 🌍 **Global Deployment**: Multi-region support with intelligent routing
- 🔒 **Advanced Security**: Hardware security module (HSM) support, FIPS 140-2 compliance
- 📈 **Enterprise Analytics**: Custom reports, data export, BI tool integration
- 🤝 **Professional Services**: Migration assistance, custom integrations, training
- 🎨 **Customization**: White-labeling, custom UI themes, branded mobile apps
- 🔧 **Advanced Configuration**: Custom classification rules, policy templates, automation

**Use Cases**:
- Large enterprises (500+ VMs)
- Multi-region deployments
- Highly regulated industries (finance, healthcare, government)
- MSPs and service providers
- Mission-critical infrastructure

---

## Feature Comparison Matrix

| Feature | Community | Professional | Enterprise |
|---------|-----------|--------------|------------|
| **Capacity** |
| Maximum VMs | 25 | Unlimited | Unlimited |
| Maximum Users | Unlimited | Unlimited | Unlimited |
| Maximum Services | Unlimited | Unlimited | Unlimited |
| **Authentication** |
| Local Authentication | ✅ | ✅ | ✅ |
| Multi-Factor Authentication (MFA) | ✅ | ✅ | ✅ |
| SSO (SAML, OAuth, OIDC) | ❌ | ✅ | ✅ |
| LDAP/Active Directory | ❌ | ✅ | ✅ |
| Custom Authentication Providers | ❌ | ❌ | ✅ |
| **Access Control** |
| Basic RBAC | ✅ | ✅ | ✅ |
| Conditional Access | ❌ | ✅ | ✅ |
| Approval Workflows | ❌ | ✅ | ✅ |
| Time-Based Access | ❌ | ✅ | ✅ |
| IP Whitelisting | ❌ | ✅ | ✅ |
| Geofencing | ❌ | ✅ | ✅ |
| Device Trust | ❌ | ✅ | ✅ |
| Custom Policy Engine | ❌ | ❌ | ✅ |
| **Service Discovery** |
| Automatic Discovery | ✅ | ✅ | ✅ |
| Basic Classification | ✅ | ✅ | ✅ |
| ML-Powered Classification | ❌ | ✅ | ✅ |
| Custom Classification Rules | ❌ | ✅ | ✅ |
| Service Dependencies | ❌ | ✅ | ✅ |
| **Monitoring & Metrics** |
| Basic Metrics | ✅ | ✅ | ✅ |
| Advanced Metrics | ❌ | ✅ | ✅ |
| Custom Dashboards | ❌ | ✅ | ✅ |
| Historical Data (retention) | 30 days | 1 year | Unlimited |
| Anomaly Detection | ❌ | ✅ | ✅ |
| Capacity Planning | ❌ | ✅ | ✅ |
| **Alerting** |
| Basic Alerts | ✅ | ✅ | ✅ |
| Advanced Alert Rules | ❌ | ✅ | ✅ |
| Alert Integrations | ❌ | ✅ | ✅ |
| Smart Alert Routing | ❌ | ✅ | ✅ |
| On-Call Scheduling | ❌ | ✅ | ✅ |
| **Secrets Management** |
| Built-in Secrets Storage | ✅ | ✅ | ✅ |
| Vault Integration | ❌ | ✅ | ✅ |
| AWS Secrets Manager | ❌ | ✅ | ✅ |
| Azure Key Vault | ❌ | ✅ | ✅ |
| HSM Support | ❌ | ❌ | ✅ |
| **Compliance & Audit** |
| Audit Logging | 30 days | 1 year | Unlimited |
| Compliance Templates | ❌ | ✅ | ✅ |
| Custom Compliance Frameworks | ❌ | ❌ | ✅ |
| Automated Reporting | ❌ | ✅ | ✅ |
| **High Availability** |
| Single Controller | ✅ | ✅ | ✅ |
| Multi-Controller HA | ❌ | ❌ | ✅ |
| Database Replication | ❌ | ❌ | ✅ |
| Geographic Distribution | ❌ | ❌ | ✅ |
| Load Balancing | ❌ | ❌ | ✅ |
| **Integrations** |
| REST API | ✅ | ✅ | ✅ |
| Webhooks | ❌ | ✅ | ✅ |
| Slack | ❌ | ✅ | ✅ |
| PagerDuty | ❌ | ✅ | ✅ |
| Jira | ❌ | ✅ | ✅ |
| ServiceNow | ❌ | ✅ | ✅ |
| Splunk | ❌ | ✅ | ✅ |
| Datadog | ❌ | ✅ | ✅ |
| Custom Integrations | ❌ | ❌ | ✅ |
| **Clients** |
| Web Interface | ✅ | ✅ | ✅ |
| Desktop Client (Basic) | ✅ | ✅ | ✅ |
| Desktop Client (Advanced) | ❌ | ✅ | ✅ |
| Mobile Apps | ❌ | ✅ | ✅ |
| CLI Tool | ✅ | ✅ | ✅ |
| **Customization** |
| Custom Themes | ❌ | ❌ | ✅ |
| White-Labeling | ❌ | ❌ | ✅ |
| Custom Branding | ❌ | ❌ | ✅ |
| **Support** |
| Community Forums | ✅ | ✅ | ✅ |
| Documentation | ✅ | ✅ | ✅ |
| Email Support | ❌ | 48-hour | 4-hour |
| Phone Support | ❌ | ❌ | 24/7 |
| Dedicated Account Manager | ❌ | ❌ | ✅ |
| On-Site Training | ❌ | ❌ | ✅ |
| Custom Development | ❌ | ❌ | Limited |

## Add-Ons & Services

### Professional Services

**Migration Services**: $5,000 - $25,000
- Assessment of existing infrastructure
- Migration planning and execution
- Data migration from existing tools
- Configuration and optimization
- Post-migration support (30 days)

**Custom Integration Development**: $10,000 - $50,000
- Custom authentication provider integration
- Third-party tool integration
- Custom classification rules
- Workflow automation
- API development

**Training & Onboarding**: $2,500/day
- On-site or remote training
- Administrator training
- Developer training
- Best practices workshops
- Custom training materials

**Architecture Review**: $5,000
- Infrastructure assessment
- Performance optimization recommendations
- Security audit
- Scalability planning
- Detailed report with action items

### Support Plans

**Extended Support** (Professional Edition): +$1,500/year
- 24/7 email support
- 24-hour response time
- Quarterly check-ins
- Priority bug fixes

**Premium Support** (Professional Edition): +$5,000/year
- 24/7 phone and email support
- 4-hour response time
- Monthly check-ins
- Dedicated support engineer
- Priority feature requests

## Volume Discounts

**Multi-Year Commitments**:
- 2-year commitment: 10% discount
- 3-year commitment: 20% discount
- 5-year commitment: 30% discount

**Multiple Deployments**:
- 2-5 deployments: 15% discount
- 6-10 deployments: 25% discount
- 11+ deployments: 35% discount

**Non-Profit & Education**:
- Non-profit organizations: 50% discount
- Educational institutions: 50% discount
- Open-source projects: Free Enterprise Edition

## Frequently Asked Questions

### Can I upgrade from Community to Professional?

Yes! You can upgrade at any time. Your existing configuration, users, and data will be preserved. Simply apply your license key and restart the controller.

### What happens if my license expires?

With an annual subscription, if your license expires:
- Your system continues to function
- You lose access to tier-specific features
- You revert to Community Edition features
- Your data is preserved
- You can renew at any time to restore features

With a perpetual license:
- Your system continues to function indefinitely
- You keep all features forever
- You lose access to updates and support after 1 year
- You can purchase support/updates separately

### Can I try Professional or Enterprise before buying?

Yes! We offer:
- 30-day free trial of Professional Edition
- 14-day free trial of Enterprise Edition
- Proof-of-concept deployments with support
- Demo environments for evaluation

### Do you offer custom pricing for large deployments?

Yes! For deployments with:
- 1,000+ VMs
- Multiple regions
- Custom requirements
- MSP/service provider use cases

Contact us for custom pricing and volume discounts.

### Is there a limit on API calls?

- Community: 1,000 API calls/hour
- Professional: 10,000 API calls/hour
- Enterprise: Unlimited API calls

### Can I run multiple controllers with Professional Edition?

No, high availability with multiple controllers requires Enterprise Edition. However, you can run multiple independent Professional Edition deployments.

### What payment methods do you accept?

- Credit card (Visa, MasterCard, Amex)
- ACH/Wire transfer
- Purchase orders (Enterprise only)
- Cryptocurrency (Bitcoin, Ethereum)

### Do you offer refunds?

- Annual subscriptions: 30-day money-back guarantee
- Perpetual licenses: 60-day money-back guarantee
- No refunds on professional services

### How is VM Gateway licensed for MSPs?

MSPs and service providers should contact us for custom licensing. We offer:
- Multi-tenant licensing
- Per-customer pricing
- White-label options
- Revenue sharing models

## Getting Started

Ready to get started with VM Gateway?

1. **Try Community Edition**: [Download and install](/docs/01-overview/05-getting-started.md) the free Community Edition
2. **Request a Trial**: Contact us for a Professional or Enterprise trial
3. **Schedule a Demo**: Book a personalized demo with our team
4. **Get a Quote**: Request a custom quote for your deployment

## Next Steps

- **[Getting Started Guide](/docs/01-overview/05-getting-started.md)**: Install and configure VM Gateway
- **[Feature Gating](/docs/01-overview/04-feature-gating.md)**: Learn how feature flags work
- **[Architecture](/docs/02-architecture/01-system-overview.md)**: Understand the system architecture
- **[Deployment](/docs/08-deployment/01-overview.md)**: Production deployment guide

## Summary

VM Gateway's flexible pricing model ensures you only pay for the features you need, with no per-user or per-VM fees. Start with the free Community Edition and upgrade as your needs grow. With perpetual licensing options and volume discounts, VM Gateway provides exceptional value for organizations of all sizes.

