"""
FastAPI application for VM Gateway documentation viewer.

This module implements the backend server for the documentation viewer,
providing endpoints to list, serve, and search documentation files.
"""

import logging
import time
from pathlib import Path
from typing import List, Optional
import re
import yaml

from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.responses import HTMLResponse, JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import markdown

# Import centralized logging and version
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))
from shared.logging import setup_logging
from shared.version import __version__

# Setup comprehensive logging
logger = setup_logging(
    component="doc_viewer",
    log_level="INFO",
    log_file="logs/doc_viewer/doc_viewer.log"
)

# Initialize FastAPI application
app = FastAPI(
    title="VM Gateway Documentation",
    description="Documentation viewer for VM Network Gateway & Access Control Platform",
    version=__version__
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all HTTP requests with timing information."""
    start_time = time.time()
    
    # Log request
    logger.info(f"Request: {request.method} {request.url.path}")
    logger.debug(f"Request headers: {dict(request.headers)}")
    logger.debug(f"Request query params: {dict(request.query_params)}")
    
    # Process request
    try:
        response = await call_next(request)
        
        # Calculate request duration
        duration = time.time() - start_time
        
        # Log response
        logger.info(
            f"Response: {request.method} {request.url.path} "
            f"- Status: {response.status_code} - Duration: {duration:.3f}s"
        )
        
        return response
    except Exception as e:
        duration = time.time() - start_time
        logger.error(
            f"Request failed: {request.method} {request.url.path} "
            f"- Error: {str(e)} - Duration: {duration:.3f}s",
            exc_info=True
        )
        raise

logger.info("=" * 80)
logger.info("VM Gateway Documentation Viewer Starting")
logger.info(f"Version: {__version__}")
logger.info(f"Docs root: {Path(__file__).parent.parent.parent / 'docs'}")
logger.info("=" * 80)

# Documentation root directory
DOCS_ROOT = Path(__file__).parent.parent.parent / "docs"

# Static files directory
STATIC_ROOT = Path(__file__).parent / "static"

# Mount static files
if STATIC_ROOT.exists():
    app.mount("/static", StaticFiles(directory=str(STATIC_ROOT)), name="static")


# Pydantic models for API responses
class DocFile(BaseModel):
    """Represents a single documentation file."""
    path: str
    section: str
    section_name: str
    title: str
    order: int
    filename: str


class DocSection(BaseModel):
    """Represents a documentation section with its files."""
    name: str
    order: int
    description: str
    directory: str
    files: List[DocFile]


class DocIndex(BaseModel):
    """Complete documentation index."""
    sections: List[DocSection]
    total_files: int
    total_sections: int


class SearchResult(BaseModel):
    """Search result item."""
    path: str
    file: str
    section: str
    title: str
    snippet: str
    relevance: float


# Markdown configuration with extensions
# Using built-in extensions for now; pymdownx extensions can be added after installation
md_parser = markdown.Markdown(
    extensions=[
        'extra',
        'codehilite',
        'toc',
        'tables',
        'fenced_code',
    ],
    extension_configs={
        'codehilite': {
            'css_class': 'highlight',
            'linenums': False
        },
        'toc': {
            'permalink': True,
            'toc_depth': 3
        }
    }
)


def extract_title_from_markdown(content: str) -> str:
    """
    Extract the first H1 heading from markdown content.
    
    Args:
        content: Markdown content
        
    Returns:
        Title string or "Untitled" if no H1 found
    """
    lines = content.split('\n')
    for line in lines:
        if line.startswith('# '):
            return line[2:].strip()
    return "Untitled"


def load_section_metadata(section_dir: Path) -> dict:
    """
    Load metadata from _meta.yaml file in a section directory.
    
    Args:
        section_dir: Path to section directory
        
    Returns:
        Dictionary with section metadata
    """
    meta_file = section_dir / "_meta.yaml"
    if meta_file.exists():
        try:
            with open(meta_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            logger.warning(f"Failed to load metadata from {meta_file}: {e}")
    
    # Default metadata if file doesn't exist or fails to load
    return {
        'name': section_dir.name,
        'order': 999,
        'description': ''
    }


def build_documentation_index() -> DocIndex:
    """
    Build a complete index of all documentation files.
    
    Scans the docs directory, reads metadata, and constructs
    a structured index of all sections and files.
    
    Returns:
        DocIndex object containing all documentation structure
    """
    logger.info("Building documentation index")
    sections = []
    total_files = 0
    
    # Get all section directories (numbered directories)
    section_dirs = sorted([
        d for d in DOCS_ROOT.iterdir()
        if d.is_dir() and re.match(r'^\d{2}-', d.name)
    ])
    
    for section_dir in section_dirs:
        # Load section metadata
        meta = load_section_metadata(section_dir)
        
        # Extract section order from directory name (e.g., "01-overview" -> 1)
        section_order = int(section_dir.name[:2])
        
        # Get all markdown files in the section (excluding _meta.yaml)
        md_files = sorted([
            f for f in section_dir.iterdir()
            if f.suffix == '.md' and not f.name.startswith('_')
        ])
        
        files = []
        for md_file in md_files:
            try:
                # Read file to extract title
                with open(md_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    title = extract_title_from_markdown(content)
                
                # Extract file order from filename (e.g., "01-introduction.md" -> 1)
                file_order = 999
                if re.match(r'^\d{2}-', md_file.name):
                    file_order = int(md_file.name[:2])
                
                files.append(DocFile(
                    path=f"{section_dir.name}/{md_file.name}",
                    section=section_dir.name,
                    section_name=meta.get('name', section_dir.name),
                    title=title,
                    order=file_order,
                    filename=md_file.name
                ))
                total_files += 1
                
            except Exception as e:
                logger.error(f"Failed to process {md_file}: {e}")
        
        if files:
            sections.append(DocSection(
                name=meta.get('name', section_dir.name),
                order=section_order,
                description=meta.get('description', ''),
                directory=section_dir.name,
                files=files
            ))
    
    logger.info(f"Documentation index built: {len(sections)} sections, {total_files} files")
    
    return DocIndex(
        sections=sections,
        total_files=total_files,
        total_sections=len(sections)
    )


@app.get("/", response_class=HTMLResponse)
async def root():
    """
    Serve the main documentation viewer page.
    
    Returns:
        HTML content for the documentation viewer interface
    """
    logger.info("Documentation viewer accessed")
    
    # Check if static index.html exists
    index_file = STATIC_ROOT / "index.html"
    if index_file.exists():
        return FileResponse(index_file)
    
    # Fallback HTML if static files not yet created
    return HTMLResponse(content="""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>VM Gateway Documentation</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                max-width: 800px;
                margin: 50px auto;
                padding: 20px;
                line-height: 1.6;
            }
            h1 { color: #2563eb; }
            .info { background: #f3f4f6; padding: 20px; border-radius: 8px; }
        </style>
    </head>
    <body>
        <h1>VM Gateway Documentation</h1>
        <div class="info">
            <p>Documentation viewer backend is running.</p>
            <p>Frontend interface will be available after completing task 16.</p>
            <p>API endpoints available:</p>
            <ul>
                <li><a href="/api/docs/list">/api/docs/list</a> - List all documentation</li>
                <li>/api/docs/{section}/{file} - Get specific documentation file</li>
                <li>/api/docs/search?q=query - Search documentation</li>
            </ul>
        </div>
    </body>
    </html>
    """)


@app.get("/api/docs/list", response_model=DocIndex)
async def list_docs():
    """
    Return list of all documentation files with metadata.
    
    Builds and returns a complete index of all documentation sections
    and files, including titles, ordering, and section information.
    
    Returns:
        DocIndex object with all documentation structure
    """
    logger.debug("Listing all documentation files")
    
    try:
        index = build_documentation_index()
        return index
    except Exception as e:
        logger.error(f"Failed to build documentation index: {e}")
        raise HTTPException(status_code=500, detail="Failed to build documentation index")


@app.get("/api/docs/{section}/{file}")
async def get_doc(section: str, file: str, anchor: Optional[str] = None):
    """
    Return rendered HTML for a specific documentation file.
    
    Reads the markdown file, parses it to HTML, and returns the rendered
    content along with metadata. Supports anchor links for jumping to specific sections.
    
    Args:
        section: Section directory name (e.g., "01-overview")
        file: Markdown filename (e.g., "01-introduction.md")
        anchor: Optional anchor/hash for jumping to specific section (e.g., "installation")
        
    Returns:
        JSON response with HTML content and metadata
    """
    logger.info(f"Serving documentation: {section}/{file}" + (f"#{anchor}" if anchor else ""))
    
    # Validate and sanitize paths to prevent directory traversal
    if '..' in section or '..' in file or '/' in section or '\\' in section:
        logger.warning(f"Invalid path attempted: {section}/{file}")
        raise HTTPException(status_code=400, detail="Invalid path")
    
    # Construct file path
    doc_path = DOCS_ROOT / section / file
    
    # Check if file exists
    if not doc_path.exists() or not doc_path.is_file():
        logger.warning(f"Documentation file not found: {doc_path}")
        raise HTTPException(status_code=404, detail="Documentation file not found")
    
    # Ensure the file is within the docs directory (security check)
    try:
        doc_path.resolve().relative_to(DOCS_ROOT.resolve())
    except ValueError:
        logger.error(f"Path traversal attempt detected: {doc_path}")
        raise HTTPException(status_code=403, detail="Access denied")
    
    try:
        # Read markdown content
        with open(doc_path, 'r', encoding='utf-8') as f:
            markdown_content = f.read()
        
        # Extract title
        title = extract_title_from_markdown(markdown_content)
        
        # Reset markdown parser state
        md_parser.reset()
        
        # Convert markdown to HTML
        html_content = md_parser.convert(markdown_content)
        
        # Get table of contents if available
        toc = getattr(md_parser, 'toc', '')
        
        # Load section metadata
        section_dir = DOCS_ROOT / section
        section_meta = load_section_metadata(section_dir)
        
        return JSONResponse(content={
            'title': title,
            'section': section,
            'section_name': section_meta.get('name', section),
            'file': file,
            'html': html_content,
            'toc': toc,
            'path': f"{section}/{file}",
            'anchor': anchor,
            'metadata': {
                'title': title,
                'section': section,
                'section_name': section_meta.get('name', section),
                'file': file,
                'path': f"{section}/{file}",
                'anchor': anchor
            }
        })
        
    except Exception as e:
        logger.error(f"Failed to render documentation {section}/{file}: {e}")
        raise HTTPException(status_code=500, detail="Failed to render documentation")


@app.get("/api/docs/search")
async def search_docs(q: str):
    """
    Search documentation content.
    
    Performs a simple text search across all documentation files,
    returning matching results with snippets and relevance scores.
    
    Args:
        q: Search query string
        
    Returns:
        JSON response with list of search results
    """
    logger.info(f"Documentation search query: {q}")
    
    if not q or len(q.strip()) < 2:
        return JSONResponse(content={'results': [], 'query': q, 'total': 0})
    
    query = q.lower().strip()
    results = []
    
    try:
        # Build index to get all files
        index = build_documentation_index()
        
        # Search through all files
        for section in index.sections:
            for doc_file in section.files:
                doc_path = DOCS_ROOT / doc_file.path
                
                try:
                    with open(doc_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    content_lower = content.lower()
                    
                    # Check if query appears in content
                    if query in content_lower:
                        # Calculate simple relevance score
                        count = content_lower.count(query)
                        title_match = query in doc_file.title.lower()
                        relevance = count + (10 if title_match else 0)
                        
                        # Extract snippet around first occurrence
                        index_pos = content_lower.find(query)
                        start = max(0, index_pos - 100)
                        end = min(len(content), index_pos + 100)
                        snippet = content[start:end].strip()
                        
                        # Add ellipsis if truncated
                        if start > 0:
                            snippet = '...' + snippet
                        if end < len(content):
                            snippet = snippet + '...'
                        
                        results.append(SearchResult(
                            path=doc_file.path,
                            file=doc_file.filename,
                            section=doc_file.section_name,
                            title=doc_file.title,
                            snippet=snippet,
                            relevance=float(relevance)
                        ))
                        
                except Exception as e:
                    logger.warning(f"Failed to search file {doc_file.path}: {e}")
        
        # Sort by relevance (highest first)
        results.sort(key=lambda x: x.relevance, reverse=True)
        
        # Limit to top 50 results
        results = results[:50]
        
        logger.info(f"Search for '{q}' returned {len(results)} results")
        
        return JSONResponse(content={
            'results': [r.model_dump() for r in results],
            'query': q,
            'total': len(results)
        })
        
    except Exception as e:
        logger.error(f"Search failed: {e}")
        raise HTTPException(status_code=500, detail="Search failed")


@app.get("/api/version")
async def get_version():
    """
    Get platform version information.
    
    Returns:
        JSON response with version details
    """
    from shared.version import get_version_info
    return JSONResponse(content=get_version_info())


@app.get("/health")
async def health_check():
    """
    Health check endpoint.
    
    Returns:
        JSON response indicating service health
    """
    return JSONResponse(content={
        'status': 'healthy',
        'service': 'doc_viewer',
        'version': __version__
    })
