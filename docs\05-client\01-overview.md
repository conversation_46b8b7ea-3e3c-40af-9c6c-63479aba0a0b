---
title: "Client Overview"
section: "Client"
order: 1
tags: ["client", "desktop", "overview", "architecture"]
last_updated: "2025-11-08"
---

# Client Overview

The VM Gateway Client is a lightweight, cross-platform desktop application that enables secure, granular access to services running on remote VMs through encrypted tunnels and local port forwarding. Unlike traditional VPN solutions that provide broad network access, the client implements a zero-trust security model where users can only access specific services they have been explicitly granted permission to use.

## Purpose and Value Proposition

The client application serves as the primary interface for end users to securely connect to remote services without requiring complex network configurations, VPN setups, or direct network access to the target VMs. It provides a seamless, user-friendly experience that abstracts away the complexity of secure tunneling while maintaining enterprise-grade security standards.

### Key Benefits

**Zero-Trust Security Model**
- No broad network access granted to users
- Per-service tunnels only, not full network access
- Continuous authentication with token expiration and refresh
- Session time limits enforced by the controller
- Automatic disconnection when policies change
- No service discovery from client without proper permissions
- Client cannot enumerate services without authorization

**Seamless User Experience**
- Simple, intuitive interface accessible from system tray
- One-click connection to authorized services
- Automatic reconnection on network changes
- Saved connection profiles for frequent access
- No manual tunnel configuration required
- Works alongside corporate VPNs without conflicts

**Enterprise-Grade Security**
- End-to-end encryption for all traffic
- Multiple tunnel protocols (WireGuard, TLS, WebSocket)
- Secure credential storage using OS-native keychains
- Certificate pinning to prevent man-in-the-middle attacks
- Automatic token rotation and refresh
- Tamper detection and integrity verification

**Cross-Platform Support**
- Native applications for Windows, macOS, and Linux
- Consistent user experience across all platforms
- Platform-specific integrations (Windows Hello, Touch ID, etc.)
- Optimized for each operating system's conventions

**Developer-Friendly**
- Local port forwarding makes remote services appear local
- Use standard tools without modification (psql, mysql, redis-cli, etc.)
- Connection profiles for different environments
- Statistics and monitoring for troubleshooting
- Built-in diagnostic tools

## Architecture Overview

The client application is built using modern cross-platform technologies that provide native performance and user experience while maintaining a single codebase for core functionality.

### Technology Stack

**Application Framework**
- **Electron** or **Tauri**: Cross-platform desktop application framework
  - Electron: Mature, widely adopted, extensive ecosystem
  - Tauri: Lightweight, Rust-based, smaller binary size
- Choice depends on deployment requirements and performance needs

**Frontend Layer**
- **React** with **TypeScript**: Modern, type-safe UI development
- **Tailwind CSS** with **shadcn/ui**: Consistent, professional styling
- **Zustand** or **Redux Toolkit**: State management for complex UI state
- **React Query**: Server state management and caching

**Backend Layer**
- **Python** or **Node.js**: Backend logic and system integration
  - Python: Consistency with platform stack, rich networking libraries
  - Node.js: Native integration with Electron, async I/O
- **IPC Bridge**: Communication between frontend and backend
- **Native Modules**: OS-specific integrations and system calls

**Networking and Tunneling**
- **WireGuard**: Modern, fast VPN protocol (preferred)
- **TLS 1.3**: Fallback tunnel protocol for restrictive networks
- **WebSocket**: Maximum compatibility for highly restricted environments
- **TCP/UDP Forwarding**: Local port forwarding implementation

**Security Components**
- **OS Keyring Integration**: Secure token storage (Windows Credential Manager, macOS Keychain, Linux Secret Service)
- **Certificate Pinning**: Prevent MITM attacks
- **Encryption Libraries**: cryptography (Python) or crypto (Node.js)
- **OAuth 2.0 / OpenID Connect**: Authentication flow

**Data Storage**
- **SQLite**: Local database for connection history, profiles, and cache
- **JSON Configuration**: User preferences and settings
- **Encrypted Storage**: Sensitive data protection

### High-Level Architecture

```
┌──────────────────────────────────────────────┐
│    Client Application (Electron/Tauri)       │
├──────────────────────────────────────────────┤
│  ┌──────────────────────────────────────┐   │
│  │   Frontend (React + TypeScript)      │   │
│  │   - Service list UI                  │   │
│  │   - Connection status dashboard      │   │
│  │   - Settings panel                   │   │
│  │   - Statistics and monitoring        │   │
│  └──────────────────────────────────────┘   │
│              ↕ IPC/Bridge                    │
│  ┌──────────────────────────────────────┐   │
│  │   Backend (Python or Node.js)        │   │
│  │                                       │   │
│  │  ┌─────────────────────────────────┐ │   │
│  │  │  Authentication Manager         │ │   │
│  │  │  - OAuth flow handling          │ │   │
│  │  │  - Token storage (OS keyring)   │ │   │
│  │  │  - Token refresh automation     │ │   │
│  │  │  - Session management           │ │   │
│  │  └─────────────────────────────────┘ │   │
│  │                                       │   │
│  │  ┌─────────────────────────────────┐ │   │
│  │  │  Connection Manager             │ │   │
│  │  │  - Request tunnels from server  │ │   │
│  │  │  - Track active connections     │ │   │
│  │  │  - Handle disconnects/reconnect │ │   │
│  │  │  - Connection lifecycle         │ │   │
│  │  └─────────────────────────────────┘ │   │
│  │                                       │   │
│  │  ┌─────────────────────────────────┐ │   │
│  │  │  Tunnel Manager                 │ │   │
│  │  │  - WireGuard or TLS tunnel      │ │   │
│  │  │  - Tunnel keep-alive            │ │   │
│  │  │  - Network change detection     │ │   │
│  │  │  - Protocol negotiation         │ │   │
│  │  └─────────────────────────────────┘ │   │
│  │                                       │   │
│  │  ┌─────────────────────────────────┐ │   │
│  │  │  Local Port Forwarder           │ │   │
│  │  │  - Bind local ports             │ │   │
│  │  │  - Forward traffic to tunnel    │ │   │
│  │  │  - Connection multiplexing      │ │   │
│  │  │  - Traffic statistics           │ │   │
│  │  └─────────────────────────────────┘ │   │
│  │                                       │   │
│  │  ┌─────────────────────────────────┐ │   │
│  │  │  State Persistence              │ │   │
│  │  │  - Saved connections            │ │   │
│  │  │  - User preferences             │ │   │
│  │  │  - Local database (SQLite)      │ │   │
│  │  │  - Connection history           │ │   │
│  │  └─────────────────────────────────┘ │   │
│  └──────────────────────────────────────┘   │
└──────────────────────────────────────────────┘
         ↕ HTTPS/WebSocket
┌──────────────────────────────────────────────┐
│         Controller API & Web Interface       │
│  - Authentication endpoints                  │
│  - Service catalog API                       │
│  - Tunnel request handling                   │
│  - Permission validation                     │
└──────────────────────────────────────────────┘
         ↕ Encrypted Tunnel
┌──────────────────────────────────────────────┐
│              Agent on Remote VM              │
│  - Tunnel termination                        │
│  - Service forwarding                        │
│  - Traffic routing                           │
└──────────────────────────────────────────────┘
```

## Core Components

### Authentication Manager

Handles all aspects of user authentication and session management:

- **OAuth 2.0 Flow**: Implements authorization code flow with PKCE for secure authentication
- **Browser Integration**: Opens system browser for authentication, intercepts callback
- **Token Management**: Stores, refreshes, and rotates access tokens automatically
- **Secure Storage**: Uses OS-native keyring/keychain for credential storage
- **Multi-Factor Authentication**: Supports TOTP, WebAuthn, and platform authenticators
- **Session Tracking**: Monitors session validity and handles expiration gracefully

### Connection Manager

Orchestrates the lifecycle of service connections:

- **Service Discovery**: Fetches available services from controller API
- **Permission Validation**: Ensures user has access before attempting connection
- **Connection Requests**: Communicates with controller to establish tunnels
- **State Management**: Tracks active, pending, and failed connections
- **Reconnection Logic**: Handles network interruptions and automatic reconnection
- **Connection Profiles**: Manages saved connection configurations

### Tunnel Manager

Establishes and maintains secure encrypted tunnels:

- **Protocol Selection**: Chooses optimal tunnel protocol (WireGuard, TLS, WebSocket)
- **Tunnel Establishment**: Creates encrypted tunnel to agent via controller
- **Keep-Alive**: Maintains tunnel with periodic heartbeats
- **Network Monitoring**: Detects network changes and adapts accordingly
- **Encryption**: Ensures all traffic is encrypted end-to-end
- **Performance Optimization**: Minimizes latency and maximizes throughput

### Local Port Forwarder

Implements local port forwarding to make remote services appear local:

- **Port Binding**: Listens on local ports (localhost or specific interface)
- **Traffic Forwarding**: Routes traffic from local port through tunnel to remote service
- **Connection Multiplexing**: Handles multiple concurrent connections per service
- **Port Management**: Assigns ports automatically or uses user-specified ports
- **Conflict Resolution**: Detects and resolves port conflicts
- **Statistics Collection**: Tracks bytes transferred, latency, and connection count

### State Persistence

Manages local data storage and user preferences:

- **SQLite Database**: Stores connection history, profiles, and cached data
- **Configuration Files**: JSON-based settings and preferences
- **Connection Profiles**: Saved configurations for frequent connections
- **Favorites**: User-starred services for quick access
- **Statistics History**: Historical connection data for analysis
- **Sync Capability**: Optional sync of preferences across devices via controller

## User Interface Philosophy

The client application prioritizes simplicity and accessibility while providing power users with advanced features when needed.

### Design Principles

**Minimal and Unobtrusive**
- Lives in system tray/menu bar, out of the way until needed
- Clean, focused interface without clutter
- Progressive disclosure: simple by default, advanced features available

**Immediate Feedback**
- Real-time connection status updates
- Clear error messages with actionable guidance
- Visual indicators for connection health
- Desktop notifications for important events

**Consistency Across Platforms**
- Familiar patterns for each operating system
- Native look and feel while maintaining brand identity
- Platform-specific integrations where appropriate

**Accessibility**
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Configurable font sizes

## Security Model

The client implements multiple layers of security to protect user credentials, data in transit, and prevent unauthorized access.

### Zero-Trust Principles

**No Implicit Trust**
- Every connection requires explicit authorization
- Permissions validated on every request
- No cached credentials for services
- Continuous authentication with token refresh

**Least Privilege Access**
- Users can only see services they have permission to access
- Cannot enumerate or discover unauthorized services
- Per-service tunnels, not network-wide access
- Time-limited sessions with automatic expiration

**Defense in Depth**
- Multiple layers of encryption (tunnel + TLS)
- Certificate pinning prevents MITM attacks
- Secure token storage in OS keyring
- Tamper detection for application integrity
- Audit logging of all connection attempts

### Encryption and Data Protection

**Data in Transit**
- All traffic encrypted end-to-end
- WireGuard: ChaCha20-Poly1305 or AES-256-GCM
- TLS 1.3: Modern cipher suites only
- Perfect forward secrecy (PFS)

**Data at Rest**
- Tokens encrypted in OS keyring
- Local database encrypted (SQLCipher)
- Configuration files with sensitive data encrypted
- Secure deletion of temporary files

**Network Security**
- Certificate pinning to controller
- Mutual TLS (mTLS) for agent communication
- DNS over HTTPS (DoH) support
- Protection against DNS hijacking

## Use Cases

### Developer Access to Databases

A developer needs to query a production database for debugging:

1. Opens client application
2. Searches for "production database"
3. Clicks "Connect" on postgres-prod-main
4. Client establishes tunnel and binds localhost:5432
5. Developer uses psql or pgAdmin to connect to localhost:5432
6. Queries execute as if database were local
7. Connection automatically logs and audits all activity
8. Session expires after configured time limit

### DevOps Engineer Accessing Multiple Services

An engineer needs to access Redis, PostgreSQL, and an API simultaneously:

1. Opens client and navigates to "Connection Profiles"
2. Selects "Staging Environment" profile
3. Profile automatically connects to all three services
4. Each service available on its configured local port
5. Engineer runs scripts that connect to localhost ports
6. All connections maintained with automatic reconnection
7. Statistics tracked for each service independently

### Remote Worker on Restricted Network

A remote worker on a corporate network with strict firewall rules:

1. Client detects WireGuard is blocked
2. Automatically falls back to TLS tunnel
3. TLS tunnel still blocked, falls back to WebSocket
4. WebSocket tunnel established over HTTPS (port 443)
5. All traffic appears as normal HTTPS to firewall
6. Worker accesses services without network restrictions
7. Performance slightly reduced but connection stable

### Emergency Access with Approval

A developer needs emergency access to production service:

1. Searches for production service in client
2. Clicks "Request Access" (service requires approval)
3. Fills out justification form
4. Approval request sent to on-call engineer
5. Engineer approves via mobile notification
6. Client receives approval and establishes connection
7. Access granted for 30 minutes only
8. Connection automatically terminated after time limit
9. Full audit trail maintained

## Performance Characteristics

The client is designed to be lightweight and efficient, minimizing resource usage while maintaining high performance.

### Resource Usage

**Memory Footprint**
- Idle: ~50-100 MB RAM
- Active with 5 connections: ~150-200 MB RAM
- Scales linearly with connection count
- Efficient memory management with connection pooling

**CPU Usage**
- Idle: <1% CPU
- Active connections: 1-5% CPU depending on traffic
- Tunnel encryption overhead: minimal with hardware acceleration
- Background tasks: periodic sync, heartbeats (negligible impact)

**Network Overhead**
- WireGuard: ~60 bytes per packet overhead
- TLS: ~40-60 bytes per record overhead
- WebSocket: ~6-14 bytes per frame overhead
- Keep-alive traffic: ~1 KB/minute per connection

**Disk Usage**
- Application size: 50-150 MB (varies by platform)
- Local database: <10 MB typical
- Logs: Configurable, default 50 MB with rotation
- Cache: <5 MB for service metadata

### Latency and Throughput

**Connection Establishment**
- Authentication: 200-500ms (cached token)
- Tunnel establishment: 100-300ms (WireGuard)
- Total time to connect: 300-800ms typical

**Data Transfer**
- Latency overhead: 1-5ms (WireGuard), 5-15ms (TLS)
- Throughput: Near line-rate with hardware acceleration
- Concurrent connections: 100+ per client (practical limit)

## Compatibility and Requirements

### Operating System Support

**Windows**
- Windows 10 (version 1809 or later)
- Windows 11 (all versions)
- Windows Server 2019 and later
- Both x64 and ARM64 architectures

**macOS**
- macOS 10.15 (Catalina) or later
- macOS 11 (Big Sur) and later recommended
- Both Intel and Apple Silicon (M1/M2/M3) supported
- Universal binary for optimal performance

**Linux**
- Ubuntu 20.04 LTS and later
- Debian 10 and later
- Fedora 35 and later
- RHEL/CentOS 8 and later
- Arch Linux (rolling release)
- Multiple distribution formats: deb, rpm, AppImage, Flatpak

### Network Requirements

**Outbound Connectivity**
- HTTPS (port 443) to controller required
- WireGuard (UDP port 51820) preferred but optional
- WebSocket fallback over HTTPS if needed
- No inbound ports required

**Firewall Compatibility**
- Works with corporate firewalls
- Proxy support (HTTP, SOCKS5)
- No special firewall rules needed
- Compatible with VPN clients

### Hardware Requirements

**Minimum**
- CPU: Dual-core 1.5 GHz
- RAM: 2 GB
- Disk: 200 MB free space
- Network: 1 Mbps connection

**Recommended**
- CPU: Quad-core 2.0 GHz or better
- RAM: 4 GB or more
- Disk: 500 MB free space
- Network: 10 Mbps or faster connection

## Future Enhancements

The client application roadmap includes several planned enhancements:

- **Mobile Applications**: iOS and Android clients for on-the-go access
- **Browser Extension**: Lightweight extension for HTTP/HTTPS services only
- **CLI Version**: Command-line client for automation and scripting
- **Connection Sharing**: Share active connections with other local applications
- **Advanced Diagnostics**: Built-in network analysis and troubleshooting tools
- **Offline Mode**: Limited functionality when controller unreachable
- **Multi-Account**: Switch between multiple controller accounts
- **Team Collaboration**: Share connection profiles with team members
- **Integration APIs**: Allow other applications to request connections programmatically
